# GitHub Actions Vercel Deployment Fix

## Problem Summary
The GitHub Actions workflow was failing with the error:
```
Error: Unable to resolve action vercel/action@v1, action not found
```

## Root Cause
**`vercel/action@v1` does not exist.** There is no official Vercel GitHub Action with that name. This was an incorrect recommendation.

## Solution: Official Vercel CLI Approach (Implemented)

I've updated your workflow to use Vercel's officially recommended approach using the Vercel CLI directly. This is the most reliable and officially supported method.

### Key Changes Made:

1. **Removed non-existent action**: Replaced `vercel/action@v1` with direct Vercel CLI commands
2. **Added Vercel CLI installation**: `npm install --global vercel@latest`
3. **Implemented proper build flow**: Pull → Build → Deploy
4. **Environment-specific deployments**: Separate flows for preview and production
5. **Proper working directory**: All commands run in `./learn-platform/apps/web`

### Updated Workflow Steps:

```yaml
- name: Install Vercel CLI
  run: npm install --global vercel@latest

- name: Pull Vercel Environment Information (Preview)
  if: github.event.inputs.environment == 'preview' || (github.event_name == 'push' && github.ref == 'refs/heads/dev')
  run: vercel pull --yes --environment=preview --token=${{ secrets.VERCEL_TOKEN }}
  working-directory: ./learn-platform/apps/web
  env:
    VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
    VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID_WEB }}

- name: Build Project Artifacts (Preview)
  if: github.event.inputs.environment == 'preview' || (github.event_name == 'push' && github.ref == 'refs/heads/dev')
  run: vercel build --token=${{ secrets.VERCEL_TOKEN }}
  working-directory: ./learn-platform/apps/web
  env:
    VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
    VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID_WEB }}

- name: Deploy Project Artifacts to Vercel (Preview)
  if: github.event.inputs.environment == 'preview' || (github.event_name == 'push' && github.ref == 'refs/heads/dev')
  run: vercel deploy --prebuilt --token=${{ secrets.VERCEL_TOKEN }}
  working-directory: ./learn-platform/apps/web
  env:
    VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
    VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID_WEB }}
```

## Alternative: Third-Party Action (If Preferred)

If you prefer using a GitHub Action instead of CLI commands, use the correct third-party action:

```yaml
- name: Deploy to Vercel (Preview)
  if: github.event.inputs.environment == 'preview' || (github.event_name == 'push' && github.ref == 'refs/heads/dev')
  uses: amondnet/vercel-action@v25
  with:
    vercel-token: ${{ secrets.VERCEL_TOKEN }}
    github-token: ${{ secrets.GITHUB_TOKEN }}
    vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
    vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID_WEB }}
    working-directory: ./learn-platform/apps/web
    scope: ${{ secrets.VERCEL_ORG_ID }}

- name: Deploy to Vercel (Production)
  if: github.event.inputs.environment == 'production' || (github.event_name == 'push' && github.ref == 'refs/heads/main')
  uses: amondnet/vercel-action@v25
  with:
    vercel-token: ${{ secrets.VERCEL_TOKEN }}
    github-token: ${{ secrets.GITHUB_TOKEN }}
    vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
    vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID_WEB }}
    vercel-args: '--prod'
    working-directory: ./learn-platform/apps/web
    scope: ${{ secrets.VERCEL_ORG_ID }}
```

## Benefits of Official CLI Approach

1. **Official Support**: Directly supported by Vercel
2. **Latest Features**: Always uses the latest Vercel CLI
3. **Better Control**: More granular control over the deployment process
4. **Nx Monorepo Compatible**: Works perfectly with Nx monorepo structure
5. **No Third-Party Dependencies**: No reliance on community-maintained actions

## Required Secrets

Ensure these secrets are configured in your GitHub repository:

- `VERCEL_TOKEN`: Your Vercel API token
- `VERCEL_ORG_ID`: Your Vercel organization ID
- `VERCEL_PROJECT_ID_WEB`: Your Vercel project ID for the web app

## Verification Steps

1. **Check Secrets**: Ensure all required secrets are set in GitHub
2. **Test Deployment**: Push to dev branch to test preview deployment
3. **Monitor Logs**: Check GitHub Actions logs for successful deployment
4. **Verify Vercel**: Check Vercel dashboard for successful deployments

## Expected Outcome

After implementing this fix:
- GitHub Actions will successfully resolve all actions
- Vercel CLI will be installed and used for deployment
- Both preview (dev branch) and production (main branch) deployments will work
- Nx monorepo structure will be properly handled
- Deployments will appear in both GitHub and Vercel dashboards

## Troubleshooting

If issues persist:
1. Verify all secrets are correctly set
2. Check that the working directory path is correct
3. Ensure the Vercel project is properly linked
4. Review Vercel CLI logs in GitHub Actions for specific errors
