# Definitive Vercel CLI Path Resolution Fix

## Root Cause: Dashboard Root Directory + Working Directory Conflict

The path duplication was caused by Vercel CLI interpreting the Dashboard Root Directory as **relative to the current working directory**:

**Problem Flow:**
1. GitHub Actions: `cd learn-platform` → Working directory: `/home/<USER>/work/.../learn-platform/`
2. Vercel Dashboard Root Directory: `learn-platform/apps/web`
3. Vercel CLI combines: `/learn-platform/` + `learn-platform/apps/web` = `/learn-platform/learn-platform/apps/web/` ❌

**Error Path:** `/home/<USER>/work/kwaci-learning/kwaci-learning/learn-platform/learn-platform/apps/web/package.json`

## Solution: Use GitHub Actions Working Directory Only

**Remove Dashboard Root Directory and use explicit working-directory in GitHub Actions.**

### 1. Updated GitHub Actions Workflow

**Before** (causing duplication):
```yaml
- name: Build Project Artifacts (Preview)
  run: |
    cd learn-platform
    vercel build --token=${{ secrets.VERCEL_TOKEN }}
```

**After** (single path resolution):
```yaml
- name: Build Project Artifacts (Preview)
  run: vercel build --token=${{ secrets.VERCEL_TOKEN }}
  working-directory: ./learn-platform/apps/web
```

### 2. Updated vercel.json

Since Vercel CLI now runs from the app directory, the install command needs to navigate to the monorepo root:

```json
{
  "version": 2,
  "public": false,
  "github": {
    "enabled": false
  },
  "buildCommand": "npm run vercel-build",
  "installCommand": "cd ../../ && bun install --frozen-lockfile",
  "framework": "nextjs",
  "outputDirectory": ".next"
}
```

### 3. Vercel Dashboard Configuration

**CRITICAL: Remove the Root Directory setting in Vercel Dashboard:**

1. Go to your Vercel project dashboard
2. Navigate to Settings → Build & Deployment
3. **Clear the Root Directory field** (leave it empty)
4. Click Save

This eliminates the conflict between Dashboard Root Directory and GitHub Actions working directory.

## How This Fixes the Issue

### New Path Resolution Flow:
1. **GitHub Actions**: Runs from repository root (`/home/<USER>/work/kwaci-learning/kwaci-learning/`)
2. **Vercel CLI**: Executes with `working-directory: ./learn-platform/apps/web`
3. **Final Working Directory**: `/home/<USER>/work/kwaci-learning/kwaci-learning/learn-platform/apps/web/`
4. **Package.json Path**: `/home/<USER>/work/kwaci-learning/kwaci-learning/learn-platform/apps/web/package.json` ✅

### No Dashboard Root Directory Conflict:
- ❌ **Old**: Dashboard Root Directory + GitHub Actions working directory = duplication
- ✅ **New**: GitHub Actions working directory only = single, correct path

## Configuration Summary

### GitHub Actions Workflow:
```yaml
- name: Build Project Artifacts (Preview)
  run: vercel build --token=${{ secrets.VERCEL_TOKEN }}
  working-directory: ./learn-platform/apps/web  # Explicit path to app
  env:
    VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
    VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID_WEB }}
```

### Vercel Dashboard:
- **Root Directory**: *(empty)* ✅
- **Build Command**: *(auto-detected from vercel.json)*
- **Install Command**: *(auto-detected from vercel.json)*

### vercel.json:
```json
{
  "buildCommand": "npm run vercel-build",
  "installCommand": "cd ../../ && bun install --frozen-lockfile",
  "framework": "nextjs"
}
```

## Expected Results

With this configuration:
- ✅ Vercel CLI finds package.json at: `/learn-platform/apps/web/package.json`
- ✅ No path duplication errors
- ✅ Bun installs dependencies from monorepo root
- ✅ Next.js builds correctly from app directory
- ✅ Both preview and production deployments work

## Verification

The debug output should show:
```
Current working directory: /home/<USER>/work/kwaci-learning/kwaci-learning
Contents of learn-platform/apps/web:
-rw-r--r-- 1 <USER> <GROUP> package.json
-rw-r--r-- 1 <USER> <GROUP> vercel.json
Vercel CLI will run from: learn-platform/apps/web
```

And Vercel CLI should successfully find:
```
/home/<USER>/work/kwaci-learning/kwaci-learning/learn-platform/apps/web/package.json
```

## Key Actions Required

1. **Clear Vercel Dashboard Root Directory** (most important!)
2. **Commit the updated GitHub Actions workflow**
3. **Test the deployment**

## Why This Works

By removing the Dashboard Root Directory and using only GitHub Actions `working-directory`, we eliminate the path resolution conflict. Vercel CLI now runs directly from the app directory without any additional path interpretation.

**Single Source of Truth:** GitHub Actions `working-directory: ./learn-platform/apps/web`

This approach is cleaner and more predictable than trying to coordinate between Dashboard settings and GitHub Actions configurations.
