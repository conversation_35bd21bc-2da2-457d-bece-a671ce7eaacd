# Vercel CLI Build Output Path Duplication Fix

## Problem Analysis

After fixing the package.json path duplication, we encountered a new issue with build output path duplication:

**Error Path:** `/learn-platform/apps/web/apps/web/.next/routes-manifest.json`
**Correct Path:** `/learn-platform/apps/web/.next/routes-manifest.json`

The "apps/web" segment was being duplicated in the build output directory resolution.

## Root Cause

The issue was caused by the `outputFileTracingRoot` configuration in `next.config.js` conflicting with Vercel CLI's working directory:

1. **Vercel CLI working directory**: `./learn-platform/apps/web`
2. **outputFileTracingRoot**: `path.join(__dirname, '../../')` (points to `learn-platform/`)
3. **Vercel CLI**: Tries to resolve build outputs relative to the tracing root, causing path duplication

## Solution

### 1. Removed Conflicting outputFileTracingRoot

**Before** (causing duplication):
```javascript
const nextConfig = {
  // Include files from the monorepo base for Vercel deployment
  outputFileTracingRoot: path.join(__dirname, '../../'),
  // ...
};
```

**After** (clean path resolution):
```javascript
const nextConfig = {
  // For Vercel deployment from app directory, let Vercel handle file tracing
  // outputFileTracingRoot: path.join(__dirname, '../../'),
  // ...
};
```

**Rationale**: Since we're now running Vercel CLI directly from the app directory (`./learn-platform/apps/web`), we don't need to manually specify the tracing root. Vercel CLI can handle file tracing automatically.

### 2. Simplified vercel.json

**Before**:
```json
{
  "buildCommand": "npm run vercel-build",
  "installCommand": "cd ../../ && bun install --frozen-lockfile",
  "framework": "nextjs",
  "outputDirectory": ".next"
}
```

**After**:
```json
{
  "buildCommand": "npm run vercel-build",
  "installCommand": "cd ../../ && bun install --frozen-lockfile",
  "framework": "nextjs"
}
```

**Rationale**: Removed explicit `outputDirectory` to let Next.js and Vercel handle the default `.next` output directory without path conflicts.

### 3. Added Build Output Debugging

Added debugging steps to help identify build output issues:

```yaml
- name: Debug build output (Preview)
  run: |
    echo "Current working directory: $(pwd)"
    echo "Contents of current directory:"
    ls -la
    echo "Looking for .next directory:"
    find . -name ".next" -type d 2>/dev/null || echo "No .next directory found"
    echo "Contents of .next (if exists):"
    ls -la .next/ 2>/dev/null || echo ".next directory not found"
  working-directory: ./learn-platform/apps/web
```

## How This Fixes the Issue

### New Build Output Resolution:
1. **Vercel CLI**: Runs from `./learn-platform/apps/web`
2. **Next.js Build**: Creates `.next` directory in current working directory
3. **Build Output Path**: `./learn-platform/apps/web/.next/` ✅
4. **No Path Duplication**: Single, clean path resolution

### Removed Conflicts:
- ❌ **Old**: `outputFileTracingRoot` + working directory = path confusion
- ✅ **New**: Let Vercel CLI handle file tracing automatically

## Configuration Summary

### next.config.js:
```javascript
const nextConfig = {
  nx: {},
  
  // Removed outputFileTracingRoot to prevent path conflicts
  // outputFileTracingRoot: path.join(__dirname, '../../'),
  
  transpilePackages: [
    '@learn-platform/shared-ui',
    '@learn-platform/shared-styles',
    '@learn-platform/trpc',
    '@learn-platform/auth'
  ],
  
  webpack: (config) => {
    config.resolve.alias = {
      ...config.resolve.alias,
      '@learn-platform/shared-styles': require('path').resolve(__dirname, '../../libs/shared/styles/src'),
    };
    return config;
  },
};
```

### vercel.json:
```json
{
  "version": 2,
  "public": false,
  "github": {
    "enabled": false
  },
  "buildCommand": "npm run vercel-build",
  "installCommand": "cd ../../ && bun install --frozen-lockfile",
  "framework": "nextjs"
}
```

### GitHub Actions:
```yaml
- name: Build Project Artifacts (Preview)
  run: vercel build --token=${{ secrets.VERCEL_TOKEN }}
  working-directory: ./learn-platform/apps/web
```

## Expected Results

With this configuration:
- ✅ Build output created at: `/learn-platform/apps/web/.next/`
- ✅ No "apps/web" path duplication
- ✅ Vercel CLI finds routes-manifest.json at correct location
- ✅ Deployment proceeds successfully
- ✅ Shared packages still transpiled correctly

## Verification

The debug output should show:
```
Current working directory: /home/<USER>/work/kwaci-learning/kwaci-learning/learn-platform/apps/web
Looking for .next directory:
./next
Contents of .next (if exists):
-rw-r--r-- 1 <USER> <GROUP> routes-manifest.json
-rw-r--r-- 1 <USER> <GROUP> BUILD_ID
drwxr-xr-x 2 <USER> <GROUP> static
```

And Vercel CLI should successfully find:
```
/home/<USER>/work/kwaci-learning/kwaci-learning/learn-platform/apps/web/.next/routes-manifest.json
```

## Key Principle

**Let Vercel CLI handle file tracing automatically when running from the app directory.**

By removing manual path configurations that conflict with Vercel CLI's working directory, we achieve clean, predictable build output paths.

## Fallback Option

If shared package imports break due to removing `outputFileTracingRoot`, we can add it back with a more specific configuration:

```javascript
// Only if needed for shared package resolution
outputFileTracingRoot: process.env.VERCEL ? path.join(__dirname, '../../') : undefined,
```

This would only apply the tracing root when running on Vercel's platform, not during local Vercel CLI builds.
