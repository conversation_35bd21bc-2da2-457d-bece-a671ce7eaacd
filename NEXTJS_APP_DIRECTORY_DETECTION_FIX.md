# Next.js App Directory Detection Fix

## Problem Analysis

After implementing dependency tracing fixes, Next.js couldn't find the `app` directory during Vercel CLI build:

**Error:**
```
> Build error occurred
[Error: > Couldn't find any `pages` or `app` directory. Please create one under the project root]
error: script "vercel-build" exited with code 1
```

**Root Cause:** When Vercel CLI runs from the monorepo root (`cd learn-platform`), the build command (`next build`) executes from the monorepo root context, not the app directory context, causing Next.js to look for `app/` in the wrong location.

## The Directory Context Problem

### Current Setup:
- **Vercel CLI execution**: From monorepo root (`/learn-platform/`)
- **App directory location**: `/learn-platform/apps/web/src/app/`
- **Build command**: `next build` (runs from monorepo root)
- **Issue**: Next.js looks for `app/` relative to current working directory

### What Next.js Expected vs. Found:
- **Expected**: `./src/app/` (relative to app directory)
- **Actually looked for**: `./src/app/` (relative to monorepo root)
- **Actual location**: `./apps/web/src/app/` (relative to monorepo root)

## Solution: Fix Build Command Context

The solution is to modify the `buildCommand` in `vercel.json` to navigate to the app directory before running `next build`.

### Updated vercel.json

**Before** (causing directory detection failure):
```json
{
  "buildCommand": "npm run vercel-build",
  "installCommand": "bun install --frozen-lockfile",
  "framework": "nextjs"
}
```

**After** (correct directory context):
```json
{
  "buildCommand": "cd apps/web && npm run vercel-build",
  "installCommand": "bun install --frozen-lockfile",
  "framework": "nextjs"
}
```

### How This Fixes the Issue

**New Build Flow:**
1. **Vercel CLI**: Runs from monorepo root (`/learn-platform/`)
2. **Install Command**: `bun install --frozen-lockfile` (installs dependencies in monorepo root)
3. **Build Command**: `cd apps/web && npm run vercel-build` (navigates to app directory, then builds)
4. **Next.js Build**: Runs from `/learn-platform/apps/web/` context
5. **Directory Detection**: Next.js finds `src/app/` relative to app directory ✅

### Path Resolution:
- **Vercel CLI working directory**: `/learn-platform/`
- **Dependencies**: Available from `/learn-platform/node_modules/` (via outputFileTracingRoot)
- **Build execution**: `/learn-platform/apps/web/` (via `cd apps/web`)
- **App directory**: `./src/app/` (relative to build execution directory) ✅

## Configuration Summary

### GitHub Actions Workflow:
```yaml
- name: Build Project Artifacts (Preview)
  run: |
    cd learn-platform
    vercel build --token=${{ secrets.VERCEL_TOKEN }}
  env:
    VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
    VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID_WEB }}
```

### Vercel Dashboard:
- **Root Directory**: `learn-platform/apps/web` ✅

### vercel.json:
```json
{
  "version": 2,
  "public": false,
  "github": {
    "enabled": false
  },
  "buildCommand": "cd apps/web && npm run vercel-build",
  "installCommand": "bun install --frozen-lockfile",
  "framework": "nextjs"
}
```

### next.config.js:
```javascript
const nextConfig = {
  outputFileTracingRoot: path.join(__dirname, '../../'),
  transpilePackages: [
    '@learn-platform/shared-ui',
    '@learn-platform/shared-styles',
    '@learn-platform/trpc',
    '@learn-platform/auth'
  ],
};
```

### package.json (apps/web):
```json
{
  "scripts": {
    "vercel-build": "next build"
  }
}
```

## Expected Results

With this configuration:
- ✅ Next.js finds `src/app/` directory correctly
- ✅ Dependencies traced from monorepo root (via outputFileTracingRoot)
- ✅ Build executes in correct app directory context
- ✅ No path duplication issues
- ✅ Shared packages transpiled correctly
- ✅ Both local development and Vercel deployment work

## Verification

The build should now succeed with output like:
```
Running "bun run vercel-build"
$ next build
✓ Creating an optimized production build
✓ Compiled successfully
✓ Linting and checking validity of types
✓ Collecting page data
✓ Generating static pages
✓ Collecting build traces
✓ Finalizing page optimization
```

## Key Principle

**Separate concerns: Install dependencies from monorepo root, build from app directory.**

This approach provides:
- ✅ Dependency access from monorepo root
- ✅ Correct Next.js directory detection from app directory
- ✅ Clean separation of install and build contexts
- ✅ Compatibility with Nx monorepo structure

## Alternative Solutions Considered

### Option 1: Change Vercel CLI execution directory
- **Pros**: Simple
- **Cons**: Would break dependency tracing and reintroduce path duplication

### Option 2: Modify next.config.js with custom directory settings
- **Pros**: Keeps build command simple
- **Cons**: More complex configuration, potential compatibility issues

### Option 3: Use build command navigation (Chosen)
- **Pros**: Clean separation, maintains all existing fixes
- **Cons**: Slightly more complex build command

## Troubleshooting

If the issue persists:
1. Verify the `src/app/` directory exists in the correct location
2. Check that the build command syntax is correct in vercel.json
3. Ensure Vercel Dashboard Root Directory is set correctly
4. Test locally by running the build command from the monorepo root

This solution maintains all previous fixes while ensuring Next.js can properly detect the App Router directory structure.
