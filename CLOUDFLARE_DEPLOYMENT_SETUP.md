# Cloudflare Workers Deployment Setup Guide

## 🎯 Overview

This guide explains how to set up Cloudflare Workers deployment using the official `cloudflare/wrangler-action@v3` GitHub Action for the `learn-platform/apps/api` application.

## ✅ Changes Made

### 1. **Updated Wrangler Configuration**
- **File**: `learn-platform/apps/api/wrangler.jsonc`
- **Change**: Removed hardcoded `account_id` 
- **Reason**: Account ID is now provided via `CLOUDFLARE_ACCOUNT_ID` environment variable for better security

### 2. **Updated GitHub Actions Workflow**
- **File**: `.github/workflows/deploy-api.yml`
- **Changes**:
  - Replaced manual `bunx nx deploy:dev/prod api` commands with `cloudflare/wrangler-action@v3`
  - Added `CLOUDFLARE_ACCOUNT_ID` environment variable
  - Enhanced verification and error messages
  - Added pre-deployment logging for better debugging

## 🔧 Required GitHub Secrets

You need to add **TWO** secrets to your GitHub repository:

### 1. CLOUDFLARE_API_TOKEN
1. Go to [Cloudflare Dashboard → API Tokens](https://dash.cloudflare.com/profile/api-tokens)
2. Click **"Create Token"** → **"Custom token"**
3. Configure permissions:
   ```
   Token name: GitHub Actions Deploy
   
   Permissions:
   - Account: Cloudflare Workers:Edit
   - Zone: Zone:Read (if using custom domains)
   
   Account Resources:
   - Include: Your Account
   
   Zone Resources:
   - Include: All zones (or specific zones)
   ```
4. Copy the generated token

### 2. CLOUDFLARE_ACCOUNT_ID
1. Go to [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. In the right sidebar, find **Account ID**
3. Copy the Account ID (format: `b6cb1379869966fe48c8ebeecc963cfa`)

### Adding Secrets to GitHub
1. Go to your repository → **Settings** → **Secrets and variables** → **Actions**
2. Click **"New repository secret"**
3. Add both secrets:
   - Name: `CLOUDFLARE_API_TOKEN`, Value: [your API token]
   - Name: `CLOUDFLARE_ACCOUNT_ID`, Value: [your account ID]

## 🚀 Deployment Environments

### Development Environment
- **Trigger**: Push to `dev` branch or manual trigger with "development"
- **Worker Name**: `learn-platform-api-dev`
- **Command**: `wrangler deploy --config wrangler.jsonc --env dev`

### Production Environment
- **Trigger**: Push to `main` branch or manual trigger with "production"
- **Worker Name**: `learn-platform-api-prod`
- **Command**: `wrangler deploy --config wrangler.jsonc --env prod`

## 🧪 Testing the Setup

### 1. Manual Workflow Trigger
1. Go to **Actions** → **Deploy API to Cloudflare Workers**
2. Click **"Run workflow"**
3. Select environment and run

### 2. Local Testing (Optional)
```bash
cd learn-platform/apps/api

# Set environment variables
export CLOUDFLARE_API_TOKEN="your-token"
export CLOUDFLARE_ACCOUNT_ID="your-account-id"

# Test deployment
wrangler deploy --config wrangler.jsonc --env dev
```

## 🔍 Troubleshooting

### Error: "Unable to authenticate request [code: 10001]"
- **Cause**: Invalid or missing API token
- **Solution**: Regenerate API token with correct permissions

### Error: "Account ID not found"
- **Cause**: Invalid account ID
- **Solution**: Verify account ID in Cloudflare dashboard

### Error: "Insufficient permissions"
- **Cause**: API token lacks required permissions
- **Solution**: Recreate token with `Cloudflare Workers:Edit` permission

## 📋 Benefits of New Approach

1. **Better Security**: Account ID no longer hardcoded in repository
2. **Official Support**: Uses Cloudflare's official GitHub Action
3. **Better Error Handling**: Improved error messages and debugging
4. **Simplified Configuration**: No need for custom Nx deployment commands
5. **Enhanced Logging**: Better visibility into deployment process

## 🔄 Migration from Old Setup

The old setup used:
```yaml
run: bunx nx deploy:dev api
```

The new setup uses:
```yaml
uses: cloudflare/wrangler-action@v3
with:
  apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
  accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
  command: deploy --config wrangler.jsonc --env dev
  workingDirectory: ./learn-platform/apps/api
```

This provides better authentication, error handling, and follows Cloudflare best practices.

## 🐛 Working Directory Fix

### Issue
The notification job was failing with:
```
Error: An error occurred trying to start process '/usr/bin/bash' with working directory '/home/<USER>/work/kwaci-learning/kwaci-learning/./learn-platform'. No such file or directory
```

### Root Cause
- The global `defaults.run.working-directory: ./learn-platform` was inherited by the notification job
- The notification job doesn't checkout the repository, so the directory doesn't exist
- The notification job only needs to echo messages, not access repository files

### Solution
Added job-level defaults override for the notification job:
```yaml
notify:
  name: Notify Deployment Status
  runs-on: ubuntu-latest
  needs: deploy
  if: always()

  # Override the global working directory for this job
  defaults:
    run:
      working-directory: .
```

### Additional Improvements
- Added repository structure verification steps for debugging
- Enhanced notification messages with more context
- Added error handling guidance in failure notifications

## 📁 Repository Structure

The workflow expects this structure:
```
kwaci-learning/                    # Repository root
├── .github/workflows/
│   └── deploy-api.yml            # This workflow file
└── learn-platform/               # Nx monorepo root
    ├── apps/api/                 # API application
    │   ├── src/
    │   └── wrangler.jsonc        # Wrangler configuration
    ├── package.json              # Root package.json
    └── nx.json                   # Nx configuration
```

The workflow correctly uses `./learn-platform` as the working directory for build and deployment steps, but removes this requirement for notification-only steps.
