#!/bin/bash

# Local Simulation Script for GitHub Actions deploy-web.yml workflow
# This script replicates the exact build process used in CI/CD for local testing

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
LEARN_PLATFORM_DIR="$PROJECT_ROOT/learn-platform"
WEB_APP_DIR="$LEARN_PLATFORM_DIR/apps/web"

# Default environment (can be overridden)
ENVIRONMENT="${1:-preview}"
RUN_TESTS="${2:-true}"

echo -e "${BLUE}🚀 Local Deployment Simulation for Web App${NC}"
echo -e "${BLUE}Environment: $ENVIRONMENT${NC}"
echo -e "${BLUE}Run Tests: $RUN_TESTS${NC}"
echo ""

# Function to print section headers
print_section() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Function to print success messages
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Function to print error messages
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to print warning messages
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Function to check prerequisites
check_prerequisites() {
    print_section "Checking Prerequisites"
    
    # Check Node.js version
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 20 ]; then
        print_error "Node.js version 20 or higher is required (current: $(node --version))"
        exit 1
    fi
    print_success "Node.js version: $(node --version)"
    
    # Check bun
    if ! command -v bun &> /dev/null; then
        print_error "Bun is not installed. Please install bun: https://bun.sh"
        exit 1
    fi
    print_success "Bun version: $(bun --version)"
    
    # Check if we're in the right directory
    if [ ! -f "$LEARN_PLATFORM_DIR/package.json" ]; then
        print_error "learn-platform directory not found. Please run this script from the repository root."
        exit 1
    fi
    print_success "Project structure verified"
    
    echo ""
}

# Function to install dependencies
install_dependencies() {
    print_section "Installing Dependencies"
    
    cd "$LEARN_PLATFORM_DIR"
    
    echo "Installing monorepo dependencies with bun..."
    if ! bun install --frozen-lockfile; then
        print_error "Failed to install monorepo dependencies"
        exit 1
    fi
    print_success "Monorepo dependencies installed"
    
    echo ""
}

# Function to run tests (mimicking the test job)
run_tests() {
    if [ "$RUN_TESTS" != "true" ]; then
        print_warning "Skipping tests (disabled)"
        return 0
    fi
    
    print_section "Running Tests"
    
    cd "$LEARN_PLATFORM_DIR"
    
    # Determine test base like the workflow does
    CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
    
    if [ "$CURRENT_BRANCH" = "main" ]; then
        echo "Running tests on main branch - comparing with HEAD~1"
        TEST_BASE="HEAD~1"
    else
        echo "Running tests on $CURRENT_BRANCH branch - comparing with main"
        TEST_BASE="main"
    fi
    
    echo "Test command: bunx nx affected --target=test --parallel --coverage --base=$TEST_BASE"
    
    if ! NODE_ENV=test bunx nx affected --target=test --parallel --coverage --base="$TEST_BASE"; then
        print_error "Tests failed! Build cannot proceed."
        exit 1
    fi
    
    print_success "All tests passed"
    echo ""
}

# Function to build library dependencies
build_library_dependencies() {
    print_section "Building Library Dependencies"
    
    cd "$LEARN_PLATFORM_DIR"
    
    # Build libraries in the exact order from the workflow
    LIBRARIES=("db" "auth" "trpc" "shared-styles" "shared-ui")
    
    for lib in "${LIBRARIES[@]}"; do
        echo ""
        echo "🏗️ Building $lib library..."
        if ! bun nx build "$lib" --verbose; then
            print_error "$lib library build failed!"
            echo "Checking for build errors..."
            exit 1
        fi
        print_success "$lib library built successfully"
    done
    
    echo ""
    print_success "All library dependencies built successfully!"
    
    # Verify build outputs like the workflow does
    echo ""
    echo "📦 Verifying build outputs..."
    echo "📁 Dist directory structure:"
    ls -la dist/ || print_error "No dist directory found"
    
    echo ""
    echo "🔍 Checking critical build outputs:"
    for lib in "${LIBRARIES[@]}"; do
        if [ "$lib" = "shared-styles" ] || [ "$lib" = "shared-ui" ]; then
            LIB_PATH="dist/libs/shared/${lib#shared-}"
        else
            LIB_PATH="dist/libs/$lib"
        fi
        
        if [ -d "$LIB_PATH" ]; then
            print_success "$lib library built to $LIB_PATH"
        else
            print_error "$lib library not built to $LIB_PATH"
        fi
    done
    
    echo ""
    echo "🎯 Verifying TypeScript declarations:"
    if [ -f "dist/libs/auth/src/index.d.ts" ]; then
        print_success "Auth types generated"
    else
        print_error "Auth types not generated"
    fi
    
    if [ -f "dist/libs/trpc/src/index.d.ts" ]; then
        print_success "tRPC types generated"
    else
        print_error "tRPC types not generated"
    fi
    
    print_success "Build verification completed!"
    echo ""
}

# Function to lint web application
lint_web_application() {
    print_section "Linting Web Application"
    
    cd "$WEB_APP_DIR"
    
    echo "Running lint for web application..."
    if ! ../../node_modules/.bin/eslint . --max-warnings=20; then
        print_error "Linting failed!"
        exit 1
    fi
    
    print_success "Linting passed"
    echo ""
}

# Function to install app-specific dependencies
install_app_dependencies() {
    print_section "Installing App-Specific Dependencies"

    cd "$WEB_APP_DIR"

    echo "Installing app-specific dependencies with npm..."
    if ! npm install --production; then
        print_error "Failed to install app-specific dependencies"
        exit 1
    fi

    print_success "App-specific dependencies installed"
    echo ""
}

# Function to setup environment variables
setup_environment_variables() {
    print_section "Setting Up Environment Variables"

    cd "$WEB_APP_DIR"

    # Check if .env.local exists
    if [ ! -f ".env.local" ]; then
        print_warning ".env.local file not found"
        echo "Creating .env.local from template..."

        # Create a basic .env.local file with required variables
        cat > .env.local << EOF
# Local simulation environment variables
# Copy from your actual .env.local or set these values

# Authentication Configuration
BETTER_AUTH_SECRET=your-super-secret-key-change-this-in-production-min-32-chars
BETTER_AUTH_URL=http://localhost:3000
BETTER_AUTH_TRUSTED_ORIGINS=http://localhost:3000,http://localhost:3001

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/database_name

# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8787
EOF

        print_warning "Created .env.local template. Please update with your actual values!"
        echo "Edit $WEB_APP_DIR/.env.local with your environment variables"
        echo ""
        read -p "Press Enter to continue after updating .env.local, or Ctrl+C to exit..."
    fi

    # Verify .env.local exists and has content
    if [ -f ".env.local" ]; then
        print_success ".env.local file found"
        ENV_VAR_COUNT=$(grep -c '=' .env.local || echo '0')
        echo "Environment variables count: $ENV_VAR_COUNT"

        # Source environment variables for the build process
        echo "🔄 Loading environment variables for build..."
        set -a  # automatically export all variables
        source .env.local
        set +a  # stop automatically exporting

        # Verify critical variables are loaded
        echo "🔍 Verifying environment variables are loaded:"
        echo "BETTER_AUTH_URL: ${BETTER_AUTH_URL:-'(not loaded)'}"
        echo "BETTER_AUTH_TRUSTED_ORIGINS: ${BETTER_AUTH_TRUSTED_ORIGINS:-'(not loaded)'}"
        echo "NEXT_PUBLIC_API_URL: ${NEXT_PUBLIC_API_URL:-'(not loaded)'}"

        # Export environment variables to ensure they're available to the build process
        export $(grep -v '^#' .env.local | xargs) 2>/dev/null || true

        print_success "Environment variables loaded"
    else
        print_error ".env.local file not found - build may fail"
        exit 1
    fi

    echo ""
}

# Function to verify monorepo structure (like the workflow)
verify_monorepo_structure() {
    print_section "Verifying Monorepo Structure"

    cd "$LEARN_PLATFORM_DIR"

    echo "Current working directory: $(pwd)"
    echo ""
    echo "📁 Root directory contents:"
    ls -la . | head -10
    echo ""
    echo "📚 Libs directory contents:"
    ls -la libs/ || print_error "libs directory not found"
    echo ""
    echo "🔐 Auth library structure:"
    ls -la libs/auth/ || print_error "libs/auth does not exist"
    echo ""
    echo "📄 Auth library files:"
    ls -la libs/auth/src/ || print_error "libs/auth/src does not exist"
    echo ""
    echo "🎯 Critical auth files:"
    ls -la libs/auth/src/auth.ts || print_error "auth.ts does not exist"
    ls -la libs/auth/src/index.ts || print_error "index.ts does not exist"

    print_success "Monorepo structure verified"
    echo ""
}

# Function to verify TypeScript configuration
verify_typescript_configuration() {
    print_section "Verifying TypeScript Configuration"

    cd "$LEARN_PLATFORM_DIR"

    echo "🔧 Base TypeScript config path mappings:"
    cat tsconfig.base.json | grep -A 10 '"paths"' || print_error "No path mappings found"
    echo ""
    echo "🌐 Web app TypeScript config:"
    cat apps/web/tsconfig.json | head -20
    echo ""
    echo "🔍 Verify auth library build outputs:"
    if [ -f "dist/libs/auth/src/index.d.ts" ]; then
        print_success "Auth library TypeScript declarations found"
        echo "📄 Auth library exports:"
        head -10 dist/libs/auth/src/index.d.ts
    else
        print_error "Auth library TypeScript declarations not found"
        echo "📁 Available dist contents:"
        ls -la dist/ || echo "No dist directory"
    fi

    echo ""
    echo "🔍 Verify TypeScript module resolution:"
    echo "Testing TypeScript path mappings and library availability..."

    # Check if built libraries exist
    echo "📦 Checking built library outputs:"
    for lib in auth db trpc; do
        if [ -f "dist/libs/${lib}/src/index.d.ts" ]; then
            print_success "${lib} library declarations available"
        else
            print_warning "${lib} library declarations not found"
        fi
    done

    for lib in styles ui; do
        if [ -f "dist/libs/shared/${lib}/src/index.d.ts" ]; then
            print_success "shared-${lib} library declarations available"
        else
            print_warning "shared-${lib} library declarations not found"
        fi
    done

    echo ""
    echo "📝 TypeScript path mappings use source files (libs/*/src/index.ts)"
    echo "   This ensures compatibility with SWC compiler used by Nx tests"

    print_success "TypeScript configuration verified"
    echo ""
}

# Function to build the web application
build_web_application() {
    print_section "Building Web Application"

    cd "$WEB_APP_DIR"

    echo "🔍 Pre-build verification:"
    echo "Checking if auth library is available for import..."
    cd ../../  # Go to monorepo root
    echo "Current directory: $(pwd)"
    echo "Checking dist/libs/auth exists: $(ls -la dist/libs/auth/ 2>/dev/null && echo '✅ Yes' || echo '❌ No')"

    echo "Testing auth import resolution..."
    node -e "
        try {
            const path = require('path');
            const fs = require('fs');
            const authPath = path.resolve('./libs/auth/src/index.ts');
            console.log('Auth source path exists:', fs.existsSync(authPath) ? '✅ Yes' : '❌ No');
            const authDistPath = path.resolve('./dist/libs/auth/src/index.d.ts');
            console.log('Auth dist types exist:', fs.existsSync(authDistPath) ? '✅ Yes' : '❌ No');
        } catch (e) {
            console.log('❌ Node.js verification failed:', e.message);
        }
    " || echo "Node.js verification skipped"

    cd apps/web  # Return to web app directory

    # Run Next.js build (equivalent to vercel build)
    echo "🚀 Starting Next.js build (simulating Vercel build)..."
    if ! npm run build; then
        echo ""
        print_error "Next.js build failed! Collecting debug information..."
        echo ""
        echo "📁 Current directory contents:"
        ls -la
        echo ""
        echo "📁 Monorepo structure:"
        ls -la ../../
        echo ""
        echo "📚 Libraries status:"
        ls -la ../../libs/ || echo "No libs directory"
        echo ""
        echo "🔐 Auth library status:"
        ls -la ../../libs/auth/ || echo "No auth library"
        echo ""
        echo "📦 Build outputs:"
        ls -la ../../dist/ || echo "No dist directory"
        echo ""
        echo "🔍 Next.js config:"
        cat next.config.js || echo "No next.config.js"
        echo ""
        echo "🔍 Package.json:"
        cat package.json || echo "No package.json"
        echo ""
        print_error "Build failed - exiting"
        exit 1
    fi

    print_success "Next.js build completed successfully!"
    echo ""
}

# Function to verify build output
verify_build_output() {
    print_section "Verifying Build Output"

    cd "$LEARN_PLATFORM_DIR"

    echo "Current working directory: $(pwd)"
    echo "Contents of apps/web:"
    ls -la apps/web/
    echo ""
    echo "Looking for .next directory in apps/web:"
    find apps/web -name ".next" -type d 2>/dev/null || echo "No .next directory found"
    echo ""
    echo "Contents of apps/web/.next (if exists):"
    if [ -d "apps/web/.next" ]; then
        ls -la apps/web/.next/ | head -20
        print_success ".next directory found with build artifacts"

        # Check for critical build files
        if [ -f "apps/web/.next/BUILD_ID" ]; then
            echo "Build ID: $(cat apps/web/.next/BUILD_ID)"
        fi

        if [ -d "apps/web/.next/static" ]; then
            print_success "Static assets generated"
        fi

        if [ -d "apps/web/.next/server" ]; then
            print_success "Server-side files generated"
        fi
    else
        print_error ".next directory not found - build may have failed"
    fi

    echo ""
}

# Function to run post-build verification
post_build_verification() {
    print_section "Post-Build Verification"

    echo "🔍 Build Summary:"
    echo "Environment: $ENVIRONMENT"
    echo "Tests Run: $RUN_TESTS"
    echo "Build Status: ✅ Success"

    echo ""
    echo "🎯 Next Steps:"
    echo "1. Review build output above for any warnings"
    echo "2. Test the application locally: cd $WEB_APP_DIR && npm run dev"
    echo "3. If everything looks good, your changes should deploy successfully"

    echo ""
    print_success "Local deployment simulation completed successfully!"
    echo ""
    echo "🚀 Your build process matches the GitHub Actions workflow."
    echo "   You can now safely push your changes to trigger the actual deployment."
}

# Main execution
main() {
    echo "Starting local deployment simulation..."
    echo "Working directory: $(pwd)"
    echo ""

    check_prerequisites
    install_dependencies

    if [ "$RUN_TESTS" = "true" ]; then
        run_tests
    fi

    build_library_dependencies
    lint_web_application
    install_app_dependencies
    setup_environment_variables
    verify_monorepo_structure
    verify_typescript_configuration
    build_web_application
    verify_build_output
    post_build_verification
}

# Help function
show_help() {
    echo "Local Deployment Simulation Script"
    echo ""
    echo "Usage: $0 [ENVIRONMENT] [RUN_TESTS]"
    echo ""
    echo "Arguments:"
    echo "  ENVIRONMENT  Environment to simulate (preview|production) [default: preview]"
    echo "  RUN_TESTS    Whether to run tests (true|false) [default: true]"
    echo ""
    echo "Examples:"
    echo "  $0                    # Run with preview environment and tests"
    echo "  $0 production         # Run with production environment and tests"
    echo "  $0 preview false      # Run with preview environment, skip tests"
    echo "  $0 production false   # Run with production environment, skip tests"
    echo ""
    echo "Environment Variables:"
    echo "  The script will create a template .env.local file if one doesn't exist."
    echo "  Make sure to update it with your actual environment variables."
    echo ""
}

# Check for help flag
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_help
    exit 0
fi

# Run main function
main
