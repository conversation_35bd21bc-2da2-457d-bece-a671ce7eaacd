#!/bin/bash

# Quick Test Script - Fast build verification without full simulation
# Use this for rapid iteration during development

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

echo -e "${BLUE}🚀 Quick Build Test${NC}"
echo "This script runs a fast build verification without the full simulation."
echo ""

# Navigate to project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
cd "$PROJECT_ROOT/learn-platform"

echo -e "${BLUE}📦 Installing dependencies...${NC}"
bun install --frozen-lockfile

echo -e "${BLUE}🏗️ Building critical libraries...${NC}"
bun nx build auth --verbose
bun nx build trpc --verbose

echo -e "${BLUE}🔍 Quick lint check...${NC}"
cd apps/web
../../node_modules/.bin/eslint src/ --max-warnings=5 --quiet

echo -e "${BLUE}🚀 Testing Next.js build...${NC}"
npm run build

echo -e "${GREEN}✅ Quick test completed successfully!${NC}"
echo ""
echo "For full simulation, run: ./scripts/simulate-deploy-web.sh"
