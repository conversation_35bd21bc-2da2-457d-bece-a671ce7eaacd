# Environment Variables Template for Local Deployment Simulation
# Copy this file to learn-platform/apps/web/.env.local and update with your values

# =============================================================================
# AUTHENTICATION CONFIGURATION
# =============================================================================

# Secret key for better-auth (REQUIRED - minimum 32 characters)
# Generate a secure random string for production
BETTER_AUTH_SECRET=your-super-secret-key-change-this-in-production-min-32-chars

# Base URL for authentication endpoints (REQUIRED)
# Development: http://localhost:3000
# Production: https://your-domain.com
BETTER_AUTH_URL=http://localhost:3000

# Trusted origins for CORS (REQUIRED)
# Comma-separated list of allowed origins
# Include all domains that will access your auth endpoints
BETTER_AUTH_TRUSTED_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:8787

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# PostgreSQL connection string (REQUIRED)
# Format: postgresql://username:password@host:port/database_name
# Development example:
DATABASE_URL=postgresql://demo:demo@localhost:5432/learn_platform_dev

# Production example:
# DATABASE_URL=********************************************/learn_platform_prod

# =============================================================================
# API CONFIGURATION
# =============================================================================

# API endpoint URL for tRPC client (REQUIRED)
# This should point to your Cloudflare Workers API
# Development: http://localhost:8787
# Production: https://your-api.workers.dev
NEXT_PUBLIC_API_URL=http://localhost:8787

# =============================================================================
# OPTIONAL DEBUG CONFIGURATION
# =============================================================================

# Enable comprehensive auth debugging (optional)
# AUTH_DEBUG=true

# Set debug log level: debug, info, warn, error (optional)
# AUTH_LOG_LEVEL=debug

# Enable database operation logging (optional)
# AUTH_LOG_DATABASE=true

# Enable request/response logging (optional)
# AUTH_LOG_REQUESTS=true
# AUTH_LOG_RESPONSES=true

# =============================================================================
# PRODUCTION ENVIRONMENT EXAMPLE
# =============================================================================
# For production-like testing, use these values instead:
#
# BETTER_AUTH_SECRET=your-production-secret-key-min-32-chars
# BETTER_AUTH_URL=https://kwaci-learning.bmbn.dev
# BETTER_AUTH_TRUSTED_ORIGINS=https://kwaci-learning.bmbn.dev
# DATABASE_URL=your-production-database-url
# NEXT_PUBLIC_API_URL=https://learn-platform-api-dev.bm.workers.dev

# =============================================================================
# SETUP INSTRUCTIONS
# =============================================================================
#
# 1. Copy this file to learn-platform/apps/web/.env.local
# 2. Update the values above with your actual configuration
# 3. Ensure BETTER_AUTH_SECRET is at least 32 characters
# 4. Make sure DATABASE_URL points to a valid PostgreSQL database
# 5. Verify NEXT_PUBLIC_API_URL matches your API endpoint
# 6. Add any additional origins to BETTER_AUTH_TRUSTED_ORIGINS as needed
#
# For local development:
# - Use localhost URLs for BETTER_AUTH_URL and NEXT_PUBLIC_API_URL
# - Include all local ports in BETTER_AUTH_TRUSTED_ORIGINS
#
# For production testing:
# - Use your actual production URLs
# - Ensure all domains are included in BETTER_AUTH_TRUSTED_ORIGINS
# - Use a secure DATABASE_URL with proper credentials
#
# Security Notes:
# - Never commit .env.local files to version control
# - Use different secrets for development and production
# - Rotate secrets regularly in production
# - Use environment-specific database URLs
