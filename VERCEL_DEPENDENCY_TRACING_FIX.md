# Vercel Dependency Tracing Fix - styled-jsx Missing

## Problem Analysis

After removing `outputFileTracingRoot` to fix path duplication, we encountered a new issue where Vercel couldn't find dependencies during deployment:

**Error:** `ENOENT: no such file or directory, lstat '/node_modules/styled-jsx/index.js'`

**Root Cause:** Without `outputFileTracingRoot`, Next.js couldn't trace dependencies from the monorepo root where `node_modules` is located.

## The Dependency Tracing Problem

In an Nx monorepo:
- **Dependencies location**: `/learn-platform/node_modules/`
- **App location**: `/learn-platform/apps/web/`
- **Issue**: Next.js needs to trace dependencies from the monorepo root to include them in the deployment bundle

When we removed `outputFileTracingRoot`, Next.js could only see files relative to the app directory, missing critical dependencies like `styled-jsx` (a Next.js built-in dependency).

## Solution: Restore outputFileTracingRoot + Run from Monorepo Root

The key insight is to:
1. **Restore `outputFileTracingRoot`** for proper dependency tracing
2. **Run Vercel CLI from monorepo root** to avoid path duplication
3. **Use Dashboard Root Directory** to specify the app location

### 1. Restored outputFileTracingRoot in next.config.js

```javascript
const nextConfig = {
  nx: {},
  
  // Include files from the monorepo base for Vercel deployment
  // This is needed to trace dependencies from the monorepo root node_modules
  outputFileTracingRoot: path.join(__dirname, '../../'),
  
  transpilePackages: [
    '@learn-platform/shared-ui',
    '@learn-platform/shared-styles',
    '@learn-platform/trpc',
    '@learn-platform/auth'
  ],
  // ...
};
```

### 2. Updated GitHub Actions to Run from Monorepo Root

**Before** (causing path duplication):
```yaml
- name: Build Project Artifacts (Preview)
  run: vercel build --token=${{ secrets.VERCEL_TOKEN }}
  working-directory: ./learn-platform/apps/web
```

**After** (clean path resolution):
```yaml
- name: Build Project Artifacts (Preview)
  run: |
    cd learn-platform
    vercel build --token=${{ secrets.VERCEL_TOKEN }}
```

### 3. Simplified vercel.json

Since Vercel CLI now runs from the monorepo root, the install command is simplified:

```json
{
  "version": 2,
  "public": false,
  "github": {
    "enabled": false
  },
  "buildCommand": "npm run vercel-build",
  "installCommand": "bun install --frozen-lockfile",
  "framework": "nextjs"
}
```

### 4. Vercel Dashboard Configuration

**IMPORTANT: Set Root Directory in Vercel Dashboard:**

1. Go to your Vercel project → Settings → Build & Deployment
2. Set **Root Directory** to: `learn-platform/apps/web`
3. Click Save

This tells Vercel where the Next.js app is located within the repository.

## How This Fixes the Issue

### New Deployment Flow:
1. **GitHub Actions**: Runs Vercel CLI from `learn-platform/` (monorepo root)
2. **Vercel Dashboard Root Directory**: Points to `apps/web/` (relative to monorepo root)
3. **outputFileTracingRoot**: Points to `../../` (monorepo root from app perspective)
4. **Result**: Clean path resolution + proper dependency tracing

### Path Resolution:
- **Vercel CLI working directory**: `/home/<USER>/work/.../learn-platform/`
- **Dashboard Root Directory**: `apps/web/` (relative to working directory)
- **Final app path**: `/home/<USER>/work/.../learn-platform/apps/web/` ✅
- **Dependency tracing**: From `/home/<USER>/work/.../learn-platform/node_modules/` ✅

### No Path Duplication:
- ❌ **Old**: Working directory + Root Directory = duplication
- ✅ **New**: Monorepo root + relative app path = clean resolution

## Expected Results

With this configuration:
- ✅ `styled-jsx` and other dependencies properly traced and included
- ✅ No path duplication issues
- ✅ Build artifacts created in correct location
- ✅ Deployment succeeds with all dependencies available
- ✅ Shared packages still transpiled correctly

## Configuration Summary

### GitHub Actions:
```yaml
- name: Build Project Artifacts (Preview)
  run: |
    cd learn-platform
    vercel build --token=${{ secrets.VERCEL_TOKEN }}
  env:
    VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
    VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID_WEB }}
```

### Vercel Dashboard:
- **Root Directory**: `learn-platform/apps/web` ✅

### next.config.js:
```javascript
const nextConfig = {
  outputFileTracingRoot: path.join(__dirname, '../../'),
  transpilePackages: [
    '@learn-platform/shared-ui',
    '@learn-platform/shared-styles',
    '@learn-platform/trpc',
    '@learn-platform/auth'
  ],
};
```

### vercel.json:
```json
{
  "buildCommand": "npm run vercel-build",
  "installCommand": "bun install --frozen-lockfile",
  "framework": "nextjs"
}
```

## Verification

The debug output should show:
```
Current working directory: /home/<USER>/work/kwaci-learning/kwaci-learning/learn-platform
Looking for .next directory in apps/web:
apps/web/.next
Contents of apps/web/.next (if exists):
-rw-r--r-- 1 <USER> <GROUP> routes-manifest.json
-rw-r--r-- 1 <USER> <GROUP> BUILD_ID
```

And the deployment should succeed with all dependencies properly included.

## Key Principle

**Use outputFileTracingRoot for dependency tracing + Run Vercel CLI from monorepo root + Use Dashboard Root Directory for app location.**

This approach provides:
- ✅ Proper dependency tracing from monorepo root
- ✅ Clean path resolution without duplication
- ✅ Compatibility with Nx monorepo structure
- ✅ Support for shared packages and bun package manager
