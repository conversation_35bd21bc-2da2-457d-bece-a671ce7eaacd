### Project structure
```bash
apps/
  web/              # Public-facing Next.js site
  admin/            # Admin panel Next.js site
  api/              # Cloudflare Worker using Hono + tRPC
libs/
  ui/               # Shared components using shadcn/ui
  trpc/             # tRPC router definitions (shared)
  db/               # Drizzle schema and queries
  auth/             # better-auth logic and adapters
  utils/            # Shared utility functions
```

### Should know
- use bun