# Vercel CLI Build Error Fix for Nx Monorepo

## Problem Summary
The Vercel CLI was failing with a path resolution error:
```
Error: ENOENT: no such file or directory, open '/home/<USER>/work/kwaci-learning/kwaci-learning/learn-platform/apps/web/learn-platform/apps/web/package.json'
```

This indicated a duplicated path issue where Vercel CLI was looking for package.json in a nested path structure.

## Root Cause Analysis
1. **Working Directory Conflict**: The workflow had `defaults.run.working-directory: ./learn-platform` but used `working-directory: ./learn-platform/apps/web` for Vercel commands, causing path duplication
2. **Double Build Process**: The workflow was building twice - once with Next.js directly and once with Vercel CLI
3. **Bun Package Manager**: Vercel CLI needed proper configuration for bun in Nx monorepos
4. **Missing Vercel Configuration**: No vercel.json file to guide Vercel CLI behavior

## Solution Implementation

### 1. Fixed Working Directory Configuration
**Problem**: Using both `defaults.run.working-directory` and individual `working-directory` caused path duplication.

**Solution**: Removed individual `working-directory` parameters and used `cd` commands within the default working directory.

**Before**:
```yaml
defaults:
  run:
    working-directory: ./learn-platform

# Later in the workflow:
- name: Pull Vercel Environment Information
  run: vercel pull --yes --environment=preview --token=${{ secrets.VERCEL_TOKEN }}
  working-directory: ./learn-platform/apps/web  # This caused duplication
```

**After**:
```yaml
defaults:
  run:
    working-directory: ./learn-platform

# Later in the workflow:
- name: Pull Vercel Environment Information
  run: |
    cd apps/web
    vercel pull --yes --environment=preview --token=${{ secrets.VERCEL_TOKEN }}
```

### 2. Updated package.json for Vercel Compatibility
Added `vercel-build` script to the web app's package.json:

```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build", 
    "start": "next start",
    "lint": "next lint",
    "vercel-build": "next build"
  }
}
```

### 3. Created vercel.json Configuration
Added `learn-platform/apps/web/vercel.json` to properly configure Vercel for the Nx monorepo:

```json
{
  "version": 2,
  "public": false,
  "github": {
    "enabled": false
  },
  "buildCommand": "npm run vercel-build",
  "installCommand": "cd ../.. && bun install --frozen-lockfile",
  "framework": "nextjs",
  "outputDirectory": ".next"
}
```

**Key Configuration Explanations**:
- `"github": {"enabled": false}`: Disables Vercel's automatic GitHub integration
- `"installCommand"`: Tells Vercel to install dependencies from the monorepo root using bun
- `"buildCommand"`: Uses the custom vercel-build script
- `"framework": "nextjs"`: Explicitly sets the framework

### 4. Removed Duplicate Build Step
Removed the manual Next.js build step since Vercel CLI handles the build process:

**Removed**:
```yaml
- name: Build web application
  run: |
    echo "Building web application..."
    cd apps/web
    ../../node_modules/.bin/next build
```

## Vercel Dashboard Configuration

To disable automatic builds on Vercel dashboard and ensure only GitHub Actions handles deployments:

### Method 1: Disable Vercel for GitHub Integration (Recommended)
1. **Go to your Vercel project dashboard**
2. **Navigate to Settings → Git**
3. **Under "Git Integration"**, click **Disconnect** to disconnect the GitHub integration
4. This ensures that only your GitHub Actions workflow triggers deployments

### Method 2: Use Ignored Build Step
If you want to keep the Git integration but skip automatic builds:

1. **Go to your Vercel project dashboard**
2. **Navigate to Settings → Git**
3. **Under "Ignored Build Step"**, add this command:
   ```bash
   exit 0
   ```
   This will skip all automatic builds, allowing only GitHub Actions to handle deployments.

### Method 3: Branch-Specific Ignore (If you want some branches to auto-deploy)
1. **Go to your Vercel project dashboard**
2. **Navigate to Settings → Git**
3. **Under "Ignored Build Step"**, add this command:
   ```bash
   if [ "$VERCEL_GIT_COMMIT_REF" = "dev" ] || [ "$VERCEL_GIT_COMMIT_REF" = "main" ]; then exit 0; else exit 1; fi
   ```
   This will skip builds for dev and main branches but allow other branches to auto-deploy.

### Verify Configuration
The `vercel.json` file we created includes `"github": {"enabled": false}` which also helps disable automatic GitHub integration at the project level.

## Benefits of This Solution

1. **Fixed Path Resolution**: Eliminated the duplicated path issue
2. **Proper Bun Support**: Configured Vercel to use bun from the monorepo root
3. **Single Build Process**: Vercel CLI now handles the entire build process
4. **Nx Monorepo Compatible**: Properly handles the monorepo structure
5. **GitHub Actions Control**: Only GitHub Actions triggers deployments

## Verification Steps

1. **Test the workflow**: Push to dev branch and monitor GitHub Actions logs
2. **Check Vercel logs**: Verify that Vercel CLI finds the correct package.json
3. **Verify deployment**: Ensure the deployment succeeds and the app works
4. **Test production**: Push to main branch to test production deployment

## Expected Outcome

After implementing these fixes:
- Vercel CLI will correctly find package.json at the right path
- Bun will be used properly for dependency installation
- The build process will complete successfully
- Both preview and production deployments will work
- No duplicate builds will occur

## Troubleshooting

If issues persist:

1. **Check Vercel CLI logs** for specific error messages
2. **Verify secrets** are correctly set in GitHub repository settings
3. **Test locally** by running Vercel CLI commands in the web app directory
4. **Check vercel.json syntax** for any JSON formatting errors
5. **Verify bun.lock** is present in the monorepo root

## Additional Recommendations

1. **Monitor build times**: The new process should be faster with single build
2. **Test environment variables**: Ensure all required env vars are available
3. **Check shared dependencies**: Verify that shared libraries are properly built
4. **Review Vercel logs**: Monitor for any warnings about bun usage
