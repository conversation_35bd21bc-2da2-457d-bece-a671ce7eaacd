# GitHub Actions Build Output Path Duplication Fix

## Problem Analysis

We encountered the build output path duplication error again:
```
Error: ENOENT: no such file or directory, lstat '/home/<USER>/work/kwaci-learning/kwaci-learning/learn-platform/apps/web/apps/web/.next/routes-manifest.json'
```

**Root Cause:** The `outputFileTracingRoot: path.join(__dirname, '../../')` configuration was causing path duplication when running Vercel CLI from the app directory in GitHub Actions.

## The outputFileTracingRoot Conflict

### The Issue:
- **Vercel CLI execution**: From app directory (`working-directory: ./learn-platform/apps/web`)
- **outputFileTracingRoot**: Points to `../../` (monorepo root)
- **Result**: Vercel CLI tries to resolve build outputs relative to tracing root, causing duplication

### Path Resolution Conflict:
1. **Vercel CLI working directory**: `/learn-platform/apps/web/`
2. **outputFileTracingRoot**: `../../` (points to `/learn-platform/`)
3. **Build output resolution**: <PERSON><PERSON><PERSON> tries to find outputs at `/learn-platform/apps/web/` + `apps/web/` = `/learn-platform/apps/web/apps/web/.next/` ❌

## Solution: Remove outputFileTracingRoot for CLI Builds

Since we're running the build in GitHub Actions (not on Vercel's platform), we don't need `outputFileTracingRoot` for dependency tracing. The dependencies are already available in the monorepo's `node_modules`.

### Updated next.config.js

**Before** (causing path duplication):
```javascript
const nextConfig = {
  outputFileTracingRoot: path.join(__dirname, '../../'),
  transpilePackages: [
    '@learn-platform/shared-ui',
    '@learn-platform/shared-styles',
    '@learn-platform/trpc',
    '@learn-platform/auth'
  ],
};
```

**After** (clean path resolution):
```javascript
const nextConfig = {
  // For GitHub Actions + Vercel CLI deployment, let Vercel handle file tracing
  // outputFileTracingRoot causes path duplication when running CLI from app directory
  // outputFileTracingRoot: path.join(__dirname, '../../'),
  
  transpilePackages: [
    '@learn-platform/shared-ui',
    '@learn-platform/shared-styles',
    '@learn-platform/trpc',
    '@learn-platform/auth'
  ],
};
```

## Why This Works

### GitHub Actions + Vercel CLI Deployment:
1. **Dependencies**: Installed in monorepo root via `cd ../../ && bun install --frozen-lockfile`
2. **Build execution**: From app directory (`working-directory: ./learn-platform/apps/web`)
3. **Dependency resolution**: Next.js can find dependencies through normal Node.js resolution
4. **Build output**: Created at `/learn-platform/apps/web/.next/` ✅
5. **No path duplication**: Clean, single path resolution

### Dependency Tracing:
- **Shared packages**: Handled by `transpilePackages` configuration
- **Node modules**: Available through normal Node.js module resolution
- **No manual tracing needed**: Since we're building from the app directory with access to monorepo dependencies

## Configuration Summary

### GitHub Actions Workflow:
```yaml
- name: Build Project Artifacts (Preview)
  run: vercel build --token=${{ secrets.VERCEL_TOKEN }}
  working-directory: ./learn-platform/apps/web
  env:
    VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
    VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID_WEB }}
```

### vercel.json:
```json
{
  "buildCommand": "npm run vercel-build",
  "installCommand": "cd ../../ && bun install --frozen-lockfile",
  "framework": "nextjs"
}
```

### next.config.js:
```javascript
const nextConfig = {
  nx: {},
  
  // No outputFileTracingRoot needed for GitHub Actions builds
  
  transpilePackages: [
    '@learn-platform/shared-ui',
    '@learn-platform/shared-styles',
    '@learn-platform/trpc',
    '@learn-platform/auth'
  ],
  
  webpack: (config) => {
    config.resolve.alias = {
      ...config.resolve.alias,
      '@learn-platform/shared-styles': require('path').resolve(__dirname, '../../libs/shared/styles/src'),
    };
    return config;
  },
};
```

### Vercel Dashboard:
- **Root Directory**: *(empty)* ✅

## Expected Results

With this configuration:
- ✅ Build output created at correct path: `/learn-platform/apps/web/.next/`
- ✅ No path duplication: `/apps/web/apps/web/.next/`
- ✅ Dependencies resolved correctly from monorepo root
- ✅ Shared packages transpiled via `transpilePackages`
- ✅ Clean, predictable build process

## Verification

The build should complete successfully with:
```
Traced Next.js server files in: 316.084ms
✓ Build completed successfully
```

And deployment should proceed without the `ENOENT` error.

## Key Insight

**For GitHub Actions + Vercel CLI deployment, outputFileTracingRoot is not needed and causes path conflicts.**

The key differences:
- **Vercel Platform Build**: Needs `outputFileTracingRoot` for dependency tracing
- **GitHub Actions + CLI Build**: Dependencies already available, `outputFileTracingRoot` causes conflicts

## Alternative Approaches Considered

### Option 1: Conditional outputFileTracingRoot
```javascript
outputFileTracingRoot: process.env.VERCEL ? path.join(__dirname, '../../') : undefined,
```
**Issue**: `process.env.VERCEL` is not set in GitHub Actions

### Option 2: Different environment variable
```javascript
outputFileTracingRoot: process.env.CI ? undefined : path.join(__dirname, '../../'),
```
**Issue**: Still complex and unnecessary for our use case

### Option 3: Remove outputFileTracingRoot (Chosen)
**Benefits**: 
- ✅ Simplest solution
- ✅ No path conflicts
- ✅ Dependencies still work via normal resolution
- ✅ Shared packages handled by transpilePackages

## Troubleshooting

If dependency issues arise:
1. Verify `transpilePackages` includes all shared packages
2. Check that `installCommand` runs from monorepo root
3. Ensure webpack aliases are correctly configured
4. Test locally with `cd learn-platform/apps/web && npm run build`

This solution provides clean, conflict-free builds while maintaining all necessary functionality for the Nx monorepo deployment.
