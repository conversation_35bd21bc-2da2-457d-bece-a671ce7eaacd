# Cloudflare Workers Deployment Guide

## 🎯 Overview

This guide documents the comprehensive Cloudflare Workers deployment solution for the Next.js web application using the OpenNext Cloudflare adapter. The solution runs alongside the existing Vercel deployment, providing users with flexible deployment options and full Next.js feature support.

## 🚀 Key Benefits of Cloudflare Workers Deployment

### Full Next.js Feature Support
- ✅ **Server-Side Rendering (SSR)** - Dynamic pages rendered on-demand
- ✅ **Static Site Generation (SSG)** - Pre-built pages for optimal performance
- ✅ **Incremental Static Regeneration (ISR)** - Update static pages without rebuilding
- ✅ **Server Actions** - Server-side form handling and mutations
- ✅ **API Routes** - Full-featured API endpoints
- ✅ **Middleware** - Request/response processing
- ✅ **Node.js APIs** - Access to Node.js runtime features

### Performance Advantages
- 🌍 **Global Edge Network** - Deploy to 300+ locations worldwide
- ⚡ **Cold Start Optimization** - Faster than traditional serverless
- 🔄 **Advanced Caching** - R2-based incremental cache support
- 📦 **Asset Optimization** - Automatic static asset optimization

### Developer Experience
- 🛠️ **Local Development** - Full Cloudflare Workers simulation
- 🔧 **TypeScript Support** - Generated types for Cloudflare bindings
- 📊 **Real-time Logs** - Comprehensive debugging and monitoring
- 🔄 **Hot Reload** - Fast development iteration

## ✅ What's Been Implemented

### 1. **GitHub Actions Workflow**
- **File**: `.github/workflows/deploy-cloudflare.yml`
- **Features**:
  - Manual deployment with environment selection (preview/production)
  - Support for both 'main' and 'dev' branch deployments
  - Comprehensive testing before deployment
  - Monorepo-aware dependency building
  - Force rebuild option for cache-busting
  - Node.js v20+ compatibility for Wrangler CLI
  - Full Next.js feature support (SSR, ISR, Server Actions, etc.)

### 2. **Cloudflare Workers Configuration**
- **File**: `learn-platform/apps/web/wrangler.jsonc`
- **Features**:
  - Environment-specific configurations (preview/production)
  - Node.js compatibility flags
  - Static assets binding configuration
  - Service bindings for self-reference
  - Optional bindings for KV, D1, R2 (commented out)

### 3. **OpenNext Configuration**
- **File**: `learn-platform/apps/web/open-next.config.ts`
- **Features**:
  - OpenNext Cloudflare adapter configuration
  - Optional R2-based incremental cache setup
  - Extensible configuration for advanced features

### 4. **Next.js Configuration Updates**
- **File**: `learn-platform/apps/web/next.config.js`
- **Features**:
  - OpenNext Cloudflare development platform setup
  - Backward compatibility with existing Vercel deployment
  - Graceful fallback if @opennextjs/cloudflare is not installed

### 5. **Package.json Enhancements**
- **File**: `learn-platform/apps/web/package.json`
- **Features**:
  - Added @opennextjs/cloudflare dependency
  - New scripts for Cloudflare Workers deployment
  - Local development and preview commands

### 6. **Additional Configuration Files**
- **File**: `learn-platform/apps/web/.dev.vars` - Local development environment variables
- **File**: `learn-platform/apps/web/public/_headers` - Static asset caching headers

## 🚀 Deployment Options

### Automatic Deployment (Default)
The workflow is configured to automatically deploy when you push to specific branches:
- **Push to `main` branch** → Production deployment (`learn-platform-web-prod`)
- **Push to `dev` branch** → Preview deployment (`learn-platform-web-preview`)

### Manual Deployment
You can also trigger deployments manually:
1. Go to GitHub Actions → "Deploy Web App to Cloudflare Workers"
2. Click "Run workflow"
3. Select environment: `preview` or `production`
4. Optionally enable "Force fresh build" for cache-busting
5. Optionally specify a different branch to deploy

### Path-Based Triggers
Automatic deployments only trigger when changes are made to:
- `learn-platform/apps/web/**`
- `learn-platform/libs/shared/**`
- `learn-platform/libs/trpc/**`
- `learn-platform/libs/auth/**`
- `.github/workflows/deploy-cloudflare.yml`

## 🔧 Required Setup

### 1. GitHub Secrets
Add these secrets to your GitHub repository:

```
CLOUDFLARE_API_TOKEN          # Cloudflare API token with Pages:Edit permissions
CLOUDFLARE_ACCOUNT_ID         # Your Cloudflare account ID
```

### 2. Cloudflare Workers Projects
The Workers will be automatically created when you first deploy:
- `learn-platform-web-preview` (for preview deployments)
- `learn-platform-web-prod` (for production deployments)

### 3. Environment Variables
Configure environment variables in Cloudflare Workers dashboard:
- Go to Workers & Pages → Your Worker → Settings → Environment variables
- Add the same environment variables currently used in Vercel:
  - `DATABASE_URL`
  - `BETTER_AUTH_SECRET`
  - `BETTER_AUTH_URL`
  - `BETTER_AUTH_TRUSTED_ORIGINS`
  - `NEXT_PUBLIC_API_URL`

### 4. Optional: R2 Bucket for Caching
For enhanced performance, create an R2 bucket for incremental cache:
- Go to R2 Object Storage → Create bucket
- Name: `learn-platform-web-cache`
- Uncomment the R2 configuration in `wrangler.jsonc`
- Uncomment the R2 cache configuration in `open-next.config.ts`

## 📋 Getting Started

### Step 1: Install Dependencies
```bash
cd learn-platform/apps/web
npm install
```

### Step 2: Test Local Development
```bash
# Test Cloudflare Workers build locally
npm run preview

# Or build and preview separately
npx opennextjs-cloudflare build
npx opennextjs-cloudflare preview
```

### Step 3: Deploy

#### Automatic Deployment (Recommended)
Simply push to the appropriate branch:
```bash
# Deploy to preview environment
git push origin dev

# Deploy to production environment
git push origin main
```

#### Manual Deployment
Use the GitHub Actions workflow or deploy manually:
```bash
# Deploy to preview
npm run deploy:preview

# Deploy to production
npm run deploy:production

# Or use the unified deploy command
npm run deploy
```

## 🔍 How It Works

### Build Process
1. **OpenNext Build**: `opennextjs-cloudflare build` creates a Cloudflare Workers-compatible build
2. **Worker Generation**: Creates `.open-next/worker.js` with the main Worker script
3. **Static Assets**: Creates `.open-next/assets/` directory with static files
4. **Deployment**: `wrangler deploy` uploads the Worker and assets to Cloudflare Workers

### Environment Handling
- **Preview Environment**: Used for development and testing
- **Production Environment**: Used for main branch deployments
- **Environment Variables**: Managed through Cloudflare Workers dashboard
- **Custom Domains**: Can be configured in Cloudflare Workers dashboard
- **Local Development**: Uses `.dev.vars` file for local environment variables

## 🎛️ Available Scripts

### Local Development
```bash
npm run dev                    # Standard Next.js development
npm run preview               # Build and preview with Cloudflare Workers locally
```

### Building
```bash
npm run build                  # Standard Next.js build
npx opennextjs-cloudflare build # Build for Cloudflare Workers
```

### Deployment
```bash
npm run deploy                # Build and deploy to default environment
npm run deploy:preview        # Deploy to preview environment
npm run deploy:production     # Deploy to production environment
npm run upload                # Build and upload without deploying
```

### Type Generation
```bash
npm run cf-typegen            # Generate TypeScript types for Cloudflare bindings
```

## 🔄 Workflow Features

### Cache-Busting
- Enable "Force fresh build" in manual deployment
- Bypasses GitHub Actions caches
- Ensures fresh builds with latest dependencies

### Testing Integration
- Runs comprehensive test suite before deployment
- Uses Nx affected commands for efficient testing
- Uploads test results and coverage reports

### Monorepo Support
- Builds library dependencies in correct order
- Handles TypeScript path mappings
- Supports shared UI and style libraries

## 🌐 Deployment URLs

### Preview Environment
- **URL**: `https://learn-platform-web-preview.workers.dev`
- **Triggers**:
  - Automatic: Push to `dev` branch
  - Manual: Workflow dispatch with "preview" environment
- **Branch**: `dev` branch (or any branch via manual deployment)

### Production Environment
- **URL**: `https://learn-platform-web-prod.workers.dev`
- **Triggers**:
  - Automatic: Push to `main` branch
  - Manual: Workflow dispatch with "production" environment
- **Branch**: `main` branch (or any branch via manual deployment)
- **Custom Domain**: Can be configured in Cloudflare Workers dashboard

## 🔧 Troubleshooting

### Common Issues

1. **Build Failures**
   - Ensure all library dependencies are built first
   - Check that @cloudflare/next-on-pages is installed
   - Verify Node.js v20+ is being used

2. **Environment Variable Issues**
   - Configure variables in Cloudflare Pages dashboard
   - Ensure CLOUDFLARE_API_TOKEN has correct permissions
   - Verify CLOUDFLARE_ACCOUNT_ID is correct

3. **Deployment Failures**
   - Check that Cloudflare Pages projects exist
   - Verify project names match workflow configuration
   - Ensure API token has Pages:Edit permissions

### Debug Commands
```bash
# Check Wrangler authentication
npx wrangler whoami

# List Cloudflare Pages projects
npx wrangler pages project list

# Check build output
ls -la .vercel/output/static
```

## 🔄 Coexistence with Vercel

This Cloudflare Pages deployment runs completely independently of the existing Vercel deployment:

- **Separate Workflows**: Different GitHub Actions workflows
- **Independent Triggers**: Manual control prevents conflicts
- **Shared Codebase**: Both deployments use the same source code
- **Environment Isolation**: Separate environment variable management

## 🚀 Next Steps

### Immediate Actions
1. Set up GitHub Secrets
2. Create Cloudflare Pages projects
3. Configure environment variables
4. Test manual deployment

### Optional Enhancements
1. **Custom Domains**: Configure production domain in Cloudflare Pages
2. **Automated Triggers**: Enable push-based deployments
3. **Additional Bindings**: Add KV, D1, or R2 bindings as needed
4. **Performance Monitoring**: Set up Cloudflare Analytics

## 📚 Resources

- [Cloudflare Workers Documentation](https://developers.cloudflare.com/workers/)
- [Next.js on Cloudflare Workers](https://developers.cloudflare.com/workers/framework-guides/web-apps/nextjs/)
- [OpenNext Cloudflare Documentation](https://opennext.js.org/cloudflare/)
- [@opennextjs/cloudflare](https://github.com/opennextjs/opennext/tree/main/packages/cloudflare)
- [Wrangler CLI Documentation](https://developers.cloudflare.com/workers/wrangler/)
- [Cloudflare Workers Runtime APIs](https://developers.cloudflare.com/workers/runtime-apis/)

---

**Status**: ✅ Ready for deployment
**Last Updated**: $(date)
**Compatibility**: Next.js 15.2.4, Node.js v20+, Wrangler v4+
