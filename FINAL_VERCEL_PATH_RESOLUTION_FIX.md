# Final Vercel CLI Path Resolution Fix

## Root Cause Identified

The path duplication issue was caused by **double path resolution**:

1. **Vercel Dashboard Root Directory**: `learn-platform/apps/web`
2. **GitHub Actions working-directory**: `./learn-platform/apps/web`
3. **Result**: Vercel CLI combined both paths = `learn-platform/apps/web` + `learn-platform/apps/web`

This created the error path: `/learn-platform/apps/web/learn-platform/apps/web/package.json`

## The Solution: Use Dashboard Root Directory Only

Since you have the Root Directory set in Vercel Dashboard (`learn-platform/apps/web`), we should:
- Run Vercel CLI from the **repository root** (`learn-platform/`)
- Let Vercel Dashboard Root Directory setting handle the path resolution
- Remove the conflicting `working-directory` parameters from GitHub Actions

## Key Changes Made

### 1. Updated GitHub Actions Workflow

**Before** (causing double path resolution):
```yaml
- name: Build Project Artifacts (Preview)
  run: vercel build --token=${{ secrets.VERCEL_TOKEN }}
  working-directory: ./learn-platform/apps/web  # This conflicts with Dashboard Root Directory
```

**After** (single path resolution):
```yaml
- name: Build Project Artifacts (Preview)
  run: |
    cd learn-platform
    vercel build --token=${{ secrets.VERCEL_TOKEN }}
  # No working-directory parameter - let Dashboard Root Directory handle it
```

### 2. Simplified vercel.json

**Before**:
```json
{
  "installCommand": "cd ../../ && bun install --frozen-lockfile && cd apps/web"
}
```

**After**:
```json
{
  "installCommand": "bun install --frozen-lockfile"
}
```

Since Vercel CLI now runs from the repository root (`learn-platform/`), the install command can be simplified.

### 3. Enhanced Debugging

Added better debugging to show the path resolution:
```yaml
- name: Debug paths (Preview)
  run: |
    echo "Current working directory: $(pwd)"
    echo "Contents of learn-platform:"
    ls -la learn-platform/
    echo "Contents of learn-platform/apps/web:"
    ls -la learn-platform/apps/web/
    echo "Checking for package.json:"
    ls -la learn-platform/apps/web/package.json
```

## How This Fixes the Issue

### Path Resolution Flow:
1. **GitHub Actions**: Runs from repository root (`/home/<USER>/work/kwaci-learning/kwaci-learning/`)
2. **Vercel CLI**: Executes from `learn-platform/` directory
3. **Vercel Dashboard**: Applies Root Directory setting `learn-platform/apps/web`
4. **Final Path**: `learn-platform/` + `apps/web/` = `learn-platform/apps/web/package.json` ✅

### No More Double Path:
- ❌ Old: `learn-platform/apps/web` + `learn-platform/apps/web` = duplication
- ✅ New: `learn-platform/` + `apps/web/` = correct path

## Configuration Alignment

### GitHub Actions Workflow:
- Runs Vercel CLI from `learn-platform/` directory
- No conflicting `working-directory` parameters
- Lets Vercel Dashboard handle app location

### Vercel Dashboard:
- Root Directory: `learn-platform/apps/web` ✅
- This tells Vercel where the Next.js app is located

### vercel.json:
- Simplified install command for repository root execution
- Build command points to the correct script
- Framework detection works correctly

## Expected Results

With this fix:
- ✅ Vercel CLI will find package.json at: `learn-platform/apps/web/package.json`
- ✅ No more path duplication errors
- ✅ Bun package manager works correctly
- ✅ Nx monorepo structure is respected
- ✅ Both preview and production deployments work

## Verification Steps

1. **Check debug output** for correct paths:
   ```
   Current working directory: /home/<USER>/work/kwaci-learning/kwaci-learning
   Contents of learn-platform/apps/web:
   -rw-r--r-- 1 <USER> <GROUP> package.json
   -rw-r--r-- 1 <USER> <GROUP> vercel.json
   ```

2. **Vercel CLI should find**:
   ```
   /home/<USER>/work/kwaci-learning/kwaci-learning/learn-platform/apps/web/package.json
   ```

3. **No more ENOENT errors** for duplicated paths

## Key Principle

**Use either Dashboard Root Directory OR GitHub Actions working-directory, never both.**

Since you have the Dashboard Root Directory set, we removed the conflicting GitHub Actions working-directory parameters and let Vercel handle the path resolution correctly.

## Troubleshooting

If issues persist:
1. Verify Vercel Dashboard Root Directory is set to: `learn-platform/apps/web`
2. Check debug output for correct directory structure
3. Ensure all GitHub secrets are properly configured
4. Test locally by running Vercel CLI from the `learn-platform/` directory

This solution eliminates the fundamental conflict between GitHub Actions and Vercel Dashboard path resolution.
