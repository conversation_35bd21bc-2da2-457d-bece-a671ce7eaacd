# Vercel Next.js Nx Monorepo Deployment Solution

## Problem Summary
The Vercel deployment was failing with the error:
```
Build Failed
No Next.js version detected. Make sure your package.json has "next" in either "dependencies" or "devDependencies". Also check your Root Directory setting matches the directory of your package.json file.
```

## Root Cause Analysis
1. **Missing Root Directory Configuration**: Vercel's Root Directory setting was empty
2. **No package.json in App Directory**: Nx monorepos typically don't have package.json files in individual app directories
3. **Missing Monorepo Configuration**: Next.js config didn't include `outputFileTracingRoot` for monorepo support
4. **Shared Libraries Not Transpiled**: Shared packages needed to be explicitly transpiled

## Solution Implementation

### 1. Vercel Dashboard Configuration
**CRITICAL**: Set the Root Directory in your Vercel project settings:

1. Go to your Vercel project dashboard
2. Navigate to Settings → Build & Deployment
3. Set **Root Directory** to: `learn-platform/apps/web`
4. Click Save

### 2. Created package.json for Web App
Created `learn-platform/apps/web/package.json` with minimal Next.js dependencies:

```json
{
  "name": "@learn-platform/web",
  "version": "0.0.1",
  "private": true,
  "scripts": {
    "dev": "next dev",
    "build": "next build", 
    "start": "next start",
    "lint": "next lint"
  },
  "dependencies": {
    "next": "~15.2.4",
    "react": "19.0.0",
    "react-dom": "19.0.0"
  }
}
```

### 3. Updated next.config.js
Enhanced the Next.js configuration for monorepo deployment:

```javascript
const nextConfig = {
  nx: {},
  
  // Include files from the monorepo base for Vercel deployment
  outputFileTracingRoot: path.join(__dirname, '../../'),
  
  // Transpile shared packages for proper bundling
  transpilePackages: [
    '@learn-platform/shared-ui',
    '@learn-platform/shared-styles', 
    '@learn-platform/trpc',
    '@learn-platform/auth'
  ],
  
  webpack: (config) => {
    // Existing CSS path mapping support
    config.resolve.alias = {
      ...config.resolve.alias,
      '@learn-platform/shared-styles': require('path').resolve(__dirname, '../../libs/shared/styles/src'),
    };
    return config;
  },
};
```

### 4. Updated GitHub Actions Workflow
Switched to the official Vercel action for better compatibility:

```yaml
- name: Deploy to Vercel (Preview)
  uses: vercel/action@v1  # Changed from amondnet/vercel-action@v25
  with:
    vercel-token: ${{ secrets.VERCEL_TOKEN }}
    vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
    vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID_WEB }}
    working-directory: ./learn-platform/apps/web
    scope: ${{ secrets.VERCEL_ORG_ID }}
```

## Key Configuration Explanations

### outputFileTracingRoot
This tells Next.js to include files from the monorepo root when tracing dependencies for deployment. Essential for monorepos where dependencies are in parent directories.

### transpilePackages
Explicitly lists shared packages that need to be transpiled during the build process. This replaces the old `next-transpile-modules` approach.

### Root Directory Setting
Tells Vercel where to find the Next.js application within the repository. Must point to the directory containing the package.json with Next.js dependencies.

## Verification Steps

1. **Check Vercel Root Directory**: Ensure it's set to `learn-platform/apps/web`
2. **Verify package.json**: Confirm the web app has its own package.json with Next.js
3. **Test Local Build**: Run `cd learn-platform/apps/web && npm run build` to verify locally
4. **Monitor Deployment**: Check Vercel deployment logs for successful Next.js detection

## Expected Outcome

After implementing these changes:
- Vercel will correctly detect Next.js in the web app directory
- Shared libraries will be properly transpiled and included
- Monorepo dependencies will be traced correctly
- Deployments will succeed for both preview and production environments

## Additional Recommendations

1. **Enable Vercel's Monorepo Features**: Consider enabling "Skip deployment for unchanged projects" in Vercel settings
2. **Environment Variables**: Ensure all necessary environment variables are configured in Vercel
3. **Build Command**: Verify the build command in Vercel matches your Nx setup
4. **Dependencies**: Keep the workspace root package.json as the source of truth for versions

## Troubleshooting

If issues persist:
1. Check Vercel build logs for specific error messages
2. Verify all shared packages are listed in `transpilePackages`
3. Ensure the `outputFileTracingRoot` path is correct
4. Test the build locally with the same Node.js version as Vercel
