name: Deploy Web App to Cloudflare Workers

# This workflow deploys the Next.js web app to Cloudflare Workers while keeping
# the existing Vercel deployment intact. It supports both preview and production
# environments with manual control and optional automated triggers.
#
# 🚀 FEATURES:
# - Manual deployment with environment selection
# - Support for both 'main' and 'dev' branch deployments
# - Comprehensive testing before deployment
# - Monorepo-aware dependency building
# - Environment-specific Cloudflare Workers deployments
# - Force rebuild option for cache-busting
# - Full Next.js feature support (SSR, ISR, Server Actions, etc.)
#
# 📋 USAGE:
# 1. Manual deployment: Use "Run workflow" → Select environment
# 2. Force rebuild: Enable "Force fresh build" option
# 3. Automated: Push to main/dev branches triggers deployment
#
# 🔧 REQUIRED GITHUB SECRETS:
# - CLOUDFLARE_API_TOKEN: Cloudflare API token with Workers:Edit permissions
# - CLOUDFLARE_ACCOUNT_ID: Your Cloudflare account ID

on:
  # Manual triggering with environment selection (primary method)
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'preview'
        type: choice
        options:
          - preview
          - production
      force_rebuild:
        description: 'Force fresh build (bypass all caches)'
        required: false
        default: false
        type: boolean
      deploy_branch:
        description: 'Branch to deploy (leave empty to use current branch)'
        required: false
        default: ''
        type: string

  # Automated triggers for branch-based deployments
  push:
    branches:
      - main      # Deploy to production
      - dev       # Deploy to preview
    paths:
      - 'learn-platform/apps/web/**'
      - 'learn-platform/libs/shared/**'
      - 'learn-platform/libs/trpc/**'
      - 'learn-platform/libs/auth/**'
      - '.github/workflows/deploy-cloudflare.yml'

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest

    steps:
      - name: Deployment Configuration
        run: |
          echo "🔍 Cloudflare Workers Deployment Configuration:"

          # Determine environment based on trigger type and branch
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            ENVIRONMENT="${{ github.event.inputs.environment || 'preview' }}"
            FORCE_REBUILD="${{ github.event.inputs.force_rebuild || 'false' }}"
            DEPLOY_BRANCH="${{ github.event.inputs.deploy_branch || github.ref_name }}"
          else
            # Automatic deployment based on branch
            if [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
              ENVIRONMENT="production"
            else
              ENVIRONMENT="preview"
            fi
            FORCE_REBUILD="false"
            DEPLOY_BRANCH="${{ github.ref_name }}"
          fi

          echo "Environment: $ENVIRONMENT"
          echo "Force rebuild: $FORCE_REBUILD"
          echo "Deploy branch: $DEPLOY_BRANCH"
          echo "Trigger event: ${{ github.event_name }}"

          # Export for use in later steps
          echo "DEPLOYMENT_ENVIRONMENT=$ENVIRONMENT" >> $GITHUB_ENV
          echo "DEPLOYMENT_FORCE_REBUILD=$FORCE_REBUILD" >> $GITHUB_ENV
          echo "DEPLOYMENT_BRANCH=$DEPLOY_BRANCH" >> $GITHUB_ENV

          if [[ "$FORCE_REBUILD" == "true" ]]; then
            echo "🚀 CACHE-BUSTING MODE ACTIVE - All caches will be bypassed"
          else
            echo "📦 Normal caching mode - Using cached dependencies when available"
          fi

      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: ${{ env.DEPLOYMENT_BRANCH }}
          fetch-depth: 0  # Needed for Nx affected commands

      - name: Fetch main branch for comparison
        if: github.ref != 'refs/heads/main'
        run: |
          git fetch origin main:main || echo "Main branch already available"

      - name: Setup Node.js v20
        uses: actions/setup-node@v4
        with:
          node-version: '20'  # Required for Wrangler CLI compatibility

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Cache dependencies
        if: env.DEPLOYMENT_FORCE_REBUILD != 'true'
        uses: actions/cache@v4
        with:
          path: |
            ./learn-platform/node_modules
            ./learn-platform/.nx/cache
          key: ${{ runner.os }}-bun-cloudflare-test-${{ hashFiles('**/bun.lock') }}
          restore-keys: |
            ${{ runner.os }}-bun-cloudflare-test-
            ${{ runner.os }}-bun-

      - name: Cache dependencies (Force rebuild)
        if: env.DEPLOYMENT_FORCE_REBUILD == 'true'
        uses: actions/cache@v4
        with:
          path: |
            ./learn-platform/node_modules
            ./learn-platform/.nx/cache
          key: ${{ runner.os }}-bun-cloudflare-test-force-${{ github.run_id }}-${{ hashFiles('**/bun.lock') }}

      - name: Install dependencies
        run: |
          cd learn-platform
          bun install --frozen-lockfile

      - name: Run affected tests
        run: |
          cd learn-platform
          if [ "${{ github.ref }}" = "refs/heads/main" ]; then
            echo "Running tests on main branch - comparing with HEAD~1"
            bunx nx affected --target=test --parallel --coverage --base=HEAD~1
          else
            echo "Running tests on dev branch - comparing with main"
            bunx nx affected --target=test --parallel --coverage --base=main
          fi
        env:
          NODE_ENV: test

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-results-cloudflare
          path: |
            ./learn-platform/coverage/
            ./learn-platform/test-results/
          retention-days: 7

  build-and-deploy:
    name: Build and Deploy to Cloudflare Workers
    runs-on: ubuntu-latest
    needs: test  # Only run if tests pass

    steps:
      - name: Deployment Status
        run: |
          echo "🔍 Cloudflare Workers Deployment Configuration:"

          # Determine environment based on trigger type and branch
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            ENVIRONMENT="${{ github.event.inputs.environment || 'preview' }}"
            FORCE_REBUILD="${{ github.event.inputs.force_rebuild || 'false' }}"
            DEPLOY_BRANCH="${{ github.event.inputs.deploy_branch || github.ref_name }}"
          else
            # Automatic deployment based on branch
            if [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
              ENVIRONMENT="production"
            else
              ENVIRONMENT="preview"
            fi
            FORCE_REBUILD="false"
            DEPLOY_BRANCH="${{ github.ref_name }}"
          fi

          echo "Environment: $ENVIRONMENT"
          echo "Force rebuild: $FORCE_REBUILD"
          echo "Deploy branch: $DEPLOY_BRANCH"
          echo "Commit: ${{ github.sha }}"

          # Export for use in later steps
          echo "DEPLOYMENT_ENVIRONMENT=$ENVIRONMENT" >> $GITHUB_ENV
          echo "DEPLOYMENT_FORCE_REBUILD=$FORCE_REBUILD" >> $GITHUB_ENV
          echo "DEPLOYMENT_BRANCH=$DEPLOY_BRANCH" >> $GITHUB_ENV

          if [[ "$FORCE_REBUILD" == "true" ]]; then
            echo "🚀 CACHE-BUSTING MODE ACTIVE:"
            echo "  ✓ GitHub Actions caches will use unique keys"
            echo "  ✓ All build artifacts will be regenerated"
          else
            echo "📦 Normal deployment mode with standard caching"
          fi

      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          ref: ${{ env.DEPLOYMENT_BRANCH }}
          fetch-depth: 0

      - name: Setup Node.js v20
        uses: actions/setup-node@v4
        with:
          node-version: '20'  # Required for Wrangler CLI compatibility

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Cache dependencies (Normal mode)
        if: env.DEPLOYMENT_FORCE_REBUILD != 'true'
        uses: actions/cache@v4
        with:
          path: |
            ./learn-platform/node_modules
            ./learn-platform/.nx/cache
          key: ${{ runner.os }}-bun-cloudflare-${{ hashFiles('**/bun.lock') }}
          restore-keys: |
            ${{ runner.os }}-bun-cloudflare-
            ${{ runner.os }}-bun-

      - name: Cache dependencies (Force rebuild mode)
        if: env.DEPLOYMENT_FORCE_REBUILD == 'true'
        uses: actions/cache@v4
        with:
          path: |
            ./learn-platform/node_modules
            ./learn-platform/.nx/cache
          key: ${{ runner.os }}-bun-cloudflare-force-${{ github.run_id }}-${{ hashFiles('**/bun.lock') }}

      - name: Install dependencies
        run: |
          cd learn-platform
          bun install --frozen-lockfile

      - name: Build Library Dependencies
        run: |
          echo "=== 🔨 Building Library Dependencies for Cloudflare Workers ==="
          cd learn-platform
          echo ""
          echo "🏗️ Building database library..."
          bun nx build db --verbose
          echo ""
          echo "🏗️ Building auth library..."
          bun nx build auth --verbose
          echo ""
          echo "🏗️ Building tRPC library..."
          bun nx build trpc --verbose
          echo ""
          echo "🏗️ Building shared styles library..."
          bun nx build shared-styles --verbose
          echo ""
          echo "🏗️ Building shared UI library..."
          bun nx build shared-ui --verbose
          echo ""
          echo "✅ All library dependencies built successfully!"

      - name: Install Cloudflare Workers Dependencies
        run: |
          cd learn-platform/apps/web
          echo "📦 Installing @opennextjs/cloudflare..."
          npm install @opennextjs/cloudflare@latest --save-dev
          echo "📦 Installing wrangler..."
          npm install wrangler@latest --save-dev
          echo "✅ Cloudflare Workers dependencies installed"

      - name: Cache Next.js Build
        if: env.DEPLOYMENT_FORCE_REBUILD != 'true'
        uses: actions/cache@v4
        with:
          path: |
            ./learn-platform/apps/web/.next/cache
            ./learn-platform/apps/web/node_modules/.cache
          key: ${{ runner.os }}-nextjs-${{ hashFiles('**/package-lock.json', '**/bun.lock') }}-${{ hashFiles('**/*.js', '**/*.jsx', '**/*.ts', '**/*.tsx') }}
          restore-keys: |
            ${{ runner.os }}-nextjs-${{ hashFiles('**/package-lock.json', '**/bun.lock') }}-
            ${{ runner.os }}-nextjs-

      - name: Cache Next.js Build (Force rebuild)
        if: env.DEPLOYMENT_FORCE_REBUILD == 'true'
        uses: actions/cache@v4
        with:
          path: |
            ./learn-platform/apps/web/.next/cache
            ./learn-platform/apps/web/node_modules/.cache
          key: ${{ runner.os }}-nextjs-force-${{ github.run_id }}-${{ hashFiles('**/package-lock.json', '**/bun.lock') }}

      - name: Build for Cloudflare Workers
        run: |
          cd learn-platform/apps/web
          echo "🏗️ Building Next.js app for Cloudflare Workers..."
          echo "Environment: ${{ env.DEPLOYMENT_ENVIRONMENT }}"

          # Set API URL based on deployment environment
          if [[ "${{ env.DEPLOYMENT_ENVIRONMENT }}" == "production" ]]; then
            export NEXT_PUBLIC_API_URL="https://learn-platform-api-prod.bm.workers.dev"
            echo "🔧 Using production API URL: $NEXT_PUBLIC_API_URL"
          else
            export NEXT_PUBLIC_API_URL="https://learn-platform-api-dev.bm.workers.dev"
            echo "🔧 Using preview API URL: $NEXT_PUBLIC_API_URL"
          fi

          # Build with OpenNext for Cloudflare Workers
          echo "Step 1: Building with OpenNext Cloudflare adapter..."
          npx opennextjs-cloudflare build

          # Verify build output
          echo "Step 2: Verifying build output..."
          if [ -f ".open-next/worker.js" ] && [ -d ".open-next/assets" ]; then
            echo "✅ Cloudflare Workers build output created successfully"
            echo "📊 Build output structure:"
            ls -la .open-next/
            echo "📊 Assets directory size:"
            du -sh .open-next/assets
            echo "📊 Worker script size:"
            du -sh .open-next/worker.js
          else
            echo "❌ Build output files not found"
            echo "Expected: .open-next/worker.js and .open-next/assets/"
            ls -la .open-next/ || echo "No .open-next directory found"
            exit 1
          fi
        env:
          NODE_ENV: production

      - name: Deploy to Cloudflare Workers
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          command: deploy --env ${{ env.DEPLOYMENT_ENVIRONMENT }}
          workingDirectory: ./learn-platform/apps/web

      - name: Deployment Success
        if: success()
        run: |
          echo "✅ Cloudflare Workers deployment completed successfully!"
          echo "Environment: ${{ env.DEPLOYMENT_ENVIRONMENT }}"
          echo "Branch: ${{ env.DEPLOYMENT_BRANCH }}"
          echo "Commit: ${{ github.sha }}"
          echo "Trigger: ${{ github.event_name }}"
          echo ""
          echo "🌐 Deployment URLs:"
          if [[ "${{ env.DEPLOYMENT_ENVIRONMENT }}" == "production" ]]; then
            echo "Production: https://learn-platform-web-prod.workers.dev"
          else
            echo "Preview: https://learn-platform-web-preview.workers.dev"
          fi
          echo ""
          echo "🔍 Deployment Configuration Summary:"
          echo "Force rebuild: ${{ env.DEPLOYMENT_FORCE_REBUILD }}"
          echo "Worker: learn-platform-web-${{ env.DEPLOYMENT_ENVIRONMENT }}"
          echo ""
          echo "🚀 Next.js Features Supported:"
          echo "  ✓ Server-Side Rendering (SSR)"
          echo "  ✓ Static Site Generation (SSG)"
          echo "  ✓ Incremental Static Regeneration (ISR)"
          echo "  ✓ Server Actions"
          echo "  ✓ API Routes"
          echo "  ✓ Middleware"
          echo "  ✓ Node.js APIs"

      - name: Deployment Failed
        if: failure()
        run: |
          echo "❌ Cloudflare Workers deployment failed!"
          echo "Please check the logs above for details."
          echo "Common issues:"
          echo "- Check CLOUDFLARE_API_TOKEN has Workers:Edit permissions"
          echo "- Verify CLOUDFLARE_ACCOUNT_ID is correct"
          echo "- Ensure wrangler.jsonc configuration is correct"
          echo "- Check that @opennextjs/cloudflare build completed successfully"
          echo "- Verify Node.js compatibility flags are enabled"
          exit 1
