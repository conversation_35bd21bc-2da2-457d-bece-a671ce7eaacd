name: Deploy Admin App to Vercel

on:
 # Temporary disable deploy to vercel, we use cloudflare workers instead
  push:
    branches:
      - deploy/vercel
    paths:
      - 'learn-platform/apps/admin/**'
      - 'learn-platform/libs/shared/**'
      - 'learn-platform/libs/trpc/**'
      - 'learn-platform/libs/auth/**'
      - '.github/workflows/deploy-admin.yml'

  # Allow manual triggering
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'production'
        type: choice
        options:
          - preview
          - production

# No default working directory to avoid path conflicts with Vercel CLI

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Needed for Nx affected commands

      - name: Fetch main branch for comparison
        if: github.ref != 'refs/heads/main'
        run: |
          git fetch origin main:main || echo "Main branch already available"

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: |
            ./learn-platform/node_modules
            ./learn-platform/.nx/cache
          key: ${{ runner.os }}-bun-test-${{ hashFiles('**/bun.lock') }}
          restore-keys: |
            ${{ runner.os }}-bun-test-
            ${{ runner.os }}-bun-

      - name: Install dependencies
        run: |
          cd learn-platform
          bun install --frozen-lockfile

      - name: Run affected tests
        run: |
          cd learn-platform
          if [ "${{ github.ref }}" = "refs/heads/main" ]; then
            echo "Running tests on main branch - comparing with HEAD~1"
            bunx nx affected --target=test --parallel --coverage --base=HEAD~1
          else
            echo "Running tests on dev branch - comparing with main"
            bunx nx affected --target=test --parallel --coverage --base=main
          fi
        env:
          NODE_ENV: test

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-results-admin
          path: |
            ./learn-platform/coverage/
            ./learn-platform/test-results/
          retention-days: 7

      - name: Upload coverage reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: coverage-reports-admin
          path: ./learn-platform/coverage/
          retention-days: 30

  build-and-deploy:
    name: Build and Deploy Admin App
    runs-on: ubuntu-latest
    needs: test  # Only run if tests pass

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Fetch main branch for comparison
        if: github.ref != 'refs/heads/main'
        run: |
          git fetch origin main:main || echo "Main branch already available"

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: |
            ./learn-platform/node_modules
            ./learn-platform/.nx/cache
          key: ${{ runner.os }}-bun-admin-${{ hashFiles('**/bun.lock') }}
          restore-keys: |
            ${{ runner.os }}-bun-admin-
            ${{ runner.os }}-bun-

      - name: Install dependencies
        run: |
          cd learn-platform
          bun install --frozen-lockfile

      - name: Lint admin application
        run: |
          cd learn-platform
          echo "Running lint for admin application..."
          cd apps/admin
          ../../node_modules/.bin/eslint . --max-warnings=50
        continue-on-error: false

      - name: Cache Vercel CLI
        uses: actions/cache@v4
        with:
          path: ~/.npm
          key: ${{ runner.os }}-vercel-cli-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-vercel-cli-

      - name: Install Vercel CLI
        run: npm install --global vercel@latest

      - name: Install app-specific dependencies
        run: |
          cd learn-platform/apps/admin
          npm install --production

      - name: Debug paths (Preview)
        if: github.event.inputs.environment == 'preview' || (github.event_name == 'push' && github.ref == 'refs/heads/dev')
        run: |
          echo "Current working directory: $(pwd)"
          echo "Contents of current directory:"
          ls -la
          echo "Contents of learn-platform:"
          ls -la learn-platform/
          echo "Contents of learn-platform/apps/admin:"
          ls -la learn-platform/apps/admin/
          echo "Checking for package.json:"
          ls -la learn-platform/apps/admin/package.json
          echo "Vercel CLI will run from: learn-platform (monorepo root)"

      - name: Pull Vercel Environment Information (Preview)
        if: github.event.inputs.environment == 'preview' || (github.event_name == 'push' && github.ref == 'refs/heads/dev')
        run: vercel pull --yes --environment=preview --token=${{ secrets.VERCEL_TOKEN }}
        working-directory: ./learn-platform/apps/admin
        env:
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID_ADMIN }}

      - name: Build Project Artifacts (Preview)
        if: github.event.inputs.environment == 'preview' || (github.event_name == 'push' && github.ref == 'refs/heads/dev')
        run: vercel build --token=${{ secrets.VERCEL_TOKEN }}
        working-directory: ./learn-platform/apps/admin
        env:
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID_ADMIN }}

      - name: Debug build output (Preview)
        if: github.event.inputs.environment == 'preview' || (github.event_name == 'push' && github.ref == 'refs/heads/dev')
        run: |
          cd learn-platform
          echo "Current working directory: $(pwd)"
          echo "Contents of current directory:"
          ls -la
          echo "Contents of apps/admin:"
          ls -la apps/admin/
          echo "Contents of apps/admin/src:"
          ls -la apps/admin/src/
          echo "Checking for app directory:"
          ls -la apps/admin/src/app/ 2>/dev/null || echo "src/app directory not found"
          echo "Looking for .next directory in apps/admin:"
          find apps/admin -name ".next" -type d 2>/dev/null || echo "No .next directory found"
          echo "Contents of apps/admin/.next (if exists):"
          ls -la apps/admin/.next/ 2>/dev/null || echo ".next directory not found"

      - name: Deploy Project Artifacts to Vercel (Preview)
        if: github.event.inputs.environment == 'preview' || (github.event_name == 'push' && github.ref == 'refs/heads/dev')
        run: vercel deploy --prebuilt --token=${{ secrets.VERCEL_TOKEN }}
        working-directory: ./learn-platform/apps/admin
        env:
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID_ADMIN }}

      - name: Debug paths (Production)
        if: github.event.inputs.environment == 'production' || (github.event_name == 'push' && github.ref == 'refs/heads/main')
        run: |
          echo "Current working directory: $(pwd)"
          echo "Contents of current directory:"
          ls -la
          echo "Contents of learn-platform:"
          ls -la learn-platform/
          echo "Contents of learn-platform/apps/admin:"
          ls -la learn-platform/apps/admin/
          echo "Checking for package.json:"
          ls -la learn-platform/apps/admin/package.json
          echo "Vercel CLI will run from: learn-platform (monorepo root)"

      - name: Pull Vercel Environment Information (Production)
        if: github.event.inputs.environment == 'production' || (github.event_name == 'push' && github.ref == 'refs/heads/main')
        run: vercel pull --yes --environment=production --token=${{ secrets.VERCEL_TOKEN }}
        working-directory: ./learn-platform/apps/admin
        env:
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID_ADMIN }}

      - name: Build Project Artifacts (Production)
        if: github.event.inputs.environment == 'production' || (github.event_name == 'push' && github.ref == 'refs/heads/main')
        run: vercel build --prod --token=${{ secrets.VERCEL_TOKEN }}
        working-directory: ./learn-platform/apps/admin
        env:
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID_ADMIN }}

      - name: Debug build output (Production)
        if: github.event.inputs.environment == 'production' || (github.event_name == 'push' && github.ref == 'refs/heads/main')
        run: |
          cd learn-platform
          echo "Current working directory: $(pwd)"
          echo "Contents of current directory:"
          ls -la
          echo "Looking for .next directory in apps/admin:"
          find apps/admin -name ".next" -type d 2>/dev/null || echo "No .next directory found"
          echo "Contents of apps/admin/.next (if exists):"
          ls -la apps/admin/.next/ 2>/dev/null || echo ".next directory not found"

      - name: Deploy Project Artifacts to Vercel (Production)
        if: github.event.inputs.environment == 'production' || (github.event_name == 'push' && github.ref == 'refs/heads/main')
        run: vercel deploy --prebuilt --prod --token=${{ secrets.VERCEL_TOKEN }}
        working-directory: ./learn-platform/apps/admin
        env:
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID_ADMIN }}

      - name: Deployment Status
        if: success()
        run: |
          echo "✅ Admin app deployment completed successfully!"
          echo "Environment: ${{ github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'production' || 'preview') }}"
          echo "Commit: ${{ github.sha }}"
          echo "Domain: admin.example.com"

      - name: Deployment Failed
        if: failure()
        run: |
          echo "❌ Admin app deployment failed!"
          echo "Please check the logs above for details."
          exit 1

  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: build-and-deploy
    if: always()
    
    steps:
      - name: Deployment Success Notification
        if: needs.build-and-deploy.result == 'success'
        run: |
          echo "🚀 Admin app deployment to Vercel completed successfully!"
          echo "Branch: ${{ github.ref_name }}"
          echo "Commit: ${{ github.sha }}"
          echo "Environment: ${{ github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'production' || 'preview') }}"
          echo "Domain: admin.example.com"

      - name: Deployment Failure Notification
        if: needs.build-and-deploy.result == 'failure'
        run: |
          echo "💥 Admin app deployment failed!"
          echo "Branch: ${{ github.ref_name }}"
          echo "Commit: ${{ github.sha }}"
          echo "Please check the workflow logs for details."
