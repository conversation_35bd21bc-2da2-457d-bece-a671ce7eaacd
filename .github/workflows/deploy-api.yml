name: Deploy API to Cloudflare Workers

on:
  # Trigger on push to main and dev branches with specific path changes
  push:
    branches:
      - main
      - dev
    paths:
      - 'learn-platform/apps/api/**'
      - 'learn-platform/libs/db/**'
      - 'learn-platform/libs/trpc/**'
      - 'learn-platform/libs/auth/**'
      - '.github/workflows/deploy-api.yml'
  
  # Allow manual triggering
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'production'
        type: choice
        options:
          - development
          - production

# Set default working directory
defaults:
  run:
    working-directory: ./learn-platform

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Needed for Nx affected commands

      - name: Verify repository structure
        run: |
          echo "📁 Current working directory: $(pwd)"
          echo "📁 Repository structure:"
          ls -la
          echo "📁 Learn platform directory exists: $(test -d . && echo 'YES' || echo 'NO')"
          echo "📁 Package.json exists: $(test -f package.json && echo 'YES' || echo 'NO')"

      - name: Fetch main branch for comparison
        if: github.ref != 'refs/heads/main'
        run: |
          git fetch origin main:main || echo "Main branch already available"

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: |
            ./learn-platform/node_modules
            ./learn-platform/.nx/cache
          key: ${{ runner.os }}-bun-test-${{ hashFiles('**/bun.lock') }}
          restore-keys: |
            ${{ runner.os }}-bun-test-
            ${{ runner.os }}-bun-

      - name: Install dependencies
        run: bun install --frozen-lockfile

      - name: Generate database schema (for tests)
        run: bun run db:generate
        continue-on-error: true

      - name: Run affected tests
        run: |
          if [ "${{ github.ref }}" = "refs/heads/main" ]; then
            echo "Running tests on main branch - comparing with HEAD~1"
            bunx nx affected --target=test --parallel --coverage --base=HEAD~1 || echo "No tests found or all tests passed"
          else
            echo "Running tests on dev branch - comparing with main"
            bunx nx affected --target=test --parallel --coverage --base=main || echo "No tests found or all tests passed"
          fi
        env:
          NODE_ENV: test

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-results-api
          path: |
            ./learn-platform/coverage/
            ./learn-platform/test-results/
          retention-days: 7

      - name: Upload coverage reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: coverage-reports-api
          path: ./learn-platform/coverage/
          retention-days: 30

  deploy:
    name: Build and Deploy API
    runs-on: ubuntu-latest
    needs: test  # Only run if tests pass

    # Environment variables for Cloudflare deployment
    env:
      CLOUDFLARE_API_TOKEN: ${{ secrets.CLOUDFLARE_API_TOKEN }}
      CLOUDFLARE_ACCOUNT_ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Verify repository structure
        run: |
          echo "📁 Current working directory: $(pwd)"
          echo "📁 Repository structure:"
          ls -la
          echo "📁 API app directory exists: $(test -d apps/api && echo 'YES' || echo 'NO')"
          echo "📁 Wrangler config exists: $(test -f apps/api/wrangler.jsonc && echo 'YES' || echo 'NO')"

      - name: Fetch main branch for comparison
        if: github.ref != 'refs/heads/main'
        run: |
          git fetch origin main:main || echo "Main branch already available"

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: |
            ./learn-platform/node_modules
            ./learn-platform/.nx/cache
          key: ${{ runner.os }}-bun-${{ hashFiles('**/bun.lock') }}
          restore-keys: |
            ${{ runner.os }}-bun-

      - name: Install dependencies
        run: bun install --frozen-lockfile

      - name: Generate database schema
        run: bun run db:generate
        continue-on-error: true

      - name: Lint API application
        run: bunx nx lint api

      - name: Build API application
        run: bunx nx build api

      - name: Verify Cloudflare Configuration
        run: |
          if [ -z "$CLOUDFLARE_API_TOKEN" ]; then
            echo "❌ CLOUDFLARE_API_TOKEN is not set"
            echo "Please add CLOUDFLARE_API_TOKEN to GitHub repository secrets"
            exit 1
          fi

          if [ -z "$CLOUDFLARE_ACCOUNT_ID" ]; then
            echo "❌ CLOUDFLARE_ACCOUNT_ID is not set"
            echo "Please add CLOUDFLARE_ACCOUNT_ID to GitHub repository secrets"
            exit 1
          fi

          echo "✅ Cloudflare credentials are configured"
          echo "Token length: ${#CLOUDFLARE_API_TOKEN} characters"
          echo "Account ID: ${CLOUDFLARE_ACCOUNT_ID:0:8}..."
          echo "Visit: https://github.com/${{ github.repository }}/settings/secrets/actions"

      - name: Deploy to Cloudflare Workers (Development)
        if: github.event.inputs.environment == 'development' || (github.event_name == 'push' && github.ref == 'refs/heads/dev')
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          command: deploy --config wrangler.jsonc --env dev
          workingDirectory: ./learn-platform/apps/api
          preCommands: |
            echo "🚀 Deploying to Development environment..."
            echo "Worker name: learn-platform-api-dev"
            echo "Account ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}"

      - name: Deploy to Cloudflare Workers (Production)
        if: github.event.inputs.environment == 'production' || (github.event_name == 'push' && github.ref == 'refs/heads/main')
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          command: deploy --config wrangler.jsonc --env prod
          workingDirectory: ./learn-platform/apps/api
          preCommands: |
            echo "🚀 Deploying to Production environment..."
            echo "Worker name: learn-platform-api-prod"
            echo "Account ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}"

      - name: Deployment Status
        if: success()
        run: |
          echo "✅ API deployment completed successfully using Cloudflare Wrangler Action!"
          echo "Environment: ${{ github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'production' || 'development') }}"
          echo "Worker Name: learn-platform-api-${{ github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'prod' || 'dev') }}"
          echo "Commit: ${{ github.sha }}"
          echo "Account ID: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}"

      - name: Deployment Failed
        if: failure()
        run: |
          echo "❌ API deployment failed!"
          echo "Please check the logs above for details."
          echo "Verify that CLOUDFLARE_API_TOKEN and CLOUDFLARE_ACCOUNT_ID are correctly set in repository secrets."
          exit 1

  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: deploy
    if: always()

    # Override the global working directory for this job since it doesn't need repository access
    defaults:
      run:
        working-directory: .

    steps:
      - name: Deployment Success Notification
        if: needs.deploy.result == 'success'
        run: |
          echo "🚀 API deployment to Cloudflare Workers completed successfully!"
          echo "Repository: ${{ github.repository }}"
          echo "Branch: ${{ github.ref_name }}"
          echo "Commit: ${{ github.sha }}"
          echo "Environment: ${{ github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'production' || 'development') }}"
          echo "Worker: learn-platform-api-${{ github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'prod' || 'dev') }}"

      - name: Deployment Failure Notification
        if: needs.deploy.result == 'failure'
        run: |
          echo "💥 API deployment failed!"
          echo "Repository: ${{ github.repository }}"
          echo "Branch: ${{ github.ref_name }}"
          echo "Commit: ${{ github.sha }}"
          echo "Please check the workflow logs for details."
          echo "Verify Cloudflare secrets are properly configured."
