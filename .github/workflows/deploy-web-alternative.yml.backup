# Alternative workflow using third-party action (amondnet/vercel-action@v25)
# This is a backup/alternative to the main deploy-web.yml file
# Rename this file to deploy-web.yml if you prefer using the third-party action

name: Deploy Web App to Vercel (Alternative)

on:
  # Trigger on push to main and dev branches with specific path changes
  push:
    branches:
      - main
      - dev
    paths:
      - 'learn-platform/apps/web/**'
      - 'learn-platform/libs/shared/**'
      - 'learn-platform/libs/trpc/**'
      - 'learn-platform/libs/auth/**'
      - '.github/workflows/deploy-web.yml'
  
  # Allow manual triggering
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'production'
        type: choice
        options:
          - preview
          - production

# Set default working directory
defaults:
  run:
    working-directory: ./learn-platform

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Needed for Nx affected commands

      - name: Fetch main branch for comparison
        if: github.ref != 'refs/heads/main'
        run: |
          git fetch origin main:main || echo "Main branch already available"

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: |
            ./learn-platform/node_modules
            ./learn-platform/.nx/cache
          key: ${{ runner.os }}-bun-test-${{ hashFiles('**/bun.lock') }}
          restore-keys: |
            ${{ runner.os }}-bun-test-
            ${{ runner.os }}-bun-

      - name: Install dependencies
        run: bun install --frozen-lockfile

      - name: Run affected tests
        run: |
          if [ "${{ github.ref }}" = "refs/heads/main" ]; then
            echo "Running tests on main branch - comparing with HEAD~1"
            bunx nx affected --target=test --parallel --coverage --base=HEAD~1
          else
            echo "Running tests on dev branch - comparing with main"
            bunx nx affected --target=test --parallel --coverage --base=main
          fi
        env:
          NODE_ENV: test

  build-and-deploy:
    name: Build and Deploy Web App
    runs-on: ubuntu-latest
    needs: test  # Only run if tests pass
    
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Cache dependencies
        uses: actions/cache@v4
        with:
          path: |
            ./learn-platform/node_modules
            ./learn-platform/.nx/cache
          key: ${{ runner.os }}-bun-web-${{ hashFiles('**/bun.lock') }}
          restore-keys: |
            ${{ runner.os }}-bun-web-
            ${{ runner.os }}-bun-

      - name: Install dependencies
        run: bun install --frozen-lockfile

      - name: Build web application
        run: |
          echo "Building web application..."
          cd apps/web
          ../../node_modules/.bin/next build
        env:
          NODE_ENV: production

      - name: Deploy to Vercel (Preview)
        if: github.event.inputs.environment == 'preview' || (github.event_name == 'push' && github.ref == 'refs/heads/dev')
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          github-token: ${{ secrets.GITHUB_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID_WEB }}
          working-directory: ./learn-platform/apps/web
          scope: ${{ secrets.VERCEL_ORG_ID }}

      - name: Deploy to Vercel (Production)
        if: github.event.inputs.environment == 'production' || (github.event_name == 'push' && github.ref == 'refs/heads/main')
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          github-token: ${{ secrets.GITHUB_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID_WEB }}
          vercel-args: '--prod'
          working-directory: ./learn-platform/apps/web
          scope: ${{ secrets.VERCEL_ORG_ID }}

      - name: Deployment Status
        if: success()
        run: |
          echo "✅ Web app deployment completed successfully!"
          echo "Environment: ${{ github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'production' || 'preview') }}"
          echo "Commit: ${{ github.sha }}"

      - name: Deployment Failed
        if: failure()
        run: |
          echo "❌ Web app deployment failed!"
          echo "Please check the logs above for details."
          exit 1
