name: Deploy Web App to Vercel

# This workflow supports cache-busting to ensure fresh deployments:
#
# 🚀 CACHE-BUSTING FEATURES:
# - Manual trigger with "force_rebuild" option bypasses all caches
# - "bypass_path_filters" allows deployment without code changes
# - Fresh environment variable pulls from Vercel dashboard
# - Vercel build cache bypass with --force flag
# - Unique GitHub Actions cache keys for force rebuilds
#
# 📋 USAGE:
# 1. Normal deployment: Push to main/dev branches (uses caching)
# 2. Force rebuild: Use "Run workflow" → Enable "Force fresh build"
# 3. Env var updates: Use "Run workflow" → Enable both options
#
# 💡 Use force rebuild when:
# - Environment variables changed in Vercel dashboard
# - Need to ensure completely fresh deployment
# - Troubleshooting cache-related deployment issues

on:
  # Temporary disable deploy to vercel, we use cloudflare workers instead
  push:
    branches:
      - deploy/vercel
    paths:
      - 'learn-platform/apps/web/**'
      - 'learn-platform/libs/shared/**'
      - 'learn-platform/libs/trpc/**'
      - 'learn-platform/libs/auth/**'
      - '.github/workflows/deploy-web.yml'

  # Allow manual triggering with cache-busting options
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'production'
        type: choice
        options:
          - preview
          - production
      force_rebuild:
        description: 'Force fresh build (bypass all caches)'
        required: false
        default: false
        type: boolean
      bypass_path_filters:
        description: 'Deploy even without code changes (useful for env var updates)'
        required: false
        default: false
        type: boolean

  # Optional: Scheduled fresh deployments (uncomment to enable weekly fresh builds)
  # schedule:
  #   - cron: '0 2 * * 1'  # Every Monday at 2 AM UTC

# No default working directory to avoid path conflicts with Vercel CLI

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest

    steps:
      - name: Cache-busting Status
        run: |
          echo "🔍 Cache-busting Configuration:"
          echo "Force rebuild: ${{ github.event.inputs.force_rebuild || 'false' }}"
          echo "Bypass path filters: ${{ github.event.inputs.bypass_path_filters || 'false' }}"
          echo "Trigger event: ${{ github.event_name }}"
          if [[ "${{ github.event.inputs.force_rebuild }}" == "true" ]]; then
            echo "🚀 CACHE-BUSTING MODE ACTIVE - All caches will be bypassed"
          else
            echo "📦 Normal caching mode - Using cached dependencies when available"
          fi

      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Needed for Nx affected commands

      - name: Fetch main branch for comparison
        if: github.ref != 'refs/heads/main'
        run: |
          git fetch origin main:main || echo "Main branch already available"

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Cache dependencies
        if: github.event.inputs.force_rebuild != 'true'
        uses: actions/cache@v4
        with:
          path: |
            ./learn-platform/node_modules
            ./learn-platform/.nx/cache
          key: ${{ runner.os }}-bun-test-${{ hashFiles('**/bun.lock') }}
          restore-keys: |
            ${{ runner.os }}-bun-test-
            ${{ runner.os }}-bun-

      - name: Cache dependencies (Force rebuild)
        if: github.event.inputs.force_rebuild == 'true'
        uses: actions/cache@v4
        with:
          path: |
            ./learn-platform/node_modules
            ./learn-platform/.nx/cache
          key: ${{ runner.os }}-bun-test-force-${{ github.run_id }}-${{ hashFiles('**/bun.lock') }}
          restore-keys: |
            # No restore keys for force rebuild - always fresh install

      - name: Install dependencies
        run: |
          cd learn-platform
          bun install --frozen-lockfile

      - name: Run affected tests
        run: |
          cd learn-platform
          if [ "${{ github.ref }}" = "refs/heads/main" ]; then
            echo "Running tests on main branch - comparing with HEAD~1"
            bunx nx affected --target=test --parallel --coverage --base=HEAD~1
          else
            echo "Running tests on dev branch - comparing with main"
            bunx nx affected --target=test --parallel --coverage --base=main
          fi
        env:
          NODE_ENV: test

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-results-web
          path: |
            ./learn-platform/coverage/
            ./learn-platform/test-results/
          retention-days: 7

      - name: Upload coverage reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: coverage-reports-web
          path: ./learn-platform/coverage/
          retention-days: 30

  build-and-deploy:
    name: Build and Deploy Web App
    runs-on: ubuntu-latest
    needs: test  # Only run if tests pass

    steps:
      - name: Cache-busting Status
        run: |
          echo "🔍 Deployment Cache-busting Configuration:"
          echo "Force rebuild: ${{ github.event.inputs.force_rebuild || 'false' }}"
          echo "Bypass path filters: ${{ github.event.inputs.bypass_path_filters || 'false' }}"
          echo "Environment: ${{ github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'production' || 'preview') }}"
          if [[ "${{ github.event.inputs.force_rebuild }}" == "true" ]]; then
            echo "🚀 CACHE-BUSTING MODE ACTIVE:"
            echo "  ✓ GitHub Actions caches will use unique keys"
            echo "  ✓ Vercel build cache will be bypassed"
            echo "  ✓ Environment variables will be force-refreshed"
            echo "  ✓ All build artifacts will be regenerated"
          else
            echo "📦 Normal deployment mode with standard caching"
          fi

      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Fetch main branch for comparison
        if: github.ref != 'refs/heads/main'
        run: |
          git fetch origin main:main || echo "Main branch already available"

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'

      - name: Setup Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest

      - name: Cache dependencies (Normal mode)
        if: github.event.inputs.force_rebuild != 'true'
        uses: actions/cache@v4
        with:
          path: |
            ./learn-platform/node_modules
            ./learn-platform/.nx/cache
          key: ${{ runner.os }}-bun-web-${{ hashFiles('**/bun.lock') }}
          restore-keys: |
            ${{ runner.os }}-bun-web-
            ${{ runner.os }}-bun-

      - name: Cache dependencies (Force rebuild mode)
        if: github.event.inputs.force_rebuild == 'true'
        uses: actions/cache@v4
        with:
          path: |
            ./learn-platform/node_modules
            ./learn-platform/.nx/cache
          key: ${{ runner.os }}-bun-web-force-${{ github.run_id }}-${{ hashFiles('**/bun.lock') }}
          restore-keys: |
            # No restore keys for force rebuild - always fresh install

      - name: Install dependencies
        run: |
          cd learn-platform
          bun install --frozen-lockfile

      - name: Build Library Dependencies
        run: |
          echo "=== 🔨 Building Library Dependencies ==="
          cd learn-platform
          echo ""
          echo "🏗️ Building database library..."
          bun nx build db --verbose || {
            echo "❌ Database library build failed!"
            echo "Checking for build errors..."
            exit 1
          }
          echo ""
          echo "🏗️ Building auth library..."
          bun nx build auth --verbose || {
            echo "❌ Auth library build failed!"
            echo "Checking for build errors..."
            exit 1
          }
          echo ""
          echo "🏗️ Building tRPC library..."
          bun nx build trpc --verbose || {
            echo "❌ tRPC library build failed!"
            echo "Checking for build errors..."
            exit 1
          }
          echo ""
          echo "🏗️ Building shared styles library..."
          bun nx build shared-styles --verbose || {
            echo "❌ Shared styles library build failed!"
            echo "Checking for build errors..."
            exit 1
          }
          echo ""
          echo "🏗️ Building shared UI library..."
          bun nx build shared-ui --verbose || {
            echo "❌ Shared UI library build failed!"
            echo "Checking for build errors..."
            exit 1
          }
          echo ""
          echo "✅ All library dependencies built successfully!"
          echo ""
          echo "📦 Verifying build outputs..."
          echo "📁 Dist directory structure:"
          ls -la dist/ || echo "❌ No dist directory found"
          echo ""
          echo "🔍 Checking critical build outputs:"
          ls -la dist/libs/db/ || echo "❌ Database library not built"
          ls -la dist/libs/auth/ || echo "❌ Auth library not built"
          ls -la dist/libs/trpc/ || echo "❌ tRPC library not built"
          ls -la dist/libs/shared/styles/ || echo "❌ Shared styles library not built"
          ls -la dist/libs/shared/ui/ || echo "❌ Shared UI library not built"
          echo ""
          echo "🎯 Verifying TypeScript declarations:"
          ls -la dist/libs/auth/src/index.d.ts || echo "❌ Auth types not generated"
          ls -la dist/libs/trpc/src/index.d.ts || echo "❌ tRPC types not generated"
          echo "✅ Build verification completed!"

      - name: Lint web application
        run: |
          cd learn-platform
          echo "Running lint for web application..."
          cd apps/web
          ../../node_modules/.bin/eslint . --max-warnings=20
        continue-on-error: false

      - name: Cache Vercel CLI (Normal mode)
        if: github.event.inputs.force_rebuild != 'true'
        uses: actions/cache@v4
        with:
          path: ~/.npm
          key: ${{ runner.os }}-vercel-cli-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-vercel-cli-

      - name: Cache Vercel CLI (Force rebuild mode)
        if: github.event.inputs.force_rebuild == 'true'
        uses: actions/cache@v4
        with:
          path: ~/.npm
          key: ${{ runner.os }}-vercel-cli-force-${{ github.run_id }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            # No restore keys for force rebuild

      - name: Install Vercel CLI
        run: |
          echo "Installing Vercel CLI..."
          if [[ "${{ github.event.inputs.force_rebuild }}" == "true" ]]; then
            echo "🚀 Force rebuild mode: Installing fresh Vercel CLI"
            npm install --global vercel@latest --force
          else
            echo "📦 Normal mode: Installing Vercel CLI"
            npm install --global vercel@latest
          fi

      - name: Install app-specific dependencies
        run: |
          cd learn-platform/apps/web
          npm install --production

      - name: Debug - Verify Monorepo Structure
        run: |
          echo "=== 🔍 Monorepo Structure Verification ==="
          echo "Current working directory: $(pwd)"
          echo ""
          echo "📁 Root directory contents:"
          ls -la learn-platform/
          echo ""
          echo "📚 Libs directory contents:"
          ls -la learn-platform/libs/ || echo "❌ libs directory not found"
          echo ""
          echo "🔐 Auth library structure:"
          ls -la learn-platform/libs/auth/ || echo "❌ libs/auth does not exist"
          echo ""
          echo "📄 Auth library files:"
          ls -la learn-platform/libs/auth/src/ || echo "❌ libs/auth/src does not exist"
          echo ""
          echo "🎯 Critical auth files:"
          ls -la learn-platform/libs/auth/src/auth.ts || echo "❌ auth.ts does not exist"
          ls -la learn-platform/libs/auth/src/index.ts || echo "❌ index.ts does not exist"
          echo ""
          echo "⚙️ Auth library configuration:"
          ls -la learn-platform/libs/auth/project.json || echo "❌ project.json does not exist"
          ls -la learn-platform/libs/auth/package.json || echo "❌ package.json does not exist"

      - name: Debug - TypeScript Configuration
        run: |
          echo "=== 📝 TypeScript Configuration Verification ==="
          echo ""
          echo "🔧 Base TypeScript config path mappings:"
          cat learn-platform/tsconfig.base.json | grep -A 10 '"paths"' || echo "❌ No path mappings found"
          echo ""
          echo "🌐 Web app TypeScript config:"
          cat learn-platform/apps/web/tsconfig.json | head -20
          echo ""
          echo "🔍 Verify auth library build outputs:"
          cd learn-platform
          echo "Checking if auth library build outputs exist..."
          if [ -f "dist/libs/auth/src/index.d.ts" ]; then
            echo "✅ Auth library TypeScript declarations found"
            echo "📄 Auth library exports:"
            head -10 dist/libs/auth/src/index.d.ts
          else
            echo "❌ Auth library TypeScript declarations not found"
            echo "📁 Available dist contents:"
            ls -la dist/ || echo "No dist directory"
          fi
          echo ""
          echo "🔍 Verify TypeScript module resolution:"
          echo "Testing TypeScript path mappings and library availability..."

          # Check if built libraries exist
          echo "📦 Checking built library outputs:"
          for lib in auth db trpc; do
            if [ -f "dist/libs/${lib}/src/index.d.ts" ]; then
              echo "  ✅ ${lib} library declarations available"
            else
              echo "  ⚠️  ${lib} library declarations not found"
            fi
          done

          for lib in styles ui; do
            if [ -f "dist/libs/shared/${lib}/src/index.d.ts" ]; then
              echo "  ✅ shared-${lib} library declarations available"
            else
              echo "  ⚠️  shared-${lib} library declarations not found"
            fi
          done

          echo ""
          echo "📝 TypeScript path mappings use source files (libs/*/src/index.ts)"
          echo "   This ensures compatibility with SWC compiler used by Nx tests"

          echo ""
          echo "🔍 Testing Next.js TypeScript compilation (quick check)..."
          cd apps/web
          # Use Next.js's built-in TypeScript checking which properly handles path mappings
          if timeout 60 npx next build --no-lint > /dev/null 2>&1; then
            echo "✅ Next.js TypeScript compilation successful"
            echo "✅ All module imports resolve correctly"
            echo "✅ Auth library import works in production build"
          else
            echo "❌ Next.js TypeScript compilation failed"
            echo "🔍 Running diagnostic build to identify issues..."
            npx next build --no-lint 2>&1 | head -20 || echo "Build diagnostic completed"
          fi
          cd ../../

      - name: Debug - Nx Build Dependencies
        run: |
          echo "=== 🏗️ Nx Build Dependencies Verification ==="
          cd learn-platform
          echo ""
          echo "📊 Nx project information for web app:"
          npx nx show project web --json | jq '.targets.build.dependsOn' || echo "❌ Failed to get build dependencies"
          echo ""
          echo "📊 Nx project information for auth library:"
          npx nx show project auth --json | jq '.targets.build' || echo "❌ Failed to get auth build config"
          echo ""
          echo "🔗 Nx dependency graph (text format):"
          npx nx graph --file=dependency-graph.json --quiet || echo "❌ Failed to generate dependency graph"
          if [ -f "dependency-graph.json" ]; then
            echo "✅ Dependency graph generated"
            cat dependency-graph.json | jq '.dependencies.web' || echo "No web dependencies found"
          fi



      - name: Debug paths (Preview)
        if: github.event.inputs.environment == 'preview' || (github.event_name == 'push' && github.ref == 'refs/heads/dev')
        run: |
          echo "Current working directory: $(pwd)"
          echo "Contents of current directory:"
          ls -la
          echo "Contents of learn-platform:"
          ls -la learn-platform/
          echo "Contents of learn-platform/apps/web:"
          ls -la learn-platform/apps/web/
          echo "Checking for package.json:"
          ls -la learn-platform/apps/web/package.json
          echo "Vercel CLI will run from: learn-platform (monorepo root)"

      - name: Pull Vercel Environment Information (Preview)
        if: github.event.inputs.environment == 'preview' || (github.event_name == 'push' && github.ref == 'refs/heads/dev')
        run: |
          echo "🔄 Pulling Vercel project configuration and environment variables..."

          # Clear any existing Vercel configuration and environment files for fresh pull
          if [[ "${{ github.event.inputs.force_rebuild }}" == "true" ]]; then
            echo "🚀 Cache-busting mode: Clearing existing Vercel configuration..."
            rm -rf .vercel/ || echo "No existing .vercel directory"
            rm -f .env.local || echo "No existing .env.local file"
            echo "✅ Cleared existing Vercel files for fresh pull"
          fi

          # Pull project configuration and environment variables with force flag if cache-busting
          # This creates .vercel/project.json and .vercel/README.txt
          if [[ "${{ github.event.inputs.force_rebuild }}" == "true" ]]; then
            echo "🚀 Force pulling Vercel project configuration..."
            vercel pull --yes --environment=preview --force --token=${{ secrets.VERCEL_TOKEN }}
          else
            echo "📦 Pulling Vercel project configuration..."
            vercel pull --yes --environment=preview --token=${{ secrets.VERCEL_TOKEN }}
          fi

          # Pull environment variables into .env.local for build process with force flag if cache-busting
          # This is critical for ensuring environment variables are available during build
          if [[ "${{ github.event.inputs.force_rebuild }}" == "true" ]]; then
            echo "🚀 Force pulling environment variables for preview environment..."
            vercel env pull .env.local --environment=preview --force --token=${{ secrets.VERCEL_TOKEN }}
          else
            echo "📥 Pulling environment variables for preview environment..."
            vercel env pull .env.local --environment=preview --token=${{ secrets.VERCEL_TOKEN }}
          fi

          # Verify the .env.local file was created successfully
          if [ -f ".env.local" ]; then
            echo "✅ Environment variables successfully pulled to .env.local"
            echo "📊 Environment file size: $(wc -c < .env.local) bytes"
            echo "📊 Environment variables count: $(grep -c '=' .env.local || echo '0')"

            if [[ "${{ github.event.inputs.force_rebuild }}" == "true" ]]; then
              echo "🚀 Cache-busting verification: Fresh environment variables loaded"
            fi
          else
            echo "❌ Failed to create .env.local file"
            echo "🔍 Checking current directory contents:"
            ls -la
            exit 1
          fi
        working-directory: ./learn-platform/apps/web
        env:
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID_WEB }}

      - name: Verify Environment Variables (Preview)
        if: github.event.inputs.environment == 'preview' || (github.event_name == 'push' && github.ref == 'refs/heads/dev')
        run: |
          echo "🔍 Checking if environment variables were pulled correctly..."
          echo "Contents of .vercel directory:"
          ls -la .vercel/ || echo "No .vercel directory found"
          echo "Environment files:"
          ls -la .env.local 2>/dev/null || echo "No .env.local file found"

          # Source the .env.local file to verify variables
          if [ -f ".env.local" ]; then
            echo "Sourcing .env.local for verification"
            set -a  # automatically export all variables
            source .env.local
            set +a  # stop automatically exporting
          fi

          echo ""
          echo "🔐 Environment Variables Status:"
          echo "BETTER_AUTH_URL is set: $([[ -n "$BETTER_AUTH_URL" ]] && echo "✅ Yes" || echo "❌ No")"
          echo "BETTER_AUTH_SECRET is set: $([[ -n "$BETTER_AUTH_SECRET" ]] && echo "✅ Yes" || echo "❌ No")"
          echo "BETTER_AUTH_TRUSTED_ORIGINS is set: $([[ -n "$BETTER_AUTH_TRUSTED_ORIGINS" ]] && echo "✅ Yes" || echo "❌ No")"
          echo "DATABASE_URL is set: $([[ -n "$DATABASE_URL" ]] && echo "✅ Yes" || echo "❌ No")"
          echo "NEXT_PUBLIC_API_URL is set: $([[ -n "$NEXT_PUBLIC_API_URL" ]] && echo "✅ Yes" || echo "❌ No")"

          echo ""
          echo "🌐 Better Auth Configuration Values (Preview):"
          echo "BETTER_AUTH_URL: ${BETTER_AUTH_URL:-'(not set)'}"
          echo "BETTER_AUTH_TRUSTED_ORIGINS: ${BETTER_AUTH_TRUSTED_ORIGINS:-'(not set)'}"
          echo "NEXT_PUBLIC_API_URL: ${NEXT_PUBLIC_API_URL:-'(not set)'}"

          echo ""
          echo "📊 Environment Variable Analysis:"
          if [[ -n "$BETTER_AUTH_TRUSTED_ORIGINS" ]]; then
            echo "Trusted origins count: $(echo "$BETTER_AUTH_TRUSTED_ORIGINS" | tr ',' '\n' | wc -l)"
            echo "Trusted origins list:"
            echo "$BETTER_AUTH_TRUSTED_ORIGINS" | tr ',' '\n' | nl

            # Check for duplicates
            UNIQUE_COUNT=$(echo "$BETTER_AUTH_TRUSTED_ORIGINS" | tr ',' '\n' | sort -u | wc -l)
            TOTAL_COUNT=$(echo "$BETTER_AUTH_TRUSTED_ORIGINS" | tr ',' '\n' | wc -l)
            if [[ $UNIQUE_COUNT -ne $TOTAL_COUNT ]]; then
              echo "⚠️  WARNING: Duplicate origins detected in BETTER_AUTH_TRUSTED_ORIGINS!"
              echo "Total origins: $TOTAL_COUNT, Unique origins: $UNIQUE_COUNT"
            else
              echo "✅ No duplicate origins detected"
            fi
          fi
        working-directory: ./learn-platform/apps/web

      - name: Build Project Artifacts (Preview)
        if: github.event.inputs.environment == 'preview' || (github.event_name == 'push' && github.ref == 'refs/heads/dev')
        run: |
          # Verify .env.local exists and show its contents (without secrets)
          if [ -f ".env.local" ]; then
            echo "✅ .env.local file found"
            echo "Environment variables count: $(grep -c '=' .env.local || echo '0')"

            # Source environment variables for the build process
            echo "🔄 Loading environment variables for build..."
            set -a  # automatically export all variables
            source .env.local
            set +a  # stop automatically exporting

            # Verify critical variables are loaded
            echo "🔍 Verifying environment variables are loaded:"
            echo "BETTER_AUTH_URL: ${BETTER_AUTH_URL:-'(not loaded)'}"
            echo "BETTER_AUTH_TRUSTED_ORIGINS: ${BETTER_AUTH_TRUSTED_ORIGINS:-'(not loaded)'}"
            echo "NEXT_PUBLIC_API_URL: ${NEXT_PUBLIC_API_URL:-'(not loaded)'}"
          else
            echo "❌ .env.local file not found - build may fail"
            exit 1
          fi

          # Build with environment variables explicitly loaded
          # The environment variables are now available in the shell environment
          echo "🏗️ Starting Vercel build with loaded environment variables..."

          # Export environment variables to ensure they're available to the build process
          export $(grep -v '^#' .env.local | xargs)

          # Verify exports worked
          echo "🔍 Final verification before build:"
          echo "BETTER_AUTH_URL in environment: ${BETTER_AUTH_URL:-'(not in environment)'}"
          echo "BETTER_AUTH_TRUSTED_ORIGINS in environment: ${BETTER_AUTH_TRUSTED_ORIGINS:-'(not in environment)'}"

          echo ""
          echo "🔍 Pre-build verification:"
          echo "Checking if auth library is available for import..."
          cd ../../  # Go to monorepo root
          echo "Current directory: $(pwd)"
          echo "Checking dist/libs/auth exists: $(ls -la dist/libs/auth/ 2>/dev/null && echo '✅ Yes' || echo '❌ No')"
          echo "Testing auth import resolution..."
          node -e "
            try {
              const path = require('path');
              const fs = require('fs');
              const authPath = path.resolve('./libs/auth/src/index.ts');
              console.log('Auth source path exists:', fs.existsSync(authPath) ? '✅ Yes' : '❌ No');
              const authDistPath = path.resolve('./dist/libs/auth/src/index.d.ts');
              console.log('Auth dist types exist:', fs.existsSync(authDistPath) ? '✅ Yes' : '❌ No');
            } catch (e) {
              console.log('❌ Node.js verification failed:', e.message);
            }
          " || echo "Node.js verification skipped"
          cd apps/web  # Return to web app directory

          # Run Vercel build with error handling and cache-busting if requested
          echo "🚀 Starting Vercel build..."
          if [[ "${{ github.event.inputs.force_rebuild }}" == "true" ]]; then
            echo "🚀 Cache-busting mode: Using --force flag to bypass build cache"
            vercel build --force --token=${{ secrets.VERCEL_TOKEN }}
          else
            echo "� Normal mode: Using standard build process"
            vercel build --token=${{ secrets.VERCEL_TOKEN }}
          fi

          # Check if build failed
          if [ $? -ne 0 ]; then
            echo ""
            echo "❌ Vercel build failed! Collecting debug information..."
            echo ""
            echo "📁 Current directory contents:"
            ls -la
            echo ""
            echo "📁 Monorepo structure:"
            ls -la ../../
            echo ""
            echo "📚 Libraries status:"
            ls -la ../../libs/ || echo "No libs directory"
            echo ""
            echo "🔐 Auth library status:"
            ls -la ../../libs/auth/ || echo "No auth library"
            echo ""
            echo "📦 Build outputs:"
            ls -la ../../dist/ || echo "No dist directory"
            echo ""
            echo "🔍 Next.js config:"
            cat next.config.js || echo "No next.config.js"
            echo ""
            echo "🔍 Package.json:"
            cat package.json || echo "No package.json"
            echo ""
            echo "❌ Build failed - exiting"
            exit 1
          fi
          echo "✅ Vercel build completed successfully!"
        working-directory: ./learn-platform/apps/web
        env:
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID_WEB }}

      - name: Debug build output (Preview)
        if: github.event.inputs.environment == 'preview' || (github.event_name == 'push' && github.ref == 'refs/heads/dev')
        run: |
          cd learn-platform
          echo "Current working directory: $(pwd)"
          echo "Contents of current directory:"
          ls -la
          echo "Contents of apps/web:"
          ls -la apps/web/
          echo "Contents of apps/web/src:"
          ls -la apps/web/src/
          echo "Checking for app directory:"
          ls -la apps/web/src/app/ 2>/dev/null || echo "src/app directory not found"
          echo "Looking for .next directory in apps/web:"
          find apps/web -name ".next" -type d 2>/dev/null || echo "No .next directory found"
          echo "Contents of apps/web/.next (if exists):"
          ls -la apps/web/.next/ 2>/dev/null || echo ".next directory not found"

      - name: Deploy Project Artifacts to Vercel (Preview)
        if: github.event.inputs.environment == 'preview' || (github.event_name == 'push' && github.ref == 'refs/heads/dev')
        run: vercel deploy --prebuilt --token=${{ secrets.VERCEL_TOKEN }}
        working-directory: ./learn-platform/apps/web
        env:
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID_WEB }}

      - name: Debug paths (Production)
        if: github.event.inputs.environment == 'production' || (github.event_name == 'push' && github.ref == 'refs/heads/main')
        run: |
          echo "Current working directory: $(pwd)"
          echo "Contents of current directory:"
          ls -la
          echo "Contents of learn-platform:"
          ls -la learn-platform/
          echo "Contents of learn-platform/apps/web:"
          ls -la learn-platform/apps/web/
          echo "Checking for package.json:"
          ls -la learn-platform/apps/web/package.json
          echo "Vercel CLI will run from: learn-platform (monorepo root)"

      - name: Debug - Verify Monorepo Structure (Production)
        if: github.event.inputs.environment == 'production' || (github.event_name == 'push' && github.ref == 'refs/heads/main')
        run: |
          echo "=== 🔍 Monorepo Structure Verification (Production) ==="
          echo "Current working directory: $(pwd)"
          echo ""
          echo "📁 Root directory contents:"
          ls -la learn-platform/
          echo ""
          echo "📚 Libs directory contents:"
          ls -la learn-platform/libs/ || echo "❌ libs directory not found"
          echo ""
          echo "🔐 Auth library structure:"
          ls -la learn-platform/libs/auth/ || echo "❌ libs/auth does not exist"
          echo ""
          echo "📄 Auth library files:"
          ls -la learn-platform/libs/auth/src/ || echo "❌ libs/auth/src does not exist"
          echo ""
          echo "🎯 Critical auth files:"
          ls -la learn-platform/libs/auth/src/auth.ts || echo "❌ auth.ts does not exist"
          ls -la learn-platform/libs/auth/src/index.ts || echo "❌ index.ts does not exist"



      - name: Pull Vercel Environment Information (Production)
        if: github.event.inputs.environment == 'production' || (github.event_name == 'push' && github.ref == 'refs/heads/main')
        run: |
          echo "🔄 Pulling Vercel project configuration and environment variables..."

          # Clear any existing Vercel configuration and environment files for fresh pull
          if [[ "${{ github.event.inputs.force_rebuild }}" == "true" ]]; then
            echo "🚀 Cache-busting mode: Clearing existing Vercel configuration..."
            rm -rf .vercel/ || echo "No existing .vercel directory"
            rm -f .env.local || echo "No existing .env.local file"
            echo "✅ Cleared existing Vercel files for fresh pull"
          fi

          # Pull project configuration and environment variables with force flag if cache-busting
          # This creates .vercel/project.json and .vercel/README.txt
          if [[ "${{ github.event.inputs.force_rebuild }}" == "true" ]]; then
            echo "🚀 Force pulling Vercel project configuration..."
            vercel pull --yes --environment=production --force --token=${{ secrets.VERCEL_TOKEN }}
          else
            echo "📦 Pulling Vercel project configuration..."
            vercel pull --yes --environment=production --token=${{ secrets.VERCEL_TOKEN }}
          fi

          # Pull environment variables into .env.local for build process with force flag if cache-busting
          # This is critical for ensuring environment variables are available during build
          if [[ "${{ github.event.inputs.force_rebuild }}" == "true" ]]; then
            echo "🚀 Force pulling environment variables for production environment..."
            vercel env pull .env.local --environment=production --force --token=${{ secrets.VERCEL_TOKEN }}
          else
            echo "📥 Pulling environment variables for production environment..."
            vercel env pull .env.local --environment=production --token=${{ secrets.VERCEL_TOKEN }}
          fi

          # Verify the .env.local file was created successfully
          if [ -f ".env.local" ]; then
            echo "✅ Environment variables successfully pulled to .env.local"
            echo "📊 Environment file size: $(wc -c < .env.local) bytes"
            echo "📊 Environment variables count: $(grep -c '=' .env.local || echo '0')"

            if [[ "${{ github.event.inputs.force_rebuild }}" == "true" ]]; then
              echo "🚀 Cache-busting verification: Fresh environment variables loaded"
            fi
          else
            echo "❌ Failed to create .env.local file"
            echo "🔍 Checking current directory contents:"
            ls -la
            exit 1
          fi
        working-directory: ./learn-platform/apps/web
        env:
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID_WEB }}

      - name: Verify Environment Variables (Production)
        if: github.event.inputs.environment == 'production' || (github.event_name == 'push' && github.ref == 'refs/heads/main')
        run: |
          echo "🔍 Checking if environment variables were pulled correctly..."
          echo "Contents of .vercel directory:"
          ls -la .vercel/ || echo "No .vercel directory found"
          echo "Environment files:"
          ls -la .env.local 2>/dev/null || echo "No .env.local file found"

          # Source the .env.local file to verify variables
          if [ -f ".env.local" ]; then
            echo "Sourcing .env.local for verification"
            set -a  # automatically export all variables
            source .env.local
            set +a  # stop automatically exporting
          fi

          echo ""
          echo "🔐 Environment Variables Status:"
          echo "BETTER_AUTH_URL is set: $([[ -n "$BETTER_AUTH_URL" ]] && echo "✅ Yes" || echo "❌ No")"
          echo "BETTER_AUTH_SECRET is set: $([[ -n "$BETTER_AUTH_SECRET" ]] && echo "✅ Yes" || echo "❌ No")"
          echo "BETTER_AUTH_TRUSTED_ORIGINS is set: $([[ -n "$BETTER_AUTH_TRUSTED_ORIGINS" ]] && echo "✅ Yes" || echo "❌ No")"
          echo "DATABASE_URL is set: $([[ -n "$DATABASE_URL" ]] && echo "✅ Yes" || echo "❌ No")"
          echo "NEXT_PUBLIC_API_URL is set: $([[ -n "$NEXT_PUBLIC_API_URL" ]] && echo "✅ Yes" || echo "❌ No")"

          echo ""
          echo "🌐 Better Auth Configuration Values (Production):"
          echo "BETTER_AUTH_URL: ${BETTER_AUTH_URL:-'(not set)'}"
          echo "BETTER_AUTH_TRUSTED_ORIGINS: ${BETTER_AUTH_TRUSTED_ORIGINS:-'(not set)'}"
          echo "NEXT_PUBLIC_API_URL: ${NEXT_PUBLIC_API_URL:-'(not set)'}"

          echo ""
          echo "📊 Environment Variable Analysis:"
          if [[ -n "$BETTER_AUTH_TRUSTED_ORIGINS" ]]; then
            echo "Trusted origins count: $(echo "$BETTER_AUTH_TRUSTED_ORIGINS" | tr ',' '\n' | wc -l)"
            echo "Trusted origins list:"
            echo "$BETTER_AUTH_TRUSTED_ORIGINS" | tr ',' '\n' | nl

            # Check for duplicates
            UNIQUE_COUNT=$(echo "$BETTER_AUTH_TRUSTED_ORIGINS" | tr ',' '\n' | sort -u | wc -l)
            TOTAL_COUNT=$(echo "$BETTER_AUTH_TRUSTED_ORIGINS" | tr ',' '\n' | wc -l)
            if [[ $UNIQUE_COUNT -ne $TOTAL_COUNT ]]; then
              echo "⚠️  WARNING: Duplicate origins detected in BETTER_AUTH_TRUSTED_ORIGINS!"
              echo "Total origins: $TOTAL_COUNT, Unique origins: $UNIQUE_COUNT"
              echo "Duplicates found:"
              echo "$BETTER_AUTH_TRUSTED_ORIGINS" | tr ',' '\n' | sort | uniq -d
            else
              echo "✅ No duplicate origins detected"
            fi
          fi

          echo ""
          echo "🎯 Expected Production Configuration:"
          echo "Expected BETTER_AUTH_URL: https://kwaci-learning.bmbn.dev"
          echo "Expected BETTER_AUTH_TRUSTED_ORIGINS should include: https://kwaci-learning.bmbn.dev"

          if [[ "$BETTER_AUTH_URL" == "https://kwaci-learning.bmbn.dev" ]]; then
            echo "✅ BETTER_AUTH_URL matches expected production URL"
          else
            echo "❌ BETTER_AUTH_URL does not match expected production URL"
          fi

          if [[ "$BETTER_AUTH_TRUSTED_ORIGINS" == *"kwaci-learning.bmbn.dev"* ]]; then
            echo "✅ BETTER_AUTH_TRUSTED_ORIGINS includes production domain"
          else
            echo "❌ BETTER_AUTH_TRUSTED_ORIGINS missing production domain"
          fi
        working-directory: ./learn-platform/apps/web

      - name: Build Project Artifacts (Production)
        if: github.event.inputs.environment == 'production' || (github.event_name == 'push' && github.ref == 'refs/heads/main')
        run: |
          # Verify .env.local exists and show its contents (without secrets)
          if [ -f ".env.local" ]; then
            echo "✅ .env.local file found"
            echo "Environment variables count: $(grep -c '=' .env.local || echo '0')"

            # Source environment variables for the build process
            echo "🔄 Loading environment variables for build..."
            set -a  # automatically export all variables
            source .env.local
            set +a  # stop automatically exporting

            # Verify critical variables are loaded
            echo "🔍 Verifying environment variables are loaded:"
            echo "BETTER_AUTH_URL: ${BETTER_AUTH_URL:-'(not loaded)'}"
            echo "BETTER_AUTH_TRUSTED_ORIGINS: ${BETTER_AUTH_TRUSTED_ORIGINS:-'(not loaded)'}"
            echo "NEXT_PUBLIC_API_URL: ${NEXT_PUBLIC_API_URL:-'(not loaded)'}"

            # Additional validation for production
            if [[ -z "$BETTER_AUTH_URL" ]]; then
              echo "❌ CRITICAL: BETTER_AUTH_URL not loaded - this will cause auth failures"
              exit 1
            fi

            if [[ -z "$BETTER_AUTH_TRUSTED_ORIGINS" ]]; then
              echo "❌ CRITICAL: BETTER_AUTH_TRUSTED_ORIGINS not loaded - this will cause 'Invalid origin' errors"
              exit 1
            fi

            if [[ "$BETTER_AUTH_URL" != "https://kwaci-learning.bmbn.dev" ]]; then
              echo "⚠️  WARNING: BETTER_AUTH_URL is not set to production URL: $BETTER_AUTH_URL"
            fi

            if [[ "$BETTER_AUTH_TRUSTED_ORIGINS" != *"kwaci-learning.bmbn.dev"* ]]; then
              echo "⚠️  WARNING: BETTER_AUTH_TRUSTED_ORIGINS does not include production domain: $BETTER_AUTH_TRUSTED_ORIGINS"
            fi
          else
            echo "❌ .env.local file not found - build will fail"
            exit 1
          fi

          # Build with environment variables explicitly loaded
          # The environment variables are now available in the shell environment
          echo "🏗️ Starting Vercel build with loaded environment variables..."

          # Export environment variables to ensure they're available to the build process
          export $(grep -v '^#' .env.local | xargs)

          # Verify exports worked
          echo "🔍 Final verification before build:"
          echo "BETTER_AUTH_URL in environment: ${BETTER_AUTH_URL:-'(not in environment)'}"
          echo "BETTER_AUTH_TRUSTED_ORIGINS in environment: ${BETTER_AUTH_TRUSTED_ORIGINS:-'(not in environment)'}"

          echo ""
          echo "🔍 Pre-build verification (Production):"
          echo "Checking if auth library is available for import..."
          cd ../../  # Go to monorepo root
          echo "Current directory: $(pwd)"
          echo "Checking dist/libs/auth exists: $(ls -la dist/libs/auth/ 2>/dev/null && echo '✅ Yes' || echo '❌ No')"
          echo "Testing auth import resolution..."
          node -e "
            try {
              const path = require('path');
              const fs = require('fs');
              const authPath = path.resolve('./libs/auth/src/index.ts');
              console.log('Auth source path exists:', fs.existsSync(authPath) ? '✅ Yes' : '❌ No');
              const authDistPath = path.resolve('./dist/libs/auth/src/index.d.ts');
              console.log('Auth dist types exist:', fs.existsSync(authDistPath) ? '✅ Yes' : '❌ No');
            } catch (e) {
              console.log('❌ Node.js verification failed:', e.message);
            }
          " || echo "Node.js verification skipped"
          cd apps/web  # Return to web app directory

          # Run Vercel build with error handling and cache-busting if requested
          echo "🚀 Starting Vercel production build..."
          if [[ "${{ github.event.inputs.force_rebuild }}" == "true" ]]; then
            echo "🚀 Cache-busting mode: Using --force flag to bypass build cache"
            vercel build --prod --force --token=${{ secrets.VERCEL_TOKEN }}
          else
            echo "📦 Normal mode: Using standard production build process"
            vercel build --prod --token=${{ secrets.VERCEL_TOKEN }}
          fi

          # Check if build failed
          if [ $? -ne 0 ]; then
            echo ""
            echo "❌ Vercel production build failed! Collecting debug information..."
            echo ""
            echo "📁 Current directory contents:"
            ls -la
            echo ""
            echo "📁 Monorepo structure:"
            ls -la ../../
            echo ""
            echo "📚 Libraries status:"
            ls -la ../../libs/ || echo "No libs directory"
            echo ""
            echo "🔐 Auth library status:"
            ls -la ../../libs/auth/ || echo "No auth library"
            echo ""
            echo "📦 Build outputs:"
            ls -la ../../dist/ || echo "No dist directory"
            echo ""
            echo "❌ Production build failed - exiting"
            exit 1
          fi
          echo "✅ Vercel production build completed successfully!"
        working-directory: ./learn-platform/apps/web
        env:
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID_WEB }}

      - name: Debug build output (Production)
        if: github.event.inputs.environment == 'production' || (github.event_name == 'push' && github.ref == 'refs/heads/main')
        run: |
          cd learn-platform
          echo "Current working directory: $(pwd)"
          echo "Contents of current directory:"
          ls -la
          echo "Looking for .next directory in apps/web:"
          find apps/web -name ".next" -type d 2>/dev/null || echo "No .next directory found"
          echo "Contents of apps/web/.next (if exists):"
          ls -la apps/web/.next/ 2>/dev/null || echo ".next directory not found"

      - name: Deploy Project Artifacts to Vercel (Production)
        if: github.event.inputs.environment == 'production' || (github.event_name == 'push' && github.ref == 'refs/heads/main')
        run: vercel deploy --prebuilt --prod --token=${{ secrets.VERCEL_TOKEN }}
        working-directory: ./learn-platform/apps/web
        env:
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID_WEB }}

      - name: Post-Deployment Verification
        if: success()
        run: |
          echo "🔍 Post-Deployment Verification"
          echo "Environment: ${{ github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'production' || 'preview') }}"
          echo "Commit: ${{ github.sha }}"

          # Determine the deployment URL
          if [[ "${{ github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'production' || 'preview') }}" == "production" ]]; then
            DEPLOYMENT_URL="https://kwaci-learning.bmbn.dev"
          else
            DEPLOYMENT_URL="https://preview-url.vercel.app"  # This would be the actual preview URL
          fi

          echo "Deployment URL: $DEPLOYMENT_URL"

          # Wait a moment for deployment to be fully ready
          echo "Waiting 30 seconds for deployment to be fully ready..."
          sleep 30

          # Test if the deployment is accessible
          echo "Testing deployment accessibility..."
          if curl -f -s -o /dev/null "$DEPLOYMENT_URL" --max-time 30; then
            echo "✅ Deployment is accessible"
          else
            echo "⚠️  Deployment may not be fully ready yet (this is normal for new deployments)"
          fi

          # Test auth endpoint if accessible
          echo "Testing auth configuration endpoint..."
          AUTH_TEST_URL="$DEPLOYMENT_URL/api/auth/session"
          if curl -f -s -o /dev/null "$AUTH_TEST_URL" --max-time 30; then
            echo "✅ Auth endpoint is accessible"
          else
            echo "ℹ️  Auth endpoint test skipped (may require authentication)"
          fi

      - name: Deployment Status
        if: success()
        run: |
          echo "✅ Web app deployment completed successfully!"
          echo "Environment: ${{ github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'production' || 'preview') }}"
          echo "Commit: ${{ github.sha }}"
          echo "Domain: https://kwaci-learning.bmbn.dev"
          echo ""
          echo "🔍 Deployment Configuration Summary:"
          echo "Force rebuild: ${{ github.event.inputs.force_rebuild || 'false' }}"
          echo "Bypass path filters: ${{ github.event.inputs.bypass_path_filters || 'false' }}"
          if [[ "${{ github.event.inputs.force_rebuild }}" == "true" ]]; then
            echo "🚀 Cache-busting was ACTIVE for this deployment:"
            echo "  ✓ All GitHub Actions caches used unique keys"
            echo "  ✓ Vercel build cache was bypassed with --force flag"
            echo "  ✓ Environment variables were force-refreshed from Vercel"
            echo "  ✓ All build artifacts were regenerated from scratch"
            echo ""
            echo "This ensures the deployment reflects the latest environment variables"
            echo "and configuration from the Vercel dashboard, even without code changes."
          else
            echo "📦 Standard deployment with normal caching behavior"
          fi

      - name: Deployment Failed
        if: failure()
        run: |
          echo "❌ Web app deployment failed!"
          echo "Please check the logs above for details."
          exit 1

  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: build-and-deploy
    if: always()
    
    steps:
      - name: Deployment Success Notification
        if: needs.build-and-deploy.result == 'success'
        run: |
          echo "🚀 Web app deployment to Vercel completed successfully!"
          echo "Branch: ${{ github.ref_name }}"
          echo "Commit: ${{ github.sha }}"
          echo "Environment: ${{ github.event.inputs.environment || (github.ref == 'refs/heads/main' && 'production' || 'preview') }}"
          echo "Domain: www.example.com"

      - name: Deployment Failure Notification
        if: needs.build-and-deploy.result == 'failure'
        run: |
          echo "💥 Web app deployment failed!"
          echo "Branch: ${{ github.ref_name }}"
          echo "Commit: ${{ github.sha }}"
          echo "Please check the workflow logs for details."
