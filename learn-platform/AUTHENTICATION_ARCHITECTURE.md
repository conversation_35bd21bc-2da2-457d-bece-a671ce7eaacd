# Authentication Architecture - Learn Platform

## Overview

This document describes the standardized authentication architecture for the Learn Platform Nx monorepo. The system uses **centralized authentication** through a Cloudflare Workers API service, ensuring consistent behavior across all applications.

## Architecture Summary

### Centralized Authentication Model

All applications (web and admin) use the **same authentication service** hosted on Cloudflare Workers:

- **Authentication Service**: `apps/api` (Cloudflare Workers)
- **Web Application**: `apps/web` (Next.js on Vercel)
- **Admin Application**: `apps/admin` (Next.js on Vercel)
- **Database**: PostgreSQL (Supabase)

### Key Benefits

✅ **Single Source of Truth**: All auth logic in one place  
✅ **Consistent Behavior**: Same authentication flow across apps  
✅ **Easier Maintenance**: Update auth logic once, affects all apps  
✅ **Better Security**: Centralized session management and CORS policies  
✅ **Scalability**: API service can be scaled independently  

## Authentication Flow Diagrams

### Current Standardized Architecture

```mermaid
graph TB
    subgraph "Frontend Applications"
        WEB[Web App<br/>localhost:3000]
        ADMIN[Admin App<br/>localhost:3001]
    end
    
    subgraph "Backend Services"
        API[API Service<br/>localhost:8787<br/>Cloudflare Workers]
        DB[(PostgreSQL<br/>Database)]
    end
    
    WEB -->|Auth Requests| API
    ADMIN -->|Auth Requests| API
    API -->|Database Operations| DB
    
    style API fill:#f9f,stroke:#333,stroke-width:3px
    style DB fill:#bbf,stroke:#333,stroke-width:2px
```

### Authentication Request Flow

```mermaid
sequenceDiagram
    participant U as User
    participant W as Web/Admin App
    participant A as API Service
    participant D as Database
    
    U->>W: Login Form Submit
    W->>A: POST /api/auth/sign-in/email
    A->>D: Validate Credentials
    D-->>A: User Data
    A->>D: Create Session
    D-->>A: Session Created
    A-->>W: Set Auth Cookies + User Data
    W-->>U: Redirect to Dashboard
    
    Note over W,A: All auth requests go through<br/>centralized API service
```

### Session Management

```mermaid
graph LR
    subgraph "Session Storage"
        DB[(Database<br/>Sessions Table)]
    end
    
    subgraph "Applications"
        WEB[Web App]
        ADMIN[Admin App]
    end
    
    subgraph "Auth Service"
        API[Cloudflare Workers<br/>API Service]
    end
    
    WEB -.->|Session Cookie| API
    ADMIN -.->|Session Cookie| API
    API <-->|Read/Write Sessions| DB
    
    style DB fill:#bbf,stroke:#333,stroke-width:2px
    style API fill:#f9f,stroke:#333,stroke-width:3px
```

## Development Setup

### Required Services

To run the complete authentication system locally, you need **3 services**:

1. **API Service** (Port 8787)
2. **Web Application** (Port 3000)  
3. **Admin Application** (Port 3001)

### Environment Configuration

#### API Service (`apps/api/.env.local`)
```bash
# Database
DATABASE_URL=postgresql://user:pass@host:port/db

# Authentication
BETTER_AUTH_SECRET=your-secret-key-min-32-chars
BETTER_AUTH_URL=http://localhost:8787
BETTER_AUTH_TRUSTED_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:8787
```

#### Web App (`apps/web/.env.local`)
```bash
# Database (for local API routes if needed)
DATABASE_URL=postgresql://user:pass@host:port/db

# Authentication
BETTER_AUTH_SECRET=your-secret-key-min-32-chars
BETTER_AUTH_URL=http://localhost:8787
BETTER_AUTH_TRUSTED_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:8787

# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8787
```

#### Admin App (`apps/admin/.env.local`)
```bash
# Database (for local API routes if needed)
DATABASE_URL=postgresql://user:pass@host:port/db

# Authentication
BETTER_AUTH_SECRET=your-secret-key-min-32-chars
BETTER_AUTH_URL=http://localhost:8787
BETTER_AUTH_TRUSTED_ORIGINS=http://localhost:3001,http://localhost:8787

# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8787
```

### Starting Development Servers

#### Option 1: Start All Services
```bash
# From monorepo root
./start-dev-servers.sh all
```

#### Option 2: Start Services Individually
```bash
# Terminal 1: API Service (start first)
bunx nx run api:dev

# Terminal 2: Web App
bunx nx run web:dev

# Terminal 3: Admin App  
bunx nx run admin:dev
```

### Service Dependencies

```mermaid
graph TD
    API[API Service<br/>Port 8787]
    WEB[Web App<br/>Port 3000]
    ADMIN[Admin App<br/>Port 3001]
    
    API -->|Required by| WEB
    API -->|Required by| ADMIN
    
    style API fill:#f9f,stroke:#333,stroke-width:3px
```

**Important**: The API service must be started **before** the frontend applications for authentication to work.

## Production Deployment

### Deployment Architecture

```mermaid
graph TB
    subgraph "Vercel"
        WEB_PROD[Web App<br/>kwaci-learning.bmbn.dev]
        ADMIN_PROD[Admin App<br/>admin.kwaci-learning.bmbn.dev]
    end
    
    subgraph "Cloudflare Workers"
        API_PROD[API Service<br/>learn-platform-api.workers.dev]
    end
    
    subgraph "Supabase"
        DB_PROD[(PostgreSQL<br/>Database)]
    end
    
    WEB_PROD -->|HTTPS| API_PROD
    ADMIN_PROD -->|HTTPS| API_PROD
    API_PROD -->|SSL| DB_PROD
    
    style API_PROD fill:#f9f,stroke:#333,stroke-width:3px
    style DB_PROD fill:#bbf,stroke:#333,stroke-width:2px
```

### Production Environment Variables

Applications in production use different base URLs but the same authentication flow:

- **Web App**: Points to production Cloudflare Workers API
- **Admin App**: Points to production Cloudflare Workers API  
- **API Service**: Uses production database and secrets

## Technical Implementation

### Authentication Libraries

The system uses the following key libraries:

- **better-auth**: Core authentication framework
- **@learn-platform/auth**: Centralized auth configuration
- **@learn-platform/db**: Database connection and schema
- **Drizzle ORM**: Database operations

### Auth Client Configuration

Both web and admin apps use identical auth client patterns:

```typescript
// apps/web/src/lib/auth-client.ts
// apps/admin/src/lib/auth-client.ts

function getBaseUrl() {
  // Always use centralized API URL
  if (typeof window !== 'undefined') {
    return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8787';
  }
  // ... SSR handling
}

export const authClient = createAuthClient({
  baseURL: getBaseUrl(),
});
```

### API Service Endpoints

The Cloudflare Workers API service provides these authentication endpoints:

- `POST /api/auth/sign-in/email` - Email/password login
- `POST /api/auth/sign-up/email` - User registration  
- `POST /api/auth/sign-out` - User logout
- `GET /api/auth/session` - Get current session
- `GET /api/auth/csrf` - CSRF token

### Database Schema

Authentication uses these database tables (managed by better-auth):

- `user` - User accounts
- `session` - Active sessions
- `account` - OAuth accounts (if enabled)
- `verification` - Email verification tokens

## Migration from Previous Architecture

### What Changed

**Before (Inconsistent)**:
- Web App → Cloudflare Workers API
- Admin App → Local Next.js API routes

**After (Standardized)**:
- Web App → Cloudflare Workers API
- Admin App → Cloudflare Workers API

### Migration Steps Completed

1. ✅ Updated admin auth client to use centralized API
2. ✅ Updated environment variables for consistency
3. ✅ Disabled local admin API routes (kept for fallback)
4. ✅ Documented new architecture

### Rollback Plan

If needed, you can revert to local admin authentication:

1. Uncomment routes in `apps/admin/src/app/api/auth/[...all]/route.ts`
2. Update `apps/admin/src/lib/auth-client.ts` to use relative URLs
3. Update `apps/admin/.env.local` BETTER_AUTH_URL to `http://localhost:3001`

## Verification Steps

### 1. Test Authentication Flow

#### Start All Services
```bash
# Start API service first
bunx nx run api:dev

# Start frontend apps
bunx nx run web:dev
bunx nx run admin:dev
```

#### Test Web App Authentication
1. Navigate to `http://localhost:3000/login`
2. Create a test account or login with existing credentials
3. Verify successful login and redirect to dashboard
4. Check browser network tab - auth requests should go to `localhost:8787`

#### Test Admin App Authentication
1. Navigate to `http://localhost:3001/login`
2. Login with the same credentials used in web app
3. Verify successful login and redirect to admin dashboard
4. Check browser network tab - auth requests should go to `localhost:8787`

#### Test Session Sharing
1. Login to web app (`localhost:3000`)
2. Open admin app (`localhost:3001`) in same browser
3. Admin app should automatically recognize the session
4. Both apps should show the same user information

### 2. Verify API Endpoints

Test the centralized API endpoints directly:

```bash
# Test CSRF endpoint
curl http://localhost:8787/api/auth/csrf

# Test session endpoint (after login)
curl -b "cookies.txt" http://localhost:8787/api/auth/session
```

### 3. Database Verification

Check that sessions are stored in the centralized database:

```sql
-- Connect to your PostgreSQL database
SELECT * FROM session WHERE user_id = 'your-user-id';
SELECT * FROM user WHERE email = 'your-test-email';
```

## Troubleshooting

### Common Issues

#### 1. "API Service Not Running"
**Symptoms**: Auth requests fail with connection errors
**Solution**:
```bash
# Ensure API service is running on port 8787
bunx nx run api:dev
# Check if port is available
lsof -i :8787
```

#### 2. "CORS/Trusted Origins Error"
**Symptoms**: Auth requests blocked by CORS policy
**Solution**: Verify trusted origins in environment variables include all app ports:
```bash
BETTER_AUTH_TRUSTED_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:8787
```

#### 3. "Session Not Shared Between Apps"
**Symptoms**: Login in one app doesn't work in the other
**Causes**:
- Different auth secrets
- Different database connections
- Cookie domain issues

**Solution**: Ensure all apps use the same:
```bash
BETTER_AUTH_SECRET=same-secret-for-all-apps
DATABASE_URL=same-database-for-all-apps
```

#### 4. "Admin App Still Using Local Routes"
**Symptoms**: Admin auth requests go to `localhost:3001` instead of `localhost:8787`
**Solution**:
1. Check `apps/admin/src/lib/auth-client.ts` uses API URL
2. Verify `NEXT_PUBLIC_API_URL=http://localhost:8787` in admin `.env.local`
3. Restart admin app after changes

#### 5. "Database Connection Issues"
**Symptoms**: Auth operations fail with database errors
**Solution**:
1. Verify `DATABASE_URL` is correct in all `.env.local` files
2. Test database connection manually
3. Check database permissions and network access

### Debug Mode

Enable debug logging for authentication:

```bash
# Add to .env.local files
DEBUG=better-auth:*
BETTER_AUTH_DEBUG=true
```

### Health Check Script

Create a simple health check:

```bash
#!/bin/bash
echo "🔍 Checking authentication services..."

# Check API service
if curl -s http://localhost:8787/api/auth/csrf > /dev/null; then
    echo "✅ API Service (8787) - Running"
else
    echo "❌ API Service (8787) - Not responding"
fi

# Check web app
if curl -s http://localhost:3000 > /dev/null; then
    echo "✅ Web App (3000) - Running"
else
    echo "❌ Web App (3000) - Not responding"
fi

# Check admin app
if curl -s http://localhost:3001 > /dev/null; then
    echo "✅ Admin App (3001) - Running"
else
    echo "❌ Admin App (3001) - Not responding"
fi
```

## Next Steps

### Recommended Enhancements

1. **Add Health Checks**: Implement `/health` endpoints for monitoring
2. **Enhanced Logging**: Add structured logging for auth operations
3. **Rate Limiting**: Implement rate limiting for auth endpoints
4. **Session Analytics**: Track session usage and patterns
5. **Multi-Factor Authentication**: Add 2FA support using better-auth plugins

### Monitoring

Consider adding monitoring for:
- Authentication success/failure rates
- Session duration and patterns
- API response times
- Database connection health

---

**Last Updated**: 2025-01-16
**Architecture Version**: 2.0 (Centralized)
**Maintained By**: Development Team
