# Nx Affected Testing Behavior - Complete Guide

## 🎯 **Understanding "No tasks were run"**

When you see `"No tasks were run"` from `bunx nx affected --target=test`, it means **no projects have been affected** by your recent changes. This is actually the **correct behavior** - Nx is being smart and only running tests when necessary!

## 🔍 **How Nx Determines "Affected" Projects**

### **1. Baseline Comparison**

Nx compares your current state against a **baseline** to determine what has changed:

```bash
# Default behavior (what our CI uses)
bunx nx affected --target=test
# Compares: current HEAD vs main branch

# Explicit baseline specification
bunx nx affected --target=test --base=main --head=HEAD
bunx nx affected --target=test --base=origin/main
bunx nx affected --target=test --base=HEAD~1  # Compare with previous commit
```

### **2. Default Baseline Logic**

In our monorepo, Nx uses this hierarchy to determine the baseline:

1. **`main` branch** (default for most repos)
2. **`master` branch** (fallback)
3. **Previous commit** (`HEAD~1`) if no main/master

### **3. What Triggers "Affected" Status**

A project becomes "affected" when:

#### **Direct Changes:**
- Files within the project directory are modified
- Project configuration files change (`project.json`, `package.json`)

#### **Dependency Changes:**
- Projects it depends on are modified
- Shared libraries it imports are changed
- Implicit dependencies are updated

#### **Global Changes:**
- Root configuration files (`nx.json`, `tsconfig.base.json`)
- Package dependencies (`package.json`, `bun.lock`)

## 📊 **Project Dependency Mapping**

Based on our monorepo structure, here's what triggers tests for each project:

### **`libs/auth` Tests Triggered By:**
```
✅ Changes to: libs/auth/**/*
✅ Changes to: libs/db/**/* (auth depends on db)
✅ Changes to: package.json (dependency updates)
✅ Changes to: nx.json, tsconfig.base.json (global config)
✅ Changes to: jest.config.ts, jest.preset.js (test config)
```

### **`libs/trpc` Tests Triggered By:**
```
✅ Changes to: libs/trpc/**/*
✅ Changes to: libs/auth/**/* (trpc depends on auth)
✅ Changes to: libs/db/**/* (indirect dependency via auth)
✅ Changes to: package.json, nx.json, tsconfig.base.json
```

### **`apps/api` Tests Triggered By:**
```
✅ Changes to: apps/api/**/*
✅ Changes to: libs/trpc/**/* (explicit dependency)
✅ Changes to: libs/auth/**/* (via trpc dependency)
✅ Changes to: libs/db/**/* (via auth dependency)
✅ Changes to: .github/workflows/deploy-api.yml (implicit dependency)
```

### **`apps/web` Tests Triggered By:**
```
✅ Changes to: apps/web/**/*
✅ Changes to: libs/shared/**/* (UI components)
✅ Changes to: libs/trpc/**/* (API client)
✅ Changes to: libs/auth/**/* (authentication)
✅ Changes to: .github/workflows/deploy-web.yml
```

## 🧪 **Testing the Affected Behavior**

### **Method 1: Make a Small Change**

```bash
# 1. Check current status (should show "No tasks were run")
bunx nx affected --target=test --dry-run

# 2. Make a small change to trigger auth tests
echo "// Test comment" >> libs/auth/src/auth.ts

# 3. Check affected projects
bunx nx affected --target=test --dry-run
# Should now show: auth, and any projects that depend on auth

# 4. Run the affected tests
bunx nx affected --target=test

# 5. Clean up
git checkout libs/auth/src/auth.ts
```

### **Method 2: Use Specific Base Comparison**

```bash
# Compare against previous commit (will likely show affected projects)
bunx nx affected --target=test --base=HEAD~1 --dry-run

# Compare against specific commit
bunx nx affected --target=test --base=abc123 --dry-run

# Force all tests to run
bunx nx run-many --target=test --all
```

### **Method 3: Check Project Dependencies**

```bash
# See project dependency graph
bunx nx graph

# Show projects that depend on auth
bunx nx show projects --affected --base=main

# Show what would be affected by auth changes
bunx nx affected:graph --base=main
```

## 🔧 **Debugging Affected Detection**

### **Check What Files Changed:**

```bash
# See what files Nx considers changed
bunx nx affected --target=test --base=main --head=HEAD --verbose

# Show affected projects with reasons
bunx nx show projects --affected --base=main --verbose
```

### **Common Scenarios:**

#### **Scenario 1: "No tasks were run" on clean main branch**
```bash
# ✅ Expected behavior - no changes since main
# Solution: Make a change or use different base
bunx nx affected --target=test --base=HEAD~1
```

#### **Scenario 2: Expected project not affected**
```bash
# Check if project has test target
bunx nx show projects --with-target=test

# Check project dependencies
bunx nx graph --focus=auth
```

#### **Scenario 3: Too many projects affected**
```bash
# Check what changed
git diff --name-only main...HEAD

# Use more specific base
bunx nx affected --target=test --base=HEAD~1
```

## 🚀 **Practical Commands for Development**

### **Daily Development:**
```bash
# Check what tests would run for your changes
bunx nx affected --target=test --dry-run

# Run affected tests (same as CI)
bunx nx affected --target=test --parallel --coverage

# Run specific library tests
bunx nx test auth
bunx nx test trpc
```

### **Before Committing:**
```bash
# Run all affected tests and linting
bunx nx affected --target=test,lint --parallel

# Check what will be affected in CI
bunx nx affected --target=test --base=origin/main --dry-run
```

### **Debugging Issues:**
```bash
# Force run all tests
bunx nx run-many --target=test --all

# Run tests with verbose output
bunx nx affected --target=test --verbose

# See dependency graph
bunx nx graph --focus=auth,trpc,api
```

## 📈 **CI/CD Behavior**

### **In GitHub Actions:**

Our workflows use:
```bash
bunx nx affected --target=test --parallel --coverage
```

This compares:
- **Base**: `origin/main` (the main branch on GitHub)
- **Head**: Current commit being tested
- **Result**: Only runs tests for projects affected by the PR/push

### **Why This Is Efficient:**

1. **Small Changes**: Only relevant tests run (fast feedback)
2. **Large Changes**: All affected tests run (comprehensive coverage)
3. **No Changes**: No tests run (saves CI resources)
4. **Dependency Awareness**: Automatically includes dependent projects

## 🎯 **Best Practices**

### **For Developers:**
1. **Use `--dry-run`** to preview what tests will run
2. **Test locally** with same command as CI: `bunx nx affected --target=test`
3. **Check dependencies** when unsure why tests aren't running
4. **Use specific base** when debugging: `--base=HEAD~1`

### **For CI/CD:**
1. **Always use `--parallel`** for performance
2. **Include `--coverage`** for quality metrics
3. **Use `fetch-depth: 0`** in checkout for full git history
4. **Set proper base branch** in complex workflows

---

## 🔍 **Quick Troubleshooting**

**Q: Why do I see "No tasks were run"?**  
A: No projects are affected by your changes since the baseline (usually main branch).

**Q: How do I force tests to run?**  
A: Use `bunx nx run-many --target=test --all` or change the base: `--base=HEAD~1`

**Q: Why didn't my change trigger expected tests?**  
A: Check project dependencies with `bunx nx graph` and verify the project has a test target.

**Q: How do I see what files changed?**  
A: Use `git diff --name-only main...HEAD` or `bunx nx affected --verbose`

This affected testing strategy ensures optimal performance while maintaining comprehensive test coverage! 🚀
