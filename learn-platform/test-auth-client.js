/**
 * Test script to check what endpoints the better-auth client calls
 */

import { createAuthClient } from 'better-auth/client';

const authClient = createAuthClient({
  baseURL: 'http://localhost:8787',
});

async function testAuthClient() {
  console.log('🔍 Testing better-auth client...\n');

  // Test getSession
  console.log('1. Testing authClient.getSession()...');
  try {
    const result = await authClient.getSession();
    console.log('✅ getSession result:', result);
  } catch (error) {
    console.log('❌ getSession failed:', error.message);
    console.log('   Error details:', error);
  }

  // Test sign-in with invalid credentials
  console.log('\n2. Testing authClient.signIn.email()...');
  try {
    const result = await authClient.signIn.email({
      email: '<EMAIL>',
      password: 'wrongpassword'
    });
    console.log('✅ signIn result:', result);
  } catch (error) {
    console.log('❌ signIn failed:', error.message);
    console.log('   Error details:', error);
  }

  console.log('\n🔍 Test complete!');
}

testAuthClient().catch(console.error);
