{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "jsx": "react-jsx", "strict": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@learn-platform/ai": ["libs/ai/src/index.ts"], "@learn-platform/auth": ["libs/auth/src/index.ts"], "@learn-platform/auth-debug": ["libs/auth-debug/src/index.ts"], "@learn-platform/db": ["libs/db/src/index.ts"], "@learn-platform/releases": ["libs/releases/src/index.ts"], "@learn-platform/shared-styles": ["libs/shared/styles/src/index.ts"], "@learn-platform/shared-ui": ["libs/shared/ui/src/index.ts"], "@learn-platform/shared-utils": ["libs/shared/utils/src/index.ts"], "@learn-platform/theme": ["libs/theme/src/index.ts"], "@learn-platform/trpc": ["libs/trpc/src/index.ts"], "@learn-platform/version": ["libs/version/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}