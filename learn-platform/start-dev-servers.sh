#!/bin/bash

# Learning Platform Development Server Manager
# Enhanced script for managing development servers in Nx monorepo
# Supports individual apps or all apps with proper process management

# Note: Don't use set -e here as we want to handle errors gracefully

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Global variables for process management
declare -a PIDS=()
declare -a APP_NAMES=()

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to check if a port is in use
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_status $YELLOW "⚠️  Port $port is already in use"
        return 1
    else
        return 0
    fi
}

# Function to cleanup processes on exit
cleanup() {
    print_status $YELLOW "\n🛑 Shutting down development servers..."

    for i in "${!PIDS[@]}"; do
        local pid=${PIDS[$i]}
        local app_name=${APP_NAMES[$i]}

        if kill -0 $pid 2>/dev/null; then
            print_status $CYAN "   Stopping $app_name (PID: $pid)..."
            kill -TERM $pid 2>/dev/null || true

            # Wait for graceful shutdown
            local count=0
            while kill -0 $pid 2>/dev/null && [ $count -lt 10 ]; do
                sleep 1
                ((count++))
            done

            # Force kill if still running
            if kill -0 $pid 2>/dev/null; then
                print_status $RED "   Force killing $app_name..."
                kill -KILL $pid 2>/dev/null || true
            fi
        fi
    done

    print_status $GREEN "✅ All servers stopped"
    exit 0
}

# Set up signal handlers for graceful shutdown
trap cleanup SIGINT SIGTERM EXIT

# Function to start an application using Nx
start_app() {
    local app_name=$1
    local port=$2
    local description=$3

    print_status $BLUE "\n📱 Starting $description..."

    # Check if port is available
    if ! check_port $port; then
        print_status $RED "   Please stop the process using port $port first"
        return 1
    fi

    # Start the application using Nx
    print_status $CYAN "   Running: npx nx run $app_name:dev"
    npx nx run $app_name:dev &
    local pid=$!

    # Store PID and app name for cleanup
    PIDS+=($pid)
    APP_NAMES+=($app_name)

    # Wait a moment to check if the process started successfully
    sleep 2
    if ! kill -0 $pid 2>/dev/null; then
        print_status $RED "   ❌ Failed to start $app_name"
        return 1
    fi

    print_status $GREEN "   ✅ Started $description (PID: $pid)"
    print_status $CYAN "   🌐 Available at: http://localhost:$port"

    return 0
}

# Function to show usage
show_usage() {
    print_status $CYAN "📖 Learning Platform Development Server Manager"
    echo ""
    print_status $BLUE "Usage:"
    echo "  $0 [app_name]"
    echo ""
    print_status $BLUE "Available apps:"
    echo "  web     - Next.js frontend (port 3000)"
    echo "  admin   - Next.js admin dashboard (port 4000)"
    echo "  api     - Cloudflare Workers API (port 8787)"
    echo "  all     - Start all applications (default)"
    echo ""
    print_status $BLUE "Examples:"
    echo "  $0           # Start all apps"
    echo "  $0 all       # Start all apps"
    echo "  $0 web       # Start only web app"
    echo "  $0 admin     # Start only admin app"
    echo "  $0 api       # Start only API server"
    echo ""
}

# Function to verify environment
verify_environment() {
    print_status $BLUE "🔍 Verifying environment..."

    # Verify we're in the right directory
    if [ ! -f "nx.json" ] || [ ! -f "package.json" ]; then
        print_status $RED "❌ Please run this script from the monorepo root directory"
        exit 1
    fi

    # Verify apps exist
    if [ ! -d "apps/web" ] || [ ! -d "apps/admin" ] || [ ! -d "apps/api" ]; then
        print_status $RED "❌ Apps directory structure not found"
        exit 1
    fi

    # Check if Nx is available
    if ! command -v npx >/dev/null 2>&1; then
        print_status $RED "❌ npx not found. Please install Node.js and npm"
        exit 1
    fi

    # Verify environment variables are set up
    if [ ! -f ".env.local" ]; then
        print_status $YELLOW "⚠️  Root .env.local not found. Some features may not work."
    fi

    print_status $GREEN "✅ Environment verified"
}

# Main execution
print_status $PURPLE "🚀 Learning Platform Development Server Manager"
print_status $PURPLE "=============================================="

# Parse command line arguments
APP_TO_START=${1:-"all"}

case $APP_TO_START in
    "help"|"-h"|"--help")
        show_usage
        exit 0
        ;;
    "web"|"admin"|"api"|"all")
        # Valid options, continue
        ;;
    *)
        print_status $RED "❌ Invalid app name: $APP_TO_START"
        show_usage
        exit 1
        ;;
esac

verify_environment

# Start the requested application(s)
print_status $BLUE "\n🚀 Starting development servers..."

case $APP_TO_START in
    "web")
        start_app "web" 3000 "Web Frontend (Next.js)"
        ;;
    "admin")
        start_app "admin" 4000 "Admin Dashboard (Next.js)"
        ;;
    "api")
        start_app "api" 8787 "API Server (Cloudflare Workers)"
        ;;
    "all")
        print_status $CYAN "Starting all applications..."

        # Start API first (other apps depend on it)
        start_app "api" 8787 "API Server (Cloudflare Workers)"
        sleep 3

        # Start frontend apps
        start_app "web" 3000 "Web Frontend (Next.js)"
        sleep 2
        start_app "admin" 4000 "Admin Dashboard (Next.js)"
        ;;
esac

# Check if any servers were started successfully
if [ ${#PIDS[@]} -eq 0 ]; then
    print_status $RED "\n❌ No servers were started successfully"
    print_status $YELLOW "🔧 Try running individual apps manually:"
    print_status $CYAN "   npx nx run web:dev"
    print_status $CYAN "   npx nx run admin:dev"
    print_status $CYAN "   npx nx run api:dev"
    exit 1
fi

# Show success information
print_status $GREEN "\n🎉 Development server(s) started successfully!"
print_status $BLUE "\n📋 Available Services:"

for i in "${!PIDS[@]}"; do
    local app_name=${APP_NAMES[$i]}
    case $app_name in
        "web")
            print_status $CYAN "   🌐 Web Frontend: http://localhost:3000"
            ;;
        "admin")
            print_status $CYAN "   👨‍💼 Admin Dashboard: http://localhost:4000"
            ;;
        "api")
            print_status $CYAN "   🔌 API Server: http://localhost:8787"
            print_status $CYAN "   📡 tRPC Endpoint: http://localhost:8787/trpc"
            ;;
    esac
done

print_status $BLUE "\n📋 Development Tips:"
print_status $CYAN "   • Environment variables are loaded from .env.local files"
print_status $CYAN "   • Hot reload is enabled for all applications"
print_status $CYAN "   • API changes require manual restart"
print_status $CYAN "   • Check browser console for any errors"

print_status $BLUE "\n🔐 Authentication Architecture:"
print_status $CYAN "   • Both web and admin apps use centralized API authentication"
print_status $CYAN "   • All auth requests go to localhost:8787 (API service)"
print_status $CYAN "   • Sessions are shared between applications"
print_status $CYAN "   • Run './scripts/verify-auth-setup.sh' to verify setup"

print_status $YELLOW "\n🛑 Press Ctrl+C to stop all servers"

# Keep the script running and wait for all processes
print_status $BLUE "\n⏳ Monitoring development servers..."
wait
