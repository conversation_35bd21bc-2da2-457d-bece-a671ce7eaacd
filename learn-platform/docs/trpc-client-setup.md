# tRPC Client Setup Documentation

This document explains how tRPC client integration is configured in both `apps/web` and `apps/admin` applications.

## Overview

The tRPC client integration provides type-safe API communication between the frontend applications and the Cloudflare Worker API. It uses React Query for caching, background updates, and optimistic updates.

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   apps/web      │    │   apps/admin    │    │   apps/api      │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ tRPC Client │ │    │ │ tRPC Client │ │    │ │ tRPC Server │ │
│ │ + React     │◄┼────┼►│ + React     │◄┼────┼►│ + Hono      │ │
│ │ Query       │ │    │ │ Query       │ │    │ │ + CF Worker │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## File Structure

### apps/web
```
src/
├── lib/
│   ├── trpc.ts          # tRPC client configuration
│   └── providers.tsx    # React providers setup
├── components/
│   └── example-trpc-usage.tsx  # Usage examples
└── app/
    └── layout.tsx       # Root layout with providers
```

### apps/admin
```
src/
├── lib/
│   ├── trpc.ts          # tRPC client configuration
│   └── providers.tsx    # React providers setup
├── components/
│   └── example-trpc-usage.tsx  # Usage examples
└── app/
    └── layout.tsx       # Root layout with providers
```

## Configuration

### Environment Variables

Create `.env.local` files in both apps with:

```bash
# For development
NEXT_PUBLIC_API_URL=http://localhost:8787

# For production
NEXT_PUBLIC_API_URL=https://your-api-domain.com
```

### Base URL Resolution

The client automatically resolves the API base URL:

1. **Browser**: Uses relative URLs (empty string)
2. **Development SSR**: Uses `NEXT_PUBLIC_API_URL` or defaults to `http://localhost:8787`
3. **Production SSR**: Uses `NEXT_PUBLIC_API_URL` or Vercel URL

## Usage Examples

### Basic Query

```typescript
import { api } from '../lib/trpc';

function MyComponent() {
  const { data, isLoading, error } = api.health.useQuery();
  
  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  
  return <div>Status: {data.status}</div>;
}
```

### Query with Input

```typescript
const { data } = api.greeting.useQuery({ name: 'John' });
```

### Mutation

```typescript
const mutation = api.createUser.useMutation({
  onSuccess: (data) => {
    console.log('User created:', data);
  },
  onError: (error) => {
    console.error('Error:', error);
  },
});

// Trigger mutation
mutation.mutate({ name: 'John', email: '<EMAIL>' });
```

### Server-Side Usage

```typescript
import { trpcClient } from '../lib/trpc';

// In a server component or API route
const data = await trpcClient.health.query();
```

## Features

### Type Safety
- Full TypeScript inference from the centralized `AppRouter`
- Compile-time error checking for API calls
- Auto-completion for available endpoints

### React Query Integration
- Automatic caching and background refetching
- Optimistic updates support
- Error handling and retry logic
- Loading states management

### Error Handling
- Automatic retry for network errors
- No retry for 4xx client errors
- Proper error formatting and display

### Performance
- Request batching for multiple queries
- Stale-while-revalidate caching strategy
- Background updates without blocking UI

## Development Workflow

1. **Start the API server**: `bun run dev` in `apps/api`
2. **Start the web app**: `bun run dev` in `apps/web`
3. **Start the admin app**: `bun run dev` in `apps/admin`

The clients will automatically connect to the local API server.

## Production Deployment

1. Deploy the API to Cloudflare Workers
2. Set `NEXT_PUBLIC_API_URL` to the deployed API domain
3. Deploy the web and admin apps to your hosting platform

## Troubleshooting

### Common Issues

1. **CORS Errors**: Ensure the API CORS configuration includes your app domains
2. **Type Errors**: Make sure `@learn-platform/trpc` is properly built and exported
3. **Connection Errors**: Verify the API server is running and accessible

### Debug Mode

Enable React Query DevTools by uncommenting the code in `providers.tsx`:

```typescript
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

// Add to your provider
<ReactQueryDevtools initialIsOpen={false} position="bottom-right" />
```
