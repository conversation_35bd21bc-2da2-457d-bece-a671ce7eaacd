# Quiz Generation Feature

## Overview

The Quiz Generation feature allows users to create personalized quizzes from their learning content using AI. The system generates questions exclusively from the provided content to prevent hallucination and ensure accuracy.

## Features

### Quiz Types Supported

1. **Flashcards** - Term and definition cards for memorization
2. **Multiple Choice** - Questions with 4 options, one correct answer
3. **True or False** - Statements to evaluate as true or false
4. **Fill in the Blank** - Complete sentences with missing words
5. **Matching Pairs** - Match terms with definitions or descriptions
6. **Free Text Answer** - Short or long-form written responses
7. **Ordering/Sequencing** - Arrange items in the correct order

### Key Capabilities

- **Content-Based Generation**: Questions are generated exclusively from learning content
- **Anti-Hallucination**: Strict validation ensures no invented facts or external knowledge
- **Multiple Difficulty Levels**: Easy, Medium, and Hard questions
- **Customizable Configuration**: Choose question types, count, and settings
- **Progress Tracking**: Track quiz attempts, scores, and performance over time
- **Review Mode**: Review answers with explanations and feedback
- **Retake Support**: Allow or restrict quiz retakes based on configuration

## Architecture

### Components

```
apps/web/src/lib/components/quiz/
├── QuizContainer.tsx           # Main quiz interface
├── QuizSelectionModal.tsx      # Quiz generation configuration
├── QuizHistorySidebar.tsx      # Quiz history and progress
├── QuizProgressBar.tsx         # Progress tracking
├── QuizNavigation.tsx          # Question navigation
├── QuizResults.tsx             # Results display
├── QuizFeedbackModal.tsx       # User feedback collection
├── QuestionRenderer.tsx        # Question type router
├── questions/                  # Individual question components
│   ├── FlashcardQuestion.tsx
│   ├── MultipleChoiceQuestion.tsx
│   ├── TrueFalseQuestion.tsx
│   ├── FillInBlankQuestion.tsx
│   ├── MatchingQuestion.tsx
│   ├── FreeTextQuestion.tsx
│   └── OrderingQuestion.tsx
└── types.ts                    # TypeScript interfaces
```

### Backend Services

```
libs/ai/src/
├── schemas/quiz-generation.ts  # Zod schemas and types
├── prompts/quiz-generation.ts  # AI prompts for generation
├── services/
│   ├── quiz-generator.ts       # Main generation service
│   ├── quiz-evaluator.ts       # Answer evaluation
│   └── content-validator.ts    # Anti-hallucination validation
```

### Database Schema

```
libs/db/src/schema/quiz.ts
├── quiz                        # Quiz metadata and questions
├── quizAttempt                 # User quiz attempts
├── quizProgress                # Progress tracking for resumable quizzes
├── quizAnalytics               # Aggregated performance data
└── quizFeedback                # User feedback on quiz quality
```

### API Endpoints

```
libs/trpc/src/procedures/quiz.ts
├── generate                    # Generate new quiz from content
├── getAll                      # List quizzes with filtering
├── getById                     # Get specific quiz with questions
├── startAttempt                # Begin quiz attempt
├── submitAnswer                # Submit individual answers
├── complete                    # Complete quiz and calculate score
├── submitFeedback              # Submit quiz feedback
└── getMyAttempts               # Get user's quiz history
```

## Usage

### Generating a Quiz

1. Navigate to a learning content page (`/dashboard/learn/[id]`)
2. Click "Generate Quiz" in the Quick Actions sidebar
3. Configure quiz settings:
   - Select question types
   - Choose difficulty level
   - Set questions per type
   - Configure additional options
4. Click "Generate Quiz" to create the quiz

### Taking a Quiz

1. Start a quiz from the generation modal or quiz history
2. Navigate through questions using the navigation controls
3. Submit answers for each question
4. Complete the quiz to see results
5. Review answers and explanations
6. Provide feedback (optional)

### Quiz Management

- **History**: View past quiz attempts in the sidebar
- **Retakes**: Retake quizzes if allowed by configuration
- **Progress**: Resume incomplete quizzes
- **Analytics**: Track performance over time

## Configuration

### Quiz Generation Options

```typescript
interface QuizGenerationConfig {
  learningContentId: string;
  quizTypes: QuizType[];
  difficulty: 'easy' | 'medium' | 'hard';
  questionsPerType: number;
  includeHints: boolean;
  includeExplanations: boolean;
  timeLimit?: number;
  shuffleQuestions: boolean;
}
```

### Question Type Configuration

Each question type supports specific configuration options:

- **Flashcards**: Front/back content, optional hints
- **Multiple Choice**: 4 options, explanations
- **True/False**: Statements with explanations
- **Fill in Blank**: Multiple blanks, case sensitivity
- **Matching**: 3-8 pairs, custom instructions
- **Free Text**: Short/long form, evaluation criteria
- **Ordering**: 3-8 items, order type (chronological, logical, etc.)

## Anti-Hallucination System

### Content Validation

The system implements multiple layers of validation to prevent AI hallucination:

1. **Source-Only Generation**: Questions generated exclusively from provided content
2. **Content Matching**: Verify question content matches source material
3. **Answer Validation**: Ensure answers are supported by source content
4. **Confidence Scoring**: AI confidence assessment for each question
5. **Manual Review**: Flag low-confidence questions for review

### Validation Process

```typescript
// Content validation pipeline
const validation = await validateQuizContent(sourceContent, question, {
  strictMode: true,
  minConfidence: 0.7,
  checkExternalKnowledge: true,
  validateAnswers: true,
});

if (!validation.isValid) {
  // Reject question or request regeneration
}
```

### Quality Metrics

- **Content Match**: Similarity between question and source content
- **Hallucination Detection**: Check for external facts or invented details
- **Answer Accuracy**: Verify answers are derivable from source
- **Clarity Assessment**: Ensure questions are unambiguous

## Performance Considerations

### Generation Time

- **Typical Generation**: 30-60 seconds for 10-20 questions
- **Validation Overhead**: Additional 10-20 seconds for content validation
- **Caching**: Generated quizzes are cached for reuse

### Scalability

- **Concurrent Generation**: Support for multiple simultaneous generations
- **Rate Limiting**: Prevent abuse of AI generation services
- **Resource Management**: Efficient memory usage for large content

## Testing

### Test Coverage

- **Unit Tests**: Individual component and service testing
- **Integration Tests**: API endpoint and database testing
- **Validation Tests**: Anti-hallucination system testing
- **E2E Tests**: Complete user workflow testing

### Test Files

```
__tests__/
├── QuizContainer.test.tsx
├── quiz-generator.test.ts
├── content-validator.test.ts
├── quiz.test.ts (tRPC procedures)
└── quiz-integration.test.ts
```

## Security

### Data Protection

- **User Privacy**: Quiz attempts and answers are user-specific
- **Content Security**: Source content validation prevents injection
- **Access Control**: Quiz access based on content permissions

### Input Validation

- **Schema Validation**: All inputs validated with Zod schemas
- **Content Sanitization**: User inputs sanitized before processing
- **Rate Limiting**: Prevent abuse of generation endpoints

## Monitoring

### Analytics

- **Generation Metrics**: Track quiz generation success rates
- **Performance Metrics**: Monitor generation and validation times
- **Quality Metrics**: Track validation confidence scores
- **User Engagement**: Monitor quiz completion rates and scores

### Error Handling

- **Graceful Degradation**: Fallback options when AI services fail
- **Error Logging**: Comprehensive error tracking and reporting
- **User Feedback**: Clear error messages and recovery options

## Future Enhancements

### Planned Features

1. **Adaptive Difficulty**: Adjust question difficulty based on performance
2. **Collaborative Quizzes**: Share quizzes with other users
3. **Advanced Analytics**: Detailed performance insights and recommendations
4. **Question Banks**: Reusable question libraries
5. **Integration**: Export quizzes to external platforms

### Technical Improvements

1. **Performance Optimization**: Faster generation and validation
2. **Enhanced Validation**: More sophisticated anti-hallucination detection
3. **Mobile Support**: Optimized mobile quiz experience
4. **Offline Mode**: Support for offline quiz taking
5. **Accessibility**: Enhanced accessibility features

## Troubleshooting

### Common Issues

1. **Generation Failures**: Check content length and AI service availability
2. **Validation Errors**: Review source content quality and specificity
3. **Performance Issues**: Monitor AI service response times
4. **User Experience**: Ensure proper error handling and feedback

### Debug Tools

- **Validation Logs**: Detailed validation results and confidence scores
- **Generation Metrics**: Track generation success and failure rates
- **Performance Monitoring**: Monitor response times and resource usage
