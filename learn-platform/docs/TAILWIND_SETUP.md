# Tailwind CSS Setup in Nx Monorepo

This document describes the centralized Tailwind CSS configuration for the Nx monorepo, including shared design tokens, custom utilities, and best practices.

## 📁 Project Structure

```
learn-platform/
├── tailwind.config.base.js          # Base Tailwind configuration
├── libs/
│   └── shared/
│       └── styles/
│           ├── src/
│           │   ├── globals.css       # Main Tailwind styles
│           │   └── index.ts          # TypeScript exports
│           └── project.json          # Nx project configuration
├── demo-app/
│   ├── tailwind.config.js           # App-specific config (extends base)
│   ├── postcss.config.js            # PostCSS configuration
│   └── src/app/
│       └── global.css               # Imports shared styles
└── docs/
    └── TAILWIND_SETUP.md            # This documentation
```

## 🎨 Design System

### Colors

The design system includes semantic color palettes:

- **Brand Colors**: Primary brand colors (`brand-50` to `brand-950`)
- **Success Colors**: Green palette for success states
- **Warning Colors**: Yellow/orange palette for warnings
- **Error Colors**: Red palette for error states

### Typography

- **Font Family**: Inter for sans-serif, JetBrains Mono for monospace
- **Font Sizes**: Extended scale from `xs` to `9xl` with line heights
- **Font Weights**: Standard weights with semantic naming

### Spacing & Layout

- **Custom Spacing**: Additional spacing values (`18`, `88`, `128`)
- **Border Radius**: Extended radius including `4xl`
- **Container Classes**: `container-narrow` and `container-wide`

## 🧩 Component Classes

### Buttons

```css
.btn              /* Base button styles */
.btn-primary      /* Primary brand button */
.btn-secondary    /* Secondary gray button */
.btn-success      /* Success green button */
.btn-warning      /* Warning yellow button */
.btn-error        /* Error red button */
.btn-outline      /* Outlined button */
.btn-ghost        /* Transparent button */
```

### Form Elements

```css
.input            /* Base input styles */
.input-error      /* Error state input */
```

### Cards

```css
.card             /* Base card container */
.card-header      /* Card header section */
.card-body        /* Card body content */
.card-footer      /* Card footer section */
```

### Badges

```css
.badge            /* Base badge styles */
.badge-primary    /* Primary brand badge */
.badge-success    /* Success badge */
.badge-warning    /* Warning badge */
.badge-error      /* Error badge */
.badge-gray       /* Gray neutral badge */
```

## 🎭 Custom Utilities

### Animations

```css
.animate-fade-in     /* Fade in animation */
.animate-slide-up    /* Slide up animation */
.animate-slide-down  /* Slide down animation */
```

### Gradients

```css
.gradient-brand      /* Brand color gradient */
.gradient-success    /* Success color gradient */
.gradient-warning    /* Warning color gradient */
.gradient-error      /* Error color gradient */
```

### Text Utilities

```css
.text-balance        /* Balanced text wrapping */
.text-pretty         /* Pretty text wrapping */
```

## 🚀 Adding New Applications

To add Tailwind CSS to a new application:

1. **Create Tailwind Config**: Create `tailwind.config.js` in your app directory:

```javascript
const { createGlobPatternsForDependencies } = require('@nx/react/tailwind');
const { join } = require('path');
const baseConfig = require('../tailwind.config.base');

module.exports = {
  ...baseConfig,
  content: [
    join(__dirname, '{src,pages,components,app}/**/*!(*.stories|*.spec).{ts,tsx,html}'),
    join(__dirname, '../libs/shared/ui/src/**/*!(*.stories|*.spec).{ts,tsx,html}'),
    ...createGlobPatternsForDependencies(__dirname),
  ],
  theme: {
    ...baseConfig.theme,
    extend: {
      ...baseConfig.theme.extend,
      // App-specific theme extensions
    },
  },
};
```

2. **Create PostCSS Config**: Create `postcss.config.js`:

```javascript
const { join } = require('path');

module.exports = {
  plugins: {
    tailwindcss: {
      config: join(__dirname, 'tailwind.config.js'),
    },
    autoprefixer: {},
  },
};
```

3. **Import Shared Styles**: In your main CSS file:

```css
@import '../../../libs/shared/styles/src/globals.css';
```

## 🎯 Best Practices

### 1. Use Shared Design Tokens

Always use the predefined color palette and spacing values:

```tsx
// ✅ Good
<div className="bg-brand-500 text-white p-4 rounded-lg">

// ❌ Avoid
<div className="bg-blue-500 text-white p-4 rounded-lg">
```

### 2. Leverage Component Classes

Use the predefined component classes for consistency:

```tsx
// ✅ Good
<button className="btn-primary">Click me</button>

// ❌ Avoid
<button className="bg-blue-500 text-white px-4 py-2 rounded">Click me</button>
```

### 3. TypeScript Integration

Import design tokens for programmatic use:

```tsx
import { colors, buttonClasses } from '@learn-platform/shared-styles';

const MyComponent = () => (
  <button 
    className={buttonClasses.primary}
    style={{ backgroundColor: colors.brand[600] }}
  >
    Styled Button
  </button>
);
```

### 4. App-Specific Customizations

Override or extend the base configuration in individual apps:

```javascript
// In app-specific tailwind.config.js
module.exports = {
  ...baseConfig,
  theme: {
    ...baseConfig.theme,
    extend: {
      ...baseConfig.theme.extend,
      colors: {
        ...baseConfig.theme.extend.colors,
        'app-specific': '#custom-color',
      },
    },
  },
};
```

## 🔧 Development Commands

```bash
# Run development server
npx nx dev demo-app

# Build application
npx nx build demo-app

# Build shared styles library
npx nx build shared-styles

# Lint Tailwind classes
npx nx lint demo-app
```

## 📦 Dependencies

The following packages are installed and configured:

- `tailwindcss` - Core Tailwind CSS framework
- `postcss` - CSS post-processor
- `autoprefixer` - CSS vendor prefixing
- `@tailwindcss/typography` - Typography plugin
- `@tailwindcss/forms` - Form styling plugin
- `@tailwindcss/aspect-ratio` - Aspect ratio utilities

## 🎨 Customization Guide

### Adding New Colors

1. Update `tailwind.config.base.js`:

```javascript
colors: {
  // Add new color palette
  'custom': {
    50: '#...',
    // ... rest of the palette
  },
}
```

2. Update `libs/shared/styles/src/index.ts`:

```typescript
export const colors = {
  // Add TypeScript constants
  custom: {
    50: '#...',
    // ...
  },
} as const;
```

### Adding New Component Classes

1. Add styles to `libs/shared/styles/src/globals.css`:

```css
@layer components {
  .my-component {
    @apply bg-white rounded-lg shadow-md p-4;
  }
}
```

2. Export class names in `libs/shared/styles/src/index.ts`:

```typescript
export const myComponentClasses = {
  base: 'my-component',
  // variants...
} as const;
```

## 🚨 Troubleshooting

### Styles Not Loading

1. Check that the CSS import is correct in your main CSS file
2. Verify the Tailwind config content paths include your files
3. Ensure PostCSS is configured properly

### Build Issues

1. Check that all dependencies are installed
2. Verify the shared styles library builds successfully
3. Check for TypeScript path mapping issues

### Performance

1. Ensure proper purging is configured in production
2. Use the `content` array to limit file scanning
3. Consider splitting large CSS files

## 📚 Resources

- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Nx Documentation](https://nx.dev/docs)
- [PostCSS Documentation](https://postcss.org/)
- [Design System Best Practices](https://designsystemsrepo.com/)
