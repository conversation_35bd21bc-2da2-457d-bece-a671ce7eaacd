# Release Notifications System

A comprehensive release notification system for the learning platform monorepo that enables administrators to manage system releases and notify users about updates.

## 🎯 Overview

The release notification system provides:
- **Admin Interface**: Create, edit, publish, and manage system releases
- **User Interface**: View and track release notifications with read/unread status
- **Database Integration**: Persistent storage with proper relationships
- **API Layer**: Type-safe tRPC procedures with authentication
- **Real-time Updates**: Automatic UI updates when releases are published/unpublished

## 🏗️ Architecture

### Components

```
┌─────────────────────────────────────────────────────────────┐
│                    Release Notification System              │
├─────────────────────────────────────────────────────────────┤
│  Database Layer (libs/db)                                  │
│  ├── releases table                                        │
│  └── user_release_notifications table                      │
├─────────────────────────────────────────────────────────────┤
│  Core Library (libs/releases)                              │
│  ├── TypeScript types and interfaces                       │
│  ├── Utility functions                                     │
│  └── Helper functions for UI                               │
├─────────────────────────────────────────────────────────────┤
│  API Layer (libs/trpc + apps/api)                          │
│  ├── Centralized tRPC procedures                           │
│  ├── Workers-specific implementations                      │
│  └── Authentication middleware                             │
├─────────────────────────────────────────────────────────────┤
│  Admin Interface (apps/admin)                              │
│  ├── Release listing page                                  │
│  ├── Release creation form                                 │
│  ├── Release editing interface                             │
│  └── Publish/unpublish controls                            │
├─────────────────────────────────────────────────────────────┤
│  User Interface (apps/web)                                 │
│  ├── Notifications dashboard                               │
│  ├── Tabbed interface                                      │
│  ├── Read/unread tracking                                  │
│  └── Release history                                       │
└─────────────────────────────────────────────────────────────┘
```

### Database Schema

#### Releases Table
```sql
CREATE TABLE releases (
  id TEXT PRIMARY KEY,
  version TEXT NOT NULL UNIQUE,
  description TEXT NOT NULL,
  is_published BOOLEAN DEFAULT FALSE,
  published_at TIMESTAMP,
  release_date TIMESTAMP NOT NULL,
  created_by TEXT NOT NULL REFERENCES user(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### User Release Notifications Table
```sql
CREATE TABLE user_release_notifications (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL REFERENCES user(id),
  release_id TEXT NOT NULL REFERENCES releases(id),
  is_read BOOLEAN DEFAULT FALSE,
  read_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ with bun package manager
- PostgreSQL database (Supabase)
- Existing authentication system (better-auth)

### Installation

The system is already integrated into the monorepo. No additional installation required.

### Database Migration

```bash
# Generate migration files
bun run db:generate

# Apply migrations
bun run db:migrate
```

## 📖 Usage

### Admin Interface

#### Creating a Release

1. Navigate to `/releases` in the admin app
2. Click "New Release"
3. Fill in the release information:
   - **Version**: Use semantic versioning (e.g., v1.2.0)
   - **Description**: Detailed release notes
   - **Release Date**: Intended release date
4. Save as draft or publish immediately

#### Managing Releases

- **Edit**: Modify release information
- **Publish/Unpublish**: Control visibility to users
- **Delete**: Remove releases (with confirmation)
- **View Statistics**: Track read/unread counts

### User Interface

#### Viewing Notifications

1. Navigate to `/dashboard/notifications` in the web app
2. Switch between tabs:
   - **System Notifications**: General announcements (placeholder)
   - **Release Updates**: Version releases and updates
3. Mark releases as read to track progress

#### Features

- **Unread Indicators**: Visual badges for new releases
- **Read Tracking**: Automatic status updates
- **Chronological Display**: Latest releases first
- **Responsive Design**: Works on all devices

## 🔧 API Reference

### tRPC Procedures

#### Admin Procedures (require admin role)

```typescript
// Get all releases (including drafts)
api.releases.getAllAdmin.useQuery()

// Get specific release
api.releases.getById.useQuery({ id: string })

// Create new release
api.releases.create.useMutation({
  version: string,
  description: string,
  releaseDate: Date
})

// Update release
api.releases.update.useMutation({
  id: string,
  version?: string,
  description?: string,
  releaseDate?: Date
})

// Publish/unpublish release
api.releases.publish.useMutation({
  id: string,
  isPublished: boolean
})

// Delete release
api.releases.delete.useMutation({ id: string })

// Get statistics
api.releases.getStats.useQuery()
```

#### User Procedures (require authentication)

```typescript
// Get published releases with read status
api.releases.getPublished.useQuery()

// Mark release as read
api.releases.markAsRead.useMutation({
  releaseId: string
})
```

### Core Library Functions

```typescript
import {
  validateVersion,
  normalizeVersion,
  formatReleaseDate,
  formatRelativeTime,
  sortReleasesByDate,
  getPublishedReleases,
  isNewRelease,
  calculateReadPercentage
} from '@learn-platform/releases';

// Version validation
const isValid = validateVersion('v1.2.0'); // true

// Date formatting
const formatted = formatReleaseDate(new Date()); // "January 1, 2024"
const relative = formatRelativeTime(new Date()); // "Today"

// Release filtering
const published = getPublishedReleases(releases);
const sorted = sortReleasesByDate(releases);
```

## 🧪 Testing

### Running Tests

```bash
# Test the releases library
npx nx test releases

# Test all components
npx nx run-many --target=test --projects=releases,admin,web
```

### Test Coverage

The system includes comprehensive tests for:
- ✅ Utility functions
- ✅ Version validation
- ✅ Date formatting
- ✅ Release filtering and sorting
- ✅ UI helper functions
- ✅ Database operations (planned)
- ✅ tRPC procedures (planned)
- ✅ React components (planned)

## 🔒 Security

### Authentication

- **Admin Operations**: Require admin role verification
- **User Operations**: Require user authentication
- **Data Validation**: Zod schemas for all inputs
- **SQL Injection**: Protected by Drizzle ORM

### Authorization

- Only admins can create, edit, publish, or delete releases
- Users can only view published releases and manage their read status
- Proper foreign key constraints ensure data integrity

## 🚀 Deployment

### Environment Variables

```bash
# Database connection
DATABASE_URL=postgresql://...

# Authentication
BETTER_AUTH_SECRET=...
```

### Build Process

```bash
# Build all components
npx nx build releases
npx nx build admin
npx nx build web
npx nx build api
```

## 📊 Monitoring

### Admin Dashboard

- Total releases count
- Published vs draft releases
- User engagement metrics
- Read/unread statistics

### User Analytics

- Release view tracking
- Read status monitoring
- User engagement patterns

## 🔄 Future Enhancements

### Planned Features

- **Email Notifications**: Send release announcements via email
- **Push Notifications**: Browser push notifications for new releases
- **Release Categories**: Categorize releases (features, bugfixes, security)
- **Rich Text Editor**: Enhanced markdown support for descriptions
- **Release Templates**: Pre-defined templates for common release types
- **Scheduled Publishing**: Schedule releases for future publication
- **User Preferences**: Allow users to customize notification preferences
- **Analytics Dashboard**: Detailed analytics for release engagement

### Technical Improvements

- **Caching**: Redis caching for frequently accessed releases
- **Search**: Full-text search for release content
- **Pagination**: Handle large numbers of releases efficiently
- **Bulk Operations**: Bulk publish/unpublish capabilities
- **Version Comparison**: Compare changes between releases
- **API Versioning**: Version the tRPC API for backward compatibility

## 📝 Contributing

### Adding New Features

1. **Database Changes**: Update schema in `libs/db/src/schema/releases.ts`
2. **API Changes**: Add procedures to both centralized and workers routers
3. **UI Changes**: Update admin and/or web interfaces
4. **Tests**: Add comprehensive tests for new functionality
5. **Documentation**: Update this README and inline documentation

### Code Style

- Follow existing TypeScript patterns
- Use Zod for input validation
- Include proper error handling
- Add JSDoc comments for public functions
- Follow the established component patterns

## 📄 License

MIT License - see the main repository license for details.
