{"name": "auth", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/auth/src", "projectType": "library", "tags": ["scope:shared", "type:lib"], "implicitDependencies": [], "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default"]}, "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/auth", "main": "libs/auth/src/index.ts", "tsConfig": "libs/auth/tsconfig.lib.json", "assets": ["libs/auth/*.md"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/auth/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/auth/**/*.ts"]}}}}