# Authentication Library Testing Guide

This document provides comprehensive information about testing the authentication library, including how to run tests, understand test coverage, and contribute new tests.

## Test Structure

The authentication library includes a comprehensive test suite covering:

### Core Test Files

- **`auth.test.ts`** - Main unit tests for authentication functionality
- **`auth.integration.test.ts`** - Integration tests for complex workflows
- **`test-setup.ts`** - Test utilities and helper functions
- **`jest.setup.ts`** - Jest configuration and global test setup

### Test Categories

1. **Unit Tests** - Individual function and component testing
2. **Integration Tests** - End-to-end authentication workflows
3. **Security Tests** - CSRF, XSS, SQL injection, and rate limiting
4. **Performance Tests** - Concurrent requests and scalability
5. **Error Handling Tests** - Network failures and edge cases

## Running Tests

### Prerequisites

Ensure you have the required dependencies installed:

```bash
# Install dependencies
bun install

# Set up environment variables (optional for tests)
cp .env.example .env.local
```

### Basic Test Commands

```bash
# Run all authentication tests
npx nx test auth

# Run tests with coverage
npx nx test auth --coverage

# Run tests in watch mode
npx nx test auth --watch

# Run specific test file
npx nx test auth --testPathPattern=auth.test.ts

# Run integration tests only
npx nx test auth --testPathPattern=integration.test.ts
```

### Advanced Test Options

```bash
# Run tests with verbose output
npx nx test auth --verbose

# Run tests matching a pattern
npx nx test auth --testNamePattern="should handle login"

# Run tests with debugging
npx nx test auth --runInBand --detectOpenHandles

# Generate coverage report
npx nx test auth --coverage --coverageReporters=html
```

## Test Coverage

The test suite aims for comprehensive coverage across all critical areas:

### Coverage Targets

- **Lines**: 80% minimum
- **Functions**: 80% minimum  
- **Branches**: 80% minimum
- **Statements**: 80% minimum

### Coverage Reports

After running tests with coverage, reports are available in:

- **Terminal**: Summary displayed after test run
- **HTML Report**: `coverage/libs/auth/index.html`
- **LCOV Report**: `coverage/libs/auth/lcov.info`

### Viewing Coverage

```bash
# Generate and open HTML coverage report
npx nx test auth --coverage
open coverage/libs/auth/index.html
```

## Test Categories Explained

### 1. Core Authentication Flow Tests

Tests the fundamental authentication operations:

- User registration with email/password
- User login with valid/invalid credentials
- User logout and session cleanup
- Password validation requirements
- Duplicate user prevention

### 2. Session Management Tests

Validates session handling:

- Session creation and validation
- Session expiration handling
- Session refresh mechanism
- Session persistence across requests
- Multiple concurrent sessions
- Session invalidation on logout

### 3. Security Tests

Ensures production-ready security:

- **Trusted Origins**: Validates allowed/blocked origins
- **CSRF Protection**: Tests cross-site request forgery prevention
- **Rate Limiting**: Validates authentication attempt limits
- **SQL Injection**: Tests query parameter sanitization
- **XSS Protection**: Validates input/output sanitization
- **Secure Cookies**: Tests httpOnly, secure, sameSite settings

### 4. Database Integration Tests

Tests database interactions:

- User creation in database
- Session storage and retrieval
- Account linking functionality
- Verification token handling
- Database connection error handling

### 5. Error Handling Tests

Validates resilience:

- Network timeout scenarios
- Database unavailability
- Invalid token formats
- Malformed requests
- Missing required fields
- Environment-specific error logging

### 6. Configuration Tests

Tests environment setup:

- Environment variable validation
- Cloudflare Workers compatibility
- Development vs production differences
- Trusted origins configuration

### 7. Performance Tests

Validates scalability:

- Concurrent authentication requests
- High-frequency session validations
- Memory efficiency with large data
- Response time benchmarks

## Test Utilities

### TestDataFactory

Creates consistent test data:

```typescript
import { TestDataFactory } from './test-setup';

// Create test user
const user = TestDataFactory.createUser({
  email: '<EMAIL>',
  emailVerified: true,
});

// Create test session
const session = TestDataFactory.createSessionResult();

// Create authenticated request
const request = TestDataFactory.createAuthenticatedRequest();
```

### MockAuthFactory

Creates auth instance mocks:

```typescript
import { MockAuthFactory } from './test-setup';

// Create mock auth instance
const mockAuth = MockAuthFactory.createAuthInstance();

// Set up successful login mock
MockAuthFactory.createSuccessfulSignInMock(mockAuth);

// Set up failed login mock
MockAuthFactory.createFailedSignInMock(mockAuth, 'Invalid credentials');
```

### SecurityTestUtils

Security testing utilities:

```typescript
import { SecurityTestUtils } from './test-setup';

// Get malicious payloads
const payloads = SecurityTestUtils.createMaliciousPayloads();

// Create CSRF attack request
const maliciousRequest = SecurityTestUtils.createCSRFRequest();

// Create rate limit scenario
const attempts = SecurityTestUtils.createRateLimitScenario();
```

### PerformanceTestUtils

Performance testing utilities:

```typescript
import { PerformanceTestUtils } from './test-setup';

// Measure execution time
const { result, duration } = await PerformanceTestUtils.measureExecutionTime(
  () => someAsyncOperation()
);

// Test concurrent operations
const results = await PerformanceTestUtils.testConcurrentOperations(
  operations,
  maxConcurrency
);
```

## Custom Jest Matchers

The test suite includes custom matchers for better assertions:

```typescript
// Validate session structure
expect(session).toBeValidSession();

// Check if session is expired
expect(expiredSession).toBeExpiredSession();

// Validate auth context
expect(context).toHaveValidAuthContext();
```

## Writing New Tests

### Test File Structure

```typescript
describe('Feature Name', () => {
  let mockAuthInstance: any;

  beforeEach(() => {
    mockAuthInstance = MockAuthFactory.createAuthInstance();
    jest.clearAllMocks();
  });

  afterEach(async () => {
    await TestCleanup.cleanup();
  });

  describe('Specific Functionality', () => {
    it('should handle specific scenario', async () => {
      // Arrange
      const testData = TestDataFactory.createSessionResult();
      MockAuthFactory.createSuccessfulSignInMock(mockAuthInstance, testData);

      // Act
      const result = await mockAuthInstance.api.signIn.email({
        email: '<EMAIL>',
        password: 'password',
      });

      // Assert
      expect(result.data).toEqual(testData);
      expect(result.error).toBeNull();
    });
  });
});
```

### Best Practices

1. **Use Test Utilities**: Leverage provided factories and utilities
2. **Mock External Dependencies**: Always mock database and external services
3. **Test Edge Cases**: Include error scenarios and boundary conditions
4. **Validate Security**: Include security-focused test cases
5. **Performance Awareness**: Consider performance implications
6. **Clean Up**: Always clean up mocks and environment changes

### Adding Security Tests

```typescript
it('should prevent security vulnerability', async () => {
  const maliciousPayload = SecurityTestUtils.createMaliciousPayloads().sqlInjection;
  
  MockAuthFactory.createFailedSignInMock(mockAuth, 'Invalid input');
  
  const result = await mockAuth.api.signIn.email({
    email: maliciousPayload,
    password: 'password',
  });
  
  expect(result.error?.message).toBe('Invalid input');
});
```

## Debugging Tests

### Common Issues

1. **Environment Variables**: Ensure test environment is properly set up
2. **Mock Cleanup**: Verify mocks are cleared between tests
3. **Async Operations**: Use proper async/await patterns
4. **Memory Leaks**: Check for unclosed resources

### Debugging Commands

```bash
# Run with debugging
npx nx test auth --runInBand --detectOpenHandles

# Run single test with logs
npx nx test auth --testNamePattern="specific test" --verbose

# Debug with Node.js inspector
node --inspect-brk node_modules/.bin/jest --runInBand
```

## Continuous Integration

The test suite is designed to run in CI environments:

- **Environment Agnostic**: Works in Node.js and Cloudflare Workers
- **No External Dependencies**: All external services are mocked
- **Fast Execution**: Optimized for quick feedback
- **Comprehensive Coverage**: Ensures production readiness

## Contributing

When contributing new features:

1. **Add Tests First**: Write tests before implementation (TDD)
2. **Maintain Coverage**: Ensure coverage thresholds are met
3. **Security Focus**: Include security test cases
4. **Documentation**: Update this guide for new test patterns
5. **Performance**: Consider performance implications

For questions or issues with testing, please refer to the main project documentation or create an issue in the repository.
