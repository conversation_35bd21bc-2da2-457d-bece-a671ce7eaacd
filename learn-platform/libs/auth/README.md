# @learn-platform/auth

Centralized authentication library for the Learn Platform monorepo. Built with [better-auth](https://better-auth.com) and designed for seamless integration with tRPC and Cloudflare Workers.

## Features

- 🔐 **Better-auth integration** - Modern, type-safe authentication framework
- 🗄️ **Drizzle ORM support** - Uses existing database setup with PostgreSQL
- 🚀 **Cloudflare Workers ready** - Optimized for edge deployment with Hono
- 🔗 **tRPC integration** - Seamless session management in tRPC procedures
- 📝 **TypeScript first** - Full type safety and IntelliSense support
- 🍪 **<PERSON>ie caching** - Optimized session retrieval with cookie caching

## Installation

This library is part of the monorepo and uses the existing database setup. No additional installation is required.

## Environment Variables

Add these environment variables to your `.env` file:

```env
# Required: Secret key for better-auth (generate a secure random string)
BETTER_AUTH_SECRET=your-super-secret-key-here

# Optional: Base URL for your application (defaults to http://localhost:3000)
BETTER_AUTH_URL=https://your-app.com

# Optional: Additional trusted origins (comma-separated list)
# Useful for production domains, staging environments, etc.
BETTER_AUTH_TRUSTED_ORIGINS=https://your-domain.com,https://staging.your-domain.com

# Required: Database connection (already configured in @learn-platform/db)
DATABASE_URL=your-postgresql-connection-string
```

## Quick Start

### 1. Basic Usage

```typescript
import { auth, getSession } from '@learn-platform/auth';

// Get session from request headers
const session = await getSession(request.headers);

if (session) {
  console.log('User:', session.user.email);
  console.log('Session expires:', session.session.expiresAt);
}
```

### 2. tRPC Integration

Update your tRPC context to use the auth library. Here's how to modify the existing `learn-platform/libs/trpc/src/context.ts`:

```typescript
import { createAuthContext } from '@learn-platform/auth';

export async function createContext(opts: CreateContextOptions) {
  const { request, env } = opts;

  // Extract URL information
  const url = new URL(request.url);

  // Extract Cloudflare-specific headers and request metadata
  const requestMetadata = {
    ip: request.headers.get('CF-Connecting-IP') ||
        request.headers.get('X-Forwarded-For') ||
        request.headers.get('X-Real-IP') ||
        'unknown',
    country: request.headers.get('CF-IPCountry') || 'unknown',
    userAgent: request.headers.get('user-agent') || '',
    timestamp: new Date().toISOString(),
    requestId: request.headers.get('CF-Ray') ||
               request.headers.get('X-Request-ID') ||
               crypto.randomUUID(),
  };

  // Create auth context using better-auth
  const authContext = await createAuthContext(request.headers);

  return {
    // Core request information
    request,
    env,
    url,

    // Enhanced metadata
    ...requestMetadata,

    // Authentication context from better-auth
    ...authContext, // Includes: session, isAuthenticated, userId, user

    // Utility functions (now using real auth data)
    getUserId: () => authContext.userId,
    getUserRoles: () => authContext.user?.roles || [],
    hasRole: (role: string) => authContext.user?.roles?.includes(role) || false,

    // Request timing for performance monitoring
    startTime: Date.now(),
  };
}
```

### 3. Protected tRPC Procedures

```typescript
import { requireAuth } from '@learn-platform/auth';
import { publicProcedure } from '@learn-platform/trpc';

export const protectedProcedure = publicProcedure.use(async ({ ctx, next }) => {
  // Throws error if not authenticated
  requireAuth(ctx.session);
  
  return next({
    ctx: {
      ...ctx,
      // Now TypeScript knows session is not null
      session: ctx.session,
    },
  });
});
```

## API Reference

### Core Functions

#### `getSession(headers, options?)`

Retrieve user session from request headers.

```typescript
const session = await getSession(request.headers);
// Returns: SessionResult (session data or null)
```

#### `createAuthContext(headers)`

Create authentication context for tRPC integration.

```typescript
const authContext = await createAuthContext(request.headers);
// Returns: { session, isAuthenticated, userId, user }
```

#### `requireAuth(session)`

Utility for requiring authentication in procedures.

```typescript
requireAuth(session); // Throws if not authenticated
```

### Utility Functions

- `isSessionValid(session)` - Check if session is valid and not expired
- `getUserId(session)` - Extract user ID with type safety
- `getUserEmail(session)` - Extract user email with type safety  
- `isEmailVerified(session)` - Check if user's email is verified

### Types

```typescript
interface AuthSession {
  session: {
    id: string;
    userId: string;
    expiresAt: Date;
    // ... more session fields
  };
  user: {
    id: string;
    email: string;
    name: string;
    // ... more user fields
  };
}

type SessionResult = AuthSession | null;

interface AuthContext {
  session: SessionResult;
  isAuthenticated: boolean;
  userId: string | null;
  user: AuthSession['user'] | null;
}
```

## Database Schema

Better-auth requires several database tables for authentication. The schema has been generated and is available in `libs/db/src/schema/auth.ts`:

- `user` - User accounts
- `session` - User sessions
- `account` - OAuth accounts (for social providers)
- `verification` - Email verification tokens

### Setup Database

1. **Set your database URL** in environment variables:
   ```bash
   DATABASE_URL=postgresql://username:password@localhost:5432/database
   ```

2. **Generate and run migrations**:
   ```bash
   bun run db:generate
   bun run db:migrate
   ```

3. **Verify tables were created**:
   The migration will create the auth tables alongside your existing tables.

### Schema Compatibility

The auth library is configured to use the `user` table structure that is compatible with better-auth requirements:

- Uses `text` ID field (better-auth requirement)
- Includes all required fields for authentication
- Supports additional fields like `avatar` and `image`

## Cloudflare Workers Integration

The library is optimized for Cloudflare Workers with Hono. Here's a complete setup example:

```typescript
import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { auth, getSession } from '@learn-platform/auth';

const app = new Hono<{
  Variables: {
    user: typeof auth.$Infer.Session.user | null;
    session: typeof auth.$Infer.Session.session | null;
  }
}>();

// Configure CORS for auth endpoints
app.use('/api/auth/*', cors({
  origin: ['http://localhost:3000', 'https://your-domain.com'],
  allowHeaders: ['Content-Type', 'Authorization'],
  allowMethods: ['POST', 'GET', 'OPTIONS'],
  credentials: true,
}));

// Mount better-auth handler
app.on(['POST', 'GET'], '/api/auth/*', (c) => {
  return auth.handler(c.req.raw);
});

// Session middleware
app.use('*', async (c, next) => {
  const session = await getSession(c.req.raw.headers);

  if (session) {
    c.set('user', session.user);
    c.set('session', session.session);
  } else {
    c.set('user', null);
    c.set('session', null);
  }

  return next();
});

// Protected route example
app.get('/api/profile', (c) => {
  const user = c.get('user');

  if (!user) {
    return c.json({ error: 'Unauthorized' }, 401);
  }

  return c.json({ user });
});

export default app;
```

## Security Considerations

- Always use HTTPS in production
- Set `BETTER_AUTH_SECRET` to a cryptographically secure random string
- Configure `trustedOrigins` for your domains
- Enable email verification for production use
- Consider implementing rate limiting for auth endpoints

## Troubleshooting

### Common Issues

1. **"BETTER_AUTH_SECRET is required"**
   - Generate a secure random string and set it in your environment

2. **Session not persisting**
   - Check cookie settings and HTTPS configuration
   - Verify `trustedOrigins` includes your domain

3. **Database connection errors**
   - Ensure `DATABASE_URL` is correctly set
   - Run database migrations: `bun run db:migrate`

## Contributing

This library follows the monorepo patterns established in `@learn-platform/db` and `@learn-platform/trpc`. When adding features:

1. Update TypeScript types in `types.ts`
2. Add utility functions in `session.ts`
3. Update this README with examples
4. Add tests for new functionality

## License

MIT
