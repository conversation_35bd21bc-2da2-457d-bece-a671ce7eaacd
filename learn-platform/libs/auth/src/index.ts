/**
 * @learn-platform/auth
 *
 * Centralized authentication library for the Learn Platform monorepo.
 * Provides better-auth integration with tRPC and Cloudflare Workers support.
 */

// Export the main auth instance and factory function
export { auth, createAuth } from './auth';
export type { Auth, Session, User } from './auth';

// Export debug utilities from separate module to avoid lazy-loading issues
export { debugTrustedOrigins } from './debug-utils';

// Export session management utilities
export {
  getSession,
  createAuthContext,
  createAuthContextWithEnv,
  isSessionValid,
  getUserId,
  getUserEmail,
  isEmailVerified,
  requireAuth,
} from './session';

// Export TypeScript types
export type {
  AuthSession,
  SessionResult,
  AuthContext,
  GetSessionOptions,
  AuthConfig,
  AuthEnvironment,
} from './types';

// Export debug utilities
export {
  AuthDebugLogger,
  traceBetterAuthFlow,
  getEndpointDescription,
  debugEnvironmentVariables,
  DEBUG_CONFIG
} from './debug';
