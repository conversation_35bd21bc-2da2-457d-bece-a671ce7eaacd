/**
 * Simple test utilities for authentication library tests
 *
 * This file provides basic data factories and utilities without complex
 * class hierarchies or global state management.
 */

import type { AuthSession, SessionResult } from './types';

/**
 * Create a test user object with optional overrides
 */
export function createTestUser(overrides: Partial<AuthSession['user']> = {}) {
  return {
    id: 'test-user-id-123',
    email: '<EMAIL>',
    name: 'Test User',
    emailVerified: false,
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z'),
    avatar: null,
    ...overrides,
  };
}

/**
 * Create a test session object with optional overrides
 */
export function createTestSession(overrides: Partial<AuthSession['session']> = {}) {
  return {
    id: 'test-session-id-123',
    userId: 'test-user-id-123',
    expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
    token: 'test-session-token-123',
    createdAt: new Date('2024-01-01T00:00:00Z'),
    updatedAt: new Date('2024-01-01T00:00:00Z'),
    ipAddress: '***********',
    userAgent: 'Mozilla/5.0 Test Browser',
    ...overrides,
  };
}

/**
 * Create a complete session result with user and session
 */
export function createSessionResult(
  userOverrides: Partial<AuthSession['user']> = {},
  sessionOverrides: Partial<AuthSession['session']> = {}
): SessionResult {
  return {
    user: createTestUser(userOverrides),
    session: createTestSession(sessionOverrides),
  };
}

/**
 * Create an expired session result
 */
export function createExpiredSessionResult(): SessionResult {
  return createSessionResult(
    {},
    {
      expiresAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
    }
  );
}

/**
 * Create a test request with optional headers
 */
export function createTestRequest(
  url = 'https://example.com',
  options: RequestInit = {}
): Request {
  return new Request(url, options);
}

/**
 * Create an authenticated request with session cookie
 */
export function createAuthenticatedRequest(
  sessionToken = 'test-session-token',
  url = 'https://example.com'
): Request {
  return new Request(url, {
    headers: {
      cookie: `session=${sessionToken}`,
    },
  });
}

// Note: Mock creation functions are removed from this file since Jest is not available
// in the utility context. Each test file should create its own mocks directly.

/**
 * Test environment variables
 */
export const TEST_ENV = {
  BETTER_AUTH_SECRET: 'test-secret-key-for-testing-min-32-chars',
  BETTER_AUTH_URL: 'https://test.example.com',
  NODE_ENV: 'test',
};

/**
 * Cloudflare environment variables
 */
export const CLOUDFLARE_ENV = {
  BETTER_AUTH_SECRET: 'cloudflare-secret-key-for-testing-min-32-chars',
  BETTER_AUTH_URL: 'https://cloudflare.example.com',
};

/**
 * Setup test environment variables
 */
export function setupTestEnv() {
  // Check if process exists (might not in Cloudflare Workers simulation)
  if (typeof process === 'undefined' || !process.env) {
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    return () => {}; // No-op if process doesn't exist
  }

  const originalEnv = process.env;
  process.env = { ...originalEnv, ...TEST_ENV };
  return () => {
    if (typeof process !== 'undefined' && process.env) {
      process.env = originalEnv;
    }
  };
}

/**
 * Malicious payloads for security testing
 */
export const MALICIOUS_PAYLOADS = {
  sqlInjection: "<EMAIL>'; DROP TABLE users; --",
  xssScript: '<script>alert("xss")</script>',
  xssImg: '<img src="x" onerror="alert(1)">',
  pathTraversal: '../../../etc/passwd',
  longString: 'a'.repeat(10000),
};
