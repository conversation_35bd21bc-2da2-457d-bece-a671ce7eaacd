/**
 * Integration tests for authentication login flows
 *
 * This test suite focuses specifically on user login scenarios,
 * credential validation, and login-related error handling.
 */

// Mock external dependencies BEFORE any imports
jest.mock('@learn-platform/db', () => ({
  db: {
    select: jest.fn().mockReturnThis(),
    from: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    values: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    set: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    execute: jest.fn().mockResolvedValue([]),
    query: jest.fn().mockResolvedValue([]),
  },
  users: {},
  session: {},
  account: {},
  verification: {},
}));

jest.mock('better-auth', () => ({
  betterAuth: jest.fn(() => ({
    api: {
      signIn: {
        email: jest.fn(),
      },
      getSession: jest.fn(),
    },
  })),
}));

jest.mock('better-auth/adapters/drizzle', () => ({
  drizzleAdapter: jest.fn(() => ({})),
}));

import { getSession, createAuthContext } from './session';
import {
  createSessionResult,
  createAuthenticatedRequest,
  setupTestEnv,
  MALICIOUS_PAYLOADS,
} from './test-utils';

// Helper functions for this test file
function createMockAuthInstance() {
  return {
    api: {
      signIn: {
        email: jest.fn(),
      },
      getSession: jest.fn(),
    },
  };
}

function mockSuccessfulSignIn(authInstance: any, sessionResult?: any) {
  const result = sessionResult || createSessionResult();
  authInstance.api.signIn.email.mockResolvedValue({
    data: result,
    error: null,
  });
  return result;
}

function mockFailedSignIn(authInstance: any, errorMessage = 'Invalid credentials') {
  authInstance.api.signIn.email.mockResolvedValue({
    data: null,
    error: { message: errorMessage },
  });
}

function mockGetSession(authInstance: any, sessionResult?: any) {
  const result = sessionResult !== undefined ? sessionResult : createSessionResult();
  authInstance.api.getSession.mockResolvedValue(result);
  return result;
}

describe('Authentication Login Integration Tests', () => {
  let mockAuthInstance: any;
  let restoreEnv: () => void;

  beforeEach(() => {
    restoreEnv = setupTestEnv();
    mockAuthInstance = createMockAuthInstance();
    jest.clearAllMocks();
  });

  afterEach(() => {
    restoreEnv();
  });

  describe('Successful Login Flows', () => {
    it('should handle successful login with valid credentials', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'SecurePassword123!',
      };

      const sessionResult = createSessionResult(
        { email: loginData.email, emailVerified: true },
        {}
      );

      mockSuccessfulSignIn(mockAuthInstance, sessionResult);

      const result = await mockAuthInstance.api.signIn.email(loginData);

      expect(result.data).toEqual(sessionResult);
      expect(result.error).toBeNull();
      expect(result.data?.user.email).toBe(loginData.email);
      expect(result.data?.user.emailVerified).toBe(true);
    });

    it('should create valid session after successful login', async () => {
      const sessionResult = createSessionResult();
      mockSuccessfulSignIn(mockAuthInstance, sessionResult);
      mockGetSession(mockAuthInstance, sessionResult);

      // Simulate login
      const loginResult = await mockAuthInstance.api.signIn.email({
        email: '<EMAIL>',
        password: 'password',
      });

      expect(loginResult.data).toEqual(sessionResult);

      // Verify session can be retrieved
      const request = createAuthenticatedRequest();
      const session = await getSession(request, {}, mockAuthInstance);

      expect(session).toEqual(sessionResult);
    });

    it('should handle login with unverified email', async () => {
      const sessionResult = createSessionResult(
        { emailVerified: false },
        {}
      );

      mockSuccessfulSignIn(mockAuthInstance, sessionResult);

      const result = await mockAuthInstance.api.signIn.email({
        email: '<EMAIL>',
        password: 'SecurePassword123!',
      });

      expect(result.data?.user.emailVerified).toBe(false);
      expect(result.error).toBeNull();
    });
  });

  describe('Failed Login Scenarios', () => {
    it('should reject login with invalid credentials', async () => {
      mockFailedSignIn(mockAuthInstance, 'Invalid credentials');

      const result = await mockAuthInstance.api.signIn.email({
        email: '<EMAIL>',
        password: 'wrongpassword',
      });

      expect(result.data).toBeNull();
      expect(result.error?.message).toBe('Invalid credentials');
    });

    it('should reject login with non-existent user', async () => {
      mockFailedSignIn(mockAuthInstance, 'User not found');

      const result = await mockAuthInstance.api.signIn.email({
        email: '<EMAIL>',
        password: 'password',
      });

      expect(result.data).toBeNull();
      expect(result.error?.message).toBe('User not found');
    });

    it('should handle malformed email addresses', async () => {
      mockFailedSignIn(mockAuthInstance, 'Invalid email format');

      const result = await mockAuthInstance.api.signIn.email({
        email: 'invalid-email',
        password: 'password',
      });

      expect(result.error?.message).toBe('Invalid email format');
    });

    it('should handle empty credentials', async () => {
      mockFailedSignIn(mockAuthInstance, 'Missing required fields');

      const result = await mockAuthInstance.api.signIn.email({
        email: '',
        password: '',
      });

      expect(result.error?.message).toBe('Missing required fields');
    });
  });

  describe('Security Validation', () => {
    it('should prevent SQL injection in login', async () => {
      mockFailedSignIn(mockAuthInstance, 'Invalid email format');

      const result = await mockAuthInstance.api.signIn.email({
        email: MALICIOUS_PAYLOADS.sqlInjection,
        password: 'password',
      });

      expect(result.error?.message).toBe('Invalid email format');
    });

    it('should handle rate limiting for failed attempts', async () => {
      // Simulate multiple failed attempts
      for (let i = 0; i < 5; i++) {
        mockFailedSignIn(mockAuthInstance, 'Invalid credentials');

        const result = await mockAuthInstance.api.signIn.email({
          email: '<EMAIL>',
          password: 'wrongpassword',
        });

        expect(result.error?.message).toBe('Invalid credentials');
      }

      // 6th attempt should be rate limited
      mockFailedSignIn(mockAuthInstance, 'Too many attempts. Please try again later.');

      const rateLimitedResult = await mockAuthInstance.api.signIn.email({
        email: '<EMAIL>',
        password: 'wrongpassword',
      });

      expect(rateLimitedResult.error?.message).toBe('Too many attempts. Please try again later.');
    });
  });

  describe('Authentication Context Integration', () => {
    it('should create authenticated context after successful login', async () => {
      const sessionResult = createSessionResult();
      mockSuccessfulSignIn(mockAuthInstance, sessionResult);
      mockGetSession(mockAuthInstance, sessionResult);

      // Login
      await mockAuthInstance.api.signIn.email({
        email: '<EMAIL>',
        password: 'password',
      });

      // Create auth context
      const request = createAuthenticatedRequest();
      const context = await createAuthContext(request, mockAuthInstance);

      expect(context.isAuthenticated).toBe(true);
      expect(context.userId).toBe(sessionResult?.user.id);
      expect(context.user).toEqual(sessionResult?.user);
      expect(context.session).toEqual(sessionResult);
    });

    it('should create unauthenticated context for failed login', async () => {
      mockFailedSignIn(mockAuthInstance, 'Invalid credentials');
      mockGetSession(mockAuthInstance, null);

      // Failed login
      const loginResult = await mockAuthInstance.api.signIn.email({
        email: '<EMAIL>',
        password: 'wrongpassword',
      });

      expect(loginResult.error).toBeDefined();

      // Create auth context
      const request = createAuthenticatedRequest();
      const context = await createAuthContext(request, mockAuthInstance);

      expect(context.isAuthenticated).toBe(false);
      expect(context.userId).toBeNull();
      expect(context.user).toBeNull();
      expect(context.session).toBeNull();
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors during login', async () => {
      mockAuthInstance.api.signIn.email.mockRejectedValue(new Error('Network error'));

      try {
        await mockAuthInstance.api.signIn.email({
          email: '<EMAIL>',
          password: 'password',
        });
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        expect((error as Error).message).toBe('Network error');
      }
    });

    it('should handle database errors during login', async () => {
      mockAuthInstance.api.signIn.email.mockRejectedValue(new Error('Database connection failed'));

      try {
        await mockAuthInstance.api.signIn.email({
          email: '<EMAIL>',
          password: 'password',
        });
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        expect((error as Error).message).toBe('Database connection failed');
      }
    });
  });
});
