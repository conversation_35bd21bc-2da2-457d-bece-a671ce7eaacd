/**
 * Tests for Better Auth trusted origins configuration
 *
 * This test file specifically validates the uniqueOrigins logic and
 * environment variable handling for BETTER_AUTH_TRUSTED_ORIGINS.
 */

import { debugTrustedOrigins } from './auth';

// We need to test the internal logic, so we'll create a test version
// of the trusted origins function
function createTrustedOrigins(config: {
  baseURL?: string;
  trustedOrigins?: string[];
}): string[] {
  const origins = [
    // Development origins
    'http://localhost:3000',
    'http://localhost:3001',
    'http://localhost:8787',
    'http://127.0.0.1:8787',
    // Production origins
    'https://kwaci-learning.bmbn.dev',
    // Include baseURL if it's different from the above
    ...(config.baseURL && !config.baseURL.includes('localhost') ? [config.baseURL] : []),
    // Include any custom trusted origins from environment
    ...(config.trustedOrigins || []),
  ];

  // Remove duplicates
  const uniqueOrigins = [...new Set(origins)];
  return uniqueOrigins;
}

function parseEnvironmentOrigins(envValue?: string): string[] {
  return envValue ? envValue.split(',').map((origin: string) => origin.trim()).filter(Boolean) : [];
}

describe('Trusted Origins Configuration', () => {
  let originalEnv: NodeJS.ProcessEnv;

  beforeEach(() => {
    originalEnv = { ...process.env };
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  describe('uniqueOrigins logic', () => {
    it('should remove duplicate origins correctly', () => {
      const config = {
        baseURL: 'http://localhost:3000', // This should be filtered out as duplicate
        trustedOrigins: ['http://localhost:3000', 'https://example.com', 'http://localhost:3000'],
      };

      const result = createTrustedOrigins(config);

      // Should not contain duplicates
      const uniqueCheck = new Set(result);
      expect(result.length).toBe(uniqueCheck.size);

      // Should contain expected origins without duplicates
      expect(result).toContain('http://localhost:3000');
      expect(result).toContain('https://example.com');
      expect(result).toContain('https://kwaci-learning.bmbn.dev');

      // Count occurrences of localhost:3000
      const localhostCount = result.filter(origin => origin === 'http://localhost:3000').length;
      expect(localhostCount).toBe(1);
    });

    it('should include production URL when no custom origins provided', () => {
      const config = {
        baseURL: 'http://localhost:3000',
        trustedOrigins: [],
      };

      const result = createTrustedOrigins(config);

      expect(result).toContain('https://kwaci-learning.bmbn.dev');
      expect(result).toContain('http://localhost:3000');
    });

    it('should include baseURL when it is not localhost', () => {
      const config = {
        baseURL: 'https://custom-domain.com',
        trustedOrigins: [],
      };

      const result = createTrustedOrigins(config);

      expect(result).toContain('https://custom-domain.com');
      expect(result).toContain('https://kwaci-learning.bmbn.dev');
    });

    it('should not duplicate baseURL if it matches production URL', () => {
      const config = {
        baseURL: 'https://kwaci-learning.bmbn.dev',
        trustedOrigins: [],
      };

      const result = createTrustedOrigins(config);

      const productionUrlCount = result.filter(origin => origin === 'https://kwaci-learning.bmbn.dev').length;
      expect(productionUrlCount).toBe(1);
    });

    it('should handle empty trustedOrigins array', () => {
      const config = {
        baseURL: 'http://localhost:3000',
        trustedOrigins: [],
      };

      const result = createTrustedOrigins(config);

      expect(result.length).toBeGreaterThan(0);
      expect(result).toContain('https://kwaci-learning.bmbn.dev');
    });

    it('should handle undefined trustedOrigins', () => {
      const config = {
        baseURL: 'http://localhost:3000',
      };

      const result = createTrustedOrigins(config);

      expect(result.length).toBeGreaterThan(0);
      expect(result).toContain('https://kwaci-learning.bmbn.dev');
    });
  });

  describe('Environment variable parsing', () => {
    it('should parse comma-separated origins correctly', () => {
      const envValue = 'https://example.com,https://staging.example.com,https://prod.example.com';
      const result = parseEnvironmentOrigins(envValue);

      expect(result).toEqual([
        'https://example.com',
        'https://staging.example.com',
        'https://prod.example.com'
      ]);
    });

    it('should handle origins with extra whitespace', () => {
      const envValue = ' https://example.com , https://staging.example.com , https://prod.example.com ';
      const result = parseEnvironmentOrigins(envValue);

      expect(result).toEqual([
        'https://example.com',
        'https://staging.example.com',
        'https://prod.example.com'
      ]);
    });

    it('should filter out empty strings', () => {
      const envValue = 'https://example.com,,https://staging.example.com,';
      const result = parseEnvironmentOrigins(envValue);

      expect(result).toEqual([
        'https://example.com',
        'https://staging.example.com'
      ]);
    });

    it('should handle single origin', () => {
      const envValue = 'https://example.com';
      const result = parseEnvironmentOrigins(envValue);

      expect(result).toEqual(['https://example.com']);
    });

    it('should handle empty environment variable', () => {
      const result = parseEnvironmentOrigins('');
      expect(result).toEqual([]);
    });

    it('should handle undefined environment variable', () => {
      const result = parseEnvironmentOrigins(undefined);
      expect(result).toEqual([]);
    });
  });

  describe('Integration with environment variables', () => {
    it('should handle BETTER_AUTH_TRUSTED_ORIGINS with duplicates', () => {
      const envOrigins = parseEnvironmentOrigins('http://localhost:3000,https://example.com,http://localhost:3000');
      const config = {
        baseURL: 'http://localhost:3000',
        trustedOrigins: envOrigins,
      };

      const result = createTrustedOrigins(config);

      // Should remove duplicates
      const localhostCount = result.filter(origin => origin === 'http://localhost:3000').length;
      expect(localhostCount).toBe(1);
      expect(result).toContain('https://example.com');
    });

    it('should prioritize production URL even with conflicting env vars', () => {
      const envOrigins = parseEnvironmentOrigins('http://localhost:3000,http://localhost:3000,http://localhost:3000');
      const config = {
        baseURL: 'http://localhost:3000',
        trustedOrigins: envOrigins,
      };

      const result = createTrustedOrigins(config);

      // Production URL should always be included
      expect(result).toContain('https://kwaci-learning.bmbn.dev');

      // Should not have excessive duplicates
      const localhostCount = result.filter(origin => origin === 'http://localhost:3000').length;
      expect(localhostCount).toBe(1);
    });
  });

  describe('Production scenario simulation', () => {
    it('should simulate production environment configuration', () => {
      // Simulate production environment variables
      const envOrigins = parseEnvironmentOrigins('https://kwaci-learning.bmbn.dev');
      const config = {
        baseURL: 'https://kwaci-learning.bmbn.dev',
        trustedOrigins: envOrigins,
      };

      const result = createTrustedOrigins(config);

      // Should contain production URL only once
      const productionUrlCount = result.filter(origin => origin === 'https://kwaci-learning.bmbn.dev').length;
      expect(productionUrlCount).toBe(1);

      // Should contain development origins for local testing
      expect(result).toContain('http://localhost:3000');
      expect(result).toContain('http://localhost:8787');
    });

    it('should handle malformed environment variable gracefully', () => {
      // Simulate malformed environment variable that might cause duplicates
      const envOrigins = parseEnvironmentOrigins('http://localhost:3000,http://localhost:3000,http://localhost:3000,https://kwaci-learning.bmbn.dev');
      const config = {
        baseURL: 'https://kwaci-learning.bmbn.dev',
        trustedOrigins: envOrigins,
      };

      const result = createTrustedOrigins(config);

      // Should handle duplicates correctly
      const uniqueCheck = new Set(result);
      expect(result.length).toBe(uniqueCheck.size);

      // Should contain expected URLs only once
      expect(result.filter(origin => origin === 'http://localhost:3000').length).toBe(1);
      expect(result.filter(origin => origin === 'https://kwaci-learning.bmbn.dev').length).toBe(1);
    });
  });

  describe('Debug utility function', () => {
    it('should provide comprehensive debug information', () => {
      // Test with environment that has duplicates (but they get filtered out by our fix)
      const mockEnv = {
        BETTER_AUTH_SECRET: 'test-secret-32-chars-minimum',
        BETTER_AUTH_URL: 'https://kwaci-learning.bmbn.dev',
        BETTER_AUTH_TRUSTED_ORIGINS: 'http://localhost:3000,http://localhost:3000,https://example.com',
      };

      const debugInfo = debugTrustedOrigins(mockEnv);

      expect(debugInfo.config).toBeDefined();
      expect(debugInfo.rawOrigins).toBeInstanceOf(Array);
      expect(debugInfo.uniqueOrigins).toBeInstanceOf(Array);
      expect(debugInfo.duplicates).toBeInstanceOf(Array);
      expect(debugInfo.hasProductionUrl).toBe(true);

      // After our fix, hardcoded duplicates are filtered out, but baseURL duplicate remains
      expect(debugInfo.duplicates.length).toBeGreaterThan(0);
      expect(debugInfo.duplicates).toContain('https://kwaci-learning.bmbn.dev'); // baseURL creates duplicate

      // Should have production URL
      expect(debugInfo.uniqueOrigins).toContain('https://kwaci-learning.bmbn.dev');

      // Should include the non-hardcoded custom origin
      expect(debugInfo.uniqueOrigins).toContain('https://example.com');
    });

    it('should handle environment without duplicates', () => {
      const mockEnv = {
        BETTER_AUTH_SECRET: 'test-secret-32-chars-minimum',
        BETTER_AUTH_URL: 'https://custom-domain.example.com', // Use different URL to avoid duplicate with hardcoded production URL
        BETTER_AUTH_TRUSTED_ORIGINS: 'https://staging.example.com,https://dev.example.com',
      };

      const debugInfo = debugTrustedOrigins(mockEnv);

      expect(debugInfo.duplicates.length).toBe(0);
      expect(debugInfo.hasProductionUrl).toBe(true);
      expect(debugInfo.uniqueOrigins).toContain('https://staging.example.com');
      expect(debugInfo.uniqueOrigins).toContain('https://dev.example.com');
      expect(debugInfo.uniqueOrigins).toContain('https://custom-domain.example.com');
    });

    it('should work without environment parameter', () => {
      const debugInfo = debugTrustedOrigins();

      expect(debugInfo.config).toBeDefined();
      expect(debugInfo.hasProductionUrl).toBe(true);
      expect(debugInfo.uniqueOrigins).toContain('https://kwaci-learning.bmbn.dev');
    });
  });
});
