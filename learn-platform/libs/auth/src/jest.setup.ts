/**
 * Simplified Jest setup file for authentication library tests
 *
 * This file provides minimal global setup for Node.js environment compatibility
 * and essential polyfills. Individual test files handle their own mocking.
 */

import { beforeAll, afterEach, afterAll, expect, jest } from '@jest/globals';

// Mock console methods to reduce noise in tests
beforeAll(() => {
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  jest.spyOn(console, 'warn').mockImplementation(() => {});
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  jest.spyOn(console, 'error').mockImplementation(() => {});
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  jest.spyOn(console, 'log').mockImplementation(() => {});

  // Mock performance API for Node.js environments
  if (typeof performance === 'undefined') {
    (global as any).performance = {
      now: () => Date.now(),
    };
  }

  // Mock Request for Node.js environments
  if (typeof Request === 'undefined') {
    (global as any).Request = class MockRequest {
      url: string;
      method: string;
      headers: Map<string, string>;

      constructor(url: string, init: RequestInit = {}) {
        this.url = url;
        this.method = init.method || 'GET';
        this.headers = new Map();

        if (init.headers) {
          if (init.headers instanceof Headers) {
            init.headers.forEach((value, key) => {
              this.headers.set(key.toLowerCase(), value);
            });
          } else if (Array.isArray(init.headers)) {
            init.headers.forEach(([key, value]) => {
              this.headers.set(key.toLowerCase(), value);
            });
          } else {
            Object.entries(init.headers).forEach(([key, value]) => {
              this.headers.set(key.toLowerCase(), value);
            });
          }
        }
      }
    };
  }

  // Mock Headers for Node.js environments
  if (typeof Headers === 'undefined') {
    (global as any).Headers = class MockHeaders extends Map<string, string> {
      constructor(init?: HeadersInit) {
        super();
        if (init) {
          if (Array.isArray(init)) {
            init.forEach(([key, value]) => {
              this.set(key.toLowerCase(), value);
            });
          } else {
            Object.entries(init).forEach(([key, value]) => {
              this.set(key.toLowerCase(), value);
            });
          }
        }
      }

      override forEach(callbackfn: (value: string, key: string, map: Map<string, string>) => void, thisArg?: any): void {
        super.forEach(callbackfn, thisArg);
      }
    };
  }
});

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks();
});

// Global cleanup
afterAll(() => {
  jest.restoreAllMocks();
});

// Extend Jest matchers for better assertions
expect.extend({
  toBeValidSession(received: any) {
    const pass = received &&
                 received.user &&
                 received.session &&
                 typeof received.user.id === 'string' &&
                 typeof received.session.token === 'string' &&
                 received.session.expiresAt instanceof Date;

    if (pass) {
      return {
        message: () => `expected ${JSON.stringify(received)} not to be a valid session`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${JSON.stringify(received)} to be a valid session with user and session objects`,
        pass: false,
      };
    }
  },

  toBeExpiredSession(received: any) {
    const isValidSession = received &&
                          received.session &&
                          received.session.expiresAt instanceof Date;

    if (!isValidSession) {
      return {
        message: () => `expected ${JSON.stringify(received)} to be a session object`,
        pass: false,
      };
    }

    const isExpired = received.session.expiresAt < new Date();

    if (isExpired) {
      return {
        message: () => `expected session to not be expired`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected session to be expired (expiresAt: ${received.session.expiresAt})`,
        pass: false,
      };
    }
  },

  toHaveValidAuthContext(received: any) {
    const pass = received &&
                 typeof received.isAuthenticated === 'boolean' &&
                 (received.userId === null || typeof received.userId === 'string') &&
                 (received.user === null || typeof received.user === 'object') &&
                 (received.session === null || typeof received.session === 'object');

    if (pass) {
      return {
        message: () => `expected ${JSON.stringify(received)} not to be a valid auth context`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${JSON.stringify(received)} to be a valid auth context`,
        pass: false,
      };
    }
  },
});

// Declare custom matchers for TypeScript
declare module '@jest/expect' {
  interface Matchers<R> {
    toBeValidSession(): R;
    toBeExpiredSession(): R;
    toHaveValidAuthContext(): R;
  }
}
