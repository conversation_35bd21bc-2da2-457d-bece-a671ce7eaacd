/**
 * Debug utilities for better-auth library
 * Provides comprehensive logging for authentication flow tracing
 */

/**
 * Debug configuration
 */
export const DEBUG_CONFIG = {
  enabled: process.env['NODE_ENV'] === 'development' || process.env['AUTH_DEBUG'] === 'true',
  logLevel: process.env['AUTH_LOG_LEVEL'] || 'info', // 'debug', 'info', 'warn', 'error'
  logDatabase: process.env['AUTH_LOG_DATABASE'] === 'true',
  logRequests: process.env['AUTH_LOG_REQUESTS'] !== 'false', // default true
  logResponses: process.env['AUTH_LOG_RESPONSES'] !== 'false', // default true
};

/**
 * Debug logger with different levels
 */
export class AuthDebugLogger {
  private prefix: string;

  constructor(prefix = 'AUTH-DEBUG') {
    this.prefix = prefix;
  }

  private shouldLog(level: string): boolean {
    if (!DEBUG_CONFIG.enabled) return false;

    const levels = ['debug', 'info', 'warn', 'error'];
    const currentLevelIndex = levels.indexOf(DEBUG_CONFIG.logLevel);
    const messageLevelIndex = levels.indexOf(level);

    return messageLevelIndex >= currentLevelIndex;
  }

  debug(message: string, ...args: any[]) {
    if (this.shouldLog('debug')) {
      console.log(`🔍 [${this.prefix}] ${message}`, ...args);
    }
  }

  info(message: string, ...args: any[]) {
    if (this.shouldLog('info')) {
      console.log(`ℹ️  [${this.prefix}] ${message}`, ...args);
    }
  }

  warn(message: string, ...args: any[]) {
    if (this.shouldLog('warn')) {
      console.warn(`⚠️  [${this.prefix}] ${message}`, ...args);
    }
  }

  error(message: string, ...args: any[]) {
    if (this.shouldLog('error')) {
      console.error(`❌ [${this.prefix}] ${message}`, ...args);
    }
  }

  timing(label: string, startTime: number) {
    if (this.shouldLog('debug')) {
      const duration = Date.now() - startTime;
      console.log(`⏱️  [${this.prefix}] ${label}: ${duration}ms`);
    }
  }
}

/**
 * Create a debug-enabled database adapter wrapper
 */
export function createDebugDatabaseAdapter(originalAdapter: any) {
  const logger = new AuthDebugLogger('DB');

  if (!DEBUG_CONFIG.logDatabase) {
    return originalAdapter;
  }

  return new Proxy(originalAdapter, {
    get(target, prop) {
      const originalMethod = target[prop];

      if (typeof originalMethod === 'function') {
        return function(this: any, ...args: any[]) {
          const startTime = Date.now();
          logger.debug(`Database operation: ${String(prop)}`, {
            args: args.map(arg =>
              typeof arg === 'object' && arg !== null
                ? JSON.stringify(arg).substring(0, 200) + (JSON.stringify(arg).length > 200 ? '...' : '')
                : arg
            )
          });

          const result = originalMethod.apply(this, args);

          // Handle promises
          if (result && typeof result.then === 'function') {
            return result
              .then((res: any) => {
                logger.timing(`Database ${String(prop)}`, startTime);
                logger.debug(`Database result: ${String(prop)}`, {
                  result: typeof res === 'object' && res !== null
                    ? JSON.stringify(res).substring(0, 200) + (JSON.stringify(res).length > 200 ? '...' : '')
                    : res
                });
                return res;
              })
              .catch((error: any) => {
                logger.error(`Database error in ${String(prop)}:`, error);
                throw error;
              });
          }

          logger.timing(`Database ${String(prop)}`, startTime);
          return result;
        };
      }

      return originalMethod;
    }
  });
}

/**
 * Better-auth endpoint mapping for debugging
 */
export const BETTER_AUTH_ENDPOINTS = {
  // Authentication endpoints
  '/sign-in/email': 'Email/Password Sign In',
  '/sign-up/email': 'Email/Password Sign Up',
  '/sign-out': 'Sign Out',
  '/session': 'Get Session',
  '/verify-email': 'Email Verification',
  '/reset-password': 'Password Reset Request',
  '/change-password': 'Change Password',
  '/update-user': 'Update User Profile',

  // OAuth endpoints (if enabled)
  '/sign-in/google': 'Google OAuth Sign In',
  '/sign-in/github': 'GitHub OAuth Sign In',
  '/callback/google': 'Google OAuth Callback',
  '/callback/github': 'GitHub OAuth Callback',

  // Session management
  '/refresh': 'Refresh Session',
  '/revoke': 'Revoke Session',
  '/list-sessions': 'List User Sessions',
} as const;

/**
 * Get human-readable description for better-auth endpoint
 */
export function getEndpointDescription(endpoint: string): string {
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  return BETTER_AUTH_ENDPOINTS[cleanEndpoint as keyof typeof BETTER_AUTH_ENDPOINTS] || `Unknown endpoint: ${cleanEndpoint}`;
}

/**
 * Trace better-auth request flow
 */
export function traceBetterAuthFlow(endpoint: string, method: string, requestId: string) {
  const logger = new AuthDebugLogger(`BETTER-AUTH-${requestId}`);

  logger.info(`🎯 Better-auth endpoint resolution:`);
  logger.info(`   Raw endpoint: ${endpoint}`);
  logger.info(`   Description: ${getEndpointDescription(endpoint)}`);
  logger.info(`   HTTP Method: ${method}`);

  // Predict the flow based on endpoint
  switch (endpoint) {
    case '/sign-in/email':
      logger.info(`📋 Expected flow for email sign-in:`);
      logger.info(`   1. Validate email/password format`);
      logger.info(`   2. Query user table by email`);
      logger.info(`   3. Verify password hash`);
      logger.info(`   4. Create session record`);
      logger.info(`   5. Set session cookie`);
      logger.info(`   6. Return user data + session`);
      break;

    case '/sign-up/email':
      logger.info(`📋 Expected flow for email sign-up:`);
      logger.info(`   1. Validate email/password format`);
      logger.info(`   2. Check if email already exists`);
      logger.info(`   3. Hash password`);
      logger.info(`   4. Insert new user record`);
      logger.info(`   5. Create session record`);
      logger.info(`   6. Set session cookie`);
      logger.info(`   7. Return user data + session`);
      break;

    case '/session':
      logger.info(`📋 Expected flow for session check:`);
      logger.info(`   1. Extract session token from cookie`);
      logger.info(`   2. Query session table`);
      logger.info(`   3. Check session expiry`);
      logger.info(`   4. Query user data if session valid`);
      logger.info(`   5. Return session + user data`);
      break;

    case '/sign-out':
      logger.info(`📋 Expected flow for sign-out:`);
      logger.info(`   1. Extract session token from cookie`);
      logger.info(`   2. Delete session record from database`);
      logger.info(`   3. Clear session cookie`);
      logger.info(`   4. Return success response`);
      break;

    default:
      logger.info(`📋 Generic better-auth flow:`);
      logger.info(`   1. Parse request and extract parameters`);
      logger.info(`   2. Validate request format and origin`);
      logger.info(`   3. Execute endpoint-specific logic`);
      logger.info(`   4. Perform database operations as needed`);
      logger.info(`   5. Return appropriate response`);
  }

  return logger;
}

/**
 * Monitor better-auth configuration
 */
export function debugAuthConfiguration(config: any) {
  const logger = new AuthDebugLogger('CONFIG');

  logger.info(`🔧 Better-auth configuration:`);
  logger.info(`   Base URL: ${config.baseURL || 'not set'}`);
  logger.info(`   Secret configured: ${!!config.secret}`);
  logger.info(`   Database adapter: ${config.database ? 'configured' : 'missing'}`);
  logger.info(`   Email/Password enabled: ${config.emailAndPassword?.enabled || false}`);
  logger.info(`   Session expires in: ${config.session?.expiresIn || 'default'} seconds`);
  logger.info(`   Cookie cache enabled: ${config.session?.cookieCache?.enabled || false}`);

  if (config.trustedOrigins) {
    logger.info(`   Trusted origins (${config.trustedOrigins.length}):`);
    config.trustedOrigins.forEach((origin: string, index: number) => {
      logger.info(`     ${index + 1}. ${origin}`);
    });
  }

  return config;
}

/**
 * Environment variable debugging
 */
export function debugEnvironmentVariables() {
  const logger = new AuthDebugLogger('ENV');

  logger.info(`🌍 Environment variables for auth:`);
  logger.info(`   NODE_ENV: ${process.env['NODE_ENV'] || 'not set'}`);
  logger.info(`   BETTER_AUTH_URL: ${process.env['BETTER_AUTH_URL'] || 'not set'}`);
  logger.info(`   BETTER_AUTH_SECRET: ${process.env['BETTER_AUTH_SECRET'] ? '[SET]' : 'not set'}`);
  logger.info(`   BETTER_AUTH_TRUSTED_ORIGINS: ${process.env['BETTER_AUTH_TRUSTED_ORIGINS'] || 'not set'}`);
  logger.info(`   DATABASE_URL: ${process.env['DATABASE_URL'] ? '[SET]' : 'not set'}`);

  // Debug flags
  logger.info(`🐛 Debug configuration:`);
  logger.info(`   AUTH_DEBUG: ${process.env['AUTH_DEBUG'] || 'not set'}`);
  logger.info(`   AUTH_LOG_LEVEL: ${process.env['AUTH_LOG_LEVEL'] || 'not set'}`);
  logger.info(`   AUTH_LOG_DATABASE: ${process.env['AUTH_LOG_DATABASE'] || 'not set'}`);
  logger.info(`   AUTH_LOG_REQUESTS: ${process.env['AUTH_LOG_REQUESTS'] || 'not set'}`);
  logger.info(`   AUTH_LOG_RESPONSES: ${process.env['AUTH_LOG_RESPONSES'] || 'not set'}`);
}
