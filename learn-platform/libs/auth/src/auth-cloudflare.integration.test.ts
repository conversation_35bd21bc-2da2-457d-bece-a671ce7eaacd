/**
 * Integration tests for Cloudflare Workers authentication functionality
 *
 * This test suite focuses specifically on Cloudflare Workers-specific authentication
 * scenarios including environment bindings, CF-specific headers, edge session management,
 * and serverless environment handling for the better-auth integration.
 */

// Mock external dependencies BEFORE any imports
jest.mock('@learn-platform/db', () => ({
  db: {
    select: jest.fn().mockReturnThis(),
    from: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    values: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    set: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    execute: jest.fn().mockResolvedValue([]),
    query: jest.fn().mockResolvedValue([]),
  },
  users: {},
  session: {},
  account: {},
  verification: {},
}));

jest.mock('better-auth', () => ({
  betterAuth: jest.fn(() => ({
    api: {
      signUp: {
        email: jest.fn(),
      },
      signIn: {
        email: jest.fn(),
      },
      getSession: jest.fn(),
      updateSession: jest.fn(),
      signOut: jest.fn(),
    },
    handler: jest.fn(),
  })),
}));

jest.mock('better-auth/adapters/drizzle', () => ({
  drizzleAdapter: jest.fn(() => ({})),
}));

jest.mock('./auth', () => ({
  createAuth: jest.fn(),
  auth: {},
}));

import { createAuth } from './auth';
import { createAuthContextWithEnv, getSession } from './session';
import {
  createSessionResult,
  createTestUser,
  setupTestEnv,
  CLOUDFLARE_ENV,
} from './test-utils';

// Helper functions for this test file
function createMockAuthInstance() {
  return {
    api: {
      signUp: {
        email: jest.fn(),
      },
      signIn: {
        email: jest.fn(),
      },
      getSession: jest.fn(),
      updateSession: jest.fn(),
      signOut: jest.fn(),
    },
    handler: jest.fn(),
  };
}

function createMockCloudflareEnv(overrides: any = {}) {
  return {
    BETTER_AUTH_SECRET: 'cloudflare-secret-key-for-testing-min-32-chars',
    BETTER_AUTH_URL: 'https://cloudflare.example.com',
    DATABASE_URL: 'postgresql://user:pass@localhost:5432/testdb',
    ...overrides,
  };
}

function createCloudflareRequest(url = 'https://cloudflare.example.com', options: any = {}) {
  const defaultHeaders = {
    'CF-Connecting-IP': '***********',
    'CF-IPCountry': 'US',
    'CF-Ray': '8a1b2c3d4e5f6789-LAX',
    'CF-Visitor': '{"scheme":"https"}',
    'User-Agent': 'Mozilla/5.0 (compatible; CloudflareBot)',
    'X-Forwarded-Proto': 'https',
    'X-Real-IP': '***********',
    ...options.headers,
  };

  return new Request(url, {
    method: 'GET',
    headers: defaultHeaders,
    ...options,
  });
}

function mockCloudflareGlobals() {
  // Mock crypto.randomUUID for Cloudflare Workers
  global.crypto = {
    randomUUID: jest.fn(() => 'mock-uuid-1234-5678-9abc-def0'),
  } as any;

  // Mock globalThis behavior for Cloudflare Workers
  globalThis.process = undefined as any;
}

function mockSuccessfulAuth(authInstance: any, sessionResult?: any) {
  const result = sessionResult || createSessionResult();
  authInstance.api.getSession.mockResolvedValue(result);
  return result;
}

function mockFailedAuth(authInstance: any, errorMessage = 'Authentication failed') {
  authInstance.api.getSession.mockResolvedValue(null);
  authInstance.api.signIn.email.mockResolvedValue({
    data: null,
    error: { message: errorMessage },
  });
}

function createCloudflareAuthContext(env: any, sessionResult?: any) {
  const mockAuth = createMockAuthInstance();
  if (sessionResult !== undefined) {
    mockSuccessfulAuth(mockAuth, sessionResult);
  } else {
    mockSuccessfulAuth(mockAuth);
  }
  return { mockAuth, env };
}

describe('Cloudflare Workers Authentication Integration Tests', () => {
  let restoreEnv: () => void;
  let originalCrypto: any;
  let originalGlobalThis: any;

  beforeEach(() => {
    restoreEnv = setupTestEnv();
    originalCrypto = global.crypto;
    originalGlobalThis = globalThis.process;
    mockCloudflareGlobals();
    jest.clearAllMocks();
  });

  afterEach(() => {
    restoreEnv();
    if (originalCrypto) {
      global.crypto = originalCrypto;
    }
    if (originalGlobalThis !== undefined) {
      globalThis.process = originalGlobalThis;
    }
  });

  describe('Cloudflare Workers Environment Handling', () => {
    it('should create auth instance with Cloudflare environment bindings', () => {
      const cloudflareEnv = createMockCloudflareEnv();
      const mockAuth = createMockAuthInstance();

      (createAuth as jest.Mock).mockReturnValue(mockAuth);

      const authInstance = createAuth(cloudflareEnv);

      expect(authInstance).toBeDefined();
      expect(authInstance.api).toBeDefined();
      expect(authInstance.handler).toBeDefined();
    });

    it('should prioritize Cloudflare env over process.env', () => {
      const mockAuth = createMockAuthInstance();
      (createAuth as jest.Mock).mockReturnValue(mockAuth);

      const cloudflareEnv = createMockCloudflareEnv({
        BETTER_AUTH_SECRET: 'cloudflare-env-secret',
        BETTER_AUTH_URL: 'https://cloudflare.example.com',
      });

      const authInstance = createAuth(cloudflareEnv);

      expect(authInstance).toBeDefined();
      expect(createAuth).toHaveBeenCalledWith(cloudflareEnv);
      // The auth instance should use Cloudflare env values, not process.env
    });

    it('should handle missing environment variables with fallbacks', () => {
      const mockAuth = createMockAuthInstance();
      (createAuth as jest.Mock).mockReturnValue(mockAuth);

      const incompleteEnv = {
        // Missing BETTER_AUTH_SECRET
        BETTER_AUTH_URL: 'https://cloudflare.example.com',
      };

      // Should not throw and should use development fallback
      const authInstance = createAuth(incompleteEnv);

      expect(authInstance).toBeDefined();
    });

    it('should handle completely empty environment', () => {
      const mockAuth = createMockAuthInstance();
      (createAuth as jest.Mock).mockReturnValue(mockAuth);

      const emptyEnv = {};

      // Should not throw and should use development fallbacks
      const authInstance = createAuth(emptyEnv);

      expect(authInstance).toBeDefined();
    });

    it('should handle undefined environment parameter', () => {
      const mockAuth = createMockAuthInstance();
      (createAuth as jest.Mock).mockReturnValue(mockAuth);

      // Should fall back to process.env behavior
      const authInstance = createAuth(undefined);

      expect(authInstance).toBeDefined();
    });

    it('should detect production environment correctly', () => {
      const mockAuth = createMockAuthInstance();
      (createAuth as jest.Mock).mockReturnValue(mockAuth);

      const productionEnv = createMockCloudflareEnv({
        NODE_ENV: 'production',
      });

      const authInstance = createAuth(productionEnv);

      expect(authInstance).toBeDefined();
      // In production, secure cookies should be enabled
    });
  });

  describe('Authentication with Environment Bindings', () => {
    it('should create auth context with Cloudflare environment', async () => {
      const cloudflareEnv = createMockCloudflareEnv();
      const request = createCloudflareRequest();
      const sessionResult = createSessionResult();

      // Mock the auth instance creation and session retrieval
      const mockAuth = createMockAuthInstance();
      mockSuccessfulAuth(mockAuth, sessionResult);

      // Mock createAuth to return our mock instance
      (createAuth as jest.Mock).mockReturnValue(mockAuth);

      const authContext = await createAuthContextWithEnv(request, cloudflareEnv);

      expect(authContext).toBeDefined();
      expect(authContext.session).toEqual(sessionResult);
      expect(authContext.isAuthenticated).toBe(true);
      expect(authContext.userId).toBe(sessionResult!.user.id);
      expect(authContext.user).toEqual(sessionResult!.user);
    });

    it('should handle unauthenticated requests with environment bindings', async () => {
      const cloudflareEnv = createMockCloudflareEnv();
      const request = createCloudflareRequest();

      const mockAuth = createMockAuthInstance();
      mockFailedAuth(mockAuth);

      (createAuth as jest.Mock).mockReturnValue(mockAuth);

      const authContext = await createAuthContextWithEnv(request, cloudflareEnv);

      expect(authContext.session).toBeNull();
      expect(authContext.isAuthenticated).toBe(false);
      expect(authContext.userId).toBeNull();
      expect(authContext.user).toBeNull();
    });

    it('should pass environment bindings to auth instance creation', async () => {
      const cloudflareEnv = createMockCloudflareEnv();
      const request = createCloudflareRequest();

      const mockAuth = createMockAuthInstance();
      mockSuccessfulAuth(mockAuth);

      (createAuth as jest.Mock).mockReturnValue(mockAuth);

      await createAuthContextWithEnv(request, cloudflareEnv);

      expect(createAuth).toHaveBeenCalledWith(cloudflareEnv);
    });
  });

  describe('Request/Response Handling in Cloudflare Workers Context', () => {
    it('should handle Cloudflare-specific headers correctly', async () => {
      const cloudflareEnv = createMockCloudflareEnv();
      const request = createCloudflareRequest('https://cloudflare.example.com', {
        headers: {
          'CF-Connecting-IP': '***********00',
          'CF-IPCountry': 'GB',
          'CF-Ray': '1a2b3c4d5e6f7890-LHR',
          'CF-Visitor': '{"scheme":"https"}',
        },
      });

      const mockAuth = createMockAuthInstance();
      const sessionResult = createSessionResult({}, {
        ipAddress: '***********00',
        userAgent: 'Mozilla/5.0 (compatible; CloudflareBot)',
      });
      mockSuccessfulAuth(mockAuth, sessionResult);

      (createAuth as jest.Mock).mockReturnValue(mockAuth);

      const authContext = await createAuthContextWithEnv(request, cloudflareEnv);

      expect(authContext.session?.session.ipAddress).toBe('***********00');
      expect(mockAuth.api.getSession).toHaveBeenCalledWith({
        headers: request.headers,
        query: { disableCookieCache: false },
      });
    });

    it('should extract request metadata from Cloudflare headers', () => {
      const request = createCloudflareRequest('https://cloudflare.example.com', {
        headers: {
          'CF-Connecting-IP': '************',
          'CF-IPCountry': 'CA',
          'CF-Ray': '9z8y7x6w5v4u3210-YYZ',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        },
      });

      // Verify headers are properly set
      expect(request.headers.get('CF-Connecting-IP')).toBe('************');
      expect(request.headers.get('CF-IPCountry')).toBe('CA');
      expect(request.headers.get('CF-Ray')).toBe('9z8y7x6w5v4u3210-YYZ');
      expect(request.headers.get('User-Agent')).toContain('Mozilla/5.0');
    });

    it('should handle missing Cloudflare headers gracefully', async () => {
      const cloudflareEnv = createMockCloudflareEnv();
      const request = new Request('https://cloudflare.example.com', {
        headers: {
          // No CF-specific headers
          'User-Agent': 'Mozilla/5.0 (compatible; TestBot)',
        },
      });

      const mockAuth = createMockAuthInstance();
      mockSuccessfulAuth(mockAuth);

      (createAuth as jest.Mock).mockReturnValue(mockAuth);

      const authContext = await createAuthContextWithEnv(request, cloudflareEnv);

      expect(authContext).toBeDefined();
      // Should still work without CF headers
    });

    it('should handle multiple proxy headers correctly', async () => {
      const cloudflareEnv = createMockCloudflareEnv();
      const request = createCloudflareRequest('https://cloudflare.example.com', {
        headers: {
          'CF-Connecting-IP': '***********',
          'X-Forwarded-For': '***********, ********',
          'X-Real-IP': '***********',
        },
      });

      const mockAuth = createMockAuthInstance();
      mockSuccessfulAuth(mockAuth);

      (createAuth as jest.Mock).mockReturnValue(mockAuth);

      const authContext = await createAuthContextWithEnv(request, cloudflareEnv);

      expect(authContext).toBeDefined();
      // CF-Connecting-IP should take precedence
    });

    it('should handle HTTPS scheme detection from CF-Visitor', () => {
      const httpsRequest = createCloudflareRequest('https://cloudflare.example.com', {
        headers: {
          'CF-Visitor': '{"scheme":"https"}',
        },
      });

      const httpRequest = createCloudflareRequest('http://cloudflare.example.com', {
        headers: {
          'CF-Visitor': '{"scheme":"http"}',
        },
      });

      expect(httpsRequest.headers.get('CF-Visitor')).toContain('https');
      expect(httpRequest.headers.get('CF-Visitor')).toContain('http');
    });
  });

  describe('Session Management with Cloudflare Workers Limitations', () => {
    it('should handle session tokens across edge locations', async () => {
      const cloudflareEnv = createMockCloudflareEnv();
      const sessionToken = 'edge-session-token-123';

      const request = createCloudflareRequest('https://cloudflare.example.com', {
        headers: {
          'Cookie': `session=${sessionToken}`,
          'CF-Ray': 'edge-location-1-LAX',
        },
      });

      const mockAuth = createMockAuthInstance();
      const sessionResult = createSessionResult({}, { token: sessionToken });
      mockSuccessfulAuth(mockAuth, sessionResult);

      (createAuth as jest.Mock).mockReturnValue(mockAuth);

      const authContext = await createAuthContextWithEnv(request, cloudflareEnv);

      expect(authContext.session?.session.token).toBe(sessionToken);
      expect(authContext.isAuthenticated).toBe(true);
    });

    it('should handle session expiration in distributed environment', async () => {
      const cloudflareEnv = createMockCloudflareEnv();
      const expiredSessionResult = createSessionResult(
        {},
        { expiresAt: new Date(Date.now() - 1000) } // 1 second ago
      );

      const request = createCloudflareRequest();
      const mockAuth = createMockAuthInstance();
      mockAuth.api.getSession.mockResolvedValue(null); // Expired session returns null

      (createAuth as jest.Mock).mockReturnValue(mockAuth);

      const authContext = await createAuthContextWithEnv(request, cloudflareEnv);

      expect(authContext.session).toBeNull();
      expect(authContext.isAuthenticated).toBe(false);
    });

    it('should handle cookie cache configuration for edge environment', () => {
      const cloudflareEnv = createMockCloudflareEnv();

      const authInstance = createAuth(cloudflareEnv);

      expect(authInstance).toBeDefined();
      // Cookie cache should be enabled for edge performance
    });

    it('should handle cross-subdomain cookies in edge environment', async () => {
      const cloudflareEnv = createMockCloudflareEnv();
      const subdomainRequest = createCloudflareRequest('https://api.cloudflare.example.com');

      const mockAuth = createMockAuthInstance();
      mockSuccessfulAuth(mockAuth);

      (createAuth as jest.Mock).mockReturnValue(mockAuth);

      const authContext = await createAuthContextWithEnv(subdomainRequest, cloudflareEnv);

      expect(authContext).toBeDefined();
      // Should work across subdomains
    });

    it('should handle session updates in serverless environment', async () => {
      const cloudflareEnv = createMockCloudflareEnv();
      const request = createCloudflareRequest();

      const mockAuth = createMockAuthInstance();
      const sessionResult = createSessionResult();
      mockSuccessfulAuth(mockAuth, sessionResult);

      // Mock session update
      mockAuth.api.updateSession.mockResolvedValue({
        data: { ...sessionResult, session: { ...sessionResult!.session, updatedAt: new Date() } },
        error: null,
      });

      (createAuth as jest.Mock).mockReturnValue(mockAuth);

      const authContext = await createAuthContextWithEnv(request, cloudflareEnv);

      expect(authContext.session).toBeDefined();
      // Session should be retrievable even in serverless environment
    });
  });

  describe('Database Connections through Cloudflare Workers', () => {
    it('should handle database adapter with Cloudflare Workers', () => {
      const cloudflareEnv = createMockCloudflareEnv();

      const authInstance = createAuth(cloudflareEnv);

      expect(authInstance).toBeDefined();
      // Database adapter should be configured for PostgreSQL
    });

    it('should handle connection pooling in serverless environment', async () => {
      const cloudflareEnv = createMockCloudflareEnv();
      const request = createCloudflareRequest();

      const mockAuth = createMockAuthInstance();
      mockSuccessfulAuth(mockAuth);

      (createAuth as jest.Mock).mockReturnValue(mockAuth);

      const authContext = await createAuthContextWithEnv(request, cloudflareEnv);

      expect(authContext).toBeDefined();
      // Database connections should work in serverless environment
    });

    it('should handle transaction handling in edge environment', async () => {
      const cloudflareEnv = createMockCloudflareEnv();
      const request = createCloudflareRequest();

      const mockAuth = createMockAuthInstance();

      // Mock database transaction
      mockAuth.api.signUp = {
        email: jest.fn().mockResolvedValue({
          data: createSessionResult(),
          error: null,
        }),
      };

      (createAuth as jest.Mock).mockReturnValue(mockAuth);

      const result = await mockAuth.api.signUp.email({
        email: '<EMAIL>',
        password: 'SecurePassword123!',
        name: 'Cloudflare Test User',
      });

      expect(result.data).toBeDefined();
      // Database transactions should work in edge environment
    });

    it('should handle database errors gracefully in serverless environment', async () => {
      const cloudflareEnv = createMockCloudflareEnv();
      const request = createCloudflareRequest();

      const mockAuth = createMockAuthInstance();

      // Mock database error
      mockAuth.api.getSession.mockRejectedValue(new Error('Database connection failed'));

      (createAuth as jest.Mock).mockReturnValue(mockAuth);

      // The createAuthContextWithEnv should handle the error gracefully
      // and return a context with no session rather than throwing
      const authContext = await createAuthContextWithEnv(request, cloudflareEnv);

      expect(authContext.session).toBeNull();
      expect(authContext.isAuthenticated).toBe(false);
    });
  });

  describe('Security Considerations for Edge Environment', () => {
    it('should validate environment variable security', () => {
      const secureEnv = createMockCloudflareEnv({
        BETTER_AUTH_SECRET: 'very-secure-secret-key-for-production-min-32-chars',
      });

      const authInstance = createAuth(secureEnv);

      expect(authInstance).toBeDefined();
      // Should use secure secret from environment
    });

    it('should validate request origin in edge environment', async () => {
      const cloudflareEnv = createMockCloudflareEnv();
      const suspiciousRequest = createCloudflareRequest('https://malicious.example.com', {
        headers: {
          'Origin': 'https://malicious.example.com',
          'CF-Connecting-IP': '***********',
        },
      });

      const mockAuth = createMockAuthInstance();
      mockFailedAuth(mockAuth, 'Invalid origin');

      (createAuth as jest.Mock).mockReturnValue(mockAuth);

      const authContext = await createAuthContextWithEnv(suspiciousRequest, cloudflareEnv);

      expect(authContext.session).toBeNull();
      expect(authContext.isAuthenticated).toBe(false);
    });

    it('should handle rate limiting in distributed edge environment', async () => {
      const cloudflareEnv = createMockCloudflareEnv();
      const ipAddress = '***********00';

      // Simulate multiple requests from same IP across different edge locations
      const edgeLocations = ['LAX', 'LHR', 'NRT', 'FRA'];

      for (const location of edgeLocations) {
        const request = createCloudflareRequest('https://cloudflare.example.com', {
          headers: {
            'CF-Connecting-IP': ipAddress,
            'CF-Ray': `rate-limit-test-${location}`,
          },
        });

        const mockAuth = createMockAuthInstance();
        mockFailedAuth(mockAuth, 'Rate limited');

        (createAuth as jest.Mock).mockReturnValue(mockAuth);

        const authContext = await createAuthContextWithEnv(request, cloudflareEnv);

        expect(authContext.session).toBeNull();
      }
    });

    it('should handle CSRF protection with CF headers', async () => {
      const cloudflareEnv = createMockCloudflareEnv();
      const csrfRequest = createCloudflareRequest('https://cloudflare.example.com', {
        method: 'POST',
        headers: {
          'Origin': 'https://malicious.example.com',
          'CF-Connecting-IP': '***********',
          'CF-Ray': 'csrf-test-123-LAX',
        },
      });

      const mockAuth = createMockAuthInstance();
      mockFailedAuth(mockAuth, 'CSRF token validation failed');

      (createAuth as jest.Mock).mockReturnValue(mockAuth);

      const authContext = await createAuthContextWithEnv(csrfRequest, cloudflareEnv);

      expect(authContext.session).toBeNull();
      expect(authContext.isAuthenticated).toBe(false);
    });

    it('should handle session token security across edge locations', async () => {
      const cloudflareEnv = createMockCloudflareEnv();
      const secureToken = 'secure-edge-token-with-proper-entropy-123456789';

      const request = createCloudflareRequest('https://cloudflare.example.com', {
        headers: {
          'Cookie': `session=${secureToken}`,
          'CF-Ray': 'security-test-456-LHR',
        },
      });

      const mockAuth = createMockAuthInstance();
      const sessionResult = createSessionResult({}, { token: secureToken });
      mockSuccessfulAuth(mockAuth, sessionResult);

      (createAuth as jest.Mock).mockReturnValue(mockAuth);

      const authContext = await createAuthContextWithEnv(request, cloudflareEnv);

      expect(authContext.session?.session.token).toBe(secureToken);
      expect(authContext.session?.session.token.length).toBeGreaterThanOrEqual(32);
    });
  });

  describe('Performance and Compatibility', () => {
    it('should handle fast execution in edge environment', async () => {
      const cloudflareEnv = createMockCloudflareEnv();
      const request = createCloudflareRequest();

      const startTime = Date.now();

      const mockAuth = createMockAuthInstance();
      mockSuccessfulAuth(mockAuth);

      (createAuth as jest.Mock).mockReturnValue(mockAuth);

      const authContext = await createAuthContextWithEnv(request, cloudflareEnv);

      const endTime = Date.now();
      const executionTime = endTime - startTime;

      expect(authContext).toBeDefined();
      expect(executionTime).toBeLessThan(100); // Should be very fast
    });

    it('should be memory efficient in serverless simulation', () => {
      const cloudflareEnv = createMockCloudflareEnv();

      // Create multiple auth instances to test memory efficiency
      for (let i = 0; i < 10; i++) {
        const authInstance = createAuth(cloudflareEnv);
        expect(authInstance).toBeDefined();
      }

      // Should not cause memory issues
    });

    it('should handle concurrent requests efficiently', async () => {
      const cloudflareEnv = createMockCloudflareEnv();
      const requests = Array.from({ length: 5 }, (_, i) =>
        createCloudflareRequest(`https://cloudflare.example.com/request-${i}`)
      );

      const mockAuth = createMockAuthInstance();
      mockSuccessfulAuth(mockAuth);

      (createAuth as jest.Mock).mockReturnValue(mockAuth);

      const promises = requests.map(request =>
        createAuthContextWithEnv(request, cloudflareEnv)
      );

      const results = await Promise.all(promises);

      results.forEach(authContext => {
        expect(authContext).toBeDefined();
        expect(authContext.isAuthenticated).toBe(true);
      });
    });

    it('should maintain compatibility with existing better-auth + Drizzle ORM setup', () => {
      const cloudflareEnv = createMockCloudflareEnv();

      const authInstance = createAuth(cloudflareEnv);

      expect(authInstance).toBeDefined();
      expect(authInstance.api).toBeDefined();
      expect(authInstance.handler).toBeDefined();
      // Should be compatible with existing setup
    });

    it('should handle Cloudflare Workers runtime globals correctly', () => {
      // Test crypto.randomUUID mock
      expect(global.crypto.randomUUID()).toBe('mock-uuid-1234-5678-9abc-def0');

      // Test globalThis.process behavior
      expect(globalThis.process).toBeUndefined();

      // Should handle Cloudflare Workers environment correctly
    });
  });
});
