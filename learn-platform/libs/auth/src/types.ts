/**
 * TypeScript type definitions for the authentication library
 */

/**
 * User session information from better-auth
 */
export interface AuthSession {
  session: {
    id: string;
    userId: string;
    expiresAt: Date;
    token: string;
    createdAt: Date;
    updatedAt: Date;
    ipAddress?: string | null;
    userAgent?: string | null;
  };
  user: {
    id: string;
    email: string;
    name: string;
    avatar?: string | null;
    emailVerified: boolean;
    createdAt: Date;
    updatedAt: Date;
  };
}

/**
 * Session result from getSession function
 * Can be null if user is not authenticated
 */
export type SessionResult = AuthSession | null;

/**
 * Context for tRPC integration
 */
export interface AuthContext {
  session: SessionResult;
  isAuthenticated: boolean;
  userId: string | null;
  user: AuthSession['user'] | null;
}

/**
 * Options for getSession function
 */
export interface GetSessionOptions {
  disableCookieCache?: boolean;
}

/**
 * Better-auth configuration options
 */
export interface AuthConfig {
  database: unknown; // Drizzle database instance
  secret: string;
  baseURL?: string;
  trustedOrigins?: string[];
}

/**
 * Environment variables required for authentication
 */
export interface AuthEnvironment {
  BETTER_AUTH_SECRET: string;
  BETTER_AUTH_URL?: string;
  DATABASE_URL: string;
}
