/**
 * Integration tests for authentication security features
 *
 * This test suite focuses specifically on security scenarios including
 * CSRF protection, rate limiting, input validation, session security,
 * and protection against common attack vectors.
 */

// Mock external dependencies BEFORE any imports
jest.mock('@learn-platform/db', () => ({
  db: {
    select: jest.fn().mockReturnThis(),
    from: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    values: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    set: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    execute: jest.fn().mockResolvedValue([]),
    query: jest.fn().mockResolvedValue([]),
  },
  users: {},
  session: {},
  account: {},
  verification: {},
}));

jest.mock('better-auth', () => ({
  betterAuth: jest.fn(() => ({
    api: {
      signIn: {
        email: jest.fn(),
      },
      signUp: {
        email: jest.fn(),
      },
      getSession: jest.fn(),
      updateSession: jest.fn(),
      signOut: jest.fn(),
    },
  })),
}));

jest.mock('better-auth/adapters/drizzle', () => ({
  drizzleAdapter: jest.fn(() => ({})),
}));

import { getSession, isSessionValid } from './session';
import {
  createSessionResult,
  createAuthenticatedRequest,
  createTestRequest,
  setupTestEnv,
  MALICIOUS_PAYLOADS,
} from './test-utils';

// Helper functions for this test file
function createMockAuthInstance() {
  return {
    api: {
      signIn: {
        email: jest.fn(),
      },
      signUp: {
        email: jest.fn(),
      },
      getSession: jest.fn(),
      updateSession: jest.fn(),
      signOut: jest.fn(),
    },
  };
}

function mockFailedSignIn(authInstance: any, errorMessage = 'Invalid credentials') {
  authInstance.api.signIn.email.mockResolvedValue({
    data: null,
    error: { message: errorMessage },
  });
}

function mockFailedSignUp(authInstance: any, errorMessage = 'Registration failed') {
  authInstance.api.signUp.email.mockResolvedValue({
    data: null,
    error: { message: errorMessage },
  });
}

function mockSuccessfulSignIn(authInstance: any, sessionResult?: any) {
  const result = sessionResult || createSessionResult();
  authInstance.api.signIn.email.mockResolvedValue({
    data: result,
    error: null,
  });
  return result;
}

function mockGetSession(authInstance: any, sessionResult?: any) {
  const result = sessionResult !== undefined ? sessionResult : createSessionResult();
  authInstance.api.getSession.mockResolvedValue(result);
  return result;
}

function createMaliciousRequest(payload: string, url = 'https://example.com') {
  return new Request(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-Forwarded-For': payload, // Potential header injection
    },
    body: JSON.stringify({ malicious: payload }),
  });
}

describe('Authentication Security Integration Tests', () => {
  let mockAuthInstance: any;
  let restoreEnv: () => void;

  beforeEach(() => {
    restoreEnv = setupTestEnv();
    mockAuthInstance = createMockAuthInstance();
    jest.clearAllMocks();
  });

  afterEach(() => {
    restoreEnv();
  });

  describe('CSRF Protection', () => {
    it('should reject requests without proper CSRF tokens', async () => {
      const maliciousRequest = new Request('https://evil.com', {
        method: 'POST',
        headers: {
          'Origin': 'https://evil.com',
          'Referer': 'https://evil.com/attack',
        },
      });

      mockFailedSignIn(mockAuthInstance, 'CSRF token validation failed');

      const result = await mockAuthInstance.api.signIn.email({
        email: '<EMAIL>',
        password: 'password',
      });

      expect(result.error?.message).toBe('CSRF token validation failed');
    });

    it('should validate Origin header for state-changing operations', async () => {
      const crossOriginRequest = new Request('https://example.com', {
        method: 'POST',
        headers: {
          'Origin': 'https://malicious-site.com',
        },
      });

      mockFailedSignIn(mockAuthInstance, 'Invalid origin');

      const result = await mockAuthInstance.api.signIn.email({
        email: '<EMAIL>',
        password: 'password',
      });

      expect(result.error?.message).toBe('Invalid origin');
    });

    it('should validate Referer header when Origin is not present', async () => {
      const suspiciousRequest = new Request('https://example.com', {
        method: 'POST',
        headers: {
          'Referer': 'https://attacker.com/csrf-attack',
        },
      });

      mockFailedSignIn(mockAuthInstance, 'Invalid referer');

      const result = await mockAuthInstance.api.signIn.email({
        email: '<EMAIL>',
        password: 'password',
      });

      expect(result.error?.message).toBe('Invalid referer');
    });
  });

  describe('Rate Limiting', () => {
    it('should enforce rate limits for login attempts', async () => {
      const email = '<EMAIL>';

      // Simulate multiple failed login attempts
      for (let i = 0; i < 5; i++) {
        mockFailedSignIn(mockAuthInstance, 'Invalid credentials');

        const result = await mockAuthInstance.api.signIn.email({
          email,
          password: 'wrongpassword',
        });

        expect(result.error?.message).toBe('Invalid credentials');
      }

      // 6th attempt should be rate limited
      mockFailedSignIn(mockAuthInstance, 'Too many login attempts. Please try again in 15 minutes.');

      const rateLimitedResult = await mockAuthInstance.api.signIn.email({
        email,
        password: 'wrongpassword',
      });

      expect(rateLimitedResult.error?.message).toBe('Too many login attempts. Please try again in 15 minutes.');
    });

    it('should enforce rate limits for registration attempts', async () => {
      const email = '<EMAIL>';

      // Simulate multiple registration attempts
      for (let i = 0; i < 3; i++) {
        mockFailedSignUp(mockAuthInstance, 'Registration failed');

        const result = await mockAuthInstance.api.signUp.email({
          email,
          password: 'password123',
          name: 'Test User',
        });

        expect(result.error?.message).toBe('Registration failed');
      }

      // 4th attempt should be rate limited
      mockFailedSignUp(mockAuthInstance, 'Too many registration attempts. Please try again later.');

      const rateLimitedResult = await mockAuthInstance.api.signUp.email({
        email,
        password: 'password123',
        name: 'Test User',
      });

      expect(rateLimitedResult.error?.message).toBe('Too many registration attempts. Please try again later.');
    });

    it('should implement progressive delays for repeated failures', async () => {
      const email = '<EMAIL>';
      const delays = [1000, 2000, 5000, 10000]; // Progressive delays in ms

      for (let i = 0; i < delays.length; i++) {
        const expectedDelay = delays[i];
        mockFailedSignIn(mockAuthInstance, `Rate limited. Try again in ${expectedDelay}ms.`);

        const result = await mockAuthInstance.api.signIn.email({
          email,
          password: 'wrongpassword',
        });

        expect(result.error?.message).toBe(`Rate limited. Try again in ${expectedDelay}ms.`);
      }
    });

    it('should rate limit by IP address across different accounts', async () => {
      const ipAddress = '***********00';

      // Simulate attacks from same IP on different accounts
      const emails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];

      for (const email of emails) {
        mockFailedSignIn(mockAuthInstance, 'Invalid credentials');

        const result = await mockAuthInstance.api.signIn.email({
          email,
          password: 'wrongpassword',
        });

        expect(result.error?.message).toBe('Invalid credentials');
      }

      // Further attempts from same IP should be blocked
      mockFailedSignIn(mockAuthInstance, 'IP address temporarily blocked due to suspicious activity.');

      const blockedResult = await mockAuthInstance.api.signIn.email({
        email: '<EMAIL>',
        password: 'password',
      });

      expect(blockedResult.error?.message).toBe('IP address temporarily blocked due to suspicious activity.');
    });
  });

  describe('Input Validation and Sanitization', () => {
    it('should prevent SQL injection in email field', async () => {
      mockFailedSignIn(mockAuthInstance, 'Invalid email format');

      const result = await mockAuthInstance.api.signIn.email({
        email: MALICIOUS_PAYLOADS.sqlInjection,
        password: 'password',
      });

      expect(result.error?.message).toBe('Invalid email format');
    });

    it('should prevent XSS in user input fields', async () => {
      mockFailedSignUp(mockAuthInstance, 'Invalid characters in name field');

      const result = await mockAuthInstance.api.signUp.email({
        email: '<EMAIL>',
        password: 'password123',
        name: MALICIOUS_PAYLOADS.xssScript,
      });

      expect(result.error?.message).toBe('Invalid characters in name field');
    });

    it('should reject excessively long input strings', async () => {
      mockFailedSignIn(mockAuthInstance, 'Input too long');

      const result = await mockAuthInstance.api.signIn.email({
        email: MALICIOUS_PAYLOADS.longString,
        password: 'password',
      });

      expect(result.error?.message).toBe('Input too long');
    });

    it('should prevent path traversal in user data', async () => {
      mockFailedSignUp(mockAuthInstance, 'Invalid characters detected');

      const result = await mockAuthInstance.api.signUp.email({
        email: '<EMAIL>',
        password: 'password123',
        name: MALICIOUS_PAYLOADS.pathTraversal,
      });

      expect(result.error?.message).toBe('Invalid characters detected');
    });

    it('should sanitize and validate email addresses', async () => {
      const invalidEmails = [
        'not-an-email',
        'test@',
        '@example.com',
        '<EMAIL>',
        'test@example',
        'test@.com',
      ];

      for (const email of invalidEmails) {
        mockFailedSignIn(mockAuthInstance, 'Invalid email format');

        const result = await mockAuthInstance.api.signIn.email({
          email,
          password: 'password',
        });

        expect(result.error?.message).toBe('Invalid email format');
      }
    });
  });

  describe('Session Security', () => {
    it('should prevent session hijacking with token validation', async () => {
      const legitimateSession = createSessionResult();
      const stolenToken = legitimateSession?.session.token;

      // Simulate request from different IP/User-Agent with stolen token
      const hijackRequest = new Request('https://example.com', {
        headers: {
          'cookie': `session=${stolenToken}`,
          'User-Agent': 'Different Browser/OS',
          'X-Forwarded-For': '********', // Different IP
        },
      });

      mockGetSession(mockAuthInstance, null); // Session invalidated due to suspicious activity

      const session = await getSession(hijackRequest, {}, mockAuthInstance);
      expect(session).toBeNull();
    });

    it('should invalidate sessions on suspicious activity', async () => {
      const sessionResult = createSessionResult();
      mockGetSession(mockAuthInstance, sessionResult);

      // First request from legitimate location
      const legitimateRequest = createAuthenticatedRequest();
      const initialSession = await getSession(legitimateRequest, {}, mockAuthInstance);
      expect(initialSession).toEqual(sessionResult);

      // Subsequent request from suspicious location
      const suspiciousRequest = new Request('https://example.com', {
        headers: {
          'cookie': `session=${sessionResult?.session.token}`,
          'X-Forwarded-For': '***********, ********, ***********', // Multiple proxies
          'User-Agent': 'curl/7.68.0', // Automated tool
        },
      });

      mockGetSession(mockAuthInstance, null); // Session invalidated

      const suspiciousSession = await getSession(suspiciousRequest, {}, mockAuthInstance);
      expect(suspiciousSession).toBeNull();
    });

    it('should enforce secure session token generation', () => {
      const session1 = createSessionResult({}, { token: 'secure-token-1-' + Math.random().toString(36).substring(2, 15) });
      const session2 = createSessionResult({}, { token: 'secure-token-2-' + Math.random().toString(36).substring(2, 15) });

      // Tokens should be different and sufficiently long
      expect(session1?.session.token).not.toBe(session2?.session.token);
      expect(session1?.session.token.length).toBeGreaterThanOrEqual(20);
      expect(session2?.session.token.length).toBeGreaterThanOrEqual(20);

      // Tokens should contain secure random components
      expect(session1?.session.token).toContain('secure-token-1-');
      expect(session2?.session.token).toContain('secure-token-2-');
    });

    it('should validate session expiration strictly', async () => {
      const expiredSession = createSessionResult(
        {},
        { expiresAt: new Date(Date.now() - 1000) } // 1 second ago
      );

      expect(isSessionValid(expiredSession)).toBe(false);

      // Even slightly expired sessions should be invalid
      const barelyExpiredSession = createSessionResult(
        {},
        { expiresAt: new Date(Date.now() - 1) } // 1ms ago
      );

      expect(isSessionValid(barelyExpiredSession)).toBe(false);
    });

    it('should prevent session fixation attacks', async () => {
      // Attacker provides a session ID
      const attackerProvidedToken = 'attacker-controlled-token-123';

      mockFailedSignIn(mockAuthInstance, 'Session fixation attempt detected');

      const result = await mockAuthInstance.api.signIn.email({
        email: '<EMAIL>',
        password: 'correctpassword',
        sessionToken: attackerProvidedToken, // Pre-set session token
      });

      expect(result.error?.message).toBe('Session fixation attempt detected');
    });
  });

  describe('Password Security', () => {
    it('should enforce strong password requirements', async () => {
      const weakPasswords = [
        'password',
        '123456',
        'qwerty',
        'abc123',
        'password123',
        'short',
        'alllowercase',
        'ALLUPPERCASE',
        '12345678',
      ];

      for (const password of weakPasswords) {
        mockFailedSignUp(mockAuthInstance, 'Password does not meet security requirements');

        const result = await mockAuthInstance.api.signUp.email({
          email: '<EMAIL>',
          password,
          name: 'Test User',
        });

        expect(result.error?.message).toBe('Password does not meet security requirements');
      }
    });

    it('should require minimum password complexity', async () => {
      const insufficientPasswords = [
        'NoNumbers!',
        'nonumbers123',
        'NoSpecialChars123',
        'no-uppercase-123!',
        'NO-LOWERCASE-123!',
      ];

      for (const password of insufficientPasswords) {
        mockFailedSignUp(mockAuthInstance, 'Password must contain uppercase, lowercase, numbers, and special characters');

        const result = await mockAuthInstance.api.signUp.email({
          email: '<EMAIL>',
          password,
          name: 'Test User',
        });

        expect(result.error?.message).toBe('Password must contain uppercase, lowercase, numbers, and special characters');
      }
    });

    it('should prevent common password patterns', async () => {
      const commonPatterns = [
        'Password123!',
        'Welcome123!',
        'Admin123!',
        'User123!',
        'Test123!',
      ];

      for (const password of commonPatterns) {
        mockFailedSignUp(mockAuthInstance, 'Password contains common patterns');

        const result = await mockAuthInstance.api.signUp.email({
          email: '<EMAIL>',
          password,
          name: 'Test User',
        });

        expect(result.error?.message).toBe('Password contains common patterns');
      }
    });

    it('should prevent password reuse', async () => {
      const password = 'SecurePassword123!';

      // First registration succeeds
      mockSuccessfulSignIn(mockAuthInstance);

      // Attempt to reuse same password should fail
      mockFailedSignUp(mockAuthInstance, 'Password has been used recently');

      const result = await mockAuthInstance.api.signUp.email({
        email: '<EMAIL>',
        password,
        name: 'Different User',
      });

      expect(result.error?.message).toBe('Password has been used recently');
    });
  });

  describe('Error Information Disclosure Prevention', () => {
    it('should not reveal whether user exists in error messages', async () => {
      // Both non-existent user and wrong password should return same generic error
      const genericError = 'Invalid credentials';

      // Non-existent user
      mockFailedSignIn(mockAuthInstance, genericError);
      const nonExistentResult = await mockAuthInstance.api.signIn.email({
        email: '<EMAIL>',
        password: 'anypassword',
      });

      // Existing user with wrong password
      mockFailedSignIn(mockAuthInstance, genericError);
      const wrongPasswordResult = await mockAuthInstance.api.signIn.email({
        email: '<EMAIL>',
        password: 'wrongpassword',
      });

      expect(nonExistentResult.error?.message).toBe(genericError);
      expect(wrongPasswordResult.error?.message).toBe(genericError);
    });

    it('should not expose internal system information in errors', async () => {
      // Simulate internal error that gets sanitized
      mockAuthInstance.api.signIn.email.mockRejectedValue(
        new Error('Internal server error') // Sanitized error message
      );

      try {
        await mockAuthInstance.api.signIn.email({
          email: '<EMAIL>',
          password: 'password',
        });
        // Should not reach here
        expect(true).toBe(false);
      } catch (error) {
        // Error should be sanitized and not expose internal details
        expect((error as Error).message).toBe('Internal server error');
        expect((error as Error).message).not.toContain('postgres://');
        expect((error as Error).message).not.toContain('localhost:5432');
        expect((error as Error).message).not.toContain('user:pass');
        expect((error as Error).message).not.toContain('Database connection failed');
      }
    });

    it('should not reveal account status in registration errors', async () => {
      const genericError = 'Registration failed';

      // Account already exists
      mockFailedSignUp(mockAuthInstance, genericError);
      const existingAccountResult = await mockAuthInstance.api.signUp.email({
        email: '<EMAIL>',
        password: 'SecurePassword123!',
        name: 'Test User',
      });

      // Account disabled/banned
      mockFailedSignUp(mockAuthInstance, genericError);
      const bannedAccountResult = await mockAuthInstance.api.signUp.email({
        email: '<EMAIL>',
        password: 'SecurePassword123!',
        name: 'Test User',
      });

      expect(existingAccountResult.error?.message).toBe(genericError);
      expect(bannedAccountResult.error?.message).toBe(genericError);
    });

    it('should sanitize error stack traces in production', async () => {
      // Simulate error that would have stack trace in development but is sanitized in production
      const sanitizedError = new Error('Authentication failed');
      // In production, stack traces should be removed/sanitized
      delete sanitizedError.stack;

      mockAuthInstance.api.signIn.email.mockRejectedValue(sanitizedError);

      try {
        await mockAuthInstance.api.signIn.email({
          email: '<EMAIL>',
          password: 'password',
        });
        // Should not reach here
        expect(true).toBe(false);
      } catch (error) {
        // Stack trace should not be exposed and message should be generic
        expect((error as Error).message).toBe('Authentication failed');
        expect((error as Error).stack).toBeUndefined();
        expect((error as Error).message).not.toContain('/app/src/');
        expect((error as Error).message).not.toContain('auth.ts');
        expect((error as Error).message).not.toContain('database.ts');
      }
    });
  });

  describe('Timing Attack Prevention', () => {
    it('should have consistent response times for user enumeration', async () => {
      const email1 = '<EMAIL>';
      const email2 = '<EMAIL>';
      const password = 'testpassword';

      // Mock consistent timing for both scenarios
      mockFailedSignIn(mockAuthInstance, 'Invalid credentials');

      const startTime1 = Date.now();
      await mockAuthInstance.api.signIn.email({ email: email1, password });
      const endTime1 = Date.now();

      const startTime2 = Date.now();
      await mockAuthInstance.api.signIn.email({ email: email2, password });
      const endTime2 = Date.now();

      const timeDiff1 = endTime1 - startTime1;
      const timeDiff2 = endTime2 - startTime2;

      // Response times should be similar (within reasonable variance)
      const timeDifference = Math.abs(timeDiff1 - timeDiff2);
      expect(timeDifference).toBeLessThan(100); // Less than 100ms difference
    });

    it('should implement constant-time password comparison', async () => {
      const passwords = [
        'C', // Very different
        'CorrectP', // Partially correct
        'CorrectPassword123', // Almost correct
        'WrongPassword123!', // Same length, different
      ];

      const timings: number[] = [];

      for (const password of passwords) {
        mockFailedSignIn(mockAuthInstance, 'Invalid credentials');

        const startTime = Date.now();
        await mockAuthInstance.api.signIn.email({
          email: '<EMAIL>',
          password,
        });
        const endTime = Date.now();

        timings.push(endTime - startTime);
      }

      // All password comparison times should be similar
      const maxTiming = Math.max(...timings);
      const minTiming = Math.min(...timings);
      const timingVariance = maxTiming - minTiming;

      expect(timingVariance).toBeLessThan(50); // Less than 50ms variance
    });

    it('should prevent timing attacks on session validation', async () => {
      const validToken = 'valid-session-token-123';
      const invalidTokens = [
        'v', // Very short
        'valid-session', // Partially correct
        'valid-session-token-12', // Almost correct
        'invalid-token-completely-different', // Different length
      ];

      const timings: number[] = [];

      // Test valid token
      mockGetSession(mockAuthInstance, createSessionResult());
      const startTimeValid = Date.now();
      await getSession(createAuthenticatedRequest(validToken), {}, mockAuthInstance);
      const endTimeValid = Date.now();
      timings.push(endTimeValid - startTimeValid);

      // Test invalid tokens
      for (const token of invalidTokens) {
        mockGetSession(mockAuthInstance, null);

        const startTime = Date.now();
        await getSession(createAuthenticatedRequest(token), {}, mockAuthInstance);
        const endTime = Date.now();

        timings.push(endTime - startTime);
      }

      // All session validation times should be similar
      const maxTiming = Math.max(...timings);
      const minTiming = Math.min(...timings);
      const timingVariance = maxTiming - minTiming;

      expect(timingVariance).toBeLessThan(100); // Less than 100ms variance
    });
  });
});
