/**
 * Session management utilities for tRPC integration
 *
 * This module provides functions for retrieving and managing user sessions
 * in the context of tRPC procedures and Cloudflare Workers.
 */

import { auth, createAuth } from './auth';
import type { GetSessionOptions, SessionResult, AuthContext } from './types';

/**
 * Safely access process.env in environments where it might not exist
 */
function getProcessEnv(key: string): string | undefined {
  try {
    return (globalThis as any).process?.env?.[key];
  } catch {
    return undefined;
  }
}

/**
 * Retrieve user session from request headers
 *
 * This function is designed to be used in tRPC context creation
 * and other server-side authentication checks.
 *
 * @param request - Request object containing headers and cookies
 * @param options - Additional options for session retrieval
 * @param authInstance - Optional auth instance (for Cloudflare Workers with env)
 * @returns Session data or null if not authenticated
 */
export async function getSession(
  request: Request,
  options: Partial<GetSessionOptions> = {},
  authInstance?: any
): Promise<SessionResult> {
  try {
    const authToUse = authInstance || auth;
    const session = await authToUse.api.getSession({
      headers: request.headers,
      query: {
        disableCookieCache: options.disableCookieCache || false,
      },
    });

    return session as SessionResult;
  } catch (error) {
    // Log error in development, but don't expose details in production
    if (getProcessEnv('NODE_ENV') === 'development') {
      console.error('Failed to retrieve session:', error);
    }

    return null;
  }
}

/**
 * Create authentication context for tRPC
 *
 * This function creates a standardized auth context object
 * that can be used throughout tRPC procedures.
 *
 * @param request - Request object
 * @param authInstance - Optional auth instance (for Cloudflare Workers with env)
 * @returns Authentication context with session data and utility functions
 */
export async function createAuthContext(
  request: Request,
  authInstance?: any
): Promise<AuthContext> {
  const session = await getSession(request, {}, authInstance);

  return {
    session,
    isAuthenticated: session !== null,
    userId: session?.user?.id || null,
    user: session?.user || null,
  };
}

/**
 * Create authentication context for Cloudflare Workers
 * This is a convenience function that creates an auth instance with the env binding
 *
 * @param request - Request object
 * @param env - Cloudflare Workers environment bindings
 * @returns Authentication context with session data and utility functions
 */
export async function createAuthContextWithEnv(
  request: Request,
  env: any
): Promise<AuthContext> {
  const authInstance = createAuth(env);
  return createAuthContext(request, authInstance);
}

/**
 * Utility function to check if a session is valid and not expired
 *
 * @param session - Session object to validate
 * @returns True if session is valid, false otherwise
 */
export function isSessionValid(session: SessionResult): boolean {
  if (!session) {
    return false;
  }

  const now = new Date();
  const expiresAt = new Date(session.session.expiresAt);

  return expiresAt > now;
}

/**
 * Extract user ID from session with type safety
 *
 * @param session - Session object
 * @returns User ID or null if not available
 */
export function getUserId(session: SessionResult): string | null {
  return session?.user?.id || null;
}

/**
 * Extract user email from session with type safety
 *
 * @param session - Session object
 * @returns User email or null if not available
 */
export function getUserEmail(session: SessionResult): string | null {
  return session?.user?.email || null;
}

/**
 * Check if user has verified their email
 *
 * @param session - Session object
 * @returns True if email is verified, false otherwise
 */
export function isEmailVerified(session: SessionResult): boolean {
  return session?.user?.emailVerified || false;
}

/**
 * Utility function for requiring authentication in tRPC procedures
 * Throws an error if the user is not authenticated
 *
 * @param session - Session object to check
 * @throws Error if user is not authenticated
 */
export function requireAuth(session: SessionResult): asserts session is NonNullable<SessionResult> {
  if (!session) {
    throw new Error('Authentication required');
  }

  if (!isSessionValid(session)) {
    throw new Error('Session expired');
  }
}
