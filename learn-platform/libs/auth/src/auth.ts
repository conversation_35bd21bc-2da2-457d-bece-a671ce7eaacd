/**
 * Better-auth configuration for the Learn Platform
 *
 * This module sets up better-auth with Drizzle ORM integration
 * for use in Cloudflare Workers with Hono framework.
 */

import { betterAuth } from 'better-auth';
import { drizzleAdapter } from 'better-auth/adapters/drizzle';
import { db, createDatabaseConnectionForWorkers } from '@learn-platform/db';
import * as authSchema from '@learn-platform/db';
import type { AuthConfig } from './types';
import {
  AuthDebugLogger,
  createDebugDatabaseAdapter,
  debugAuthConfiguration,
  debugEnvironmentVariables,
  DEBUG_CONFIG
} from './debug';

/**
 * Cloudflare Workers environment interface
 */
interface CloudflareEnv {
  BETTER_AUTH_SECRET?: string;
  BETTER_AUTH_URL?: string;
  BETTER_AUTH_TRUSTED_ORIGINS?: string;
  VERCEL_URL?: string;
  [key: string]: any;
}

/**
 * Safely access process.env in environments where it might not exist
 */
function getProcessEnv(key: string): string | undefined {
  try {
    return (globalThis as any).process?.env?.[key];
  } catch {
    return undefined;
  }
}



/**
 * Get authentication configuration from environment variables
 * Supports both Node.js process.env and Cloudflare Workers env binding
 */
function getAuthConfig(env?: CloudflareEnv): AuthConfig {
  // Try Cloudflare Workers env first, then fall back to process.env (if available)
  const secret = env?.BETTER_AUTH_SECRET || getProcessEnv('BETTER_AUTH_SECRET');
  const baseURL = env?.BETTER_AUTH_URL || getProcessEnv('BETTER_AUTH_URL') || 'http://localhost:3000';

  // Get custom trusted origins from environment variable
  const customOrigins = env?.['BETTER_AUTH_TRUSTED_ORIGINS'] || getProcessEnv('BETTER_AUTH_TRUSTED_ORIGINS');
  const trustedOrigins = customOrigins ? customOrigins.split(',').map((origin: string) => origin.trim()).filter(Boolean) : [];

  // Filter out origins that are already hardcoded to avoid unnecessary duplicates
  const hardcodedOrigins = [
    'http://localhost:3000',
    'http://localhost:3001',
    'http://localhost:8787',
    'http://127.0.0.1:8787',
    'https://kwaci-learning.bmbn.dev'
  ];
  const filteredTrustedOrigins = trustedOrigins.filter(origin => !hardcodedOrigins.includes(origin));

  // Enhanced debug logging for environment variable detection
  const isCloudflareWorkers = !!env;
  const nodeEnv = getProcessEnv('NODE_ENV');
  console.log(`[Better Auth Config] Environment: ${isCloudflareWorkers ? 'Cloudflare Workers' : 'Node.js'}, NODE_ENV: ${nodeEnv}`);
  console.log(`[Better Auth Config] Base URL: ${baseURL}`);
  console.log(`[Better Auth Config] Secret set: ${!!secret}`);
  console.log(`[Better Auth Config] Custom trusted origins: ${customOrigins || 'none'}`);

  // Enhanced debugging for Cloudflare Workers environment
  if (isCloudflareWorkers && env) {
    console.log(`[Better Auth Config] Cloudflare Workers env keys:`, Object.keys(env));
    console.log(`[Better Auth Config] BETTER_AUTH_URL from env:`, env.BETTER_AUTH_URL);
    console.log(`[Better Auth Config] BETTER_AUTH_TRUSTED_ORIGINS from env:`, env.BETTER_AUTH_TRUSTED_ORIGINS);
    console.log(`[Better Auth Config] BETTER_AUTH_SECRET exists in env:`, !!env.BETTER_AUTH_SECRET);
  }

  if (!secret) {
    // For development, use a default secret
    console.warn('BETTER_AUTH_SECRET not set, using default secret for development');
    const defaultSecret = 'dev-secret-key-change-this-in-production-min-32-chars';
    return {
      database: db,
      secret: defaultSecret,
      baseURL,
      trustedOrigins: filteredTrustedOrigins,
    };
  }

  return {
    database: db,
    secret,
    baseURL,
    trustedOrigins: filteredTrustedOrigins,
  };
}

/**
 * Lazy-loaded auth configuration
 * Note: We don't cache the config to ensure environment variables are read fresh each time
 * This is important for different deployment contexts (Vercel vs Cloudflare Workers)
 */
function getOrCreateAuthConfig(env?: CloudflareEnv): AuthConfig {
  // Always create fresh config to ensure environment variables are read correctly
  return getAuthConfig(env);
}

/**
 * Create better-auth instance with optional Cloudflare Workers environment
 */
function createAuth(env?: CloudflareEnv) {
  const logger = new AuthDebugLogger('CREATE-AUTH');

  // Debug environment variables
  if (DEBUG_CONFIG.enabled) {
    debugEnvironmentVariables();
  }

  const config = getOrCreateAuthConfig(env);
  logger.info(`🏗️  Creating better-auth instance`);
  logger.info(`   Environment type: ${env ? 'Cloudflare Workers' : 'Node.js'}`);

  // Use Cloudflare Workers database connection if env is provided
  const database = env ? createDatabaseConnectionForWorkers(env).db : db;
  logger.info(`   Database: ${env ? 'Cloudflare Workers connection' : 'Default connection'}`);

  // Create debug-enabled database adapter
  const baseAdapter = drizzleAdapter(database, {
    provider: 'pg', // PostgreSQL
    schema: authSchema,
  });

  const debugAdapter = DEBUG_CONFIG.logDatabase ? createDebugDatabaseAdapter(baseAdapter) : baseAdapter;
  logger.info(`   Database adapter: ${DEBUG_CONFIG.logDatabase ? 'Debug-enabled' : 'Standard'} Drizzle adapter`);

  const authConfig = {
    database: debugAdapter,
    secret: config.secret,
    baseURL: config.baseURL,
    trustedOrigins: (() => {
      const origins = [
        // Development origins
        'http://localhost:3000',
        'http://localhost:3001',
        'http://localhost:8787',
        'http://127.0.0.1:8787',
        // Production origins
        'https://kwaci-learning.bmbn.dev',
        // Include baseURL if it's different from the above
        ...(config.baseURL && !config.baseURL.includes('localhost') ? [config.baseURL] : []),
        // Include any custom trusted origins from environment (filtered to avoid duplicates)
        ...(config.trustedOrigins || []),
      ];

      // Enhanced debugging for trusted origins configuration
      console.log(`[Better Auth Debug] Raw origins before deduplication:`, origins);
      console.log(`[Better Auth Debug] Config baseURL: ${config.baseURL}`);
      console.log(`[Better Auth Debug] Config trustedOrigins: ${JSON.stringify(config.trustedOrigins)}`);
      console.log(`[Better Auth Debug] Origins array length before deduplication: ${origins.length}`);

      // Remove duplicates
      const uniqueOrigins = [...new Set(origins)];

      console.log(`[Better Auth Debug] Origins array length after deduplication: ${uniqueOrigins.length}`);
      console.log(`[Better Auth] Configured trusted origins: ${uniqueOrigins.join(', ')}`);

      // Additional validation
      if (uniqueOrigins.length !== origins.length) {
        const duplicates = origins.filter((item, index) => origins.indexOf(item) !== index);
        console.warn(`[Better Auth Warning] Duplicates removed: ${[...new Set(duplicates)].join(', ')}`);
      }

      // Ensure production URL is included
      if (!uniqueOrigins.includes('https://kwaci-learning.bmbn.dev')) {
        console.error(`[Better Auth Error] Production URL missing from trusted origins!`);
      }

      return uniqueOrigins;
    })(),

    // Email and password authentication
    emailAndPassword: {
      enabled: true,
      requireEmailVerification: false, // Can be enabled later
    },

    // Session configuration optimized for Cloudflare Workers
    session: {
      expiresIn: 60 * 60 * 24 * 7, // 7 days
      updateAge: 60 * 60 * 24, // 1 day
      cookieCache: {
        enabled: true,
        maxAge: 5 * 60, // 5 minutes
      },
    },

    // Advanced configuration for Cloudflare Workers
    advanced: {
      crossSubDomainCookies: {
        enabled: false, // Enable if using subdomains
      },
      defaultCookieAttributes: {
        sameSite: 'lax' as const,
        secure: getProcessEnv('NODE_ENV') === 'production',
        httpOnly: true,
      },
    },

    // User configuration
    user: {
      additionalFields: {
        avatar: {
          type: 'string' as const,
          required: false,
        },
      },
    },
  };

  // Debug the final configuration
  if (DEBUG_CONFIG.enabled) {
    debugAuthConfiguration(authConfig);
  }

  logger.info(`✅ Better-auth instance created successfully`);

  return betterAuth(authConfig);
}

/**
 * Lazy-loaded default auth instance for backward compatibility
 */
let _defaultAuth: any = null;

function getDefaultAuth() {
  if (!_defaultAuth) {
    _defaultAuth = createAuth();
  }
  return _defaultAuth;
}

/**
 * Default auth instance for backward compatibility
 * This will use process.env and show the warning if BETTER_AUTH_SECRET is not set
 */
export const auth = new Proxy({} as any, {
  get(_target, prop) {
    return getDefaultAuth()[prop];
  }
});

/**
 * Create auth instance with Cloudflare Workers environment
 * Use this in your Cloudflare Workers to pass the env binding
 */
export { createAuth };



/**
 * Type inference for better-auth
 */
export type Auth = ReturnType<typeof createAuth>;
export type Session = Auth['$Infer']['Session'];
export type User = Session['user'];
// Test comment for affected detection - Sat Jun 14 07:45:38 WIB 2025
