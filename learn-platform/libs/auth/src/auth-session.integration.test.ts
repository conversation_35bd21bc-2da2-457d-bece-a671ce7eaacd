/**
 * Integration tests for authentication session management
 *
 * This test suite focuses specifically on session creation, validation,
 * expiration, and session-related operations.
 */

// Mock external dependencies BEFORE any imports
jest.mock('@learn-platform/db', () => ({
  db: {
    select: jest.fn().mockReturnThis(),
    from: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    values: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    set: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    execute: jest.fn().mockResolvedValue([]),
    query: jest.fn().mockResolvedValue([]),
  },
  users: {},
  session: {},
  account: {},
  verification: {},
}));

jest.mock('better-auth', () => ({
  betterAuth: jest.fn(() => ({
    api: {
      getSession: jest.fn(),
      updateSession: jest.fn(),
      deleteSession: jest.fn(),
      signOut: jest.fn(),
    },
  })),
}));

jest.mock('better-auth/adapters/drizzle', () => ({
  drizzleAdapter: jest.fn(() => ({})),
}));

import { getSession, createAuthContext, isSessionValid, requireAuth } from './session';
import {
  createSessionResult,
  createExpiredSessionResult,
  createTestRequest,
  createAuthenticatedRequest,
  setupTestEnv,
} from './test-utils';

// Helper functions for this test file
function createMockAuthInstance() {
  return {
    api: {
      getSession: jest.fn(),
      updateSession: jest.fn(),
      deleteSession: jest.fn(),
      signOut: jest.fn(),
    },
  };
}

function mockGetSession(authInstance: any, sessionResult?: any) {
  const result = sessionResult !== undefined ? sessionResult : createSessionResult();
  authInstance.api.getSession.mockResolvedValue(result);
  return result;
}

function mockSignOut(authInstance: any, success = true) {
  authInstance.api.signOut.mockResolvedValue({ success });
}

describe('Authentication Session Integration Tests', () => {
  let mockAuthInstance: any;
  let restoreEnv: () => void;

  beforeEach(() => {
    restoreEnv = setupTestEnv();
    mockAuthInstance = createMockAuthInstance();
    jest.clearAllMocks();
  });

  afterEach(() => {
    restoreEnv();
  });

  describe('Session Creation and Validation', () => {
    it('should create and validate valid sessions', async () => {
      const sessionResult = createSessionResult();
      mockGetSession(mockAuthInstance, sessionResult);

      const request = createAuthenticatedRequest();
      const session = await getSession(request, {}, mockAuthInstance);

      expect(session).toEqual(sessionResult);
      expect(isSessionValid(session)).toBe(true);
    });

    it('should handle session retrieval with different tokens', async () => {
      const sessionResult1 = createSessionResult(
        { id: 'user-1', email: '<EMAIL>' },
        { id: 'session-1', token: 'token-1' }
      );
      const sessionResult2 = createSessionResult(
        { id: 'user-2', email: '<EMAIL>' },
        { id: 'session-2', token: 'token-2' }
      );

      // Mock different responses for different tokens
      mockAuthInstance.api.getSession
        .mockResolvedValueOnce(sessionResult1)
        .mockResolvedValueOnce(sessionResult2);

      const request1 = createAuthenticatedRequest('token-1');
      const request2 = createAuthenticatedRequest('token-2');

      const session1 = await getSession(request1, {}, mockAuthInstance);
      const session2 = await getSession(request2, {}, mockAuthInstance);

      expect(session1?.session.id).toBe('session-1');
      expect(session2?.session.id).toBe('session-2');
      expect(session1?.user.email).toBe('<EMAIL>');
      expect(session2?.user.email).toBe('<EMAIL>');
    });

    it('should handle requests without session cookies', async () => {
      mockGetSession(mockAuthInstance, null);

      const request = createTestRequest();
      const session = await getSession(request, {}, mockAuthInstance);

      expect(session).toBeNull();
      expect(isSessionValid(session)).toBe(false);
    });

    it('should handle invalid session tokens', async () => {
      mockGetSession(mockAuthInstance, null);

      const request = createAuthenticatedRequest('invalid-token');
      const session = await getSession(request, {}, mockAuthInstance);

      expect(session).toBeNull();
    });
  });

  describe('Session Expiration', () => {
    it('should detect expired sessions', () => {
      const expiredSession = createExpiredSessionResult();

      expect(isSessionValid(expiredSession)).toBe(false);
    });

    it('should validate non-expired sessions', () => {
      const validSession = createSessionResult();

      expect(isSessionValid(validSession)).toBe(true);
    });

    it('should handle session expiration in auth context', async () => {
      const expiredSession = createExpiredSessionResult();
      mockGetSession(mockAuthInstance, expiredSession);

      const request = createAuthenticatedRequest();
      const context = await createAuthContext(request, mockAuthInstance);

      // Context should still contain the session data, but isSessionValid should return false
      expect(context.session).toEqual(expiredSession);
      expect(isSessionValid(context.session)).toBe(false);
    });

    it('should require valid session for protected operations', () => {
      const expiredSession = createExpiredSessionResult();

      expect(() => requireAuth(expiredSession)).toThrow('Session expired');
    });
  });

  describe('Session Updates and Refresh', () => {
    it('should handle session refresh', async () => {
      const originalSession = createSessionResult();
      const refreshedSession = createSessionResult(
        originalSession?.user,
        {
          ...originalSession?.session,
          updatedAt: new Date(),
          expiresAt: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14 days
        }
      );

      mockAuthInstance.api.updateSession.mockResolvedValue({
        data: refreshedSession,
        error: null,
      });

      const result = await mockAuthInstance.api.updateSession({
        sessionId: originalSession?.session.id,
      });

      expect(result.data).toEqual(refreshedSession);
      expect(result.data?.session.expiresAt.getTime()).toBeGreaterThan(
        originalSession?.session.expiresAt.getTime() || 0
      );
    });

    it('should handle session update failures', async () => {
      mockAuthInstance.api.updateSession.mockResolvedValue({
        data: null,
        error: { message: 'Session not found' },
      });

      const result = await mockAuthInstance.api.updateSession({
        sessionId: 'non-existent-session',
      });

      expect(result.data).toBeNull();
      expect(result.error?.message).toBe('Session not found');
    });
  });

  describe('Session Termination', () => {
    it('should handle session logout', async () => {
      mockSignOut(mockAuthInstance, true);

      const result = await mockAuthInstance.api.signOut();
      expect(result.success).toBe(true);
    });

    it('should invalidate session after logout', async () => {
      const sessionResult = createSessionResult();

      // First, session exists
      mockGetSession(mockAuthInstance, sessionResult);
      const request = createAuthenticatedRequest();
      const initialSession = await getSession(request, {}, mockAuthInstance);
      expect(initialSession).toEqual(sessionResult);

      // After logout, session should be null
      mockSignOut(mockAuthInstance, true);
      mockGetSession(mockAuthInstance, null);

      await mockAuthInstance.api.signOut();
      const postLogoutSession = await getSession(request, {}, mockAuthInstance);
      expect(postLogoutSession).toBeNull();
    });

    it('should handle session deletion', async () => {
      mockAuthInstance.api.deleteSession.mockResolvedValue({
        success: true,
      });

      const result = await mockAuthInstance.api.deleteSession({
        sessionId: 'test-session-id',
      });

      expect(result.success).toBe(true);
    });

    it('should handle logout failures', async () => {
      mockSignOut(mockAuthInstance, false);

      const result = await mockAuthInstance.api.signOut();
      expect(result.success).toBe(false);
    });
  });

  describe('Session Persistence', () => {
    it('should maintain session across multiple requests', async () => {
      const sessionResult = createSessionResult();
      mockGetSession(mockAuthInstance, sessionResult);

      const request1 = createAuthenticatedRequest('test-token');
      const request2 = createAuthenticatedRequest('test-token');

      const session1 = await getSession(request1, {}, mockAuthInstance);
      const session2 = await getSession(request2, {}, mockAuthInstance);

      expect(session1).toEqual(session2);
      expect(session1).toEqual(sessionResult);
    });

    it('should handle concurrent session requests', async () => {
      const sessionResult = createSessionResult();
      mockGetSession(mockAuthInstance, sessionResult);

      const requests = Array.from({ length: 5 }, (_, i) =>
        createAuthenticatedRequest(`token-${i}`)
      );

      const sessionPromises = requests.map(request =>
        getSession(request, {}, mockAuthInstance)
      );

      const sessions = await Promise.all(sessionPromises);

      sessions.forEach(session => {
        expect(session).toEqual(sessionResult);
      });
    });
  });

  describe('Authentication Context with Sessions', () => {
    it('should create authenticated context with valid session', async () => {
      const sessionResult = createSessionResult();
      mockGetSession(mockAuthInstance, sessionResult);

      const request = createAuthenticatedRequest();
      const context = await createAuthContext(request, mockAuthInstance);

      expect(context.isAuthenticated).toBe(true);
      expect(context.userId).toBe(sessionResult?.user.id);
      expect(context.user).toEqual(sessionResult?.user);
      expect(context.session).toEqual(sessionResult);
    });

    it('should create unauthenticated context with null session', async () => {
      mockGetSession(mockAuthInstance, null);

      const request = createTestRequest();
      const context = await createAuthContext(request, mockAuthInstance);

      expect(context.isAuthenticated).toBe(false);
      expect(context.userId).toBeNull();
      expect(context.user).toBeNull();
      expect(context.session).toBeNull();
    });

    it('should handle context creation with expired session', async () => {
      const expiredSession = createExpiredSessionResult();
      mockGetSession(mockAuthInstance, expiredSession);

      const request = createAuthenticatedRequest();
      const context = await createAuthContext(request, mockAuthInstance);

      expect(context.session).toEqual(expiredSession);
      expect(context.isAuthenticated).toBe(true); // Context creation doesn't validate expiration
      expect(isSessionValid(context.session)).toBe(false); // But validation should fail
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors during session retrieval', async () => {
      mockAuthInstance.api.getSession.mockRejectedValue(
        new Error('Database connection failed')
      );

      const request = createAuthenticatedRequest();
      const session = await getSession(request, {}, mockAuthInstance);

      expect(session).toBeNull();
    });

    it('should handle network timeouts during session operations', async () => {
      mockAuthInstance.api.getSession.mockImplementation(
        () => new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Network timeout')), 100)
        )
      );

      const request = createAuthenticatedRequest();
      const session = await getSession(request, {}, mockAuthInstance);

      expect(session).toBeNull();
    });
  });
});
