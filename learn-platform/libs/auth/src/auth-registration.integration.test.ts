/**
 * Integration tests for user registration functionality
 *
 * This test suite focuses specifically on user registration scenarios including
 * successful registration, input validation, security testing, database integration,
 * and error handling for the better-auth email/password registration flow.
 */

// Mock external dependencies BEFORE any imports
jest.mock('@learn-platform/db', () => ({
  db: {
    select: jest.fn().mockReturnThis(),
    from: jest.fn().mockReturnThis(),
    where: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    values: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    set: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    execute: jest.fn().mockResolvedValue([]),
    query: jest.fn().mockResolvedValue([]),
  },
  users: {},
  session: {},
  account: {},
  verification: {},
}));

jest.mock('better-auth', () => ({
  betterAuth: jest.fn(() => ({
    api: {
      signUp: {
        email: jest.fn(),
      },
      signIn: {
        email: jest.fn(),
      },
      getSession: jest.fn(),
      updateSession: jest.fn(),
      signOut: jest.fn(),
    },
  })),
}));

jest.mock('better-auth/adapters/drizzle', () => ({
  drizzleAdapter: jest.fn(() => ({})),
}));

import { getSession } from './session';
import {
  createSessionResult,
  createTestUser,
  createTestRequest,
  setupTestEnv,
  MALICIOUS_PAYLOADS,
} from './test-utils';

// Helper functions for this test file
function createMockAuthInstance() {
  return {
    api: {
      signUp: {
        email: jest.fn(),
      },
      signIn: {
        email: jest.fn(),
      },
      getSession: jest.fn(),
      updateSession: jest.fn(),
      signOut: jest.fn(),
    },
  };
}

function mockSuccessfulSignUp(authInstance: any, userOverrides = {}, sessionOverrides = {}) {
  const sessionResult = createSessionResult(userOverrides, sessionOverrides);
  authInstance.api.signUp.email.mockResolvedValue({
    data: sessionResult,
    error: null,
  });
  return sessionResult;
}

function mockFailedSignUp(authInstance: any, errorMessage = 'Registration failed') {
  authInstance.api.signUp.email.mockResolvedValue({
    data: null,
    error: { message: errorMessage },
  });
}

function mockDatabaseConstraintViolation(authInstance: any, constraint = 'unique_email') {
  authInstance.api.signUp.email.mockRejectedValue(
    new Error(`Database constraint violation: ${constraint}`)
  );
}

function createRegistrationRequest(data: any, url = 'https://example.com') {
  return new Request(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data),
  });
}

function createValidRegistrationData(overrides: any = {}) {
  return {
    email: '<EMAIL>',
    password: 'SecurePassword123!',
    name: 'New User',
    ...overrides,
  };
}

describe('User Registration Integration Tests', () => {
  let mockAuthInstance: any;
  let restoreEnv: () => void;

  beforeEach(() => {
    restoreEnv = setupTestEnv();
    mockAuthInstance = createMockAuthInstance();
    jest.clearAllMocks();
  });

  afterEach(() => {
    restoreEnv();
  });

  describe('Core Registration Scenarios', () => {
    it('should successfully register a new user with valid data', async () => {
      const registrationData = createValidRegistrationData();
      const expectedUser = createTestUser({
        email: registrationData.email,
        name: registrationData.name,
        emailVerified: false,
      });

      mockSuccessfulSignUp(mockAuthInstance, expectedUser);

      const result = await mockAuthInstance.api.signUp.email(registrationData);

      expect(result.error).toBeNull();
      expect(result.data).toBeDefined();
      expect(result.data.user.email).toBe(registrationData.email);
      expect(result.data.user.name).toBe(registrationData.name);
      expect(result.data.user.emailVerified).toBe(false);
      expect(result.data.session).toBeDefined();
    });

    it('should create user record in database with correct fields', async () => {
      const registrationData = createValidRegistrationData({
        avatar: 'https://example.com/avatar.jpg',
      });

      mockSuccessfulSignUp(mockAuthInstance, {
        email: registrationData.email,
        name: registrationData.name,
        avatar: registrationData.avatar,
      });

      const result = await mockAuthInstance.api.signUp.email(registrationData);

      expect(result.data.user).toMatchObject({
        email: registrationData.email,
        name: registrationData.name,
        avatar: registrationData.avatar,
        emailVerified: false,
      });
      expect(result.data.user.id).toBeDefined();
      expect(result.data.user.createdAt).toBeDefined();
      expect(result.data.user.updatedAt).toBeDefined();
    });

    it('should create session after successful registration', async () => {
      const registrationData = createValidRegistrationData();

      mockSuccessfulSignUp(mockAuthInstance);

      const result = await mockAuthInstance.api.signUp.email(registrationData);

      expect(result.data.session).toBeDefined();
      expect(result.data.session.token).toBeDefined();
      expect(result.data.session.userId).toBe(result.data.user.id);
      expect(result.data.session.expiresAt).toBeInstanceOf(Date);
      expect(result.data.session.expiresAt.getTime()).toBeGreaterThan(Date.now());
    });

    it('should handle registration with optional fields', async () => {
      const registrationData = createValidRegistrationData({
        avatar: 'https://example.com/profile.jpg',
      });

      mockSuccessfulSignUp(mockAuthInstance, {
        avatar: registrationData.avatar,
      });

      const result = await mockAuthInstance.api.signUp.email(registrationData);

      expect(result.data.user.avatar).toBe(registrationData.avatar);
    });
  });

  describe('Input Validation', () => {
    it('should reject invalid email formats', async () => {
      const invalidEmails = [
        'not-an-email',
        'test@',
        '@example.com',
        '<EMAIL>',
        'test@example',
        'test@.com',
        'test@example.',
        'test <EMAIL>',
      ];

      for (const email of invalidEmails) {
        mockFailedSignUp(mockAuthInstance, 'Invalid email format');

        const result = await mockAuthInstance.api.signUp.email(
          createValidRegistrationData({ email })
        );

        expect(result.error?.message).toBe('Invalid email format');
      }
    });

    it('should enforce minimum password length requirements', async () => {
      const shortPasswords = [
        '',
        'a',
        'ab',
        'abc',
        'abcd',
        'abcde',
        'abcdef',
        'abcdefg', // 7 chars, assuming 8 is minimum
      ];

      for (const password of shortPasswords) {
        mockFailedSignUp(mockAuthInstance, 'Password is too short');

        const result = await mockAuthInstance.api.signUp.email(
          createValidRegistrationData({ password })
        );

        expect(result.error?.message).toBe('Password is too short');
      }
    });

    it('should require all mandatory fields', async () => {
      const requiredFields = ['email', 'password', 'name'];

      for (const field of requiredFields) {
        const incompleteData = createValidRegistrationData();
        delete incompleteData[field as keyof typeof incompleteData];

        mockFailedSignUp(mockAuthInstance, `${field} is required`);

        const result = await mockAuthInstance.api.signUp.email(incompleteData);

        expect(result.error?.message).toBe(`${field} is required`);
      }
    });

    it('should reject empty string values for required fields', async () => {
      const emptyFieldTests = [
        { email: '', expectedError: 'Email cannot be empty' },
        { password: '', expectedError: 'Password cannot be empty' },
        { name: '', expectedError: 'Name cannot be empty' },
      ];

      for (const test of emptyFieldTests) {
        mockFailedSignUp(mockAuthInstance, test.expectedError);

        const result = await mockAuthInstance.api.signUp.email(
          createValidRegistrationData(test)
        );

        expect(result.error?.message).toBe(test.expectedError);
      }
    });

    it('should enforce field length limits', async () => {
      const longName = 'a'.repeat(256); // Assuming 255 char limit
      const longEmail = 'a'.repeat(250) + '@example.com'; // Very long email

      mockFailedSignUp(mockAuthInstance, 'Field too long');

      const nameResult = await mockAuthInstance.api.signUp.email(
        createValidRegistrationData({ name: longName })
      );

      expect(nameResult.error?.message).toBe('Field too long');

      const emailResult = await mockAuthInstance.api.signUp.email(
        createValidRegistrationData({ email: longEmail })
      );

      expect(emailResult.error?.message).toBe('Field too long');
    });
  });

  describe('Duplicate Email Prevention', () => {
    it('should prevent registration with existing email address', async () => {
      const existingEmail = '<EMAIL>';

      mockFailedSignUp(mockAuthInstance, 'Email already exists');

      const result = await mockAuthInstance.api.signUp.email(
        createValidRegistrationData({ email: existingEmail })
      );

      expect(result.error?.message).toBe('Email already exists');
    });

    it('should handle case-insensitive email uniqueness', async () => {
      const emailVariations = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];

      for (const email of emailVariations) {
        mockFailedSignUp(mockAuthInstance, 'Email already exists');

        const result = await mockAuthInstance.api.signUp.email(
          createValidRegistrationData({ email })
        );

        expect(result.error?.message).toBe('Email already exists');
      }
    });

    it('should handle database constraint violations gracefully', async () => {
      mockDatabaseConstraintViolation(mockAuthInstance, 'unique_email_constraint');

      try {
        await mockAuthInstance.api.signUp.email(
          createValidRegistrationData({ email: '<EMAIL>' })
        );
        // Should not reach here
        expect(true).toBe(false);
      } catch (error) {
        // Error should be caught and handled gracefully
        expect((error as Error).message).toContain('Database constraint violation');
      }
    });
  });

  describe('Security Testing', () => {
    it('should prevent SQL injection in email field', async () => {
      mockFailedSignUp(mockAuthInstance, 'Invalid email format');

      const result = await mockAuthInstance.api.signUp.email(
        createValidRegistrationData({ email: MALICIOUS_PAYLOADS.sqlInjection })
      );

      expect(result.error?.message).toBe('Invalid email format');
    });

    it('should prevent XSS attacks in name field', async () => {
      mockFailedSignUp(mockAuthInstance, 'Invalid characters in name field');

      const result = await mockAuthInstance.api.signUp.email(
        createValidRegistrationData({ name: MALICIOUS_PAYLOADS.xssScript })
      );

      expect(result.error?.message).toBe('Invalid characters in name field');
    });

    it('should prevent path traversal in user input', async () => {
      mockFailedSignUp(mockAuthInstance, 'Invalid characters detected');

      const result = await mockAuthInstance.api.signUp.email(
        createValidRegistrationData({ name: MALICIOUS_PAYLOADS.pathTraversal })
      );

      expect(result.error?.message).toBe('Invalid characters detected');
    });

    it('should reject excessively long input strings', async () => {
      mockFailedSignUp(mockAuthInstance, 'Input too long');

      const result = await mockAuthInstance.api.signUp.email(
        createValidRegistrationData({ name: MALICIOUS_PAYLOADS.longString })
      );

      expect(result.error?.message).toBe('Input too long');
    });

    it('should enforce rate limiting for registration attempts', async () => {
      const email = '<EMAIL>';

      // Simulate multiple registration attempts
      for (let i = 0; i < 3; i++) {
        mockFailedSignUp(mockAuthInstance, 'Registration failed');

        const result = await mockAuthInstance.api.signUp.email(
          createValidRegistrationData({ email: `attempt${i}@example.com` })
        );

        expect(result.error?.message).toBe('Registration failed');
      }

      // 4th attempt should be rate limited
      mockFailedSignUp(mockAuthInstance, 'Too many registration attempts. Please try again later.');

      const rateLimitedResult = await mockAuthInstance.api.signUp.email(
        createValidRegistrationData({ email })
      );

      expect(rateLimitedResult.error?.message).toBe('Too many registration attempts. Please try again later.');
    });

    it('should rate limit by IP address across different email attempts', async () => {
      const ipAddress = '*************';

      // Simulate attacks from same IP with different emails
      const emails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];

      for (const email of emails) {
        mockFailedSignUp(mockAuthInstance, 'Registration failed');

        const result = await mockAuthInstance.api.signUp.email(
          createValidRegistrationData({ email })
        );

        expect(result.error?.message).toBe('Registration failed');
      }

      // Further attempts from same IP should be blocked
      mockFailedSignUp(mockAuthInstance, 'IP address temporarily blocked due to suspicious activity.');

      const blockedResult = await mockAuthInstance.api.signUp.email(
        createValidRegistrationData({ email: '<EMAIL>' })
      );

      expect(blockedResult.error?.message).toBe('IP address temporarily blocked due to suspicious activity.');
    });
  });

  describe('Password Security', () => {
    it('should enforce strong password requirements', async () => {
      const weakPasswords = [
        'password',
        '123456',
        'qwerty',
        'abc123',
        'password123',
        'short',
        'alllowercase',
        'ALLUPPERCASE',
        '12345678',
      ];

      for (const password of weakPasswords) {
        mockFailedSignUp(mockAuthInstance, 'Password does not meet security requirements');

        const result = await mockAuthInstance.api.signUp.email(
          createValidRegistrationData({ password })
        );

        expect(result.error?.message).toBe('Password does not meet security requirements');
      }
    });

    it('should require minimum password complexity', async () => {
      const insufficientPasswords = [
        'NoNumbers!',
        'nonumbers123',
        'NoSpecialChars123',
        'no-uppercase-123!',
        'NO-LOWERCASE-123!',
      ];

      for (const password of insufficientPasswords) {
        mockFailedSignUp(mockAuthInstance, 'Password must contain uppercase, lowercase, numbers, and special characters');

        const result = await mockAuthInstance.api.signUp.email(
          createValidRegistrationData({ password })
        );

        expect(result.error?.message).toBe('Password must contain uppercase, lowercase, numbers, and special characters');
      }
    });

    it('should prevent common password patterns', async () => {
      const commonPatterns = [
        'Password123!',
        'Welcome123!',
        'Admin123!',
        'User123!',
        'Test123!',
        'Login123!',
      ];

      for (const password of commonPatterns) {
        mockFailedSignUp(mockAuthInstance, 'Password contains common patterns');

        const result = await mockAuthInstance.api.signUp.email(
          createValidRegistrationData({ password })
        );

        expect(result.error?.message).toBe('Password contains common patterns');
      }
    });

    it('should verify password hashing is applied', async () => {
      const plainPassword = 'SecurePassword123!';

      mockSuccessfulSignUp(mockAuthInstance);

      const result = await mockAuthInstance.api.signUp.email(
        createValidRegistrationData({ password: plainPassword })
      );

      // Password should not be stored in plain text (this is handled by better-auth)
      expect(result.data).toBeDefined();
      expect(result.data.user).toBeDefined();
      // The actual password hash verification would be done at the database/auth layer
      // Here we're testing that the registration succeeds with a strong password
    });
  });

  describe('Database Integration', () => {
    it('should verify user record creation with correct schema', async () => {
      const registrationData = createValidRegistrationData({
        email: '<EMAIL>',
        name: 'Database Test User',
        avatar: 'https://example.com/avatar.jpg',
      });

      const expectedUser = createTestUser({
        email: registrationData.email,
        name: registrationData.name,
        avatar: registrationData.avatar,
        emailVerified: false,
      });

      mockSuccessfulSignUp(mockAuthInstance, expectedUser);

      const result = await mockAuthInstance.api.signUp.email(registrationData);

      // Verify all required database fields are present
      expect(result.data.user).toMatchObject({
        id: expect.any(String),
        email: registrationData.email,
        name: registrationData.name,
        avatar: registrationData.avatar,
        emailVerified: false,
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date),
      });
    });

    it('should handle database transaction rollback on failure', async () => {
      // Simulate a database transaction failure
      mockAuthInstance.api.signUp.email.mockRejectedValue(
        new Error('Database transaction failed')
      );

      try {
        await mockAuthInstance.api.signUp.email(
          createValidRegistrationData({ email: '<EMAIL>' })
        );
        // Should not reach here
        expect(true).toBe(false);
      } catch (error) {
        expect((error as Error).message).toBe('Database transaction failed');
      }
    });

    it('should enforce database constraints', async () => {
      // Test various database constraint scenarios
      const constraintTests = [
        { constraint: 'unique_email', error: 'Email already exists' },
        { constraint: 'not_null_name', error: 'Name cannot be null' },
        { constraint: 'email_format', error: 'Invalid email format' },
      ];

      for (const test of constraintTests) {
        mockDatabaseConstraintViolation(mockAuthInstance, test.constraint);

        try {
          await mockAuthInstance.api.signUp.email(
            createValidRegistrationData()
          );
          // Should not reach here
          expect(true).toBe(false);
        } catch (error) {
          expect((error as Error).message).toContain('Database constraint violation');
        }
      }
    });

    it('should properly clean up on registration failure', async () => {
      // Simulate partial registration failure where user is created but session fails
      mockAuthInstance.api.signUp.email.mockRejectedValue(
        new Error('Session creation failed after user creation')
      );

      try {
        await mockAuthInstance.api.signUp.email(
          createValidRegistrationData({ email: '<EMAIL>' })
        );
        // Should not reach here
        expect(true).toBe(false);
      } catch (error) {
        expect((error as Error).message).toBe('Session creation failed after user creation');
        // In a real scenario, the user record should be cleaned up
      }
    });
  });

  describe('Error Handling and Information Disclosure Prevention', () => {
    it('should not reveal whether email exists in error messages', async () => {
      // Both new email and existing email should return same generic error for certain scenarios
      const genericError = 'Registration failed';

      // Existing email
      mockFailedSignUp(mockAuthInstance, genericError);
      const existingEmailResult = await mockAuthInstance.api.signUp.email(
        createValidRegistrationData({ email: '<EMAIL>' })
      );

      // Different email with same error type
      mockFailedSignUp(mockAuthInstance, genericError);
      const newEmailResult = await mockAuthInstance.api.signUp.email(
        createValidRegistrationData({ email: '<EMAIL>' })
      );

      expect(existingEmailResult.error?.message).toBe(genericError);
      expect(newEmailResult.error?.message).toBe(genericError);
    });

    it('should not expose internal system information in errors', async () => {
      // Simulate internal error that gets sanitized
      mockAuthInstance.api.signUp.email.mockRejectedValue(
        new Error('Internal server error') // Sanitized error message
      );

      try {
        await mockAuthInstance.api.signUp.email(
          createValidRegistrationData()
        );
        // Should not reach here
        expect(true).toBe(false);
      } catch (error) {
        // Error should be sanitized and not expose internal details
        expect((error as Error).message).toBe('Internal server error');
        expect((error as Error).message).not.toContain('postgres://');
        expect((error as Error).message).not.toContain('localhost:5432');
        expect((error as Error).message).not.toContain('user:pass');
        expect((error as Error).message).not.toContain('Database connection failed');
      }
    });

    it('should sanitize error stack traces in production', async () => {
      // Simulate error that would have stack trace in development but is sanitized in production
      const sanitizedError = new Error('Registration failed');
      // In production, stack traces should be removed/sanitized
      delete sanitizedError.stack;

      mockAuthInstance.api.signUp.email.mockRejectedValue(sanitizedError);

      try {
        await mockAuthInstance.api.signUp.email(
          createValidRegistrationData()
        );
        // Should not reach here
        expect(true).toBe(false);
      } catch (error) {
        // Stack trace should not be exposed and message should be generic
        expect((error as Error).message).toBe('Registration failed');
        expect((error as Error).stack).toBeUndefined();
        expect((error as Error).message).not.toContain('/app/src/');
        expect((error as Error).message).not.toContain('auth.ts');
        expect((error as Error).message).not.toContain('database.ts');
      }
    });

    it('should have consistent response times to prevent user enumeration', async () => {
      const email1 = '<EMAIL>';
      const email2 = '<EMAIL>';

      // Mock consistent timing for both scenarios
      mockFailedSignUp(mockAuthInstance, 'Registration failed');

      const startTime1 = Date.now();
      await mockAuthInstance.api.signUp.email(createValidRegistrationData({ email: email1 }));
      const endTime1 = Date.now();

      const startTime2 = Date.now();
      await mockAuthInstance.api.signUp.email(createValidRegistrationData({ email: email2 }));
      const endTime2 = Date.now();

      const timeDiff1 = endTime1 - startTime1;
      const timeDiff2 = endTime2 - startTime2;

      // Response times should be similar (within reasonable variance)
      const timeDifference = Math.abs(timeDiff1 - timeDiff2);
      expect(timeDifference).toBeLessThan(100); // Less than 100ms difference
    });
  });

  describe('Email Verification Flow', () => {
    it('should create user with emailVerified false by default', async () => {
      mockSuccessfulSignUp(mockAuthInstance, { emailVerified: false });

      const result = await mockAuthInstance.api.signUp.email(
        createValidRegistrationData()
      );

      expect(result.data.user.emailVerified).toBe(false);
    });

    it('should handle email verification when enabled', async () => {
      // Simulate registration with email verification enabled
      mockSuccessfulSignUp(mockAuthInstance, { emailVerified: false });

      const result = await mockAuthInstance.api.signUp.email(
        createValidRegistrationData()
      );

      expect(result.data.user.emailVerified).toBe(false);
      // In a real scenario with email verification enabled,
      // a verification token would be created and email sent
    });

    it('should prevent login before email verification when required', async () => {
      // This would be tested in conjunction with sign-in tests
      // but we can verify the user state after registration
      mockSuccessfulSignUp(mockAuthInstance, { emailVerified: false });

      const result = await mockAuthInstance.api.signUp.email(
        createValidRegistrationData()
      );

      expect(result.data.user.emailVerified).toBe(false);
      // User should exist but not be able to sign in until verified
    });
  });
});
