# tRPC Integration Example

This document shows how to update your existing tRPC context to use the new `@learn-platform/auth` library.

## Before: Mock Authentication

Your current `learn-platform/libs/trpc/src/context.ts` has mock authentication:

```typescript
// Current implementation with mock auth
function parseUserSession(token?: string): UserSession {
  if (!token) {
    return {
      isAuthenticated: false,
      userId: null,
      roles: [],
    };
  }

  // Mock implementation - replace with actual JWT decoding
  try {
    const mockUser = {
      isAuthenticated: true,
      userId: 'user-' + token.slice(-8),
      roles: ['user'],
      email: '<EMAIL>',
      name: 'Demo User',
    };
    return mockUser;
  } catch (error) {
    return {
      isAuthenticated: false,
      userId: null,
      roles: [],
    };
  }
}
```

## After: Real Authentication with better-auth

Replace the mock implementation with real authentication:

```typescript
/**
 * tRPC context creation for Cloudflare Workers with better-auth
 */

import { createAuthContext } from '@learn-platform/auth';

export interface CreateContextOptions {
  request: Request;
  env?: any; // Cloudflare Worker environment bindings
}

/**
 * Request metadata extracted from headers and Cloudflare
 */
export interface RequestMetadata {
  ip: string;
  country: string;
  userAgent: string;
  timestamp: string;
  requestId: string;
}

/**
 * Creates the tRPC context from the incoming request
 */
export async function createContext(opts: CreateContextOptions) {
  const { request, env } = opts;

  // Extract URL information
  const url = new URL(request.url);

  // Extract Cloudflare-specific headers and request metadata
  const requestMetadata: RequestMetadata = {
    ip: request.headers.get('CF-Connecting-IP') ||
        request.headers.get('X-Forwarded-For') ||
        request.headers.get('X-Real-IP') ||
        'unknown',
    country: request.headers.get('CF-IPCountry') || 'unknown',
    userAgent: request.headers.get('user-agent') || '',
    timestamp: new Date().toISOString(),
    requestId: request.headers.get('CF-Ray') ||
               request.headers.get('X-Request-ID') ||
               crypto.randomUUID(),
  };

  // Get authentication context using better-auth
  const authContext = await createAuthContext(request.headers);

  // Database connection (using the same connection as auth)
  // const { db } = await import('@learn-platform/db');

  return {
    // Core request information
    request,
    env,
    url,

    // Enhanced metadata
    ...requestMetadata,

    // Authentication context from better-auth
    session: authContext.session,
    user: authContext.user,
    isAuthenticated: authContext.isAuthenticated,
    userId: authContext.userId,

    // Database connection
    // db,

    // Utility functions (now using real auth data)
    getUserId: () => authContext.userId,
    getUserRoles: () => [], // Implement role system as needed
    hasRole: (role: string) => false, // Implement role system as needed

    // Request timing for performance monitoring
    startTime: Date.now(),
  };
}

export type Context = ReturnType<typeof createContext>;
```

## Key Changes

1. **Removed mock `parseUserSession` function** - No longer needed
2. **Added `createAuthContext` import** - Uses real authentication
3. **Updated context properties** - Now includes real session data
4. **Maintained existing structure** - All existing context properties preserved
5. **Added type safety** - Full TypeScript support for session data

## Using in tRPC Procedures

### Protected Procedure Example

```typescript
import { requireAuth } from '@learn-platform/auth';
import { publicProcedure } from '@learn-platform/trpc';

export const protectedProcedure = publicProcedure.use(async ({ ctx, next }) => {
  // This will throw if user is not authenticated
  requireAuth(ctx.session);
  
  return next({
    ctx: {
      ...ctx,
      // TypeScript now knows session is not null
      session: ctx.session,
      user: ctx.user,
    },
  });
});
```

### Using Session Data

```typescript
export const getUserProfile = protectedProcedure
  .query(async ({ ctx }) => {
    // ctx.user is guaranteed to exist in protected procedures
    return {
      id: ctx.user.id,
      email: ctx.user.email,
      name: ctx.user.name,
      avatar: ctx.user.avatar,
      emailVerified: ctx.user.emailVerified,
    };
  });
```

## Environment Variables

Make sure these environment variables are set:

```env
# Required for better-auth
BETTER_AUTH_SECRET=your-super-secret-key-here
BETTER_AUTH_URL=https://your-app.com

# Required for database (already configured)
DATABASE_URL=your-postgresql-connection-string
```

## Next Steps

1. **Update your context file** with the new implementation
2. **Run database migrations** to create auth tables
3. **Test authentication** with your tRPC procedures
4. **Add role-based access** if needed for your application
