{"name": "@learn-platform/theme", "version": "0.0.1", "type": "module", "main": "./src/index.js", "module": "./src/index.js", "types": "./src/index.d.ts", "exports": {".": {"import": "./src/index.js", "types": "./src/index.d.ts"}}, "peerDependencies": {"react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0"}, "dependencies": {"zustand": "^5.0.2", "tslib": "^2.3.0"}, "devDependencies": {"@types/node": "^18.16.9"}}