{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "../../dist/out-tsc", "declaration": true, "types": ["node"], "target": "es6", "strictNullChecks": true, "noImplicitAny": true, "strictBindCallApply": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "jsx": "react-jsx"}, "include": ["src/**/*"], "exclude": ["jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts", "src/**/*.spec.tsx", "src/**/*.test.tsx"]}