{"name": "theme", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/theme/src", "projectType": "library", "tags": ["scope:shared", "type:lib"], "implicitDependencies": [], "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default"]}, "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/theme", "main": "libs/theme/src/index.ts", "tsConfig": "libs/theme/tsconfig.lib.json", "assets": ["libs/theme/*.md"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/theme/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/theme/**/*.ts", "libs/theme/**/*.tsx"]}}}}