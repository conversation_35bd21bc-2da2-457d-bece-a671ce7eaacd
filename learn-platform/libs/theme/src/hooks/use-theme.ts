/**
 * Main theme hook for consuming theme state
 */

'use client';

import { useContext } from 'react';
import { ThemeContext } from '../providers/theme-provider';
import type { ThemeContextType } from '../types/theme';

/**
 * Hook to access theme state and actions
 * Must be used within a ThemeProvider
 */
export function useTheme(): ThemeContextType {
  const context = useContext(ThemeContext);

  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }

  return context;
}
