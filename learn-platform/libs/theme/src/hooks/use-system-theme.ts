/**
 * Hook for system theme detection
 */

'use client';

import { useEffect, useState } from 'react';
import type { ResolvedTheme, SystemThemeResult } from '../types/theme';
import { getSystemTheme, createSystemThemeListener } from '../utils/theme-utils';

/**
 * Hook to detect and track system theme preference
 * Returns the current system theme and whether system theme detection is supported
 */
export function useSystemTheme(): SystemThemeResult {
  const [systemTheme, setSystemTheme] = useState<ResolvedTheme>('light');
  const [isSupported, setIsSupported] = useState(false);

  useEffect(() => {
    // Get initial system theme
    const result = getSystemTheme();
    setSystemTheme(result.theme);
    setIsSupported(result.isSupported);

    // Set up listener for system theme changes
    if (result.isSupported) {
      const cleanup = createSystemThemeListener((theme) => {
        setSystemTheme(theme);
      });

      return cleanup;
    }

    // Return undefined for cases where system theme is not supported
    return undefined;
  }, []);

  return {
    theme: systemTheme,
    isSupported,
  };
}
