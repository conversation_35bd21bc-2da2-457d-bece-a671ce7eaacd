/**
 * Theme types for the learning platform
 */

/**
 * Available theme options
 */
export type Theme = 'light' | 'dark' | 'system';

/**
 * Resolved theme (what's actually applied)
 */
export type ResolvedTheme = 'light' | 'dark';

/**
 * Theme state interface for Zustand store
 */
export interface ThemeState {
  /** Current theme setting */
  theme: Theme;
  
  /** Resolved theme (actual applied theme) */
  resolvedTheme: ResolvedTheme;
  
  /** Whether the theme system is initialized */
  isInitialized: boolean;
  
  /** Set the theme */
  setTheme: (theme: Theme) => void;
  
  /** Toggle between light and dark themes */
  toggleTheme: () => void;
  
  /** Initialize the theme system */
  initialize: () => void;
}

/**
 * Theme provider props
 */
export interface ThemeProviderProps {
  children: React.ReactNode;
  /** Default theme to use */
  defaultTheme?: Theme;
  /** Storage key for persisting theme preference */
  storageKey?: string;
  /** Disable system theme detection */
  disableSystemTheme?: boolean;
}

/**
 * Theme context type
 */
export interface ThemeContextType {
  theme: Theme;
  resolvedTheme: ResolvedTheme;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
}

/**
 * System theme detection result
 */
export interface SystemThemeResult {
  theme: ResolvedTheme;
  isSupported: boolean;
}
