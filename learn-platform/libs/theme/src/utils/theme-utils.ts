/**
 * Theme utilities for system detection and theme application
 */

import type { Theme, ResolvedTheme, SystemThemeResult } from '../types/theme';

/**
 * Check if system theme detection is supported
 */
export function isSystemThemeSupported(): boolean {
  return typeof window !== 'undefined' && window.matchMedia !== undefined;
}

/**
 * Get the current system theme preference
 */
export function getSystemTheme(): SystemThemeResult {
  if (!isSystemThemeSupported()) {
    return { theme: 'light', isSupported: false };
  }

  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
  return {
    theme: mediaQuery.matches ? 'dark' : 'light',
    isSupported: true,
  };
}

/**
 * Resolve theme to actual applied theme
 * - 'light' -> 'light'
 * - 'dark' -> 'dark'
 * - 'system' -> system preference or 'light' as fallback
 */
export function resolveTheme(theme: Theme): ResolvedTheme {
  if (theme === 'system') {
    const systemResult = getSystemTheme();
    return systemResult.theme;
  }
  return theme;
}

/**
 * Apply theme to the document
 * Adds or removes the 'dark' class on the document element
 */
export function applyTheme(resolvedTheme: ResolvedTheme): void {
  if (typeof document === 'undefined') {
    return;
  }

  const root = document.documentElement;

  if (resolvedTheme === 'dark') {
    root.classList.add('dark');
  } else {
    root.classList.remove('dark');
  }
}

/**
 * Create a system theme change listener
 * Returns a cleanup function to remove the listener
 */
export function createSystemThemeListener(
  callback: (theme: ResolvedTheme) => void
): () => void {
  if (!isSystemThemeSupported()) {
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    return () => {}; // No-op cleanup function
  }

  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

  const handleChange = (e: MediaQueryListEvent) => {
    callback(e.matches ? 'dark' : 'light');
  };

  // Use addEventListener if available (modern browsers)
  if (mediaQuery.addEventListener) {
    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }
  // Fallback for older browsers
  else if (mediaQuery.addListener) {
    mediaQuery.addListener(handleChange);
    return () => mediaQuery.removeListener(handleChange);
  }

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  return () => {}; // No-op cleanup function
}

/**
 * Get the next theme in the cycle (for toggle functionality)
 * light -> dark -> light
 */
export function getNextTheme(currentTheme: Theme): Theme {
  switch (currentTheme) {
    case 'light': {
      return 'dark';
    }
    case 'dark': {
      return 'light';
    }
    case 'system': {
      // When system is current, toggle to the opposite of current system preference
      const systemTheme = getSystemTheme().theme;
      return systemTheme === 'light' ? 'dark' : 'light';
    }
    default: {
      return 'light';
    }
  }
}
