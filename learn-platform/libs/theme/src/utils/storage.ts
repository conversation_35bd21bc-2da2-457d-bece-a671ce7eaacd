/**
 * Storage utilities for theme persistence
 */

import type { Theme } from '../types/theme';

/** Default storage key for theme preference */
export const DEFAULT_STORAGE_KEY = 'learn-platform-theme';

/**
 * Get stored theme from localStorage
 * Returns null if not found or if localStorage is not available
 */
export function getStoredTheme(storageKey: string = DEFAULT_STORAGE_KEY): Theme | null {
  if (typeof window === 'undefined') {
    return null;
  }

  try {
    const stored = localStorage.getItem(storageKey);
    if (stored && ['light', 'dark', 'system'].includes(stored)) {
      return stored as Theme;
    }
  } catch (error) {
    console.warn('Failed to read theme from localStorage:', error);
  }

  return null;
}

/**
 * Store theme in localStorage
 */
export function setStoredTheme(theme: Theme, storageKey: string = DEFAULT_STORAGE_KEY): void {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    localStorage.setItem(storageKey, theme);
  } catch (error) {
    console.warn('Failed to store theme in localStorage:', error);
  }
}

/**
 * Remove stored theme from localStorage
 */
export function removeStoredTheme(storageKey: string = DEFAULT_STORAGE_KEY): void {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    localStorage.removeItem(storageKey);
  } catch (error) {
    console.warn('Failed to remove theme from localStorage:', error);
  }
}
