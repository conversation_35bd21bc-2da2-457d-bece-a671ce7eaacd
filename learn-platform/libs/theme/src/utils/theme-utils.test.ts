/**
 * Tests for theme utilities
 */

import {
  isSystemThemeSupported,
  getSystemTheme,
  resolveTheme,
  applyTheme,
  getNextTheme
} from './theme-utils';

// Mock window.matchMedia
const mockMatchMedia = (matches: boolean) => {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation((query) => ({
      matches,
      media: query,
      onchange: null,
      addListener: jest.fn(), // deprecated
      removeListener: jest.fn(), // deprecated
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      dispatchEvent: jest.fn(),
    })),
  });
};

describe('theme-utils', () => {
  beforeEach(() => {
    // Reset DOM
    document.documentElement.className = '';
  });

  describe('isSystemThemeSupported', () => {
    it('should return true when matchMedia is available', () => {
      mockMatchMedia(false);
      expect(isSystemThemeSupported()).toBe(true);
    });

    it('should return false when matchMedia is not available', () => {
      const originalMatchMedia = window.matchMedia;
      // @ts-expect-error - Testing undefined matchMedia
      window.matchMedia = undefined;
      expect(isSystemThemeSupported()).toBe(false);
      // Restore
      window.matchMedia = originalMatchMedia;
    });
  });

  describe('getSystemTheme', () => {
    it('should return dark theme when system prefers dark', () => {
      mockMatchMedia(true);
      const result = getSystemTheme();
      expect(result.theme).toBe('dark');
      expect(result.isSupported).toBe(true);
    });

    it('should return light theme when system prefers light', () => {
      mockMatchMedia(false);
      const result = getSystemTheme();
      expect(result.theme).toBe('light');
      expect(result.isSupported).toBe(true);
    });

    it('should return light theme with unsupported when matchMedia is not available', () => {
      const originalMatchMedia = window.matchMedia;
      // @ts-expect-error - Testing undefined matchMedia
      window.matchMedia = undefined;
      const result = getSystemTheme();
      expect(result.theme).toBe('light');
      expect(result.isSupported).toBe(false);
      // Restore
      window.matchMedia = originalMatchMedia;
    });
  });

  describe('resolveTheme', () => {
    it('should return light for light theme', () => {
      expect(resolveTheme('light')).toBe('light');
    });

    it('should return dark for dark theme', () => {
      expect(resolveTheme('dark')).toBe('dark');
    });

    it('should return system preference for system theme', () => {
      mockMatchMedia(true);
      expect(resolveTheme('system')).toBe('dark');

      mockMatchMedia(false);
      expect(resolveTheme('system')).toBe('light');
    });
  });

  describe('applyTheme', () => {
    it('should add dark class for dark theme', () => {
      applyTheme('dark');
      expect(document.documentElement.classList.contains('dark')).toBe(true);
    });

    it('should remove dark class for light theme', () => {
      document.documentElement.classList.add('dark');
      applyTheme('light');
      expect(document.documentElement.classList.contains('dark')).toBe(false);
    });
  });

  describe('getNextTheme', () => {
    it('should cycle from light to dark', () => {
      expect(getNextTheme('light')).toBe('dark');
    });

    it('should cycle from dark to light', () => {
      expect(getNextTheme('dark')).toBe('light');
    });

    it('should toggle system theme based on current system preference', () => {
      mockMatchMedia(true); // system is dark
      expect(getNextTheme('system')).toBe('light');

      mockMatchMedia(false); // system is light
      expect(getNextTheme('system')).toBe('dark');
    });
  });
});
