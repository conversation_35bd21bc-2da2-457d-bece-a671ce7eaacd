/**
 * Zustand store for theme state management
 */

import { create } from 'zustand';
import type { ThemeState, Theme } from '../types/theme';
import {
  getStoredTheme,
  setStoredTheme,
  DEFAULT_STORAGE_KEY
} from '../utils/storage';
import {
  resolveTheme,
  applyTheme,
  createSystemThemeListener
} from '../utils/theme-utils';

/**
 * Create the theme store with Zustand
 */
export const useThemeStore = create<ThemeState>((set, get) => {
  let systemThemeCleanup: (() => void) | null = null;

  const updateResolvedTheme = (theme: Theme) => {
    const resolved = resolveTheme(theme);
    applyTheme(resolved);
    return resolved;
  };

  const setupSystemThemeListener = () => {
    // Clean up existing listener
    if (systemThemeCleanup) {
      systemThemeCleanup();
    }

    // Only set up listener if current theme is 'system'
    const currentTheme = get().theme;
    if (currentTheme === 'system') {
      systemThemeCleanup = createSystemThemeListener((systemTheme) => {
        set((state) => ({
          ...state,
          resolvedTheme: systemTheme,
        }));
        applyTheme(systemTheme);
      });
    }
  };

  return {
    // Initial state - will be updated during initialization
    theme: 'system',
    resolvedTheme: 'light',
    isInitialized: false,

    setTheme: (theme: Theme) => {
      set((state) => {
        const resolvedTheme = updateResolvedTheme(theme);

        // Store the preference
        setStoredTheme(theme, DEFAULT_STORAGE_KEY);

        return {
          ...state,
          theme,
          resolvedTheme,
        };
      });

      // Set up or clean up system theme listener
      setupSystemThemeListener();
    },

    toggleTheme: () => {
      const currentTheme = get().theme;
      const nextTheme = currentTheme === 'light' ? 'dark' : 'light';
      get().setTheme(nextTheme);
    },

    initialize: () => {
      if (get().isInitialized) {
        return;
      }

      // Get stored theme or default to 'system'
      const storedTheme = getStoredTheme(DEFAULT_STORAGE_KEY) || 'system';
      const resolvedTheme = updateResolvedTheme(storedTheme);

      set({
        theme: storedTheme,
        resolvedTheme,
        isInitialized: true,
      });

      // Set up system theme listener if needed
      setupSystemThemeListener();
    },
  };
});

/**
 * Initialize the theme store
 * This should be called once when the app starts
 */
export function initializeThemeStore(): void {
  useThemeStore.getState().initialize();
}
