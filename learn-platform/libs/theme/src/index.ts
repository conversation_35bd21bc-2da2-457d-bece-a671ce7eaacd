/**
 * @learn-platform/theme
 * 
 * A centralized theme management library for the learning platform monorepo.
 * Provides dark mode support with light, dark, and system theme options.
 */

// Types
export type { 
  Theme, 
  ResolvedTheme, 
  ThemeState, 
  ThemeProviderProps, 
  ThemeContextType,
  SystemThemeResult 
} from './types/theme';

// Store
export { useThemeStore } from './store/theme-store';

// Provider and hooks
export { ThemeProvider } from './providers/theme-provider';
export { useTheme } from './hooks/use-theme';
export { useSystemTheme } from './hooks/use-system-theme';

// Components
export { ThemeToggle } from './components/theme-toggle';
export { ThemeSelector } from './components/theme-selector';

// Utilities
export { 
  getStoredTheme, 
  setStoredTheme, 
  removeStoredTheme 
} from './utils/storage';
export { 
  getSystemTheme, 
  isSystemThemeSupported,
  applyTheme,
  resolveTheme
} from './utils/theme-utils';
