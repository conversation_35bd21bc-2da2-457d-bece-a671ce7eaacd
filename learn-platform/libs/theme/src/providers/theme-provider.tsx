/**
 * Theme provider component
 */

'use client';

import React, { createContext, useEffect, useMemo } from 'react';
import type { ThemeProviderProps, ThemeContextType } from '../types/theme';
import { useThemeStore } from '../store/theme-store';

/**
 * Theme context
 */
export const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

/**
 * Theme provider component
 * Provides theme state and actions to the component tree
 */
export function ThemeProvider({
  children,
  defaultTheme = 'system',
}: ThemeProviderProps) {
  // Get theme state from Zustand store
  const {
    theme,
    resolvedTheme,
    isInitialized,
    setTheme,
    toggleTheme,
    initialize,
  } = useThemeStore();

  // Initialize the theme store on mount
  useEffect(() => {
    if (!isInitialized) {
      initialize();
    }
  }, [isInitialized, initialize]);

  // Prevent hydration mismatch by not rendering until initialized
  const contextValue = useMemo<ThemeContextType>(() => ({
    theme,
    resolvedTheme,
    setTheme,
    toggleTheme,
  }), [theme, resolvedTheme, setTheme, toggleTheme]);

  // During SSR or before initialization, render with a basic context
  if (!isInitialized) {
    const fallbackContext: ThemeContextType = {
      theme: defaultTheme,
      resolvedTheme: 'light', // Safe default for SSR
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      setTheme: () => {}, // No-op during SSR
      // eslint-disable-next-line @typescript-eslint/no-empty-function
      toggleTheme: () => {}, // No-op during SSR
    };

    return (
      <ThemeContext.Provider value={fallbackContext}>
        {children}
      </ThemeContext.Provider>
    );
  }

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
}
