/**
 * Theme selector component for settings pages
 */

'use client';

import React from 'react';
import { useTheme } from '../hooks/use-theme';
import type { Theme } from '../types/theme';

/**
 * Props for ThemeSelector component
 */
export interface ThemeSelectorProps {
  /** Additional CSS classes */
  className?: string;
  /** Show description text */
  showDescription?: boolean;
}

/**
 * Theme selector component
 * Provides full theme selection including system option
 */
export function ThemeSelector({ 
  className = '',
  showDescription = true 
}: ThemeSelectorProps) {
  const { theme, setTheme } = useTheme();

  const themes: Array<{ value: Theme; label: string; description: string }> = [
    {
      value: 'light',
      label: 'Light',
      description: 'Light theme with bright colors',
    },
    {
      value: 'dark',
      label: 'Dark',
      description: 'Dark theme with muted colors',
    },
    {
      value: 'system',
      label: 'System',
      description: 'Follow your system preference',
    },
  ];

  return (
    <div className={`space-y-4 ${className}`}>
      <div>
        <label className="block text-sm font-medium text-foreground mb-2">
          Theme
        </label>
        {showDescription && (
          <p className="text-sm text-muted-foreground mb-4">
            Choose how the interface looks. Select a single theme, or sync with your system and automatically switch between day and night themes.
          </p>
        )}
      </div>

      <div className="space-y-2">
        {themes.map((themeOption) => (
          <label
            key={themeOption.value}
            className={`
              flex items-start space-x-3 p-3 rounded-lg border cursor-pointer
              transition-colors duration-200
              ${theme === themeOption.value
                ? 'border-primary bg-primary/5'
                : 'border-border hover:bg-accent'
              }
            `}
          >
            <input
              type="radio"
              name="theme"
              value={themeOption.value}
              checked={theme === themeOption.value}
              onChange={(e) => setTheme(e.target.value as Theme)}
              className="mt-1 h-4 w-4 text-primary border-border focus:ring-primary focus:ring-2"
            />
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2">
                <ThemeIcon theme={themeOption.value} />
                <span className="text-sm font-medium text-foreground">
                  {themeOption.label}
                </span>
              </div>
              {showDescription && (
                <p className="text-xs text-muted-foreground mt-1">
                  {themeOption.description}
                </p>
              )}
            </div>
          </label>
        ))}
      </div>
    </div>
  );
}

/**
 * Theme icon component
 */
function ThemeIcon({ theme }: { theme: Theme }) {
  const iconClasses = "h-4 w-4 text-muted-foreground";

  switch (theme) {
    case 'light':
      return (
        <svg className={iconClasses} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <circle cx="12" cy="12" r="5" />
          <path d="M12 1v2M12 21v2M4.22 4.22l1.42 1.42M18.36 18.36l1.42 1.42M1 12h2M21 12h2M4.22 19.78l1.42-1.42M18.36 5.64l1.42-1.42" />
        </svg>
      );
    case 'dark':
      return (
        <svg className={iconClasses} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path d="M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z" />
        </svg>
      );
    case 'system':
      return (
        <svg className={iconClasses} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <rect x="2" y="3" width="20" height="14" rx="2" ry="2" />
          <line x1="8" y1="21" x2="16" y2="21" />
          <line x1="12" y1="17" x2="12" y2="21" />
        </svg>
      );
    default:
      return null;
  }
}
