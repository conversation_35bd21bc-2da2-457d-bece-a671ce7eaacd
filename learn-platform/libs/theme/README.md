# @learn-platform/theme

A centralized theme management library for the learning platform monorepo, providing comprehensive dark mode support with light, dark, and system theme options.

## Features

- 🌙 **Dark Mode Support** - Complete dark mode implementation with proper contrast ratios
- 🎨 **Three Theme Modes** - Light, dark, and system (follows OS preference)
- 💾 **Persistent Preferences** - Theme selection persists across browser sessions
- 🔄 **Synchronized State** - Theme state synchronized across the entire application
- ♿ **Accessible** - Proper ARIA attributes and keyboard navigation support
- 🚀 **SSR Compatible** - Prevents theme flash on server-side rendering
- 📦 **Type Safe** - Full TypeScript support with proper type exports

## Installation

This library is part of the monorepo and is consumed by both web and admin applications.

```bash
# Install dependencies
bun install
```

## Usage

### Basic Setup

1. **Add Theme Provider** to your app layout:

```tsx
import { ThemeProvider } from '@learn-platform/theme';

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body>
        <ThemeProvider>
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}
```

2. **Use Theme Hook** in components:

```tsx
import { useTheme } from '@learn-platform/theme';

export function MyComponent() {
  const { theme, resolvedTheme, setTheme, toggleTheme } = useTheme();
  
  return (
    <div>
      <p>Current theme: {theme}</p>
      <p>Resolved theme: {resolvedTheme}</p>
      <button onClick={toggleTheme}>Toggle Theme</button>
    </div>
  );
}
```

### Components

#### ThemeToggle

Quick toggle button for switching between light and dark themes:

```tsx
import { ThemeToggle } from '@learn-platform/theme';

export function Header() {
  return (
    <header>
      <ThemeToggle />
    </header>
  );
}
```

#### ThemeSelector

Full theme selector for settings pages:

```tsx
import { ThemeSelector } from '@learn-platform/theme';

export function SettingsPage() {
  return (
    <div>
      <h2>Appearance Settings</h2>
      <ThemeSelector />
    </div>
  );
}
```

## Architecture

The theme system is built on:

- **Zustand** - For state management
- **CSS Variables** - For theme switching (defined in shared styles)
- **localStorage** - For persistence
- **Media Queries** - For system theme detection
- **React Context** - For component integration

## Integration

This library integrates with:

- **Shared Styles** - Uses CSS variables from `@learn-platform/shared-styles`
- **Tailwind CSS** - Works with Tailwind's dark mode utilities
- **Both Applications** - Consumed by web and admin projects

## Development

```bash
# Build the library
bun nx build theme

# Run tests
bun nx test theme

# Lint
bun nx lint theme
```
