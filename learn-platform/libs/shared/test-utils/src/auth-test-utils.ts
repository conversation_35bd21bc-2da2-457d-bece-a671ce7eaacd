/**
 * Shared authentication test utilities
 * 
 * This module provides consistent test credentials and authentication flows
 * across all test files to prevent duplicate user creation and ensure
 * test reliability.
 */

/**
 * Get environment variable with fallback for tests
 */
export function getTestEnvVar(key: string, fallback: string): string {
  try {
    return (globalThis as any).process?.env?.[key] || fallback;
  } catch {
    return fallback;
  }
}

/**
 * Consistent test user credentials used across all test files
 * Uses environment variables for flexibility and security
 */
export const TEST_CREDENTIALS = {
  email: getTestEnvVar('TEST_USER_EMAIL', '<EMAIL>'),
  password: getTestEnvVar('TEST_USER_PASSWORD', 'TestPassword123!'),
  name: 'Test User',
} as const;

/**
 * Alternative test credentials for multi-user scenarios
 */
export const TEST_CREDENTIALS_ALT = {
  email: getTestEnvVar('TEST_USER_EMAIL_ALT', '<EMAIL>'),
  password: getTestEnvVar('TEST_USER_PASSWORD_ALT', 'TestPassword456!'),
  name: 'Test User Alt',
} as const;

/**
 * Test user data for registration scenarios
 */
export const TEST_USER_REGISTRATION = {
  ...TEST_CREDENTIALS,
  confirmPassword: TEST_CREDENTIALS.password,
} as const;

/**
 * Test user data for login scenarios
 */
export const TEST_USER_LOGIN = {
  email: TEST_CREDENTIALS.email,
  password: TEST_CREDENTIALS.password,
} as const;

/**
 * Mock user data for testing (doesn't use real credentials)
 */
export const MOCK_USER = {
  id: 'test-user-id-123',
  email: TEST_CREDENTIALS.email,
  name: TEST_CREDENTIALS.name,
  emailVerified: false,
  createdAt: new Date('2024-01-01T00:00:00Z'),
  updatedAt: new Date('2024-01-01T00:00:00Z'),
  avatar: null,
} as const;

/**
 * Mock session data for testing
 */
export const MOCK_SESSION = {
  id: 'test-session-id-123',
  userId: MOCK_USER.id,
  expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
  token: 'test-session-token',
} as const;

/**
 * Complete mock auth result for testing
 */
export const MOCK_AUTH_RESULT = {
  user: MOCK_USER,
  session: MOCK_SESSION,
} as const;

/**
 * Environment variables documentation
 */
export const ENV_VARS_DOCS = {
  TEST_USER_EMAIL: 'Primary test user email address',
  TEST_USER_PASSWORD: 'Primary test user password',
  TEST_USER_EMAIL_ALT: 'Alternative test user email address',
  TEST_USER_PASSWORD_ALT: 'Alternative test user password',
} as const;

/**
 * Validation helpers for test credentials
 */
export const validateTestCredentials = () => {
  const issues: string[] = [];
  
  if (!TEST_CREDENTIALS.email.includes('@')) {
    issues.push('TEST_USER_EMAIL must be a valid email address');
  }
  
  if (TEST_CREDENTIALS.password.length < 8) {
    issues.push('TEST_USER_PASSWORD must be at least 8 characters long');
  }
  
  if (TEST_CREDENTIALS.email === '<EMAIL>') {
    issues.push('Consider using a more specific test email domain to avoid conflicts');
  }
  
  return {
    isValid: issues.length === 0,
    issues,
  };
};

/**
 * Helper to log test credentials (for debugging)
 */
export const logTestCredentials = () => {
  console.log('🧪 Test Credentials:');
  console.log(`   Email: ${TEST_CREDENTIALS.email}`);
  console.log(`   Password: ${'*'.repeat(TEST_CREDENTIALS.password.length)}`);
  console.log(`   Name: ${TEST_CREDENTIALS.name}`);
};
