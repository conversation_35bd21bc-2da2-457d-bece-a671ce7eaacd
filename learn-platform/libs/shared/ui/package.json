{"name": "@learn-platform/shared-ui", "version": "0.0.1", "type": "module", "main": "./src/index.js", "module": "./src/index.js", "types": "./src/index.d.ts", "exports": {".": {"import": "./src/index.js", "types": "./src/index.d.ts"}}, "peerDependencies": {"react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0"}, "dependencies": {"@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.0.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "lucide-react": "^0.294.0", "tailwind-merge": "^2.0.0"}}