# @learn-platform/shared-ui

A centralized UI component library built with shadcn/ui for the Learn Platform monorepo.

## Overview

This library provides reusable, accessible, and customizable UI components that maintain design consistency across all applications in the monorepo. Built on top of shadcn/ui with Radix UI primitives and styled with Tailwind CSS.

## Features

- 🎨 **Consistent Design System** - Integrates with centralized Tailwind configuration
- ♿ **Accessible Components** - Built on Radix UI primitives
- 🔧 **Customizable** - Variant-based styling with class-variance-authority
- 📦 **Tree-shakeable** - Import only what you need
- 🎯 **TypeScript Support** - Full type safety and IntelliSense

## Installation

The library is already configured in the monorepo. Import components directly:

```tsx
import { Button, Card, Input } from '@learn-platform/shared-ui';
```

## Available Components

### Button

A versatile button component with multiple variants and sizes.

```tsx
import { Button } from '@learn-platform/shared-ui';

// Basic usage
<Button>Click me</Button>

// With variants
<Button variant="destructive">Delete</Button>
<Button variant="outline">Cancel</Button>
<Button variant="ghost">Ghost</Button>

// With sizes
<Button size="sm">Small</Button>
<Button size="lg">Large</Button>
<Button size="icon">🔍</Button>
```

**Variants:** `default`, `destructive`, `outline`, `secondary`, `ghost`, `link`
**Sizes:** `default`, `sm`, `lg`, `icon`

### Card

A flexible card component with header, content, and footer sections.

```tsx
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@learn-platform/shared-ui';

<Card>
  <CardHeader>
    <CardTitle>Card Title</CardTitle>
    <CardDescription>Card description goes here</CardDescription>
  </CardHeader>
  <CardContent>
    <p>Card content</p>
  </CardContent>
  <CardFooter>
    <Button>Action</Button>
  </CardFooter>
</Card>
```

### Input

A styled input component with proper form integration.

```tsx
import { Input } from '@learn-platform/shared-ui';

<Input type="email" placeholder="Enter your email" />
<Input type="password" placeholder="Password" />
```

## Styling

Components use CSS variables defined in the centralized Tailwind configuration. The design system supports both light and dark themes automatically.

## Development

### Building the Library

```bash
bun nx build shared-ui
```

### Running Tests

```bash
bun nx test shared-ui
```

### Linting

```bash
bun nx lint shared-ui
```

## Contributing

When adding new components:

1. Follow the shadcn/ui patterns and conventions
2. Use the `cn` utility for class merging
3. Implement proper TypeScript types
4. Add components to the main index.ts export
5. Update this README with usage examples

## Integration

This library integrates with:
- **Tailwind CSS** - Centralized configuration at `tailwind.config.base.js`
- **Shared Styles** - CSS variables and global styles from `@learn-platform/shared-styles`
- **TypeScript** - Path mapping configured in `tsconfig.base.json`

## Testing

The library includes comprehensive tests using Jest and React Testing Library:

```bash
# Run tests
bun nx test shared-ui

# Run tests in watch mode
bun nx test shared-ui --watch

# Run tests with coverage
bun nx test shared-ui --coverage
```

## Architecture

The UI library follows these architectural principles:

- **Component Composition** - Built on Radix UI primitives for accessibility
- **Variant-based Styling** - Uses class-variance-authority for consistent variants
- **CSS Variables** - Leverages CSS custom properties for theming
- **Tree-shaking** - Optimized exports for minimal bundle size
- **Type Safety** - Full TypeScript support with proper type exports

## Possible Enhancements

Future improvements could include:

1. **Additional Components**
   - Form components (Label, Textarea, Select, Checkbox, Radio)
   - Navigation components (Tabs, Breadcrumb, Pagination)
   - Feedback components (Alert, Toast, Progress, Spinner)
   - Layout components (Container, Grid, Stack)
   - Data display components (Table, Badge, Avatar)

2. **Advanced Features**
   - Dark mode support with automatic theme switching
   - Animation presets using Framer Motion
   - Responsive design utilities
   - Component composition patterns
   - Storybook integration for component documentation

3. **Developer Experience**
   - VS Code snippets for common component patterns
   - ESLint rules for consistent component usage
   - Automated visual regression testing
   - Component usage analytics

4. **Performance Optimizations**
   - Bundle size analysis and optimization
   - Lazy loading for complex components
   - CSS-in-JS alternatives evaluation
   - Runtime performance monitoring
