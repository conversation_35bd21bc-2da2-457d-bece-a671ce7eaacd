{"extends": "../../../tsconfig.base.json", "compilerOptions": {"jsx": "react-jsx", "allowJs": false, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true}, "files": [], "include": [], "references": [{"path": "./tsconfig.lib.json"}, {"path": "./tsconfig.spec.json"}]}