{"name": "shared-ui", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/ui/src", "projectType": "library", "tags": ["scope:shared", "type:ui"], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/shared/ui", "main": "libs/shared/ui/src/index.ts", "tsConfig": "libs/shared/ui/tsconfig.lib.json", "assets": []}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/shared/ui/**/*.{ts,tsx}"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/shared/ui/jest.config.ts"}}}}