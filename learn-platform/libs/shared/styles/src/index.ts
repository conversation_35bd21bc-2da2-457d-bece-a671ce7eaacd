// CSS files should be imported directly by applications
// import '@learn-platform/shared-styles/globals.css';

// Export design tokens as TypeScript constants for programmatic use
export const colors = {
  brand: {
    50: '#f0f9ff',
    100: '#e0f2fe',
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#0ea5e9',
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e',
    950: '#082f49',
  },
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e',
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
    950: '#052e16',
  },
  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
    950: '#451a03',
  },
  error: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
    950: '#450a0a',
  },
} as const;

export const spacing = {
  18: '4.5rem',
  88: '22rem',
  128: '32rem',
} as const;

export const borderRadius = {
  '4xl': '2rem',
} as const;

export const boxShadow = {
  soft: '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',
  medium: '0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  hard: '0 10px 40px -10px rgba(0, 0, 0, 0.2)',
} as const;

// CSS class name constants for type safety
export const buttonClasses = {
  base: 'btn',
  primary: 'btn-primary',
  secondary: 'btn-secondary',
  success: 'btn-success',
  warning: 'btn-warning',
  error: 'btn-error',
  outline: 'btn-outline',
  ghost: 'btn-ghost',
} as const;

export const inputClasses = {
  base: 'input',
  error: 'input-error',
} as const;

export const cardClasses = {
  base: 'card',
  header: 'card-header',
  body: 'card-body',
  footer: 'card-footer',
} as const;

export const badgeClasses = {
  base: 'badge',
  primary: 'badge-primary',
  success: 'badge-success',
  warning: 'badge-warning',
  error: 'badge-error',
  gray: 'badge-gray',
} as const;
