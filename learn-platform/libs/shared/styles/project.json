{"name": "shared-styles", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/styles/src", "projectType": "library", "tags": ["scope:shared", "type:styles"], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/shared/styles", "main": "libs/shared/styles/src/index.ts", "tsConfig": "libs/shared/styles/tsconfig.lib.json", "assets": [{"glob": "**/*.css", "input": "libs/shared/styles/src", "output": "."}]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/shared/styles/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/shared/styles/jest.config.ts"}}}}