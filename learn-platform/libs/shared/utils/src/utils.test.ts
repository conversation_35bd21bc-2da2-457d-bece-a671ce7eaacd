/**
 * Test suite for shared utilities library
 * Tests utility functions, helpers, and common functionality
 */

describe('Shared Utils Library', () => {
  describe('Library Structure', () => {
    it('should be a valid utils library', () => {
      // Basic test to ensure the library is properly configured
      expect(true).toBe(true);
    });

    it('should export utility functions', () => {
      // Test that the library can be imported
      expect(() => {
        // This would import the actual utils in a real implementation
        const utils = {};
        return utils;
      }).not.toThrow();
    });
  });

  describe('String Utilities', () => {
    it('should provide string manipulation functions', () => {
      const stringUtils = {
        capitalize: (str: string) => str.charAt(0).toUpperCase() + str.slice(1),
        slugify: (str: string) => str.toLowerCase().replace(/\s+/g, '-'),
        truncate: (str: string, length: number) =>
          str.length > length ? str.slice(0, length) + '...' : str,
        camelCase: (str: string) =>
          str.replace(/[-_\s]+(.)?/g, (_, c) => c ? c.toUpperCase() : ''),
      };

      expect(stringUtils.capitalize('hello')).toBe('Hello');
      expect(stringUtils.slugify('Hello World')).toBe('hello-world');
      expect(stringUtils.truncate('Hello World', 5)).toBe('Hello...');
      expect(stringUtils.camelCase('hello-world')).toBe('helloWorld');
    });

    it('should validate string formats', () => {
      const validators = {
        isEmail: (email: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email),
        isUrl: (url: string) => {
          try {
            new URL(url);
            return true;
          } catch {
            return false;
          }
        },
        isUuid: (uuid: string) =>
          /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(uuid),
      };

      expect(validators.isEmail('<EMAIL>')).toBe(true);
      expect(validators.isEmail('invalid-email')).toBe(false);
      expect(validators.isUrl('https://example.com')).toBe(true);
      expect(validators.isUrl('invalid-url')).toBe(false);
      expect(validators.isUuid('123e4567-e89b-12d3-a456-************')).toBe(true);
      expect(validators.isUuid('invalid-uuid')).toBe(false);
    });
  });

  describe('Array Utilities', () => {
    it('should provide array manipulation functions', () => {
      const arrayUtils = {
        unique: <T>(arr: T[]) => [...new Set(arr)],
        chunk: <T>(arr: T[], size: number) => {
          const chunks = [];
          for (let i = 0; i < arr.length; i += size) {
            chunks.push(arr.slice(i, i + size));
          }
          return chunks;
        },
        shuffle: <T>(arr: T[]) => {
          const shuffled = [...arr];
          for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
          }
          return shuffled;
        },
        groupBy: <T>(arr: T[], key: keyof T) => {
          return arr.reduce((groups, item) => {
            const group = item[key] as string;
            groups[group] = groups[group] || [];
            groups[group].push(item);
            return groups;
          }, {} as Record<string, T[]>);
        },
      };

      expect(arrayUtils.unique([1, 2, 2, 3, 3, 3])).toEqual([1, 2, 3]);
      expect(arrayUtils.chunk([1, 2, 3, 4, 5], 2)).toEqual([[1, 2], [3, 4], [5]]);

      const shuffled = arrayUtils.shuffle([1, 2, 3, 4, 5]);
      expect(shuffled).toHaveLength(5);
      expect(shuffled).toEqual(expect.arrayContaining([1, 2, 3, 4, 5]));

      const grouped = arrayUtils.groupBy(
        [{ type: 'a', value: 1 }, { type: 'b', value: 2 }, { type: 'a', value: 3 }],
        'type'
      );
      expect(grouped['a']).toHaveLength(2);
      expect(grouped['b']).toHaveLength(1);
    });

    it('should provide array search and filter functions', () => {
      const searchUtils = {
        findByProperty: <T>(arr: T[], property: keyof T, value: any) =>
          arr.find(item => item[property] === value),
        filterByProperty: <T>(arr: T[], property: keyof T, value: any) =>
          arr.filter(item => item[property] === value),
        sortBy: <T>(arr: T[], property: keyof T, direction: 'asc' | 'desc' = 'asc') =>
          [...arr].sort((a, b) => {
            const aVal = a[property];
            const bVal = b[property];
            if (direction === 'asc') {
              return aVal < bVal ? -1 : aVal > bVal ? 1 : 0;
            } else {
              return aVal > bVal ? -1 : aVal < bVal ? 1 : 0;
            }
          }),
      };

      const testArray = [
        { id: 1, name: 'Alice', age: 30 },
        { id: 2, name: 'Bob', age: 25 },
        { id: 3, name: 'Charlie', age: 35 },
      ];

      expect(searchUtils.findByProperty(testArray, 'name', 'Bob')).toEqual({ id: 2, name: 'Bob', age: 25 });
      expect(searchUtils.filterByProperty(testArray, 'age', 30)).toHaveLength(1);

      const sortedByAge = searchUtils.sortBy(testArray, 'age', 'asc');
      expect(sortedByAge[0].age).toBe(25);
      expect(sortedByAge[2].age).toBe(35);
    });
  });

  describe('Object Utilities', () => {
    it('should provide object manipulation functions', () => {
      const objectUtils = {
        pick: <T extends object, K extends keyof T>(obj: T, keys: K[]): Pick<T, K> => {
          const result = {} as Pick<T, K>;
          keys.forEach(key => {
            if (key in obj) {
              result[key] = obj[key];
            }
          });
          return result;
        },
        omit: <T, K extends keyof T>(obj: T, keys: K[]): Omit<T, K> => {
          const result = { ...obj };
          keys.forEach(key => {
            delete result[key];
          });
          return result;
        },
        deepMerge: (target: any, source: any): any => {
          const result = { ...target };
          for (const key in source) {
            if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
              result[key] = objectUtils.deepMerge(result[key] || {}, source[key]);
            } else {
              result[key] = source[key];
            }
          }
          return result;
        },
      };

      const testObj = { a: 1, b: 2, c: 3, d: 4 };

      expect(objectUtils.pick(testObj, ['a', 'c'])).toEqual({ a: 1, c: 3 });
      expect(objectUtils.omit(testObj, ['b', 'd'])).toEqual({ a: 1, c: 3 });

      const merged = objectUtils.deepMerge(
        { a: { x: 1 }, b: 2 },
        { a: { y: 2 }, c: 3 }
      );
      expect(merged).toEqual({ a: { x: 1, y: 2 }, b: 2, c: 3 });
    });

    it('should provide object validation functions', () => {
      const validators = {
        isEmpty: (obj: any) => Object.keys(obj).length === 0,
        hasProperty: (obj: any, property: string) => property in obj,
        isPlainObject: (obj: any) =>
          obj !== null && typeof obj === 'object' && obj.constructor === Object,
      };

      expect(validators.isEmpty({})).toBe(true);
      expect(validators.isEmpty({ a: 1 })).toBe(false);
      expect(validators.hasProperty({ a: 1 }, 'a')).toBe(true);
      expect(validators.hasProperty({ a: 1 }, 'b')).toBe(false);
      expect(validators.isPlainObject({})).toBe(true);
      expect(validators.isPlainObject([])).toBe(false);
      expect(validators.isPlainObject(null)).toBe(false);
    });
  });

  describe('Date Utilities', () => {
    it('should provide date formatting functions', () => {
      const dateUtils = {
        formatDate: (date: Date, format = 'YYYY-MM-DD') => {
          const year = date.getFullYear();
          const month = String(date.getMonth() + 1).padStart(2, '0');
          const day = String(date.getDate()).padStart(2, '0');

          return format
            .replace('YYYY', String(year))
            .replace('MM', month)
            .replace('DD', day);
        },
        isValidDate: (date: any) => date instanceof Date && !isNaN(date.getTime()),
        addDays: (date: Date, days: number) => {
          const result = new Date(date);
          result.setDate(result.getDate() + days);
          return result;
        },
        diffInDays: (date1: Date, date2: Date) => {
          const diffTime = Math.abs(date2.getTime() - date1.getTime());
          return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        },
      };

      const testDate = new Date('2024-01-15');

      expect(dateUtils.formatDate(testDate)).toBe('2024-01-15');
      expect(dateUtils.isValidDate(testDate)).toBe(true);
      expect(dateUtils.isValidDate('invalid')).toBe(false);

      const futureDate = dateUtils.addDays(testDate, 5);
      expect(dateUtils.formatDate(futureDate)).toBe('2024-01-20');

      expect(dateUtils.diffInDays(testDate, futureDate)).toBe(5);
    });

    it('should provide relative time functions', () => {
      const relativeTime = {
        timeAgo: (date: Date) => {
          const now = new Date();
          const diffMs = now.getTime() - date.getTime();
          const diffMins = Math.floor(diffMs / (1000 * 60));
          const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
          const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

          if (diffMins < 1) return 'just now';
          if (diffMins < 60) return `${diffMins} minutes ago`;
          if (diffHours < 24) return `${diffHours} hours ago`;
          return `${diffDays} days ago`;
        },
      };

      const now = new Date();
      const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);
      const twoHoursAgo = new Date(now.getTime() - 2 * 60 * 60 * 1000);

      expect(relativeTime.timeAgo(fiveMinutesAgo)).toContain('minutes ago');
      expect(relativeTime.timeAgo(twoHoursAgo)).toContain('hours ago');
    });
  });

  describe('Number Utilities', () => {
    it('should provide number formatting functions', () => {
      const numberUtils = {
        formatCurrency: (amount: number, currency = 'USD') => {
          return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency,
          }).format(amount);
        },
        formatPercent: (value: number) => {
          return new Intl.NumberFormat('en-US', {
            style: 'percent',
            minimumFractionDigits: 1,
          }).format(value);
        },
        clamp: (value: number, min: number, max: number) => {
          return Math.min(Math.max(value, min), max);
        },
        random: (min: number, max: number) => {
          return Math.floor(Math.random() * (max - min + 1)) + min;
        },
      };

      expect(numberUtils.formatCurrency(1234.56)).toBe('$1,234.56');
      expect(numberUtils.formatPercent(0.1234)).toBe('12.3%');
      expect(numberUtils.clamp(15, 10, 20)).toBe(15);
      expect(numberUtils.clamp(5, 10, 20)).toBe(10);
      expect(numberUtils.clamp(25, 10, 20)).toBe(20);

      const randomNum = numberUtils.random(1, 10);
      expect(randomNum).toBeGreaterThanOrEqual(1);
      expect(randomNum).toBeLessThanOrEqual(10);
    });
  });

  describe('Async Utilities', () => {
    it('should provide async helper functions', () => {
      const asyncUtils = {
        delay: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),
        retry: async <T>(fn: () => Promise<T>, maxAttempts = 3): Promise<T> => {
          let lastError;
          for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
              return await fn();
            } catch (error) {
              lastError = error;
              if (attempt === maxAttempts) throw error;
              await asyncUtils.delay(1000 * attempt);
            }
          }
          throw lastError;
        },
        timeout: <T>(promise: Promise<T>, ms: number): Promise<T> => {
          return Promise.race([
            promise,
            new Promise<never>((_, reject) =>
              setTimeout(() => reject(new Error('Timeout')), ms)
            ),
          ]);
        },
      };

      expect(typeof asyncUtils.delay).toBe('function');
      expect(typeof asyncUtils.retry).toBe('function');
      expect(typeof asyncUtils.timeout).toBe('function');
    });
  });

  describe('Type Guards', () => {
    it('should provide type checking utilities', () => {
      const typeGuards = {
        isString: (value: any): value is string => typeof value === 'string',
        isNumber: (value: any): value is number => typeof value === 'number' && !isNaN(value),
        isBoolean: (value: any): value is boolean => typeof value === 'boolean',
        isArray: (value: any): value is any[] => Array.isArray(value),
        isFunction: (value: any): value is (...args: any[]) => any => typeof value === 'function',
        isNull: (value: any): value is null => value === null,
        isUndefined: (value: any): value is undefined => value === undefined,
        isNullish: (value: any): value is null | undefined => value == null,
      };

      expect(typeGuards.isString('hello')).toBe(true);
      expect(typeGuards.isString(123)).toBe(false);
      expect(typeGuards.isNumber(123)).toBe(true);
      expect(typeGuards.isNumber('123')).toBe(false);
      expect(typeGuards.isBoolean(true)).toBe(true);
      expect(typeGuards.isArray([])).toBe(true);
      expect(typeGuards.isFunction(() => 42)).toBe(true);
      expect(typeGuards.isNull(null)).toBe(true);
      expect(typeGuards.isUndefined(undefined)).toBe(true);
      expect(typeGuards.isNullish(null)).toBe(true);
      expect(typeGuards.isNullish(undefined)).toBe(true);
      expect(typeGuards.isNullish('')).toBe(false);
    });
  });
});
