{"name": "version", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/version/src", "projectType": "library", "tags": ["scope:shared", "type:lib"], "targets": {"generate-version": {"executor": "nx:run-commands", "options": {"command": "node scripts/generate-version-info.js apps/web", "cwd": "{workspaceRoot}"}}, "build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "dependsOn": ["generate-version"], "options": {"outputPath": "dist/libs/version", "main": "libs/version/src/index.ts", "tsConfig": "libs/version/tsconfig.lib.json", "assets": ["libs/version/*.md"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/version/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/version/**/*.ts"]}}}}