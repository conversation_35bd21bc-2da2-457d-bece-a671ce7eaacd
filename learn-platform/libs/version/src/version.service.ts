// Browser-compatible version service
// Note: This version provides build-time injected version info
// Generated at build time - DO NOT EDIT MANUALLY

export interface VersionInfo {
  version: string;
  gitHash: string;
  gitHashShort: string;
  buildDate: string;
}

export class VersionService {
  private static instance: VersionService;
  private versionInfo: VersionInfo | null = null;

  // Private constructor to prevent direct instantiation
  private constructor() {
    // Singleton pattern - initialization happens in getInstance()
  }

  public static getInstance(): VersionService {
    if (!VersionService.instance) {
      VersionService.instance = new VersionService();
    }
    return VersionService.instance;
  }

  public getVersionInfo(): VersionInfo {
    if (this.versionInfo) {
      return this.versionInfo;
    }

    // Build-time injected version info
    this.versionInfo = {
    "version": "0.0.1",
    "gitHash": "3ce2fb51c3e37b1312f734353081bf74ed45634c",
    "gitHashShort": "3ce2fb5",
    "buildDate": "2025-06-19T04:02:04.172Z"
};

    return this.versionInfo;
  }

  public clearCache(): void {
    this.versionInfo = null;
  }
}

// Convenience function for easy import
export function getVersionInfo(): VersionInfo {
  return VersionService.getInstance().getVersionInfo();
}
