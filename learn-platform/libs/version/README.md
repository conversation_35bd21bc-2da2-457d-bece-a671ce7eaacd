# Version Service

A production-ready version service that provides build-time injected version information including git hash, version, and build date.

## Features

- **Build-time injection**: Version information is injected during the build process
- **Git integration**: Automatically extracts git hash and short hash
- **Build timestamp**: Records when the build was created
- **Browser compatible**: Works in both Node.js and browser environments
- **Singleton pattern**: Ensures consistent version information across the application

## Usage

```typescript
import { getVersionInfo, VersionService } from '@learn-platform/version';

// Simple usage
const versionInfo = getVersionInfo();
console.log(versionInfo);
// Output: {
//   version: "1.0.0",
//   gitHash: "3ce2fb51c3e37b1312f734353081bf74ed45634c",
//   gitHashShort: "3ce2fb5",
//   buildDate: "2025-06-19T03:55:35.637Z"
// }

// Using the service directly
const service = VersionService.getInstance();
const info = service.getVersionInfo();
```

## Version Information Structure

```typescript
interface VersionInfo {
  version: string;      // From package.json
  gitHash: string;      // Full git commit hash
  gitHashShort: string; // Short git commit hash (7 chars)
  buildDate: string;    // ISO timestamp of build
}
```

## Build Process

The version service uses a modular build-time injection system:

1. **Generation Script**: `scripts/generate-version-info.js` extracts:
   - Version from specified project's `package.json`
   - Git hash and short hash
   - Build timestamp

2. **Modular Usage**: The script accepts a project path parameter:
   ```bash
   # Generate version for web project (default configuration)
   node scripts/generate-version-info.js apps/web
   
   # Generate version for admin project
   node scripts/generate-version-info.js apps/admin
   
   # Use workspace root package.json (fallback)
   node scripts/generate-version-info.js
   ```

3. **Build Integration**: The `generate-version` target runs before `build`:
   ```bash
   bunx nx build version
   ```

4. **Output**: Generates `libs/version/src/version.service.ts` with injected values

### Build Commands

```bash
# Generate version info manually
node scripts/generate-version-info.js

# Build the version library (automatically generates version info)
bunx nx build version
```

## Development vs Production

- **Development**: Version info shows current git state and build time
- **Production**: Version info is frozen at build time for consistent deployment

## Monorepo Configuration

The version service is designed to work with multiple projects in the monorepo:

### Current Configuration
- **Web Project**: Configured to use `apps/web/package.json` (version: 0.0.1)
- **Admin Project**: Can be configured to use `apps/admin/package.json`

### Adding New Projects

1. **Create project-specific script** (optional):
   ```javascript
   // scripts/generate-version-[project].js
   const { generateVersionInfo } = require('./generate-version-info');
   generateVersionInfo('apps/[project]');
   ```

2. **Update project.json** for the target project:
   ```json
   {
     "generate-version": {
       "executor": "nx:run-commands",
       "options": {
         "command": "node scripts/generate-version-info.js apps/[project]",
         "cwd": "{workspaceRoot}"
       }
     }
   }
   ```

3. **Ensure build dependency**:
   ```json
   {
     "build": {
       "dependsOn": ["generate-version"]
     }
   }
   ```

## CI/CD Integration

For production builds, ensure the build environment has:
- Git repository access (for commit hash extraction)
- Proper `package.json` with version field in target project
- Node.js environment for script execution

Example GitHub Actions step:
```yaml
- name: Build with version info
  run: bunx nx build version
```

The version service automatically works with CI/CD pipelines:

- Git information is extracted from the build environment
- Version comes from `package.json`
- Build date reflects the actual deployment time
- No additional configuration needed

## Error Handling

If git information cannot be extracted (e.g., not in a git repository), the service gracefully falls back to:

- `gitHash`: "unknown"
- `gitHashShort`: "unknown"
- `version`: From package.json or "0.0.0"

## File Structure

```
libs/version/
├── src/
│   ├── index.ts              # Public API exports
│   └── version.service.ts    # Generated service (DO NOT EDIT)
├── project.json              # NX build configuration
└── README.md                 # This file

scripts/
└── generate-version-info.js  # Build-time generation script
```

## Important Notes

⚠️ **DO NOT EDIT** `version.service.ts` manually - it's generated at build time

✅ **DO EDIT** `scripts/generate-version-info.js` to customize version generation logic