/* eslint-disable */
export default {
  displayName: 'releases',
  preset: '../../jest.preset.js',
  testEnvironment: 'node',
  setupFilesAfterEnv: ['<rootDir>/src/jest.setup.ts'],
  transform: {
    '^.+\\.[tj]s$': ['ts-jest', { tsconfig: '<rootDir>/tsconfig.spec.json' }],
  },
  moduleFileExtensions: ['ts', 'js', 'html'],
  coverageDirectory: '../../coverage/libs/releases',
  collectCoverageFrom: [
    'src/**/*.{ts,js}',
    '!src/**/*.test.{ts,js}',
    '!src/**/*.spec.{ts,js}',
    '!src/jest.setup.ts',
    '!src/test-setup.ts',
  ],
  // coverageReporters: ['text', 'lcov', 'html'], // Commented out due to Jest version compatibility
  coverageThreshold: {
    global: {
      branches: 40,
      functions: 40,
      lines: 40,
      statements: 40,
    },
  },
  testMatch: [
    '<rootDir>/src/**/*.test.{ts,js}',
    '<rootDir>/src/**/*.spec.{ts,js}',
  ],
};
