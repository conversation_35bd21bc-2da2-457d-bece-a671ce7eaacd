# @learn-platform/releases

Centralized release notifications library for the learning platform monorepo. Provides TypeScript types, utilities, and helper functions for managing system releases and user notifications.

## Features

- 🎯 **TypeScript Types**: Complete type definitions for releases and notifications
- 🛠️ **Utility Functions**: Helper functions for release management and formatting
- 📊 **Status Tracking**: Track read/unread status for user notifications
- 🔄 **Version Validation**: Semantic version validation and normalization
- 📅 **Date Formatting**: Consistent date and time formatting utilities
- 🎨 **UI Helpers**: Status colors, truncation, and display utilities

## Installation

The library is already configured as part of the workspace. Dependencies are managed at the workspace level.

## Usage

### Basic Types

```typescript
import { Release, UserReleaseNotification, ReleaseWithStatus } from '@learn-platform/releases';

// Create a new release
const newRelease: NewRelease = {
  id: generateReleaseId(),
  version: 'v1.2.0',
  description: 'Bug fixes and performance improvements',
  releaseDate: new Date(),
  createdBy: 'admin-user-id',
};
```

### Utility Functions

```typescript
import {
  validateVersion,
  normalizeVersion,
  formatReleaseDate,
  formatRelativeTime,
  sortReleasesByDate,
  getPublishedReleases,
} from '@learn-platform/releases';

// Version validation
const isValid = validateVersion('v1.2.0'); // true
const normalized = normalizeVersion('1.2.0'); // 'v1.2.0'

// Date formatting
const formatted = formatReleaseDate(new Date()); // 'January 1, 2024'
const relative = formatRelativeTime(new Date()); // 'Today'

// Release filtering and sorting
const published = getPublishedReleases(releases);
const sorted = sortReleasesByDate(releases);
```

### Status and UI Helpers

```typescript
import {
  getReleaseStatusText,
  getReleaseStatusColor,
  isNewRelease,
  calculateReadPercentage,
  truncateDescription,
} from '@learn-platform/releases';

// Status helpers
const statusText = getReleaseStatusText(release); // 'Published' | 'Draft'
const statusColor = getReleaseStatusColor(release); // 'green' | 'yellow'
const isNew = isNewRelease(release); // boolean

// UI helpers
const readPercentage = calculateReadPercentage(releaseWithStatus); // number
const shortDesc = truncateDescription(longDescription, 100); // string
```

## Architecture

```
libs/releases/
├── src/
│   ├── index.ts          # Main exports
│   ├── types.ts          # TypeScript type definitions
│   ├── utils.ts          # Utility functions
│   └── jest.setup.ts     # Jest test setup
├── package.json          # Dependencies
├── project.json          # Nx project configuration
├── tsconfig.json         # TypeScript configuration
├── tsconfig.lib.json     # Library TypeScript config
├── tsconfig.spec.json    # Test TypeScript config
├── jest.config.ts        # Jest configuration
└── README.md            # This file
```

## Integration with Other Libraries

### Using with Database

```typescript
import { releases, userReleaseNotifications } from '@learn-platform/releases';
import { db } from '@learn-platform/db';

// Query releases
const allReleases = await db.select().from(releases);
const publishedReleases = await db.select().from(releases).where(eq(releases.isPublished, true));
```

### Using with tRPC

```typescript
import { CreateReleaseInput, UpdateReleaseInput } from '@learn-platform/releases';

// In tRPC procedures
const createRelease = adminProcedure
  .input(z.object({
    version: z.string(),
    description: z.string(),
    releaseDate: z.date(),
  }))
  .mutation(async ({ input, ctx }) => {
    // Implementation using types from @learn-platform/releases
  });
```

## Development

### Building

```bash
npx nx build releases
```

### Testing

```bash
npx nx test releases
```

### Type Checking

```bash
npx nx type-check releases
```

## Contributing

When adding new functionality:

1. **Add types** to `src/types.ts`
2. **Add utilities** to `src/utils.ts`
3. **Export** from `src/index.ts`
4. **Add tests** for new functionality
5. **Update documentation** as needed

## License

MIT
