{"name": "releases", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/releases/src", "projectType": "library", "tags": ["scope:shared", "type:lib"], "implicitDependencies": [], "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default"]}, "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/releases", "main": "libs/releases/src/index.ts", "tsConfig": "libs/releases/tsconfig.lib.json", "assets": ["libs/releases/*.md"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/releases/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/releases/**/*.ts"]}}}}