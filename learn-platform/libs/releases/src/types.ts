import type { Release, NewRelease, UserReleaseNotification, NewUserReleaseNotification } from '@learn-platform/db';

/**
 * Re-export database types for convenience
 */
export type { Release, NewRelease, UserReleaseNotification, NewUserReleaseNotification };

/**
 * Release with additional computed properties for UI
 */
export interface ReleaseWithStatus extends Release {
  /** Whether the current user has read this release */
  isReadByUser?: boolean;
  /** Number of users who have read this release */
  readCount?: number;
  /** Total number of users who should see this release */
  totalUsers?: number;
}

/**
 * Release creation input (for forms)
 */
export interface CreateReleaseInput {
  version: string;
  description: string;
  releaseDate: Date;
}

/**
 * Release update input (for forms)
 */
export interface UpdateReleaseInput {
  id: string;
  version?: string;
  description?: string;
  releaseDate?: Date;
  isPublished?: boolean;
}

/**
 * Release publication input
 */
export interface PublishReleaseInput {
  id: string;
  isPublished: boolean;
}

/**
 * User release notification with release details
 */
export interface UserReleaseNotificationWithRelease extends UserReleaseNotification {
  release: Release;
}

/**
 * Release statistics for admin dashboard
 */
export interface ReleaseStats {
  totalReleases: number;
  publishedReleases: number;
  draftReleases: number;
  totalNotifications: number;
  readNotifications: number;
  unreadNotifications: number;
}

/**
 * Release notification preferences (for future use)
 */
export interface NotificationPreferences {
  emailNotifications: boolean;
  inAppNotifications: boolean;
  releaseTypes: string[];
}
