import type { Release, ReleaseWithStatus } from './types';

/**
 * Generate a unique ID for releases
 */
export function generateReleaseId(): string {
  return `release_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Generate a unique ID for user release notifications
 */
export function generateNotificationId(): string {
  return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Validate version string format
 * Supports semantic versioning (e.g., v1.0.0, v2.1.0-beta.1)
 */
export function validateVersion(version: string): boolean {
  const semverRegex = /^v?\d+\.\d+\.\d+(-[a-zA-Z0-9.-]+)?$/;
  return semverRegex.test(version);
}

/**
 * Normalize version string (ensure it starts with 'v')
 */
export function normalizeVersion(version: string): string {
  return version.startsWith('v') ? version : `v${version}`;
}

/**
 * Format release date for display
 */
export function formatReleaseDate(date: Date | string): string {
  const releaseDate = typeof date === 'string' ? new Date(date) : date;
  return releaseDate.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
}

/**
 * Format relative time (e.g., "2 days ago", "1 week ago")
 */
export function formatRelativeTime(date: Date | string): string {
  const releaseDate = typeof date === 'string' ? new Date(date) : date;
  const now = new Date();
  const diffInMs = now.getTime() - releaseDate.getTime();
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

  if (diffInDays === 0) {
    return 'Today';
  } else if (diffInDays === 1) {
    return 'Yesterday';
  } else if (diffInDays < 7) {
    return `${diffInDays} days ago`;
  } else if (diffInDays < 30) {
    const weeks = Math.floor(diffInDays / 7);
    return weeks === 1 ? '1 week ago' : `${weeks} weeks ago`;
  } else if (diffInDays < 365) {
    const months = Math.floor(diffInDays / 30);
    return months === 1 ? '1 month ago' : `${months} months ago`;
  } else {
    const years = Math.floor(diffInDays / 365);
    return years === 1 ? '1 year ago' : `${years} years ago`;
  }
}

/**
 * Sort releases by date (newest first)
 */
export function sortReleasesByDate(releases: Release[]): Release[] {
  return [...releases].sort((a, b) => {
    const dateA = new Date(a.releaseDate);
    const dateB = new Date(b.releaseDate);
    return dateB.getTime() - dateA.getTime();
  });
}

/**
 * Filter published releases only
 */
export function getPublishedReleases(releases: Release[]): Release[] {
  return releases.filter(release => release.isPublished);
}

/**
 * Filter draft releases only
 */
export function getDraftReleases(releases: Release[]): Release[] {
  return releases.filter(release => !release.isPublished);
}

/**
 * Get release status text for UI
 */
export function getReleaseStatusText(release: Release): string {
  if (release.isPublished) {
    return 'Published';
  }
  return 'Draft';
}

/**
 * Get release status color for UI
 */
export function getReleaseStatusColor(release: Release): 'green' | 'yellow' | 'gray' {
  if (release.isPublished) {
    return 'green';
  }
  return 'yellow';
}

/**
 * Check if a release is new (published within last 7 days)
 */
export function isNewRelease(release: Release): boolean {
  if (!release.isPublished || !release.publishedAt) {
    return false;
  }
  
  const publishedDate = new Date(release.publishedAt);
  const sevenDaysAgo = new Date();
  sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
  
  return publishedDate > sevenDaysAgo;
}

/**
 * Calculate read percentage for a release
 */
export function calculateReadPercentage(release: ReleaseWithStatus): number {
  if (!release.totalUsers || release.totalUsers === 0) {
    return 0;
  }
  
  const readCount = release.readCount || 0;
  return Math.round((readCount / release.totalUsers) * 100);
}

/**
 * Truncate description for preview
 */
export function truncateDescription(description: string, maxLength: number = 150): string {
  if (description.length <= maxLength) {
    return description;
  }
  
  return description.substring(0, maxLength).trim() + '...';
}
