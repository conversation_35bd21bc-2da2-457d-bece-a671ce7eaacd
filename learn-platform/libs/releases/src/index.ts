/**
 * @learn-platform/releases
 * 
 * Centralized release notifications library for the learning platform monorepo.
 * Provides TypeScript types, utilities, and helper functions for managing
 * system releases and user notifications.
 */

// Export all types
export * from './types';

// Export all utilities
export * from './utils';

// Re-export database schema types for convenience
export {
  releases,
  userReleaseNotifications,
  type Release,
  type NewRelease,
  type UserReleaseNotification,
  type NewUserReleaseNotification,
} from '@learn-platform/db';
