/**
 * Tests for release utility functions
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import {
  generateReleaseId,
  generateNotificationId,
  validateVersion,
  normalizeVersion,
  formatReleaseDate,
  formatRelativeTime,
  sortReleasesByDate,
  getPublishedReleases,
  getDraftReleases,
  getReleaseStatusText,
  getReleaseStatusColor,
  isNewRelease,
  calculateReadPercentage,
  truncateDescription,
} from './utils';
import type { Release, ReleaseWithStatus } from './types';

// Mock Date.now for consistent testing
const mockDate = new Date('2024-01-01T00:00:00.000Z');
beforeEach(() => {
  jest.spyOn(global, 'Date').mockImplementation(() => mockDate as any);
  Date.now = jest.fn(() => mockDate.getTime());
});

describe('Release Utility Functions', () => {
  describe('ID Generation', () => {
    it('should generate unique release IDs', () => {
      const id1 = generateReleaseId();
      const id2 = generateReleaseId();
      
      expect(id1).toMatch(/^release_\d+_[a-z0-9]{9}$/);
      expect(id2).toMatch(/^release_\d+_[a-z0-9]{9}$/);
      expect(id1).not.toBe(id2);
    });

    it('should generate unique notification IDs', () => {
      const id1 = generateNotificationId();
      const id2 = generateNotificationId();
      
      expect(id1).toMatch(/^notification_\d+_[a-z0-9]{9}$/);
      expect(id2).toMatch(/^notification_\d+_[a-z0-9]{9}$/);
      expect(id1).not.toBe(id2);
    });
  });

  describe('Version Validation', () => {
    it('should validate correct semantic versions', () => {
      expect(validateVersion('1.0.0')).toBe(true);
      expect(validateVersion('v1.0.0')).toBe(true);
      expect(validateVersion('2.1.3')).toBe(true);
      expect(validateVersion('v10.20.30')).toBe(true);
      expect(validateVersion('1.0.0-beta.1')).toBe(true);
      expect(validateVersion('v2.0.0-alpha.1')).toBe(true);
      expect(validateVersion('1.0.0-rc.1')).toBe(true);
    });

    it('should reject invalid version formats', () => {
      expect(validateVersion('1.0')).toBe(false);
      expect(validateVersion('1')).toBe(false);
      expect(validateVersion('1.0.0.0')).toBe(false);
      expect(validateVersion('invalid')).toBe(false);
      expect(validateVersion('')).toBe(false);
      expect(validateVersion('v1.0')).toBe(false);
    });

    it('should normalize versions correctly', () => {
      expect(normalizeVersion('1.0.0')).toBe('v1.0.0');
      expect(normalizeVersion('v1.0.0')).toBe('v1.0.0');
      expect(normalizeVersion('2.1.3-beta.1')).toBe('v2.1.3-beta.1');
    });
  });

  describe('Date Formatting', () => {
    it('should format release dates correctly', () => {
      const date = new Date('2024-01-15T10:30:00Z');
      expect(formatReleaseDate(date)).toBe('January 15, 2024');
      expect(formatReleaseDate('2024-01-15T10:30:00Z')).toBe('January 15, 2024');
    });

    it('should format relative time correctly', () => {
      const now = new Date('2024-01-01T00:00:00Z');
      
      // Same day
      expect(formatRelativeTime(now)).toBe('Today');
      
      // Yesterday
      const yesterday = new Date('2023-12-31T00:00:00Z');
      expect(formatRelativeTime(yesterday)).toBe('Yesterday');
      
      // Days ago
      const threeDaysAgo = new Date('2023-12-29T00:00:00Z');
      expect(formatRelativeTime(threeDaysAgo)).toBe('3 days ago');
      
      // Weeks ago
      const twoWeeksAgo = new Date('2023-12-18T00:00:00Z');
      expect(formatRelativeTime(twoWeeksAgo)).toBe('2 weeks ago');
      
      // Months ago
      const twoMonthsAgo = new Date('2023-11-01T00:00:00Z');
      expect(formatRelativeTime(twoMonthsAgo)).toBe('2 months ago');
      
      // Years ago
      const twoYearsAgo = new Date('2022-01-01T00:00:00Z');
      expect(formatRelativeTime(twoYearsAgo)).toBe('2 years ago');
    });
  });

  describe('Release Filtering and Sorting', () => {
    const mockReleases: Release[] = [
      {
        id: '1',
        version: 'v1.0.0',
        description: 'First release',
        isPublished: true,
        publishedAt: new Date('2024-01-01'),
        releaseDate: new Date('2024-01-01'),
        createdBy: 'admin',
        createdAt: new Date('2023-12-01'),
        updatedAt: new Date('2023-12-01'),
      },
      {
        id: '2',
        version: 'v1.1.0',
        description: 'Second release',
        isPublished: false,
        publishedAt: null,
        releaseDate: new Date('2024-01-15'),
        createdBy: 'admin',
        createdAt: new Date('2023-12-15'),
        updatedAt: new Date('2023-12-15'),
      },
      {
        id: '3',
        version: 'v1.2.0',
        description: 'Third release',
        isPublished: true,
        publishedAt: new Date('2024-01-10'),
        releaseDate: new Date('2024-01-10'),
        createdBy: 'admin',
        createdAt: new Date('2023-12-20'),
        updatedAt: new Date('2023-12-20'),
      },
    ];

    it('should sort releases by date (newest first)', () => {
      const sorted = sortReleasesByDate(mockReleases);
      expect(sorted[0].version).toBe('v1.1.0'); // 2024-01-15
      expect(sorted[1].version).toBe('v1.2.0'); // 2024-01-10
      expect(sorted[2].version).toBe('v1.0.0'); // 2024-01-01
    });

    it('should filter published releases', () => {
      const published = getPublishedReleases(mockReleases);
      expect(published).toHaveLength(2);
      expect(published.every(r => r.isPublished)).toBe(true);
    });

    it('should filter draft releases', () => {
      const drafts = getDraftReleases(mockReleases);
      expect(drafts).toHaveLength(1);
      expect(drafts.every(r => !r.isPublished)).toBe(true);
    });
  });

  describe('Release Status Helpers', () => {
    it('should return correct status text', () => {
      const publishedRelease = { isPublished: true } as Release;
      const draftRelease = { isPublished: false } as Release;
      
      expect(getReleaseStatusText(publishedRelease)).toBe('Published');
      expect(getReleaseStatusText(draftRelease)).toBe('Draft');
    });

    it('should return correct status colors', () => {
      const publishedRelease = { isPublished: true } as Release;
      const draftRelease = { isPublished: false } as Release;
      
      expect(getReleaseStatusColor(publishedRelease)).toBe('green');
      expect(getReleaseStatusColor(draftRelease)).toBe('yellow');
    });

    it('should identify new releases correctly', () => {
      const newRelease = {
        isPublished: true,
        publishedAt: new Date('2023-12-28T00:00:00Z'), // 4 days ago
      } as Release;
      
      const oldRelease = {
        isPublished: true,
        publishedAt: new Date('2023-12-20T00:00:00Z'), // 12 days ago
      } as Release;
      
      const unpublishedRelease = {
        isPublished: false,
        publishedAt: null,
      } as Release;
      
      expect(isNewRelease(newRelease)).toBe(true);
      expect(isNewRelease(oldRelease)).toBe(false);
      expect(isNewRelease(unpublishedRelease)).toBe(false);
    });
  });

  describe('UI Helpers', () => {
    it('should calculate read percentage correctly', () => {
      const releaseWithStats: ReleaseWithStatus = {
        id: '1',
        version: 'v1.0.0',
        description: 'Test',
        isPublished: true,
        publishedAt: new Date(),
        releaseDate: new Date(),
        createdBy: 'admin',
        createdAt: new Date(),
        updatedAt: new Date(),
        readCount: 75,
        totalUsers: 100,
      };
      
      expect(calculateReadPercentage(releaseWithStats)).toBe(75);
      
      // Test edge cases
      const noUsers = { ...releaseWithStats, totalUsers: 0 };
      expect(calculateReadPercentage(noUsers)).toBe(0);
      
      const noReads = { ...releaseWithStats, readCount: 0 };
      expect(calculateReadPercentage(noReads)).toBe(0);
    });

    it('should truncate descriptions correctly', () => {
      const longDescription = 'This is a very long description that should be truncated when it exceeds the maximum length limit that we have set for the preview text.';
      
      expect(truncateDescription(longDescription, 50)).toBe('This is a very long description that should be...');
      expect(truncateDescription('Short text', 50)).toBe('Short text');
      expect(truncateDescription(longDescription)).toHaveLength(153); // 150 + '...'
    });
  });
});
