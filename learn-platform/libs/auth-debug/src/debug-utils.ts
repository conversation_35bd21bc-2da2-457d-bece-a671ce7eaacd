/**
 * Debug utilities for Better Auth configuration
 * 
 * This module provides debug functions that can be imported independently
 * without triggering the lazy-loaded auth instance.
 */

/**
 * Auth configuration interface for debug purposes
 */
interface AuthConfig {
  database: any;
  secret: string;
  baseURL: string;
  trustedOrigins: string[];
}

/**
 * Cloudflare Workers environment interface
 */
interface CloudflareEnv {
  BETTER_AUTH_SECRET?: string;
  BETTER_AUTH_URL?: string;
  BETTER_AUTH_TRUSTED_ORIGINS?: string;
  VERCEL_URL?: string;
  [key: string]: any;
}

/**
 * Helper function to safely access process.env in different environments
 */
function getProcessEnv(key: string): string | undefined {
  if (typeof process !== 'undefined' && process.env) {
    return process.env[key];
  }
  return undefined;
}

/**
 * Get auth configuration for debug purposes
 */
function getAuthConfig(env?: CloudflareEnv): AuthConfig {
  // Try Cloudflare Workers env first, then fall back to process.env (if available)
  const secret = env?.BETTER_AUTH_SECRET || getProcessEnv('BETTER_AUTH_SECRET');
  const baseURL = env?.BETTER_AUTH_URL || getProcessEnv('BETTER_AUTH_URL') || 'http://localhost:3000';

  // Get custom trusted origins from environment variable
  const customOrigins = env?.['BETTER_AUTH_TRUSTED_ORIGINS'] || getProcessEnv('BETTER_AUTH_TRUSTED_ORIGINS');
  const trustedOrigins = customOrigins ? customOrigins.split(',').map((origin: string) => origin.trim()).filter(Boolean) : [];

  if (!secret) {
    throw new Error('BETTER_AUTH_SECRET is required but not provided');
  }

  return {
    database: null, // Not needed for debug purposes
    secret,
    baseURL,
    trustedOrigins,
  };
}

/**
 * Debug function to analyze trusted origins configuration
 * This function can be imported without triggering the lazy-loaded auth instance
 */
export function debugTrustedOrigins(env?: CloudflareEnv): {
  config: AuthConfig;
  rawOrigins: string[];
  uniqueOrigins: string[];
  duplicates: string[];
  hasProductionUrl: boolean;
} {
  const config = getAuthConfig(env);

  const rawOrigins = [
    'http://localhost:3000',
    'http://localhost:3001',
    'http://localhost:8787',
    'http://127.0.0.1:8787',
    'https://kwaci-learning.bmbn.dev',
    ...(config.baseURL && !config.baseURL.includes('localhost') ? [config.baseURL] : []),
    ...(config.trustedOrigins || []),
  ];

  const uniqueOrigins = [...new Set(rawOrigins)];
  const duplicates = rawOrigins.filter((item, index) => rawOrigins.indexOf(item) !== index);
  const hasProductionUrl = uniqueOrigins.includes('https://kwaci-learning.bmbn.dev');

  return {
    config,
    rawOrigins,
    uniqueOrigins,
    duplicates,
    hasProductionUrl,
  };
}