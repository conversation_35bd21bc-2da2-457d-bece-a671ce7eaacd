{"name": "auth-debug", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/auth-debug/src", "projectType": "library", "tags": ["scope:shared", "type:util"], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/auth-debug", "main": "libs/auth-debug/src/index.ts", "tsConfig": "libs/auth-debug/tsconfig.lib.json", "assets": ["libs/auth-debug/*.md"]}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/auth-debug/**/*.ts"]}}}}