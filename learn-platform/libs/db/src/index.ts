/**
 * @learn-platform/db
 *
 * Centralized database library for the Learn Platform monorepo.
 * Provides Drizzle ORM setup with PostgreSQL and Supabase integration.
 */

// Export the main database connection
export { default as db } from './connection';

// Export connection utilities
export {
  createDatabaseConnection,
  createDatabaseConnectionFromEnv,
  createDatabaseConnectionForWorkers,
  client,
  type DatabaseConfig,
} from './connection';

// Export all schema definitions and types
export * from './schema';

// Re-export Drizzle ORM types for convenience
export type { InferSelectModel, InferInsertModel } from 'drizzle-orm';
