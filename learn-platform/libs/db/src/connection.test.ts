/**
 * Test suite for database connection utilities
 * Tests Drizzle ORM setup, connection creation, and configuration
 */

import { createDatabaseConnection, createDatabaseConnectionFromEnv } from './connection';

// Mock postgres driver
jest.mock('postgres', () => {
  return jest.fn(() => ({
    connect: jest.fn(),
    end: jest.fn(),
    query: jest.fn(),
  }));
});

// Mock drizzle-orm
jest.mock('drizzle-orm/postgres-js', () => ({
  drizzle: jest.fn(() => ({
    select: jest.fn(),
    insert: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  })),
}));

describe('Database Connection', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createDatabaseConnection', () => {
    it('should create database connection with valid config', () => {
      const config = {
        connectionString: 'postgresql://user:password@localhost:5432/testdb',
      };

      const result = createDatabaseConnection(config);

      expect(result).toBeDefined();
      expect(result.db).toBeDefined();
      expect(result.client).toBeDefined();
      expect(typeof result.db.select).toBe('function');
      expect(typeof result.db.insert).toBe('function');
      expect(typeof result.db.update).toBe('function');
      expect(typeof result.db.delete).toBe('function');
    });

    it('should handle SSL configuration', () => {
      const config = {
        connectionString: 'postgresql://user:password@localhost:5432/testdb',
      };

      const result = createDatabaseConnection(config);

      expect(result).toBeDefined();
      expect(result.db).toBeDefined();
      expect(result.client).toBeDefined();
    });

    it('should handle connection pooling configuration', () => {
      const config = {
        connectionString: 'postgresql://user:password@localhost:5432/testdb',
        max: 10,
        idle_timeout: 30,
      };

      const result = createDatabaseConnection(config);

      expect(result).toBeDefined();
      expect(result.db).toBeDefined();
      expect(result.client).toBeDefined();
    });
  });

  describe('createDatabaseConnectionFromEnv', () => {
    const originalEnv = process.env;

    beforeEach(() => {
      jest.resetModules();
      process.env = { ...originalEnv };
    });

    afterEach(() => {
      process.env = originalEnv;
    });

    it('should create connection from DATABASE_URL environment variable', () => {
      process.env.DATABASE_URL = 'postgresql://user:password@localhost:5432/testdb';

      const result = createDatabaseConnectionFromEnv();

      expect(result).toBeDefined();
      expect(result.db).toBeDefined();
      expect(result.client).toBeDefined();
    });

    it('should throw error when DATABASE_URL is missing', () => {
      delete process.env.DATABASE_URL;

      expect(() => createDatabaseConnectionFromEnv()).toThrow(
        'DATABASE_URL environment variable is required'
      );
    });

    it('should handle Supabase connection string format', () => {
      process.env.DATABASE_URL = 'postgresql://postgres.project:<EMAIL>:5432/postgres';

      const result = createDatabaseConnectionFromEnv();

      expect(result).toBeDefined();
      expect(result.db).toBeDefined();
      expect(result.client).toBeDefined();
    });

    it('should handle local development connection string', () => {
      process.env.DATABASE_URL = 'postgresql://postgres:postgres@localhost:54322/postgres';

      const result = createDatabaseConnectionFromEnv();

      expect(result).toBeDefined();
      expect(result.db).toBeDefined();
      expect(result.client).toBeDefined();
    });
  });

  describe('Database Configuration', () => {
    it('should handle connection options', () => {
      const config = {
        connectionString: 'postgresql://user:password@localhost:5432/testdb',
        ssl: false,
        max: 20,
        idle_timeout: 60,
        connect_timeout: 30,
      };

      const result = createDatabaseConnection(config);

      expect(result).toBeDefined();
      expect(result.db).toBeDefined();
      expect(result.client).toBeDefined();
    });

    it('should accept valid connection strings', () => {
      const validConfigs = [
        { connectionString: 'postgresql://user:password@localhost:5432/testdb' },
        { connectionString: 'postgresql://postgres:postgres@localhost:54322/postgres' },
        { connectionString: 'postgresql://user@localhost/db' },
      ];

      validConfigs.forEach(config => {
        const result = createDatabaseConnection(config);
        expect(result).toBeDefined();
        expect(result.db).toBeDefined();
        expect(result.client).toBeDefined();
      });
    });
  });

  describe('Connection Validation', () => {
    it('should create connection with valid configuration', () => {
      const config = {
        connectionString: 'postgresql://user:password@localhost:5432/testdb',
      };

      const result = createDatabaseConnection(config);

      expect(result).toBeDefined();
      expect(result.db).toBeDefined();
      expect(result.client).toBeDefined();
    });

    it('should handle different connection string formats', () => {
      const configs = [
        { connectionString: 'postgresql://user:password@localhost:5432/testdb' },
        { connectionString: 'postgres://user:password@localhost:5432/testdb' },
      ];

      configs.forEach(config => {
        const result = createDatabaseConnection(config);
        expect(result).toBeDefined();
        expect(result.db).toBeDefined();
        expect(result.client).toBeDefined();
      });
    });
  });

  describe('Connection Pooling', () => {
    it('should configure connection pool settings', () => {
      const config = {
        connectionString: 'postgresql://user:password@localhost:5432/testdb',
        max: 15,
        idle_timeout: 45,
        connect_timeout: 20,
      };

      const result = createDatabaseConnection(config);

      expect(result).toBeDefined();
      expect(result.db).toBeDefined();
      expect(result.client).toBeDefined();
    });

    it('should use default pool settings when not specified', () => {
      const config = {
        connectionString: 'postgresql://user:password@localhost:5432/testdb',
      };

      const result = createDatabaseConnection(config);

      expect(result).toBeDefined();
      expect(result.db).toBeDefined();
      expect(result.client).toBeDefined();
    });
  });

  describe('SSL Configuration', () => {
    it('should enable SSL for production connections', () => {
      const config = {
        connectionString: 'postgresql://user:<EMAIL>:5432/testdb',
        ssl: true,
      };

      const result = createDatabaseConnection(config);

      expect(result).toBeDefined();
      expect(result.db).toBeDefined();
      expect(result.client).toBeDefined();
    });

    it('should disable SSL for local development', () => {
      const config = {
        connectionString: 'postgresql://user:password@localhost:5432/testdb',
      };

      const result = createDatabaseConnection(config);

      expect(result).toBeDefined();
      expect(result.db).toBeDefined();
      expect(result.client).toBeDefined();
    });
  });

  describe('Environment-specific Configuration', () => {
    const originalEnv = process.env;

    beforeEach(() => {
      process.env = { ...originalEnv };
    });

    afterEach(() => {
      process.env = originalEnv;
    });

    it('should handle production environment', () => {
      process.env.NODE_ENV = 'production';
      process.env.DATABASE_URL = 'postgresql://user:<EMAIL>:5432/proddb';

      const result = createDatabaseConnectionFromEnv();

      expect(result).toBeDefined();
      expect(result.db).toBeDefined();
      expect(result.client).toBeDefined();
    });

    it('should handle development environment', () => {
      process.env.NODE_ENV = 'development';
      process.env.DATABASE_URL = 'postgresql://postgres:postgres@localhost:54322/postgres';

      const result = createDatabaseConnectionFromEnv();

      expect(result).toBeDefined();
      expect(result.db).toBeDefined();
      expect(result.client).toBeDefined();
    });

    it('should handle test environment', () => {
      process.env.NODE_ENV = 'test';
      process.env.DATABASE_URL = 'postgresql://postgres:postgres@localhost:54322/test_db';

      const result = createDatabaseConnectionFromEnv();

      expect(result).toBeDefined();
      expect(result.db).toBeDefined();
      expect(result.client).toBeDefined();
    });
  });
});
