import { pgTable, text, timestamp, integer, boolean, json, unique } from "drizzle-orm/pg-core";
import { user } from "./auth";
import { learningContent } from "./learning-content";

/**
 * Learning content analytics table
 * Tracks user interactions with learning content
 */
export const learningContentAnalytics = pgTable("learning_content_analytics", {
  id: text('id').primaryKey(),

  // Content and user references
  contentId: text('content_id').notNull().references(() => learningContent.id, { onDelete: 'cascade' }),
  userId: text('user_id').references(() => user.id, { onDelete: 'set null' }), // Allow anonymous tracking

  // Analytics data
  eventType: text('event_type').notNull().$type<'view' | 'start' | 'complete' | 'step_complete' | 'bookmark' | 'share' | 'feedback'>(),

  // Session and progress tracking
  sessionId: text('session_id'), // Track user sessions
  stepId: text('step_id'), // For step-specific events
  stepIndex: integer('step_index'), // Step position in content
  timeSpent: integer('time_spent'), // Time spent in seconds
  completionPercentage: integer('completion_percentage'), // 0-100

  // Additional metadata
  metadata: json('metadata').$type<{
    userAgent?: string;
    country?: string;
    referrer?: string;
    deviceType?: string;
    rating?: number; // For feedback events
    feedbackText?: string; // For feedback events
    shareMethod?: string; // For share events
  }>(),

  // Timestamps
  createdAt: timestamp('created_at').$defaultFn(() => new Date()).notNull(),
});

/**
 * Learning content feedback table
 * Stores user feedback and ratings for learning content
 */
export const learningContentFeedback = pgTable("learning_content_feedback", {
  id: text('id').primaryKey(),

  // Content and user references
  contentId: text('content_id').notNull().references(() => learningContent.id, { onDelete: 'cascade' }),
  userId: text('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),

  // Feedback data
  rating: integer('rating').notNull(), // 1-5 stars
  feedbackText: text('feedback_text'),
  isHelpful: boolean('is_helpful'),

  // Improvement suggestions
  suggestedImprovements: json('suggested_improvements').$type<{
    contentQuality?: boolean;
    clarity?: boolean;
    examples?: boolean;
    length?: boolean;
    difficulty?: boolean;
    other?: string;
  }>(),

  // Regeneration requests
  requestRegeneration: boolean('request_regeneration').default(false),
  regenerationReason: text('regeneration_reason'),

  // Timestamps
  createdAt: timestamp('created_at').$defaultFn(() => new Date()).notNull(),
  updatedAt: timestamp('updated_at').$defaultFn(() => new Date()).notNull(),
});

/**
 * Learning progress tracking table
 * Tracks user progress through learning content
 */
export const learningProgress = pgTable("learning_progress", {
  id: text('id').primaryKey(),

  // Content and user references
  contentId: text('content_id').notNull().references(() => learningContent.id, { onDelete: 'cascade' }),
  userId: text('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),

  // Progress data
  currentStepIndex: integer('current_step_index').default(0),
  completedSteps: json('completed_steps').$type<number[]>().default([]),
  totalTimeSpent: integer('total_time_spent').default(0), // Total time in seconds
  completionPercentage: integer('completion_percentage').default(0), // 0-100
  isCompleted: boolean('is_completed').default(false),

  // Bookmarks and notes
  bookmarks: json('bookmarks').$type<{
    stepIndex: number;
    note?: string;
    timestamp: string;
  }[]>().default([]),

  notes: json('notes').$type<{
    stepIndex: number;
    content: string;
    timestamp: string;
  }[]>().default([]),

  // Session tracking
  lastAccessedAt: timestamp('last_accessed_at').$defaultFn(() => new Date()).notNull(),
  sessionCount: integer('session_count').default(1),

  // Timestamps
  createdAt: timestamp('created_at').$defaultFn(() => new Date()).notNull(),
  updatedAt: timestamp('updated_at').$defaultFn(() => new Date()).notNull(),
}, (table) => ({
  // Unique constraint to ensure one progress record per user per content
  uniqueUserContent: unique().on(table.contentId, table.userId),
}));

// Export types for TypeScript inference
export type LearningContentAnalytics = typeof learningContentAnalytics.$inferSelect;
export type NewLearningContentAnalytics = typeof learningContentAnalytics.$inferInsert;
export type LearningContentFeedback = typeof learningContentFeedback.$inferSelect;
export type NewLearningContentFeedback = typeof learningContentFeedback.$inferInsert;
export type LearningProgress = typeof learningProgress.$inferSelect;
export type NewLearningProgress = typeof learningProgress.$inferInsert;
