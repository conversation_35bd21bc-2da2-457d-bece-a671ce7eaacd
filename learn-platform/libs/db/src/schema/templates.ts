import { pgTable, text, timestamp, json } from "drizzle-orm/pg-core";
import { user } from "./auth";

/**
 * Explainer templates table
 * Stores user-created explainer templates with their content and metadata
 */
export const explainerTemplates = pgTable("explainer_templates", {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  description: text('description').default(''),
  
  // Store the complete template data as JSON
  // This includes steps array with all content blocks
  steps: json('steps').notNull().$type<Array<{
    id: string;
    title: string;
    icon: string;
    blocks: Array<{
      id: string;
      type: string;
      data: any;
      isEditing?: boolean;
    }>;
  }>>(),
  
  // User ownership
  userId: text('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),
  
  // Timestamps
  createdAt: timestamp('created_at').$defaultFn(() => new Date()).notNull(),
  updatedAt: timestamp('updated_at').$defaultFn(() => new Date()).notNull(),
});

// Export type for TypeScript inference
export type ExplainerTemplate = typeof explainerTemplates.$inferSelect;
export type NewExplainerTemplate = typeof explainerTemplates.$inferInsert;
