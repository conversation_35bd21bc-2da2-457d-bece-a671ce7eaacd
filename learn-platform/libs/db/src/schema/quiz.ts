import { pgTable, text, timestamp, json, boolean, integer, decimal, index } from "drizzle-orm/pg-core";
import { user } from "./auth";
import { learningContent } from "./learning-content";

/**
 * Quiz table
 * Stores generated quizzes based on learning content
 */
export const quiz = pgTable("quiz", {
  id: text('id').primaryKey(),
  title: text('title').notNull(),
  description: text('description').default(''),
  
  // Reference to the learning content this quiz was generated from
  learningContentId: text('learning_content_id').notNull().references(() => learningContent.id, { onDelete: 'cascade' }),
  
  // Quiz configuration
  difficulty: text('difficulty').notNull().$type<'easy' | 'medium' | 'hard'>(),
  estimatedDuration: integer('estimated_duration').notNull(), // in minutes
  totalPoints: integer('total_points').notNull(),
  
  // Quiz questions stored as JSON array
  questions: json('questions').notNull().$type<Array<{
    id: string;
    type: 'flashcard' | 'multipleChoice' | 'trueFalse' | 'fillInBlank' | 'matching' | 'freeText' | 'ordering';
    difficulty: 'easy' | 'medium' | 'hard';
    sourceStepId: string;
    sourceContent: string;
    points: number;
    // Type-specific question data
    [key: string]: any;
  }>>(),
  
  // Generation metadata
  metadata: json('metadata').$type<{
    generatedAt: string;
    aiModel: string;
    sourceStepsUsed: string[];
    difficultyDistribution: Record<string, number>;
    typeDistribution: Record<string, number>;
    validationPassed?: boolean;
    generationOptions?: Record<string, any>;
  }>(),
  
  // Quiz settings
  isPublic: boolean('is_public').default(false).notNull(),
  allowRetakes: boolean('allow_retakes').default(true).notNull(),
  showCorrectAnswers: boolean('show_correct_answers').default(true).notNull(),
  shuffleQuestions: boolean('shuffle_questions').default(false).notNull(),
  timeLimit: integer('time_limit'), // in minutes, null for no limit
  
  // Ownership and timestamps
  createdBy: text('created_by').notNull().references(() => user.id, { onDelete: 'cascade' }),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  learningContentIdx: index('quiz_learning_content_idx').on(table.learningContentId),
  createdByIdx: index('quiz_created_by_idx').on(table.createdBy),
  difficultyIdx: index('quiz_difficulty_idx').on(table.difficulty),
  createdAtIdx: index('quiz_created_at_idx').on(table.createdAt),
}));

/**
 * Quiz attempts table
 * Tracks user attempts at quizzes
 */
export const quizAttempt = pgTable("quiz_attempt", {
  id: text('id').primaryKey(),
  
  // References
  quizId: text('quiz_id').notNull().references(() => quiz.id, { onDelete: 'cascade' }),
  userId: text('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),
  
  // Attempt details
  startedAt: timestamp('started_at').defaultNow().notNull(),
  completedAt: timestamp('completed_at'),
  isCompleted: boolean('is_completed').default(false).notNull(),
  
  // User answers stored as JSON
  answers: json('answers').$type<Array<{
    questionId: string;
    questionType: string;
    answer: any; // Union type based on question type
    timeSpent: number; // seconds spent on this question
    isCorrect?: boolean; // Calculated after submission
    pointsEarned?: number;
  }>>().default([]),
  
  // Scoring
  totalTimeSpent: integer('total_time_spent').default(0), // in seconds
  score: json('score').$type<{
    totalPoints: number;
    earnedPoints: number;
    percentage: number;
    correctAnswers: number;
    totalQuestions: number;
  }>(),
  
  // Question-level results
  questionResults: json('question_results').$type<Array<{
    questionId: string;
    isCorrect: boolean;
    pointsEarned: number;
    feedback?: string;
    userAnswer?: any;
    correctAnswer?: any;
  }>>(),
  
  // Metadata
  metadata: json('metadata').$type<{
    userAgent?: string;
    ipAddress?: string;
    deviceType?: string;
    browserInfo?: string;
    questionOrder?: string[]; // Order questions were presented
    pausedDurations?: number[]; // Times when quiz was paused
  }>(),
  
}, (table) => ({
  quizUserIdx: index('quiz_attempt_quiz_user_idx').on(table.quizId, table.userId),
  userIdx: index('quiz_attempt_user_idx').on(table.userId),
  completedAtIdx: index('quiz_attempt_completed_at_idx').on(table.completedAt),
  isCompletedIdx: index('quiz_attempt_is_completed_idx').on(table.isCompleted),
}));

/**
 * Quiz progress table
 * Tracks ongoing quiz progress for resumable quizzes
 */
export const quizProgress = pgTable("quiz_progress", {
  id: text('id').primaryKey(),
  
  // References
  attemptId: text('attempt_id').notNull().references(() => quizAttempt.id, { onDelete: 'cascade' }),
  
  // Progress tracking
  currentQuestionIndex: integer('current_question_index').default(0).notNull(),
  questionsAnswered: integer('questions_answered').default(0).notNull(),
  totalQuestions: integer('total_questions').notNull(),
  
  // Time tracking
  timeSpentSoFar: integer('time_spent_so_far').default(0), // in seconds
  lastActiveAt: timestamp('last_active_at').defaultNow().notNull(),
  
  // Current state
  currentAnswers: json('current_answers').$type<Array<{
    questionId: string;
    answer: any;
    timeSpent: number;
    isTemporary?: boolean; // For draft answers
  }>>().default([]),
  
  // Bookmarks and notes
  bookmarkedQuestions: json('bookmarked_questions').$type<string[]>().default([]),
  questionNotes: json('question_notes').$type<Record<string, string>>().default({}),
  
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
}, (table) => ({
  attemptIdx: index('quiz_progress_attempt_idx').on(table.attemptId),
  lastActiveIdx: index('quiz_progress_last_active_idx').on(table.lastActiveAt),
}));

/**
 * Quiz analytics table
 * Aggregated analytics for quiz performance
 */
export const quizAnalytics = pgTable("quiz_analytics", {
  id: text('id').primaryKey(),
  
  // Reference
  quizId: text('quiz_id').notNull().references(() => quiz.id, { onDelete: 'cascade' }),
  
  // Aggregate statistics
  totalAttempts: integer('total_attempts').default(0).notNull(),
  completedAttempts: integer('completed_attempts').default(0).notNull(),
  averageScore: decimal('average_score', { precision: 5, scale: 2 }),
  averageTimeSpent: integer('average_time_spent'), // in seconds
  
  // Question-level analytics
  questionStats: json('question_stats').$type<Record<string, {
    questionId: string;
    totalAnswers: number;
    correctAnswers: number;
    averageTimeSpent: number;
    difficultyRating: number; // Based on success rate
    commonWrongAnswers?: any[];
  }>>().default({}),
  
  // Performance distribution
  scoreDistribution: json('score_distribution').$type<Record<string, number>>().default({}), // Score ranges and counts
  timeDistribution: json('time_distribution').$type<Record<string, number>>().default({}), // Time ranges and counts
  
  // Trends
  dailyStats: json('daily_stats').$type<Record<string, {
    date: string;
    attempts: number;
    completions: number;
    averageScore: number;
  }>>().default({}),
  
  // Last updated
  lastCalculatedAt: timestamp('last_calculated_at').defaultNow().notNull(),
  
}, (table) => ({
  quizIdx: index('quiz_analytics_quiz_idx').on(table.quizId),
  lastCalculatedIdx: index('quiz_analytics_last_calculated_idx').on(table.lastCalculatedAt),
}));

/**
 * Quiz feedback table
 * User feedback on quiz quality and difficulty
 */
export const quizFeedback = pgTable("quiz_feedback", {
  id: text('id').primaryKey(),
  
  // References
  quizId: text('quiz_id').notNull().references(() => quiz.id, { onDelete: 'cascade' }),
  userId: text('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),
  attemptId: text('attempt_id').references(() => quizAttempt.id, { onDelete: 'set null' }),
  
  // Feedback ratings (1-5 scale)
  difficultyRating: integer('difficulty_rating'), // 1=too easy, 5=too hard
  clarityRating: integer('clarity_rating'), // 1=unclear, 5=very clear
  relevanceRating: integer('relevance_rating'), // 1=not relevant, 5=very relevant
  overallRating: integer('overall_rating'), // 1=poor, 5=excellent
  
  // Text feedback
  comments: text('comments'),
  suggestions: text('suggestions'),
  
  // Question-specific feedback
  questionFeedback: json('question_feedback').$type<Array<{
    questionId: string;
    rating: number;
    comment?: string;
    reportedIssue?: 'unclear' | 'incorrect' | 'too_hard' | 'too_easy' | 'irrelevant';
  }>>().default([]),
  
  // Metadata
  isAnonymous: boolean('is_anonymous').default(false).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  
}, (table) => ({
  quizUserIdx: index('quiz_feedback_quiz_user_idx').on(table.quizId, table.userId),
  overallRatingIdx: index('quiz_feedback_overall_rating_idx').on(table.overallRating),
  createdAtIdx: index('quiz_feedback_created_at_idx').on(table.createdAt),
}));
