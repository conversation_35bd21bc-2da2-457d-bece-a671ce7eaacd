import { pgTable, text, timestamp, boolean } from "drizzle-orm/pg-core";
import { user } from "./auth";

/**
 * Releases table
 * Stores system releases with version information and publication status
 */
export const releases = pgTable("releases", {
  id: text('id').primaryKey(),
  version: text('version').notNull().unique(), // e.g., "v1.2.0", "v2.0.0-beta.1"
  description: text('description').notNull(), // Rich text description of the release
  
  // Publication management
  isPublished: boolean('is_published').$defaultFn(() => false).notNull(),
  publishedAt: timestamp('published_at'), // When the release was published (null if unpublished)
  releaseDate: timestamp('release_date').notNull(), // Intended release date
  
  // User ownership - admin who created the release
  createdBy: text('created_by').notNull().references(() => user.id, { onDelete: 'cascade' }),
  
  // Timestamps
  createdAt: timestamp('created_at').$defaultFn(() => new Date()).notNull(),
  updatedAt: timestamp('updated_at').$defaultFn(() => new Date()).notNull(),
});

/**
 * User release notifications table
 * Tracks which users have seen which releases
 */
export const userReleaseNotifications = pgTable("user_release_notifications", {
  id: text('id').primaryKey(),
  
  // Foreign keys
  userId: text('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),
  releaseId: text('release_id').notNull().references(() => releases.id, { onDelete: 'cascade' }),
  
  // Tracking information
  isRead: boolean('is_read').$defaultFn(() => false).notNull(),
  readAt: timestamp('read_at'), // When the user marked it as read (null if unread)
  
  // Timestamps
  createdAt: timestamp('created_at').$defaultFn(() => new Date()).notNull(),
  updatedAt: timestamp('updated_at').$defaultFn(() => new Date()).notNull(),
});

// Export types for TypeScript inference
export type Release = typeof releases.$inferSelect;
export type NewRelease = typeof releases.$inferInsert;
export type UserReleaseNotification = typeof userReleaseNotifications.$inferSelect;
export type NewUserReleaseNotification = typeof userReleaseNotifications.$inferInsert;
