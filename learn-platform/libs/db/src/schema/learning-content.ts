import { pgTable, text, timestamp, json, boolean, integer } from "drizzle-orm/pg-core";
import { user } from "./auth";

/**
 * Learning content table
 * Stores user-generated learning content for public consumption
 * This is separate from explainer templates which are admin-created
 */
export const learningContent = pgTable("learning_content", {
  id: text('id').primaryKey(),
  title: text('title').notNull(),
  description: text('description').default(''),

  // Store the complete learning content as JSON
  // This includes steps array with all content blocks
  steps: json('steps').notNull().$type<Array<{
    id: string;
    title: string;
    icon: string;
    blocks: Array<{
      id: string;
      type: string;
      data: any;
      isEditing?: boolean;
    }>;
  }>>(),

  // Learning metadata
  learningLevel: text('learning_level').notNull().$type<'beginner' | 'intermediate' | 'advanced'>(),
  estimatedReadingTime: integer('estimated_reading_time').notNull(), // in minutes

  // Content visibility and categorization
  isPublic: boolean('is_public').default(false).notNull(),
  tags: json('tags').$type<string[]>().default([]),

  // AI generation metadata (optional)
  aiMetadata: json('ai_metadata').$type<{
    aiModel?: string;
    generatedAt?: string;
    contentTypes?: string[];
    originalPrompt?: string;
  }>(),

  // User ownership
  userId: text('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),

  // Timestamps
  createdAt: timestamp('created_at').$defaultFn(() => new Date()).notNull(),
  updatedAt: timestamp('updated_at').$defaultFn(() => new Date()).notNull(),
});

// Export type for TypeScript inference
export type LearningContent = typeof learningContent.$inferSelect;
export type NewLearningContent = typeof learningContent.$inferInsert;
