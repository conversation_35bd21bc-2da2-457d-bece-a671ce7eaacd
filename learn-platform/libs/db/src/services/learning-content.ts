/**
 * Learning content database service
 * Provides functions to save, retrieve, and manage AI-generated learning content
 */

import { eq, and, or, desc, asc, like, ilike } from 'drizzle-orm';
import { learningContent, type LearningContent, type NewLearningContent } from '../schema/learning-content';
import type { Database } from '../connection';

export interface LearningContentFilters {
  userId?: string;
  learningLevel?: 'beginner' | 'intermediate' | 'advanced';
  isPublic?: boolean;
  tags?: string[];
  search?: string;
  limit?: number;
  offset?: number;
  sortBy?: 'createdAt' | 'updatedAt' | 'title';
  sortOrder?: 'asc' | 'desc';
}

export interface LearningContentStats {
  totalContent: number;
  publicContent: number;
  privateContent: number;
  contentByLevel: {
    beginner: number;
    intermediate: number;
    advanced: number;
  };
}

/**
 * Save AI-generated learning content to the database
 */
export async function saveLearningContent(
  db: Database,
  content: Omit<NewLearningContent, 'id' | 'createdAt' | 'updatedAt'>
): Promise<LearningContent> {
  const contentId = crypto.randomUUID();
  const now = new Date();

  const newContent: NewLearningContent = {
    id: contentId,
    ...content,
    createdAt: now,
    updatedAt: now,
  };

  await db.insert(learningContent).values(newContent);

  // Return the created content
  const created = await db
    .select()
    .from(learningContent)
    .where(eq(learningContent.id, contentId))
    .limit(1);

  if (!created.length) {
    throw new Error('Failed to create learning content');
  }

  return created[0];
}

/**
 * Get user's learning content with filtering and pagination
 */
export async function getUserLearningContent(
  db: Database,
  userId: string,
  filters: Omit<LearningContentFilters, 'userId'> = {}
): Promise<{ content: LearningContent[]; total: number }> {
  const whereConditions = [eq(learningContent.userId, userId)];

  // Add filters
  if (filters.learningLevel) {
    whereConditions.push(eq(learningContent.learningLevel, filters.learningLevel));
  }

  if (filters.isPublic !== undefined) {
    whereConditions.push(eq(learningContent.isPublic, filters.isPublic));
  }

  if (filters.search) {
    whereConditions.push(
      or(
        ilike(learningContent.title, `%${filters.search}%`),
        ilike(learningContent.description, `%${filters.search}%`)
      )
    );
  }

  // Build sort order
  const sortColumn = filters.sortBy === 'title' ? learningContent.title :
                    filters.sortBy === 'createdAt' ? learningContent.createdAt :
                    learningContent.updatedAt;
  const sortOrder = filters.sortOrder === 'asc' ? asc : desc;

  // Get total count
  const totalResult = await db
    .select({ count: learningContent.id })
    .from(learningContent)
    .where(and(...whereConditions));

  // Get content with pagination
  const content = await db
    .select()
    .from(learningContent)
    .where(and(...whereConditions))
    .orderBy(sortOrder(sortColumn))
    .limit(filters.limit || 20)
    .offset(filters.offset || 0);

  return {
    content,
    total: totalResult.length,
  };
}

/**
 * Get learning content by ID with access control
 */
export async function getLearningContentById(
  db: Database,
  contentId: string,
  userId?: string
): Promise<LearningContent | null> {
  const content = await db
    .select()
    .from(learningContent)
    .where(eq(learningContent.id, contentId))
    .limit(1);

  if (!content.length) {
    return null;
  }

  const contentItem = content[0];

  // Check access permissions
  if (!contentItem.isPublic && contentItem.userId !== userId) {
    throw new Error('Access denied to private content');
  }

  return contentItem;
}

/**
 * Search learning content across all public content and user's private content
 */
export async function searchLearningContent(
  db: Database,
  filters: LearningContentFilters
): Promise<{ content: LearningContent[]; total: number }> {
  const whereConditions = [];

  // Access control: public content + user's private content
  if (filters.userId) {
    whereConditions.push(
      or(
        eq(learningContent.isPublic, true),
        eq(learningContent.userId, filters.userId)
      )
    );
  } else {
    whereConditions.push(eq(learningContent.isPublic, true));
  }

  // Add filters
  if (filters.learningLevel) {
    whereConditions.push(eq(learningContent.learningLevel, filters.learningLevel));
  }

  if (filters.search) {
    whereConditions.push(
      or(
        ilike(learningContent.title, `%${filters.search}%`),
        ilike(learningContent.description, `%${filters.search}%`)
      )
    );
  }

  // Build sort order
  const sortColumn = filters.sortBy === 'title' ? learningContent.title :
                    filters.sortBy === 'createdAt' ? learningContent.createdAt :
                    learningContent.updatedAt;
  const sortOrder = filters.sortOrder === 'asc' ? asc : desc;

  // Get total count
  const totalResult = await db
    .select({ count: learningContent.id })
    .from(learningContent)
    .where(and(...whereConditions));

  // Get content with pagination
  const content = await db
    .select()
    .from(learningContent)
    .where(and(...whereConditions))
    .orderBy(sortOrder(sortColumn))
    .limit(filters.limit || 20)
    .offset(filters.offset || 0);

  return {
    content,
    total: totalResult.length,
  };
}

/**
 * Update learning content
 */
export async function updateLearningContent(
  db: Database,
  contentId: string,
  userId: string,
  updates: Partial<Omit<LearningContent, 'id' | 'userId' | 'createdAt' | 'updatedAt'>>
): Promise<LearningContent> {
  // Verify ownership
  const existing = await db
    .select()
    .from(learningContent)
    .where(
      and(
        eq(learningContent.id, contentId),
        eq(learningContent.userId, userId)
      )
    )
    .limit(1);

  if (!existing.length) {
    throw new Error('Learning content not found or access denied');
  }

  // Update the content
  await db
    .update(learningContent)
    .set({
      ...updates,
      updatedAt: new Date(),
    })
    .where(eq(learningContent.id, contentId));

  // Return updated content
  const updated = await db
    .select()
    .from(learningContent)
    .where(eq(learningContent.id, contentId))
    .limit(1);

  return updated[0];
}

/**
 * Delete learning content
 */
export async function deleteLearningContent(
  db: Database,
  contentId: string,
  userId: string
): Promise<boolean> {
  // Verify ownership
  const existing = await db
    .select()
    .from(learningContent)
    .where(
      and(
        eq(learningContent.id, contentId),
        eq(learningContent.userId, userId)
      )
    )
    .limit(1);

  if (!existing.length) {
    throw new Error('Learning content not found or access denied');
  }

  // Delete the content
  await db
    .delete(learningContent)
    .where(eq(learningContent.id, contentId));

  return true;
}

/**
 * Duplicate learning content
 */
export async function duplicateLearningContent(
  db: Database,
  contentId: string,
  userId: string,
  newTitle?: string
): Promise<LearningContent> {
  // Get the original content
  const original = await getLearningContentById(db, contentId, userId);
  
  if (!original) {
    throw new Error('Learning content not found or access denied');
  }

  // Create a duplicate
  const duplicateData: Omit<NewLearningContent, 'id' | 'createdAt' | 'updatedAt'> = {
    title: newTitle || `${original.title} (Copy)`,
    description: original.description,
    steps: original.steps,
    learningLevel: original.learningLevel,
    estimatedReadingTime: original.estimatedReadingTime,
    isPublic: false, // Duplicates are private by default
    tags: original.tags,
    aiMetadata: original.aiMetadata,
    userId,
  };

  return await saveLearningContent(db, duplicateData);
}

/**
 * Get learning content statistics for a user
 */
export async function getLearningContentStats(
  db: Database,
  userId: string
): Promise<LearningContentStats> {
  const userContent = await db
    .select()
    .from(learningContent)
    .where(eq(learningContent.userId, userId));

  const stats: LearningContentStats = {
    totalContent: userContent.length,
    publicContent: userContent.filter(c => c.isPublic).length,
    privateContent: userContent.filter(c => !c.isPublic).length,
    contentByLevel: {
      beginner: userContent.filter(c => c.learningLevel === 'beginner').length,
      intermediate: userContent.filter(c => c.learningLevel === 'intermediate').length,
      advanced: userContent.filter(c => c.learningLevel === 'advanced').length,
    },
  };

  return stats;
}
