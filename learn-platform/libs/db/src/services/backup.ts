/**
 * Database backup and recovery service
 * Provides functions for backing up and recovering learning content data
 */

import { eq, and, desc, sql } from 'drizzle-orm';
import { learningContent, learningContentAnalytics, learningContentFeedback, learningProgress } from '../schema';
import type { Database } from '../connection';

export interface BackupData {
  version: string;
  timestamp: string;
  userId: string;
  data: {
    learningContent: any[];
    analytics: any[];
    feedback: any[];
    progress: any[];
  };
  metadata: {
    totalRecords: number;
    contentCount: number;
    analyticsCount: number;
    feedbackCount: number;
    progressCount: number;
  };
}

export interface BackupOptions {
  includeAnalytics?: boolean;
  includeFeedback?: boolean;
  includeProgress?: boolean;
  dateFrom?: Date;
  dateTo?: Date;
}

export interface DataIntegrityReport {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  summary: {
    totalContent: number;
    orphanedAnalytics: number;
    orphanedFeedback: number;
    orphanedProgress: number;
    missingReferences: number;
  };
}

/**
 * Export user's learning content data
 */
export async function exportUserData(
  db: Database,
  userId: string,
  options: BackupOptions = {}
): Promise<BackupData> {
  const {
    includeAnalytics = true,
    includeFeedback = true,
    includeProgress = true,
    dateFrom,
    dateTo
  } = options;

  // Build date filter conditions
  const dateConditions = [];
  if (dateFrom) {
    dateConditions.push(`created_at >= '${dateFrom.toISOString()}'`);
  }
  if (dateTo) {
    dateConditions.push(`created_at <= '${dateTo.toISOString()}'`);
  }

  // Export learning content
  let contentQuery = db
    .select()
    .from(learningContent)
    .where(eq(learningContent.userId, userId));

  if (dateFrom || dateTo) {
    // Apply date filters if specified
    const conditions = [eq(learningContent.userId, userId)];
    if (dateFrom) {
      conditions.push(sql`${learningContent.createdAt} >= ${dateFrom}`);
    }
    if (dateTo) {
      conditions.push(sql`${learningContent.createdAt} <= ${dateTo}`);
    }
    contentQuery = db
      .select()
      .from(learningContent)
      .where(and(...conditions));
  }

  const contentData = await contentQuery;

  // Get content IDs for related data
  const contentIds = contentData.map(c => c.id);

  // Export analytics data
  let analyticsData: any[] = [];
  if (includeAnalytics && contentIds.length > 0) {
    analyticsData = await db
      .select()
      .from(learningContentAnalytics)
      .where(
        and(
          eq(learningContentAnalytics.userId, userId),
          sql`${learningContentAnalytics.contentId} IN (${contentIds.map(id => `'${id}'`).join(',')})`
        )
      );
  }

  // Export feedback data
  let feedbackData: any[] = [];
  if (includeFeedback && contentIds.length > 0) {
    feedbackData = await db
      .select()
      .from(learningContentFeedback)
      .where(
        and(
          eq(learningContentFeedback.userId, userId),
          sql`${learningContentFeedback.contentId} IN (${contentIds.map(id => `'${id}'`).join(',')})`
        )
      );
  }

  // Export progress data
  let progressData: any[] = [];
  if (includeProgress && contentIds.length > 0) {
    progressData = await db
      .select()
      .from(learningProgress)
      .where(
        and(
          eq(learningProgress.userId, userId),
          sql`${learningProgress.contentId} IN (${contentIds.map(id => `'${id}'`).join(',')})`
        )
      );
  }

  const backup: BackupData = {
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    userId,
    data: {
      learningContent: contentData,
      analytics: analyticsData,
      feedback: feedbackData,
      progress: progressData,
    },
    metadata: {
      totalRecords: contentData.length + analyticsData.length + feedbackData.length + progressData.length,
      contentCount: contentData.length,
      analyticsCount: analyticsData.length,
      feedbackCount: feedbackData.length,
      progressCount: progressData.length,
    },
  };

  return backup;
}

/**
 * Import user data from backup
 */
export async function importUserData(
  db: Database,
  backupData: BackupData,
  options: {
    overwriteExisting?: boolean;
    validateIntegrity?: boolean;
  } = {}
): Promise<{
  success: boolean;
  imported: {
    content: number;
    analytics: number;
    feedback: number;
    progress: number;
  };
  errors: string[];
}> {
  const { overwriteExisting = false, validateIntegrity = true } = options;
  const errors: string[] = [];
  const imported = { content: 0, analytics: 0, feedback: 0, progress: 0 };

  try {
    // Validate backup data integrity if requested
    if (validateIntegrity) {
      const integrityReport = await validateBackupIntegrity(backupData);
      if (!integrityReport.isValid) {
        errors.push(...integrityReport.errors);
        return { success: false, imported, errors };
      }
    }

    // Import learning content
    for (const content of backupData.data.learningContent) {
      try {
        if (overwriteExisting) {
          // Check if content exists and update or insert
          const existing = await db
            .select()
            .from(learningContent)
            .where(eq(learningContent.id, content.id))
            .limit(1);

          if (existing.length > 0) {
            await db
              .update(learningContent)
              .set(content)
              .where(eq(learningContent.id, content.id));
          } else {
            await db.insert(learningContent).values(content);
          }
        } else {
          // Only insert if doesn't exist
          const existing = await db
            .select()
            .from(learningContent)
            .where(eq(learningContent.id, content.id))
            .limit(1);

          if (existing.length === 0) {
            await db.insert(learningContent).values(content);
          }
        }
        imported.content++;
      } catch (error) {
        errors.push(`Failed to import content ${content.id}: ${error}`);
      }
    }

    // Import analytics data
    for (const analytics of backupData.data.analytics) {
      try {
        if (!overwriteExisting) {
          const existing = await db
            .select()
            .from(learningContentAnalytics)
            .where(eq(learningContentAnalytics.id, analytics.id))
            .limit(1);

          if (existing.length === 0) {
            await db.insert(learningContentAnalytics).values(analytics);
            imported.analytics++;
          }
        } else {
          await db.insert(learningContentAnalytics).values(analytics);
          imported.analytics++;
        }
      } catch (error) {
        errors.push(`Failed to import analytics ${analytics.id}: ${error}`);
      }
    }

    // Import feedback data
    for (const feedback of backupData.data.feedback) {
      try {
        if (overwriteExisting) {
          const existing = await db
            .select()
            .from(learningContentFeedback)
            .where(eq(learningContentFeedback.id, feedback.id))
            .limit(1);

          if (existing.length > 0) {
            await db
              .update(learningContentFeedback)
              .set(feedback)
              .where(eq(learningContentFeedback.id, feedback.id));
          } else {
            await db.insert(learningContentFeedback).values(feedback);
          }
        } else {
          const existing = await db
            .select()
            .from(learningContentFeedback)
            .where(eq(learningContentFeedback.id, feedback.id))
            .limit(1);

          if (existing.length === 0) {
            await db.insert(learningContentFeedback).values(feedback);
          }
        }
        imported.feedback++;
      } catch (error) {
        errors.push(`Failed to import feedback ${feedback.id}: ${error}`);
      }
    }

    // Import progress data
    for (const progress of backupData.data.progress) {
      try {
        if (overwriteExisting) {
          const existing = await db
            .select()
            .from(learningProgress)
            .where(eq(learningProgress.id, progress.id))
            .limit(1);

          if (existing.length > 0) {
            await db
              .update(learningProgress)
              .set(progress)
              .where(eq(learningProgress.id, progress.id));
          } else {
            await db.insert(learningProgress).values(progress);
          }
        } else {
          const existing = await db
            .select()
            .from(learningProgress)
            .where(eq(learningProgress.id, progress.id))
            .limit(1);

          if (existing.length === 0) {
            await db.insert(learningProgress).values(progress);
          }
        }
        imported.progress++;
      } catch (error) {
        errors.push(`Failed to import progress ${progress.id}: ${error}`);
      }
    }

    return {
      success: errors.length === 0,
      imported,
      errors,
    };
  } catch (error) {
    errors.push(`Import failed: ${error}`);
    return { success: false, imported, errors };
  }
}

/**
 * Validate backup data integrity
 */
export async function validateBackupIntegrity(backupData: BackupData): Promise<DataIntegrityReport> {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Validate backup structure
  if (!backupData.version || !backupData.timestamp || !backupData.userId) {
    errors.push('Invalid backup structure: missing required metadata');
  }

  if (!backupData.data) {
    errors.push('Invalid backup structure: missing data section');
    return {
      isValid: false,
      errors,
      warnings,
      summary: { totalContent: 0, orphanedAnalytics: 0, orphanedFeedback: 0, orphanedProgress: 0, missingReferences: 0 }
    };
  }

  const contentIds = new Set(backupData.data.learningContent.map(c => c.id));
  let orphanedAnalytics = 0;
  let orphanedFeedback = 0;
  let orphanedProgress = 0;

  // Check for orphaned analytics records
  for (const analytics of backupData.data.analytics) {
    if (!contentIds.has(analytics.contentId)) {
      orphanedAnalytics++;
    }
  }

  // Check for orphaned feedback records
  for (const feedback of backupData.data.feedback) {
    if (!contentIds.has(feedback.contentId)) {
      orphanedFeedback++;
    }
  }

  // Check for orphaned progress records
  for (const progress of backupData.data.progress) {
    if (!contentIds.has(progress.contentId)) {
      orphanedProgress++;
    }
  }

  if (orphanedAnalytics > 0) {
    warnings.push(`Found ${orphanedAnalytics} orphaned analytics records`);
  }
  if (orphanedFeedback > 0) {
    warnings.push(`Found ${orphanedFeedback} orphaned feedback records`);
  }
  if (orphanedProgress > 0) {
    warnings.push(`Found ${orphanedProgress} orphaned progress records`);
  }

  // Validate content structure
  for (const content of backupData.data.learningContent) {
    if (!content.id || !content.title || !content.steps) {
      errors.push(`Invalid content structure for content ${content.id || 'unknown'}`);
    }
    if (!content.userId || content.userId !== backupData.userId) {
      errors.push(`Content ${content.id} belongs to different user`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    summary: {
      totalContent: backupData.data.learningContent.length,
      orphanedAnalytics,
      orphanedFeedback,
      orphanedProgress,
      missingReferences: orphanedAnalytics + orphanedFeedback + orphanedProgress,
    },
  };
}

/**
 * Check database integrity for learning content
 */
export async function checkDatabaseIntegrity(db: Database): Promise<DataIntegrityReport> {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check for orphaned analytics records
  const orphanedAnalytics = await db
    .select({ count: sql<number>`COUNT(*)` })
    .from(learningContentAnalytics)
    .leftJoin(learningContent, eq(learningContentAnalytics.contentId, learningContent.id))
    .where(sql`${learningContent.id} IS NULL`);

  // Check for orphaned feedback records
  const orphanedFeedback = await db
    .select({ count: sql<number>`COUNT(*)` })
    .from(learningContentFeedback)
    .leftJoin(learningContent, eq(learningContentFeedback.contentId, learningContent.id))
    .where(sql`${learningContent.id} IS NULL`);

  // Check for orphaned progress records
  const orphanedProgress = await db
    .select({ count: sql<number>`COUNT(*)` })
    .from(learningProgress)
    .leftJoin(learningContent, eq(learningProgress.contentId, learningContent.id))
    .where(sql`${learningContent.id} IS NULL`);

  const orphanedAnalyticsCount = orphanedAnalytics[0]?.count || 0;
  const orphanedFeedbackCount = orphanedFeedback[0]?.count || 0;
  const orphanedProgressCount = orphanedProgress[0]?.count || 0;

  if (orphanedAnalyticsCount > 0) {
    warnings.push(`Found ${orphanedAnalyticsCount} orphaned analytics records`);
  }
  if (orphanedFeedbackCount > 0) {
    warnings.push(`Found ${orphanedFeedbackCount} orphaned feedback records`);
  }
  if (orphanedProgressCount > 0) {
    warnings.push(`Found ${orphanedProgressCount} orphaned progress records`);
  }

  // Get total content count
  const totalContent = await db
    .select({ count: sql<number>`COUNT(*)` })
    .from(learningContent);

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    summary: {
      totalContent: totalContent[0]?.count || 0,
      orphanedAnalytics: orphanedAnalyticsCount,
      orphanedFeedback: orphanedFeedbackCount,
      orphanedProgress: orphanedProgressCount,
      missingReferences: orphanedAnalyticsCount + orphanedFeedbackCount + orphanedProgressCount,
    },
  };
}
