/**
 * Learning content analytics service
 * Provides functions to track and retrieve analytics data for learning content
 */

import { eq, and, desc, count, sql } from 'drizzle-orm';
import { 
  learningContentAnalytics, 
  learningContentFeedback, 
  learningProgress,
  type LearningContentAnalytics,
  type NewLearningContentAnalytics,
  type LearningContentFeedback,
  type NewLearningContentFeedback,
  type LearningProgress,
  type NewLearningProgress
} from '../schema/analytics';
import { learningContent } from '../schema/learning-content';
import type { Database } from '../connection';

export interface AnalyticsEvent {
  contentId: string;
  userId?: string;
  eventType: 'view' | 'start' | 'complete' | 'step_complete' | 'bookmark' | 'share' | 'feedback';
  sessionId?: string;
  stepId?: string;
  stepIndex?: number;
  timeSpent?: number;
  completionPercentage?: number;
  metadata?: {
    userAgent?: string;
    country?: string;
    referrer?: string;
    deviceType?: string;
    rating?: number;
    feedbackText?: string;
    shareMethod?: string;
  };
}

export interface ContentAnalytics {
  contentId: string;
  totalViews: number;
  uniqueViews: number;
  completions: number;
  averageTimeSpent: number;
  averageRating: number;
  totalFeedback: number;
  completionRate: number;
}

/**
 * Track an analytics event
 */
export async function trackAnalyticsEvent(
  db: Database,
  event: AnalyticsEvent
): Promise<LearningContentAnalytics> {
  const eventId = crypto.randomUUID();
  
  const analyticsData: NewLearningContentAnalytics = {
    id: eventId,
    contentId: event.contentId,
    userId: event.userId || null,
    eventType: event.eventType,
    sessionId: event.sessionId || null,
    stepId: event.stepId || null,
    stepIndex: event.stepIndex || null,
    timeSpent: event.timeSpent || null,
    completionPercentage: event.completionPercentage || null,
    metadata: event.metadata || null,
    createdAt: new Date(),
  };

  await db.insert(learningContentAnalytics).values(analyticsData);

  // Return the created analytics record
  const created = await db
    .select()
    .from(learningContentAnalytics)
    .where(eq(learningContentAnalytics.id, eventId))
    .limit(1);

  return created[0];
}

/**
 * Get analytics for specific content
 */
export async function getContentAnalytics(
  db: Database,
  contentId: string
): Promise<ContentAnalytics> {
  // Get total views
  const viewsResult = await db
    .select({ count: count() })
    .from(learningContentAnalytics)
    .where(
      and(
        eq(learningContentAnalytics.contentId, contentId),
        eq(learningContentAnalytics.eventType, 'view')
      )
    );

  // Get unique views (distinct users)
  const uniqueViewsResult = await db
    .select({ count: sql<number>`COUNT(DISTINCT ${learningContentAnalytics.userId})` })
    .from(learningContentAnalytics)
    .where(
      and(
        eq(learningContentAnalytics.contentId, contentId),
        eq(learningContentAnalytics.eventType, 'view')
      )
    );

  // Get completions
  const completionsResult = await db
    .select({ count: count() })
    .from(learningContentAnalytics)
    .where(
      and(
        eq(learningContentAnalytics.contentId, contentId),
        eq(learningContentAnalytics.eventType, 'complete')
      )
    );

  // Get average time spent
  const timeSpentResult = await db
    .select({ avg: sql<number>`AVG(${learningContentAnalytics.timeSpent})` })
    .from(learningContentAnalytics)
    .where(
      and(
        eq(learningContentAnalytics.contentId, contentId),
        sql`${learningContentAnalytics.timeSpent} IS NOT NULL`
      )
    );

  // Get average rating from feedback
  const ratingResult = await db
    .select({ avg: sql<number>`AVG(${learningContentFeedback.rating})` })
    .from(learningContentFeedback)
    .where(eq(learningContentFeedback.contentId, contentId));

  // Get total feedback count
  const feedbackResult = await db
    .select({ count: count() })
    .from(learningContentFeedback)
    .where(eq(learningContentFeedback.contentId, contentId));

  const totalViews = viewsResult[0]?.count || 0;
  const uniqueViews = uniqueViewsResult[0]?.count || 0;
  const completions = completionsResult[0]?.count || 0;
  const averageTimeSpent = timeSpentResult[0]?.avg || 0;
  const averageRating = ratingResult[0]?.avg || 0;
  const totalFeedback = feedbackResult[0]?.count || 0;
  const completionRate = totalViews > 0 ? (completions / totalViews) * 100 : 0;

  return {
    contentId,
    totalViews,
    uniqueViews,
    completions,
    averageTimeSpent,
    averageRating,
    totalFeedback,
    completionRate,
  };
}

/**
 * Submit user feedback for content
 */
export async function submitContentFeedback(
  db: Database,
  feedback: Omit<NewLearningContentFeedback, 'id' | 'createdAt' | 'updatedAt'>
): Promise<LearningContentFeedback> {
  const feedbackId = crypto.randomUUID();
  const now = new Date();

  const feedbackData: NewLearningContentFeedback = {
    id: feedbackId,
    ...feedback,
    createdAt: now,
    updatedAt: now,
  };

  await db.insert(learningContentFeedback).values(feedbackData);

  // Also track as analytics event
  await trackAnalyticsEvent(db, {
    contentId: feedback.contentId,
    userId: feedback.userId,
    eventType: 'feedback',
    metadata: {
      rating: feedback.rating,
      feedbackText: feedback.feedbackText || undefined,
    },
  });

  // Return the created feedback
  const created = await db
    .select()
    .from(learningContentFeedback)
    .where(eq(learningContentFeedback.id, feedbackId))
    .limit(1);

  return created[0];
}

/**
 * Update user progress for content
 */
export async function updateLearningProgress(
  db: Database,
  contentId: string,
  userId: string,
  progressUpdate: Partial<Omit<LearningProgress, 'id' | 'contentId' | 'userId' | 'createdAt' | 'updatedAt'>>
): Promise<LearningProgress> {
  // Check if progress record exists
  const existing = await db
    .select()
    .from(learningProgress)
    .where(
      and(
        eq(learningProgress.contentId, contentId),
        eq(learningProgress.userId, userId)
      )
    )
    .limit(1);

  const now = new Date();

  if (existing.length > 0) {
    // Update existing progress
    await db
      .update(learningProgress)
      .set({
        ...progressUpdate,
        lastAccessedAt: now,
        updatedAt: now,
        sessionCount: sql`${learningProgress.sessionCount} + 1`,
      })
      .where(
        and(
          eq(learningProgress.contentId, contentId),
          eq(learningProgress.userId, userId)
        )
      );
  } else {
    // Create new progress record
    const progressId = crypto.randomUUID();
    const newProgress: NewLearningProgress = {
      id: progressId,
      contentId,
      userId,
      ...progressUpdate,
      lastAccessedAt: now,
      createdAt: now,
      updatedAt: now,
    };

    await db.insert(learningProgress).values(newProgress);
  }

  // Return updated progress
  const updated = await db
    .select()
    .from(learningProgress)
    .where(
      and(
        eq(learningProgress.contentId, contentId),
        eq(learningProgress.userId, userId)
      )
    )
    .limit(1);

  return updated[0];
}

/**
 * Get user progress for content
 */
export async function getUserProgress(
  db: Database,
  contentId: string,
  userId: string
): Promise<LearningProgress | null> {
  const progress = await db
    .select()
    .from(learningProgress)
    .where(
      and(
        eq(learningProgress.contentId, contentId),
        eq(learningProgress.userId, userId)
      )
    )
    .limit(1);

  return progress.length > 0 ? progress[0] : null;
}

/**
 * Get user's learning statistics
 */
export async function getUserLearningStats(
  db: Database,
  userId: string
): Promise<{
  totalContentViewed: number;
  totalContentCompleted: number;
  totalTimeSpent: number;
  averageCompletionRate: number;
  currentStreak: number;
}> {
  // Get total content viewed
  const viewedResult = await db
    .select({ count: sql<number>`COUNT(DISTINCT ${learningContentAnalytics.contentId})` })
    .from(learningContentAnalytics)
    .where(
      and(
        eq(learningContentAnalytics.userId, userId),
        eq(learningContentAnalytics.eventType, 'view')
      )
    );

  // Get total content completed
  const completedResult = await db
    .select({ count: count() })
    .from(learningProgress)
    .where(
      and(
        eq(learningProgress.userId, userId),
        eq(learningProgress.isCompleted, true)
      )
    );

  // Get total time spent
  const timeSpentResult = await db
    .select({ total: sql<number>`SUM(${learningProgress.totalTimeSpent})` })
    .from(learningProgress)
    .where(eq(learningProgress.userId, userId));

  // Get average completion rate
  const avgCompletionResult = await db
    .select({ avg: sql<number>`AVG(${learningProgress.completionPercentage})` })
    .from(learningProgress)
    .where(eq(learningProgress.userId, userId));

  const totalContentViewed = viewedResult[0]?.count || 0;
  const totalContentCompleted = completedResult[0]?.count || 0;
  const totalTimeSpent = timeSpentResult[0]?.total || 0;
  const averageCompletionRate = avgCompletionResult[0]?.avg || 0;

  // Calculate current streak (simplified - days with learning activity)
  // This would need more complex logic for actual streak calculation
  const currentStreak = 0; // Placeholder

  return {
    totalContentViewed,
    totalContentCompleted,
    totalTimeSpent,
    averageCompletionRate,
    currentStreak,
  };
}
