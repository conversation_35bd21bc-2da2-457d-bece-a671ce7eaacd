import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema';

/**
 * Database connection configuration
 */
export interface DatabaseConfig {
  connectionString: string;
  max?: number;
  idle_timeout?: number;
  connect_timeout?: number;
}

/**
 * Create a database connection using the provided configuration
 */
export function createDatabaseConnection(config: DatabaseConfig) {
  // Create postgres client
  const client = postgres(config.connectionString, {
    max: config.max ?? 10,
    idle_timeout: config.idle_timeout ?? 20,
    connect_timeout: config.connect_timeout ?? 10,
  });

  // Create drizzle instance with schema
  const db = drizzle(client, { schema });

  return { db, client };
}

/**
 * Safely access process.env in environments where it might not exist
 */
function getProcessEnv(key: string): string | undefined {
  try {
    return (globalThis as any).process?.env?.[key];
  } catch {
    return undefined;
  }
}

/**
 * Create a database connection using environment variables
 * Supports both Node.js (process.env) and Cloudflare Workers (env binding)
 */
export function createDatabaseConnectionFromEnv(env?: Record<string, any>) {
  // Try Cloudflare Workers env binding first, then fall back to process.env
  const connectionString = env?.['DATABASE_URL'] || getProcessEnv('DATABASE_URL');

  if (!connectionString) {
    throw new Error(
      'DATABASE_URL environment variable is required. ' +
      'Please set it to your PostgreSQL connection string. ' +
      'For Cloudflare Workers, ensure it\'s defined in wrangler.jsonc vars section.'
    );
  }

  // For demo/development with dummy connection string, create a mock connection
  if (connectionString.includes('demo:demo@demo')) {
    console.warn('Using demo database connection - authentication features will be limited');

    // Create a mock postgres client that doesn't actually connect
    const mockClient = {
      end: () => Promise.resolve(),
    } as any;

    // Create a mock drizzle instance
    const mockDb = {
      select: () => ({ from: () => ({ where: () => Promise.resolve([]) }) }),
      insert: () => ({ values: () => Promise.resolve({ insertId: 'mock-id' }) }),
      update: () => ({ set: () => ({ where: () => Promise.resolve() }) }),
      delete: () => ({ where: () => Promise.resolve() }),
    } as any;

    return { db: mockDb, client: mockClient };
  }

  return createDatabaseConnection({ connectionString });
}

/**
 * Create a database connection specifically for Cloudflare Workers
 * This function accepts the Cloudflare Workers env binding
 */
export function createDatabaseConnectionForWorkers(env: Record<string, any>) {
  return createDatabaseConnectionFromEnv(env);
}

/**
 * Lazy-loaded database instance
 */
let _db: any = null;
let _client: any = null;

function getDatabase() {
  if (!_db) {
    const result = createDatabaseConnectionFromEnv();
    _db = result.db;
    _client = result.client;
  }
  return { db: _db, client: _client };
}

/**
 * Default database instance using environment variables
 * This is the main export that most applications should use
 */
export const db = new Proxy({} as any, {
  get(_target, prop) {
    return getDatabase().db[prop];
  }
});

export const client = new Proxy({} as any, {
  get(_target, prop) {
    return getDatabase().client[prop];
  }
});

// Export the database instance as default
export default db;
