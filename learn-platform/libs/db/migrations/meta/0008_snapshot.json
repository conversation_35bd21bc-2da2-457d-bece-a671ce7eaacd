{"id": "1322f982-eddf-4a60-b406-a9dee2d49d43", "prevId": "f729c3e7-1a27-41ab-b3d0-13c3728ec69c", "version": "7", "dialect": "postgresql", "tables": {"public.learning_content_analytics": {"name": "learning_content_analytics", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "content_id": {"name": "content_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false}, "event_type": {"name": "event_type", "type": "text", "primaryKey": false, "notNull": true}, "session_id": {"name": "session_id", "type": "text", "primaryKey": false, "notNull": false}, "step_id": {"name": "step_id", "type": "text", "primaryKey": false, "notNull": false}, "step_index": {"name": "step_index", "type": "integer", "primaryKey": false, "notNull": false}, "time_spent": {"name": "time_spent", "type": "integer", "primaryKey": false, "notNull": false}, "completion_percentage": {"name": "completion_percentage", "type": "integer", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"learning_content_analytics_content_id_learning_content_id_fk": {"name": "learning_content_analytics_content_id_learning_content_id_fk", "tableFrom": "learning_content_analytics", "tableTo": "learning_content", "columnsFrom": ["content_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "learning_content_analytics_user_id_user_id_fk": {"name": "learning_content_analytics_user_id_user_id_fk", "tableFrom": "learning_content_analytics", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.learning_content_feedback": {"name": "learning_content_feedback", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "content_id": {"name": "content_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": true}, "feedback_text": {"name": "feedback_text", "type": "text", "primaryKey": false, "notNull": false}, "is_helpful": {"name": "is_helpful", "type": "boolean", "primaryKey": false, "notNull": false}, "suggested_improvements": {"name": "suggested_improvements", "type": "json", "primaryKey": false, "notNull": false}, "request_regeneration": {"name": "request_regeneration", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "regeneration_reason": {"name": "regeneration_reason", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"learning_content_feedback_content_id_learning_content_id_fk": {"name": "learning_content_feedback_content_id_learning_content_id_fk", "tableFrom": "learning_content_feedback", "tableTo": "learning_content", "columnsFrom": ["content_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "learning_content_feedback_user_id_user_id_fk": {"name": "learning_content_feedback_user_id_user_id_fk", "tableFrom": "learning_content_feedback", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.learning_progress": {"name": "learning_progress", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "content_id": {"name": "content_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "current_step_index": {"name": "current_step_index", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "completed_steps": {"name": "completed_steps", "type": "json", "primaryKey": false, "notNull": false, "default": "'[]'::json"}, "total_time_spent": {"name": "total_time_spent", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "completion_percentage": {"name": "completion_percentage", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "is_completed": {"name": "is_completed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "bookmarks": {"name": "bookmarks", "type": "json", "primaryKey": false, "notNull": false, "default": "'[]'::json"}, "notes": {"name": "notes", "type": "json", "primaryKey": false, "notNull": false, "default": "'[]'::json"}, "last_accessed_at": {"name": "last_accessed_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "session_count": {"name": "session_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"learning_progress_content_id_learning_content_id_fk": {"name": "learning_progress_content_id_learning_content_id_fk", "tableFrom": "learning_progress", "tableTo": "learning_content", "columnsFrom": ["content_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "learning_progress_user_id_user_id_fk": {"name": "learning_progress_user_id_user_id_fk", "tableFrom": "learning_progress", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"learning_progress_content_id_user_id_unique": {"name": "learning_progress_content_id_user_id_unique", "nullsNotDistinct": false, "columns": ["content_id", "user_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.account": {"name": "account", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"account_user_id_user_id_fk": {"name": "account_user_id_user_id_fk", "tableFrom": "account", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.session": {"name": "session", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"session_user_id_user_id_fk": {"name": "session_user_id_user_id_fk", "tableFrom": "session", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"session_token_unique": {"name": "session_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "avatar": {"name": "avatar", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_unique": {"name": "user_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verification": {"name": "verification", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.explainer_templates": {"name": "explainer_templates", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "steps": {"name": "steps", "type": "json", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"explainer_templates_user_id_user_id_fk": {"name": "explainer_templates_user_id_user_id_fk", "tableFrom": "explainer_templates", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.learning_content": {"name": "learning_content", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "steps": {"name": "steps", "type": "json", "primaryKey": false, "notNull": true}, "learning_level": {"name": "learning_level", "type": "text", "primaryKey": false, "notNull": true}, "estimated_reading_time": {"name": "estimated_reading_time", "type": "integer", "primaryKey": false, "notNull": true}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "tags": {"name": "tags", "type": "json", "primaryKey": false, "notNull": false, "default": "'[]'::json"}, "ai_metadata": {"name": "ai_metadata", "type": "json", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"learning_content_user_id_user_id_fk": {"name": "learning_content_user_id_user_id_fk", "tableFrom": "learning_content", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.releases": {"name": "releases", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "version": {"name": "version", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "is_published": {"name": "is_published", "type": "boolean", "primaryKey": false, "notNull": true}, "published_at": {"name": "published_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "release_date": {"name": "release_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"releases_created_by_user_id_fk": {"name": "releases_created_by_user_id_fk", "tableFrom": "releases", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"releases_version_unique": {"name": "releases_version_unique", "nullsNotDistinct": false, "columns": ["version"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_release_notifications": {"name": "user_release_notifications", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "release_id": {"name": "release_id", "type": "text", "primaryKey": false, "notNull": true}, "is_read": {"name": "is_read", "type": "boolean", "primaryKey": false, "notNull": true}, "read_at": {"name": "read_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"user_release_notifications_user_id_user_id_fk": {"name": "user_release_notifications_user_id_user_id_fk", "tableFrom": "user_release_notifications", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_release_notifications_release_id_releases_id_fk": {"name": "user_release_notifications_release_id_releases_id_fk", "tableFrom": "user_release_notifications", "tableTo": "releases", "columnsFrom": ["release_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.quiz": {"name": "quiz", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "learning_content_id": {"name": "learning_content_id", "type": "text", "primaryKey": false, "notNull": true}, "difficulty": {"name": "difficulty", "type": "text", "primaryKey": false, "notNull": true}, "estimated_duration": {"name": "estimated_duration", "type": "integer", "primaryKey": false, "notNull": true}, "total_points": {"name": "total_points", "type": "integer", "primaryKey": false, "notNull": true}, "questions": {"name": "questions", "type": "json", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "allow_retakes": {"name": "allow_retakes", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "show_correct_answers": {"name": "show_correct_answers", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "shuffle_questions": {"name": "shuffle_questions", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "time_limit": {"name": "time_limit", "type": "integer", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"quiz_learning_content_idx": {"name": "quiz_learning_content_idx", "columns": [{"expression": "learning_content_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "quiz_created_by_idx": {"name": "quiz_created_by_idx", "columns": [{"expression": "created_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "quiz_difficulty_idx": {"name": "quiz_difficulty_idx", "columns": [{"expression": "difficulty", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "quiz_created_at_idx": {"name": "quiz_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"quiz_learning_content_id_learning_content_id_fk": {"name": "quiz_learning_content_id_learning_content_id_fk", "tableFrom": "quiz", "tableTo": "learning_content", "columnsFrom": ["learning_content_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "quiz_created_by_user_id_fk": {"name": "quiz_created_by_user_id_fk", "tableFrom": "quiz", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.quiz_analytics": {"name": "quiz_analytics", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "quiz_id": {"name": "quiz_id", "type": "text", "primaryKey": false, "notNull": true}, "total_attempts": {"name": "total_attempts", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "completed_attempts": {"name": "completed_attempts", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "average_score": {"name": "average_score", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "average_time_spent": {"name": "average_time_spent", "type": "integer", "primaryKey": false, "notNull": false}, "question_stats": {"name": "question_stats", "type": "json", "primaryKey": false, "notNull": false, "default": "'{}'::json"}, "score_distribution": {"name": "score_distribution", "type": "json", "primaryKey": false, "notNull": false, "default": "'{}'::json"}, "time_distribution": {"name": "time_distribution", "type": "json", "primaryKey": false, "notNull": false, "default": "'{}'::json"}, "daily_stats": {"name": "daily_stats", "type": "json", "primaryKey": false, "notNull": false, "default": "'{}'::json"}, "last_calculated_at": {"name": "last_calculated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"quiz_analytics_quiz_idx": {"name": "quiz_analytics_quiz_idx", "columns": [{"expression": "quiz_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "quiz_analytics_last_calculated_idx": {"name": "quiz_analytics_last_calculated_idx", "columns": [{"expression": "last_calculated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"quiz_analytics_quiz_id_quiz_id_fk": {"name": "quiz_analytics_quiz_id_quiz_id_fk", "tableFrom": "quiz_analytics", "tableTo": "quiz", "columnsFrom": ["quiz_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.quiz_attempt": {"name": "quiz_attempt", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "quiz_id": {"name": "quiz_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "started_at": {"name": "started_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_completed": {"name": "is_completed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "answers": {"name": "answers", "type": "json", "primaryKey": false, "notNull": false, "default": "'[]'::json"}, "total_time_spent": {"name": "total_time_spent", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "score": {"name": "score", "type": "json", "primaryKey": false, "notNull": false}, "question_results": {"name": "question_results", "type": "json", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}}, "indexes": {"quiz_attempt_quiz_user_idx": {"name": "quiz_attempt_quiz_user_idx", "columns": [{"expression": "quiz_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "quiz_attempt_user_idx": {"name": "quiz_attempt_user_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "quiz_attempt_completed_at_idx": {"name": "quiz_attempt_completed_at_idx", "columns": [{"expression": "completed_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "quiz_attempt_is_completed_idx": {"name": "quiz_attempt_is_completed_idx", "columns": [{"expression": "is_completed", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"quiz_attempt_quiz_id_quiz_id_fk": {"name": "quiz_attempt_quiz_id_quiz_id_fk", "tableFrom": "quiz_attempt", "tableTo": "quiz", "columnsFrom": ["quiz_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "quiz_attempt_user_id_user_id_fk": {"name": "quiz_attempt_user_id_user_id_fk", "tableFrom": "quiz_attempt", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.quiz_feedback": {"name": "quiz_feedback", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "quiz_id": {"name": "quiz_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "attempt_id": {"name": "attempt_id", "type": "text", "primaryKey": false, "notNull": false}, "difficulty_rating": {"name": "difficulty_rating", "type": "integer", "primaryKey": false, "notNull": false}, "clarity_rating": {"name": "clarity_rating", "type": "integer", "primaryKey": false, "notNull": false}, "relevance_rating": {"name": "relevance_rating", "type": "integer", "primaryKey": false, "notNull": false}, "overall_rating": {"name": "overall_rating", "type": "integer", "primaryKey": false, "notNull": false}, "comments": {"name": "comments", "type": "text", "primaryKey": false, "notNull": false}, "suggestions": {"name": "suggestions", "type": "text", "primaryKey": false, "notNull": false}, "question_feedback": {"name": "question_feedback", "type": "json", "primaryKey": false, "notNull": false, "default": "'[]'::json"}, "is_anonymous": {"name": "is_anonymous", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"quiz_feedback_quiz_user_idx": {"name": "quiz_feedback_quiz_user_idx", "columns": [{"expression": "quiz_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "quiz_feedback_overall_rating_idx": {"name": "quiz_feedback_overall_rating_idx", "columns": [{"expression": "overall_rating", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "quiz_feedback_created_at_idx": {"name": "quiz_feedback_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"quiz_feedback_quiz_id_quiz_id_fk": {"name": "quiz_feedback_quiz_id_quiz_id_fk", "tableFrom": "quiz_feedback", "tableTo": "quiz", "columnsFrom": ["quiz_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "quiz_feedback_user_id_user_id_fk": {"name": "quiz_feedback_user_id_user_id_fk", "tableFrom": "quiz_feedback", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "quiz_feedback_attempt_id_quiz_attempt_id_fk": {"name": "quiz_feedback_attempt_id_quiz_attempt_id_fk", "tableFrom": "quiz_feedback", "tableTo": "quiz_attempt", "columnsFrom": ["attempt_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.quiz_progress": {"name": "quiz_progress", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "attempt_id": {"name": "attempt_id", "type": "text", "primaryKey": false, "notNull": true}, "current_question_index": {"name": "current_question_index", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "questions_answered": {"name": "questions_answered", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "total_questions": {"name": "total_questions", "type": "integer", "primaryKey": false, "notNull": true}, "time_spent_so_far": {"name": "time_spent_so_far", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "last_active_at": {"name": "last_active_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "current_answers": {"name": "current_answers", "type": "json", "primaryKey": false, "notNull": false, "default": "'[]'::json"}, "bookmarked_questions": {"name": "bookmarked_questions", "type": "json", "primaryKey": false, "notNull": false, "default": "'[]'::json"}, "question_notes": {"name": "question_notes", "type": "json", "primaryKey": false, "notNull": false, "default": "'{}'::json"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"quiz_progress_attempt_idx": {"name": "quiz_progress_attempt_idx", "columns": [{"expression": "attempt_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "quiz_progress_last_active_idx": {"name": "quiz_progress_last_active_idx", "columns": [{"expression": "last_active_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"quiz_progress_attempt_id_quiz_attempt_id_fk": {"name": "quiz_progress_attempt_id_quiz_attempt_id_fk", "tableFrom": "quiz_progress", "tableTo": "quiz_attempt", "columnsFrom": ["attempt_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}