{"id": "f85dbb25-b1df-421e-ac06-7e639b7621fb", "prevId": "fe309809-be5b-49b3-9c6d-6ab54b6189ed", "version": "7", "dialect": "postgresql", "tables": {"public.learning_content_analytics": {"name": "learning_content_analytics", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "content_id": {"name": "content_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": false}, "event_type": {"name": "event_type", "type": "text", "primaryKey": false, "notNull": true}, "session_id": {"name": "session_id", "type": "text", "primaryKey": false, "notNull": false}, "step_id": {"name": "step_id", "type": "text", "primaryKey": false, "notNull": false}, "step_index": {"name": "step_index", "type": "integer", "primaryKey": false, "notNull": false}, "time_spent": {"name": "time_spent", "type": "integer", "primaryKey": false, "notNull": false}, "completion_percentage": {"name": "completion_percentage", "type": "integer", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"learning_content_analytics_content_id_learning_content_id_fk": {"name": "learning_content_analytics_content_id_learning_content_id_fk", "tableFrom": "learning_content_analytics", "tableTo": "learning_content", "columnsFrom": ["content_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "learning_content_analytics_user_id_user_id_fk": {"name": "learning_content_analytics_user_id_user_id_fk", "tableFrom": "learning_content_analytics", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.learning_content_feedback": {"name": "learning_content_feedback", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "content_id": {"name": "content_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": true}, "feedback_text": {"name": "feedback_text", "type": "text", "primaryKey": false, "notNull": false}, "is_helpful": {"name": "is_helpful", "type": "boolean", "primaryKey": false, "notNull": false}, "suggested_improvements": {"name": "suggested_improvements", "type": "json", "primaryKey": false, "notNull": false}, "request_regeneration": {"name": "request_regeneration", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "regeneration_reason": {"name": "regeneration_reason", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"learning_content_feedback_content_id_learning_content_id_fk": {"name": "learning_content_feedback_content_id_learning_content_id_fk", "tableFrom": "learning_content_feedback", "tableTo": "learning_content", "columnsFrom": ["content_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "learning_content_feedback_user_id_user_id_fk": {"name": "learning_content_feedback_user_id_user_id_fk", "tableFrom": "learning_content_feedback", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.learning_progress": {"name": "learning_progress", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "content_id": {"name": "content_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "current_step_index": {"name": "current_step_index", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "completed_steps": {"name": "completed_steps", "type": "json", "primaryKey": false, "notNull": false, "default": "'[]'::json"}, "total_time_spent": {"name": "total_time_spent", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "completion_percentage": {"name": "completion_percentage", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "is_completed": {"name": "is_completed", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "bookmarks": {"name": "bookmarks", "type": "json", "primaryKey": false, "notNull": false, "default": "'[]'::json"}, "notes": {"name": "notes", "type": "json", "primaryKey": false, "notNull": false, "default": "'[]'::json"}, "last_accessed_at": {"name": "last_accessed_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "session_count": {"name": "session_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"learning_progress_content_id_learning_content_id_fk": {"name": "learning_progress_content_id_learning_content_id_fk", "tableFrom": "learning_progress", "tableTo": "learning_content", "columnsFrom": ["content_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "learning_progress_user_id_user_id_fk": {"name": "learning_progress_user_id_user_id_fk", "tableFrom": "learning_progress", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.account": {"name": "account", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "account_id": {"name": "account_id", "type": "text", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"account_user_id_user_id_fk": {"name": "account_user_id_user_id_fk", "tableFrom": "account", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.session": {"name": "session", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"session_user_id_user_id_fk": {"name": "session_user_id_user_id_fk", "tableFrom": "session", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"session_token_unique": {"name": "session_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "avatar": {"name": "avatar", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_unique": {"name": "user_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verification": {"name": "verification", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.explainer_templates": {"name": "explainer_templates", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "steps": {"name": "steps", "type": "json", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"explainer_templates_user_id_user_id_fk": {"name": "explainer_templates_user_id_user_id_fk", "tableFrom": "explainer_templates", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.learning_content": {"name": "learning_content", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "steps": {"name": "steps", "type": "json", "primaryKey": false, "notNull": true}, "learning_level": {"name": "learning_level", "type": "text", "primaryKey": false, "notNull": true}, "estimated_reading_time": {"name": "estimated_reading_time", "type": "integer", "primaryKey": false, "notNull": true}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "tags": {"name": "tags", "type": "json", "primaryKey": false, "notNull": false, "default": "'[]'::json"}, "ai_metadata": {"name": "ai_metadata", "type": "json", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"learning_content_user_id_user_id_fk": {"name": "learning_content_user_id_user_id_fk", "tableFrom": "learning_content", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}