CREATE TABLE "learning_content" (
	"id" text PRIMARY KEY NOT NULL,
	"title" text NOT NULL,
	"description" text DEFAULT '',
	"steps" json NOT NULL,
	"learning_level" text NOT NULL,
	"estimated_reading_time" integer NOT NULL,
	"is_public" boolean DEFAULT false NOT NULL,
	"tags" json DEFAULT '[]'::json,
	"ai_metadata" json,
	"user_id" text NOT NULL,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL
);
--> statement-breakpoint
ALTER TABLE "learning_content" ADD CONSTRAINT "learning_content_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;