CREATE TABLE "quiz" (
	"id" text PRIMARY KEY NOT NULL,
	"title" text NOT NULL,
	"description" text DEFAULT '',
	"learning_content_id" text NOT NULL,
	"difficulty" text NOT NULL,
	"estimated_duration" integer NOT NULL,
	"total_points" integer NOT NULL,
	"questions" json NOT NULL,
	"metadata" json,
	"is_public" boolean DEFAULT false NOT NULL,
	"allow_retakes" boolean DEFAULT true NOT NULL,
	"show_correct_answers" boolean DEFAULT true NOT NULL,
	"shuffle_questions" boolean DEFAULT false NOT NULL,
	"time_limit" integer,
	"created_by" text NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "quiz_analytics" (
	"id" text PRIMARY KEY NOT NULL,
	"quiz_id" text NOT NULL,
	"total_attempts" integer DEFAULT 0 NOT NULL,
	"completed_attempts" integer DEFAULT 0 NOT NULL,
	"average_score" numeric(5, 2),
	"average_time_spent" integer,
	"question_stats" json DEFAULT '{}'::json,
	"score_distribution" json DEFAULT '{}'::json,
	"time_distribution" json DEFAULT '{}'::json,
	"daily_stats" json DEFAULT '{}'::json,
	"last_calculated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "quiz_attempt" (
	"id" text PRIMARY KEY NOT NULL,
	"quiz_id" text NOT NULL,
	"user_id" text NOT NULL,
	"started_at" timestamp DEFAULT now() NOT NULL,
	"completed_at" timestamp,
	"is_completed" boolean DEFAULT false NOT NULL,
	"answers" json DEFAULT '[]'::json,
	"total_time_spent" integer DEFAULT 0,
	"score" json,
	"question_results" json,
	"metadata" json
);
--> statement-breakpoint
CREATE TABLE "quiz_feedback" (
	"id" text PRIMARY KEY NOT NULL,
	"quiz_id" text NOT NULL,
	"user_id" text NOT NULL,
	"attempt_id" text,
	"difficulty_rating" integer,
	"clarity_rating" integer,
	"relevance_rating" integer,
	"overall_rating" integer,
	"comments" text,
	"suggestions" text,
	"question_feedback" json DEFAULT '[]'::json,
	"is_anonymous" boolean DEFAULT false NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "quiz_progress" (
	"id" text PRIMARY KEY NOT NULL,
	"attempt_id" text NOT NULL,
	"current_question_index" integer DEFAULT 0 NOT NULL,
	"questions_answered" integer DEFAULT 0 NOT NULL,
	"total_questions" integer NOT NULL,
	"time_spent_so_far" integer DEFAULT 0,
	"last_active_at" timestamp DEFAULT now() NOT NULL,
	"current_answers" json DEFAULT '[]'::json,
	"bookmarked_questions" json DEFAULT '[]'::json,
	"question_notes" json DEFAULT '{}'::json,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "quiz" ADD CONSTRAINT "quiz_learning_content_id_learning_content_id_fk" FOREIGN KEY ("learning_content_id") REFERENCES "public"."learning_content"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "quiz" ADD CONSTRAINT "quiz_created_by_user_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "quiz_analytics" ADD CONSTRAINT "quiz_analytics_quiz_id_quiz_id_fk" FOREIGN KEY ("quiz_id") REFERENCES "public"."quiz"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "quiz_attempt" ADD CONSTRAINT "quiz_attempt_quiz_id_quiz_id_fk" FOREIGN KEY ("quiz_id") REFERENCES "public"."quiz"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "quiz_attempt" ADD CONSTRAINT "quiz_attempt_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "quiz_feedback" ADD CONSTRAINT "quiz_feedback_quiz_id_quiz_id_fk" FOREIGN KEY ("quiz_id") REFERENCES "public"."quiz"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "quiz_feedback" ADD CONSTRAINT "quiz_feedback_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "quiz_feedback" ADD CONSTRAINT "quiz_feedback_attempt_id_quiz_attempt_id_fk" FOREIGN KEY ("attempt_id") REFERENCES "public"."quiz_attempt"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "quiz_progress" ADD CONSTRAINT "quiz_progress_attempt_id_quiz_attempt_id_fk" FOREIGN KEY ("attempt_id") REFERENCES "public"."quiz_attempt"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "quiz_learning_content_idx" ON "quiz" USING btree ("learning_content_id");--> statement-breakpoint
CREATE INDEX "quiz_created_by_idx" ON "quiz" USING btree ("created_by");--> statement-breakpoint
CREATE INDEX "quiz_difficulty_idx" ON "quiz" USING btree ("difficulty");--> statement-breakpoint
CREATE INDEX "quiz_created_at_idx" ON "quiz" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "quiz_analytics_quiz_idx" ON "quiz_analytics" USING btree ("quiz_id");--> statement-breakpoint
CREATE INDEX "quiz_analytics_last_calculated_idx" ON "quiz_analytics" USING btree ("last_calculated_at");--> statement-breakpoint
CREATE INDEX "quiz_attempt_quiz_user_idx" ON "quiz_attempt" USING btree ("quiz_id","user_id");--> statement-breakpoint
CREATE INDEX "quiz_attempt_user_idx" ON "quiz_attempt" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "quiz_attempt_completed_at_idx" ON "quiz_attempt" USING btree ("completed_at");--> statement-breakpoint
CREATE INDEX "quiz_attempt_is_completed_idx" ON "quiz_attempt" USING btree ("is_completed");--> statement-breakpoint
CREATE INDEX "quiz_feedback_quiz_user_idx" ON "quiz_feedback" USING btree ("quiz_id","user_id");--> statement-breakpoint
CREATE INDEX "quiz_feedback_overall_rating_idx" ON "quiz_feedback" USING btree ("overall_rating");--> statement-breakpoint
CREATE INDEX "quiz_feedback_created_at_idx" ON "quiz_feedback" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "quiz_progress_attempt_idx" ON "quiz_progress" USING btree ("attempt_id");--> statement-breakpoint
CREATE INDEX "quiz_progress_last_active_idx" ON "quiz_progress" USING btree ("last_active_at");