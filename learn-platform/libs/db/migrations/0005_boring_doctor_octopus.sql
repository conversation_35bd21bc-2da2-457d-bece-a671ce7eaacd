CREATE TABLE "learning_content_analytics" (
	"id" text PRIMARY KEY NOT NULL,
	"content_id" text NOT NULL,
	"user_id" text,
	"event_type" text NOT NULL,
	"session_id" text,
	"step_id" text,
	"step_index" integer,
	"time_spent" integer,
	"completion_percentage" integer,
	"metadata" json,
	"created_at" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "learning_content_feedback" (
	"id" text PRIMARY KEY NOT NULL,
	"content_id" text NOT NULL,
	"user_id" text NOT NULL,
	"rating" integer NOT NULL,
	"feedback_text" text,
	"is_helpful" boolean,
	"suggested_improvements" json,
	"request_regeneration" boolean DEFAULT false,
	"regeneration_reason" text,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL
);
--> statement-breakpoint
CREATE TABLE "learning_progress" (
	"id" text PRIMARY KEY NOT NULL,
	"content_id" text NOT NULL,
	"user_id" text NOT NULL,
	"current_step_index" integer DEFAULT 0,
	"completed_steps" json DEFAULT '[]'::json,
	"total_time_spent" integer DEFAULT 0,
	"completion_percentage" integer DEFAULT 0,
	"is_completed" boolean DEFAULT false,
	"bookmarks" json DEFAULT '[]'::json,
	"notes" json DEFAULT '[]'::json,
	"last_accessed_at" timestamp NOT NULL,
	"session_count" integer DEFAULT 1,
	"created_at" timestamp NOT NULL,
	"updated_at" timestamp NOT NULL
);
--> statement-breakpoint
ALTER TABLE "learning_content_analytics" ADD CONSTRAINT "learning_content_analytics_content_id_learning_content_id_fk" FOREIGN KEY ("content_id") REFERENCES "public"."learning_content"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "learning_content_analytics" ADD CONSTRAINT "learning_content_analytics_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "learning_content_feedback" ADD CONSTRAINT "learning_content_feedback_content_id_learning_content_id_fk" FOREIGN KEY ("content_id") REFERENCES "public"."learning_content"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "learning_content_feedback" ADD CONSTRAINT "learning_content_feedback_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "learning_progress" ADD CONSTRAINT "learning_progress_content_id_learning_content_id_fk" FOREIGN KEY ("content_id") REFERENCES "public"."learning_content"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "learning_progress" ADD CONSTRAINT "learning_progress_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;