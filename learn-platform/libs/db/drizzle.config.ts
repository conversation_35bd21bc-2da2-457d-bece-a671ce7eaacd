import { defineConfig } from 'drizzle-kit';

// Load environment variables from .env files
// This will load .env.local first, then .env as fallback
try {
  require('dotenv').config({ path: '.env.local' });
  console.log('✅ Loaded environment variables from .env.local');
} catch {
  // Fallback to .env if .env.local doesn't exist
  try {
    require('dotenv').config({ path: '.env' });
    console.log('✅ Loaded environment variables from .env');
  } catch {
    console.log('ℹ️  No .env files found, using system environment variables');
  }
}

// Safely access environment variables with validation
function getEnvVar(key: string): string {
  const value = process.env[key];

  if (!value) {
    console.error(`❌ Environment variable ${key} is not set.`);
    console.error(`📝 Please ensure ${key} is set in one of these files:`);
    console.error(`   - .env.local (recommended for local development)`);
    console.error(`   - .env (fallback)`);
    console.error(`💡 You can copy .env.example to .env.local and update the values.`);
    return '';
  }

  return value;
}

export default defineConfig({
  schema: './libs/db/src/schema/*',
  out: './libs/db/migrations',
  dialect: 'postgresql',
  dbCredentials: {
    url: getEnvVar('DATABASE_URL'),
  },
  verbose: true,
  strict: true,
});
