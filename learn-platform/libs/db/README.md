# @learn-platform/db

Centralized database library for the Learn Platform monorepo. Provides a complete Drizzle ORM setup with PostgreSQL and Supabase integration.

## Features

- 🗄️ **PostgreSQL Support**: Full PostgreSQL syntax with Drizzle ORM
- 🔗 **Supabase Integration**: Direct connection to Supabase PostgreSQL database
- 🏗️ **Schema Management**: Organized schema definitions with TypeScript types
- 🔄 **Migration Support**: Automated migration generation and execution
- 🎯 **Type Safety**: Full TypeScript support with inferred types
- 📦 **Monorepo Ready**: Designed for workspace-wide usage

## Installation

The library is already configured as part of the workspace. Dependencies are managed at the workspace level.

## Environment Setup

Create a `.env` file in your workspace root with your Supabase connection string:

```env
DATABASE_URL="postgresql://user:password@host:port/database"
```

For Supabase, you can find your connection string in:
1. Supabase Dashboard → Settings → Database
2. Look for "Connection string" under "Connection parameters"
3. Use the "URI" format

## Usage

### Basic Database Connection

```typescript
import { db } from '@learn-platform/db';

// The database instance is ready to use
const users = await db.select().from(schema.user);
```

### Using Schema and Types

```typescript
import { db, user } from '@learn-platform/db';

// Insert a new user
const newUser = {
  id: 'user_123',
  email: '<EMAIL>',
  name: 'John Doe',
  emailVerified: false,
};

const [insertedUser] = await db.insert(user).values(newUser).returning();

// Query users
const allUsers = await db.select().from(user);

// Query with conditions
import { eq } from '@learn-platform/db';
const user = await db.select().from(users).where(eq(users.email, '<EMAIL>'));
```

### Custom Connection Configuration

```typescript
import { createDatabaseConnection } from '@learn-platform/db';

const { db, client } = createDatabaseConnection({
  connectionString: 'your-connection-string',
  max: 20, // connection pool size
  idle_timeout: 30,
  connect_timeout: 15,
});
```

## Schema Management

### Adding New Tables

1. Create a new schema file in `src/schema/`:

```typescript
// src/schema/posts.ts
import { pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

export const posts = pgTable('posts', {
  id: uuid('id').primaryKey().defaultRandom(),
  title: text('title').notNull(),
  content: text('content'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

export type Post = typeof posts.$inferSelect;
export type NewPost = typeof posts.$inferInsert;
```

2. Export it from `src/schema/index.ts`:

```typescript
export * from './posts';
```

### Running Migrations

1. **Generate migration files** after schema changes:
```bash
bun run db:generate
```

2. **Apply migrations** to the database:
```bash
bun run db:migrate
```

## Available Scripts

- `bun run db:generate` - Generate migration files from schema changes
- `bun run db:migrate` - Apply pending migrations to the database

## Project Structure

```
libs/db/
├── src/
│   ├── index.ts              # Main exports
│   ├── connection.ts         # Database connection setup
│   └── schema/
│       ├── index.ts          # Schema exports
│       └── users.ts          # Example user schema
├── migrations/               # Generated migration files
├── drizzle.config.ts        # Drizzle Kit configuration
├── package.json             # Library dependencies
└── README.md               # This file
```

## Integration with Other Libraries

### Using with tRPC

```typescript
// In your tRPC context
import { db } from '@learn-platform/db';

export function createContext() {
  return {
    db,
    // ... other context properties
  };
}
```

### Using in API Routes

```typescript
// In Cloudflare Workers or API routes
import { db, user, eq } from '@learn-platform/db';

export async function getUserByEmail(email: string) {
  const [foundUser] = await db.select().from(user).where(eq(user.email, email));
  return foundUser;
}
```

## Best Practices

1. **Always use transactions** for related operations
2. **Use TypeScript types** for better development experience
3. **Keep schema files focused** - one table per file
4. **Use meaningful table and column names**
5. **Add indexes** for frequently queried columns
6. **Use environment variables** for connection strings

## Troubleshooting

### Connection Issues

- Verify your `DATABASE_URL` environment variable
- Check Supabase connection limits
- Ensure your IP is whitelisted in Supabase

### Migration Issues

- Always backup your database before running migrations
- Review generated migration files before applying
- Use transactions for complex migrations

## Contributing

When adding new schemas or modifying existing ones:

1. Follow the existing naming conventions
2. Add proper TypeScript types
3. Update this README if needed
4. Test your changes thoroughly
5. Generate and review migrations before committing
