/**
 * Type definitions for the AI service
 */

import { z } from 'zod';

// Learning levels
export type LearningLevel = 'beginner' | 'intermediate' | 'advanced';

// Content types matching MultiStepExplain component
export type ContentType = 
  | 'paragraph'
  | 'infoBox'
  | 'bulletList'
  | 'numberedList'
  | 'grid'
  | 'comparison'
  | 'table'
  | 'scatterPlot'
  | 'keyValueGrid';

// AI models available through OpenRouter
export type AIModel = 
  | 'anthropic/claude-3-sonnet'
  | 'anthropic/claude-3-opus'
  | 'openai/gpt-4o'
  | 'openai/gpt-4-turbo'
  | 'openai/gpt-3.5-turbo'
  | 'meta-llama/llama-3.1-70b-instruct'
  | 'meta-llama/llama-3.1-8b-instruct';

// Input for learning content generation
export interface LearningContentInput {
  topic: string;
  learningLevel: LearningLevel;
  preferredContentTypes: ContentType[];
  focusAreas?: string;
}

// Generated learning content structure
export interface GeneratedLearningContent {
  title: string;
  description: string;
  estimatedReadingTime: number;
  steps: LearningStep[];
  metadata: {
    aiModel: AIModel;
    generatedAt: string;
    contentTypes: ContentType[];
    learningLevel: LearningLevel;
  };
}

// Individual learning step
export interface LearningStep {
  id: string;
  title: string;
  icon: string;
  content: StepContent;
}

// Step content based on type
export type StepContent = 
  | ParagraphContent
  | InfoBoxContent
  | BulletListContent
  | NumberedListContent
  | GridContent
  | ComparisonContent
  | TableContent
  | ScatterPlotContent
  | KeyValueGridContent;

// Content type definitions
export interface ParagraphContent {
  type: 'paragraph';
  text: string;
}

export interface InfoBoxContent {
  type: 'infoBox';
  title: string;
  content: string;
  variant: 'info' | 'warning' | 'success' | 'error';
}

export interface BulletListContent {
  type: 'bulletList';
  items: string[];
}

export interface NumberedListContent {
  type: 'numberedList';
  items: string[];
}

export interface GridContent {
  type: 'grid';
  items: Array<{
    title: string;
    description: string;
    icon?: string;
  }>;
}

export interface ComparisonContent {
  type: 'comparison';
  title: string;
  items: Array<{
    label: string;
    before: string;
    after: string;
  }>;
}

export interface TableContent {
  type: 'table';
  headers: string[];
  rows: string[][];
}

export interface ScatterPlotContent {
  type: 'scatterPlot';
  title: string;
  xLabel: string;
  yLabel: string;
  data: Array<{
    x: number;
    y: number;
    label: string;
  }>;
}

export interface KeyValueGridContent {
  type: 'keyValueGrid';
  items: Array<{
    key: string;
    value: string;
  }>;
}

// Configuration options for content generation
export interface ContentGenerationOptions {
  model?: AIModel;
  temperature?: number;
  maxTokens?: number;
  maxSteps?: number;
  includeExamples?: boolean;
  includeVisuals?: boolean;
}

// Error types
export class AIServiceError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'AIServiceError';
  }
}

export class RateLimitError extends AIServiceError {
  constructor(message: string, details?: any) {
    super(message, 'RATE_LIMIT', details);
    this.name = 'RateLimitError';
  }
}

export class InvalidInputError extends AIServiceError {
  constructor(message: string, details?: any) {
    super(message, 'INVALID_INPUT', details);
    this.name = 'InvalidInputError';
  }
}

export class ModelError extends AIServiceError {
  constructor(message: string, details?: any) {
    super(message, 'MODEL_ERROR', details);
    this.name = 'ModelError';
  }
}
