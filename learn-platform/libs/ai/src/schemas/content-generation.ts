/**
 * Zod schemas for AI content generation
 * These schemas match the MultiStepExplain component structure exactly
 */

import { z } from 'zod';

// Learning levels
export const learningLevelSchema = z.enum(['beginner', 'intermediate', 'advanced']);

// Content types matching MultiStepExplain component
export const contentTypeSchema = z.enum([
  'paragraph',
  'infoBox',
  'bulletList',
  'numberedList',
  'grid',
  'comparison',
  'table',
  'scatterPlot',
  'keyValueGrid'
]);

// Input schema for learning content generation
export const learningContentInputSchema = z.object({
  topic: z.string().min(3).max(200),
  learningLevel: learningLevelSchema,
  preferredContentTypes: z.array(contentTypeSchema).min(1),
  focusAreas: z.string().max(500).optional(),
});

// Content type data schemas matching MultiStepExplain exactly

// 1. Paragraph - can be string or array of strings
export const paragraphDataSchema = z.union([
  z.string(),
  z.array(z.string())
]);

// 2. Info Box - heading and lines array
export const infoBoxDataSchema = z.object({
  heading: z.string().optional(),
  lines: z.array(z.string())
});

// 3. Bullet List - array of strings
export const bulletListDataSchema = z.array(z.string());

// 4. Numbered List - array of strings
export const numberedListDataSchema = z.array(z.string());

// 5. Grid - array of title/content objects
// Accept both 'content' and 'description' for AI compatibility
export const gridDataSchema = z.array(z.object({
  title: z.string(),
  content: z.string().optional(),
  description: z.string().optional()
}).refine(
  (data) => data.content || data.description,
  { message: "Either 'content' or 'description' must be provided" }
).transform((data) => ({
  title: data.title,
  content: data.content || data.description || ''
})));

// 6. Comparison - array of before/after objects
export const comparisonDataSchema = z.array(z.object({
  label: z.string(),
  before: z.string(),
  after: z.string()
}));

// 7. Table - headers and rows
export const tableDataSchema = z.object({
  headers: z.array(z.string()),
  rows: z.array(z.array(z.string()))
});

// 8. Scatter Plot - data points with coordinates
export const scatterPlotDataSchema = z.object({
  data: z.array(z.object({
    x: z.number(),
    y: z.number(),
    label: z.string()
  })),
  width: z.number().default(400),
  height: z.number().default(300)
});

// 9. Key-Value Grid - array of key/value pairs
export const keyValueDataSchema = z.array(z.object({
  key: z.string(),
  value: z.string()
}));

// Step configuration schema matching MultiStepExplain StepConfig
export const stepConfigSchema = z.object({
  title: z.string(),
  icon: z.string(), // Icon name as string for AI generation
  type: contentTypeSchema,
  data: z.any() // Will be validated based on type
});

// Generated learning content schema
export const generatedLearningContentSchema = z.object({
  title: z.string(),
  description: z.string(),
  estimatedReadingTime: z.number().min(1),
  steps: z.array(stepConfigSchema).min(1).max(15),
  metadata: z.object({
    aiModel: z.string(),
    generatedAt: z.string(),
    contentTypes: z.array(contentTypeSchema),
    learningLevel: learningLevelSchema,
    topic: z.string()
  })
});

// Validation function for step data based on type
export function validateStepData(type: string, data: any): boolean {
  try {
    switch (type) {
      case 'paragraph':
        paragraphDataSchema.parse(data);
        return true;
      case 'infoBox':
        infoBoxDataSchema.parse(data);
        return true;
      case 'bulletList':
        bulletListDataSchema.parse(data);
        return true;
      case 'numberedList':
        numberedListDataSchema.parse(data);
        return true;
      case 'grid':
        gridDataSchema.parse(data);
        return true;
      case 'comparison':
        comparisonDataSchema.parse(data);
        return true;
      case 'table':
        tableDataSchema.parse(data);
        return true;
      case 'scatterPlot':
        scatterPlotDataSchema.parse(data);
        return true;
      case 'keyValueGrid':
        keyValueDataSchema.parse(data);
        return true;
      default:
        return false;
    }
  } catch {
    return false;
  }
}

// Transform AI-generated data to expected format
export function transformAIGeneratedData(type: string, data: any): any {
  if (!data) return data;

  switch (type) {
    case 'paragraph':
      // Handle common AI-generated paragraph formats
      if (typeof data === 'string' || Array.isArray(data)) {
        return data; // Already in correct format
      }

      // Handle object formats
      if (typeof data === 'object') {
        // Try common property names
        if (data.content) return data.content;
        if (data.text) return data.text;
        if (data.paragraph) return data.paragraph;
        if (data.paragraphs) return data.paragraphs;
        if (data.body) return data.body;
        if (data.description) return data.description;

        // Handle nested data
        if (data.data && typeof data.data === 'object') {
          return transformAIGeneratedData(type, data.data);
        }
      }
      break;

    case 'infoBox':
      // Handle infoBox transformations
      if (typeof data === 'object' && data.lines) {
        return data; // Already correct
      }
      if (typeof data === 'object') {
        // Try to extract from common AI formats
        if (data.content && Array.isArray(data.content)) {
          return { heading: data.heading || data.title, lines: data.content };
        }
        if (data.items && Array.isArray(data.items)) {
          return { heading: data.heading || data.title, lines: data.items };
        }
      }
      break;

    case 'bulletList':
    case 'numberedList':
      // Handle list transformations
      if (Array.isArray(data)) {
        return data; // Already correct
      }
      if (typeof data === 'object') {
        if (data.items) return data.items;
        if (data.list) return data.list;
        if (data.points) return data.points;
        if (data.content && Array.isArray(data.content)) return data.content;
      }
      break;

    case 'grid':
      // Handle grid transformations
      if (Array.isArray(data)) {
        return data; // Already correct or will be validated
      }
      if (typeof data === 'object') {
        if (data.items && Array.isArray(data.items)) return data.items;
        if (data.grid && Array.isArray(data.grid)) return data.grid;
      }
      break;

    case 'comparison':
      // Handle comparison transformations
      if (Array.isArray(data)) {
        return data; // Already correct or will be validated
      }
      if (typeof data === 'object') {
        if (data.comparisons && Array.isArray(data.comparisons)) return data.comparisons;
        if (data.items && Array.isArray(data.items)) return data.items;
      }
      break;

    case 'table':
      // Handle table transformations
      if (typeof data === 'object' && data.headers && data.rows) {
        return data; // Already correct
      }
      if (typeof data === 'object') {
        if (data.table && typeof data.table === 'object') {
          return data.table;
        }
      }
      break;

    case 'keyValueGrid':
      // Handle keyValueGrid transformations
      if (Array.isArray(data)) {
        return data; // Already correct or will be validated
      }
      if (typeof data === 'object') {
        if (data.items && Array.isArray(data.items)) return data.items;
        if (data.pairs && Array.isArray(data.pairs)) return data.pairs;
        if (data.definitions && Array.isArray(data.definitions)) return data.definitions;
      }
      break;
  }

  return data; // Return original if no transformation applied
}

// Enhanced validation function with detailed error reporting
export function validateStepDataWithDetails(type: string, data: any): {
  isValid: boolean;
  error?: string;
  actualData?: any;
  expectedFormat?: string;
} {
  try {
    switch (type) {
      case 'paragraph':
        paragraphDataSchema.parse(data);
        return { isValid: true };
      case 'infoBox':
        infoBoxDataSchema.parse(data);
        return { isValid: true };
      case 'bulletList':
        bulletListDataSchema.parse(data);
        return { isValid: true };
      case 'numberedList':
        numberedListDataSchema.parse(data);
        return { isValid: true };
      case 'grid':
        gridDataSchema.parse(data);
        return { isValid: true };
      case 'comparison':
        comparisonDataSchema.parse(data);
        return { isValid: true };
      case 'table':
        tableDataSchema.parse(data);
        return { isValid: true };
      case 'scatterPlot':
        scatterPlotDataSchema.parse(data);
        return { isValid: true };
      case 'keyValueGrid':
        keyValueDataSchema.parse(data);
        return { isValid: true };
      default:
        return {
          isValid: false,
          error: `Unknown content type: ${type}`,
          actualData: data
        };
    }
  } catch (error: any) {
    const expectedFormats = {
      paragraph: 'string | string[] (e.g., "text" or ["para1", "para2"])',
      infoBox: '{ heading?: string, lines: string[] }',
      bulletList: 'string[] (e.g., ["item1", "item2"])',
      numberedList: 'string[] (e.g., ["step1", "step2"])',
      grid: '{ title: string, content: string }[]',
      comparison: '{ label: string, before: string, after: string }[]',
      table: '{ headers: string[], rows: string[][] }',
      scatterPlot: '{ data: { x: number, y: number, label: string }[], width?: number, height?: number }',
      keyValueGrid: '{ key: string, value: string }[]'
    };

    return {
      isValid: false,
      error: error.message || 'Validation failed',
      actualData: data,
      expectedFormat: expectedFormats[type as keyof typeof expectedFormats] || 'Unknown format'
    };
  }
}

// Helper function to get schema for a specific content type
export function getSchemaForContentType(type: string) {
  switch (type) {
    case 'paragraph':
      return paragraphDataSchema;
    case 'infoBox':
      return infoBoxDataSchema;
    case 'bulletList':
      return bulletListDataSchema;
    case 'numberedList':
      return numberedListDataSchema;
    case 'grid':
      return gridDataSchema;
    case 'comparison':
      return comparisonDataSchema;
    case 'table':
      return tableDataSchema;
    case 'scatterPlot':
      return scatterPlotDataSchema;
    case 'keyValueGrid':
      return keyValueDataSchema;
    default:
      throw new Error(`Unknown content type: ${type}`);
  }
}

// Export types inferred from schemas
export type LearningContentInput = z.infer<typeof learningContentInputSchema>;
export type GeneratedLearningContent = z.infer<typeof generatedLearningContentSchema>;
export type StepConfig = z.infer<typeof stepConfigSchema>;
export type ContentType = z.infer<typeof contentTypeSchema>;
export type LearningLevel = z.infer<typeof learningLevelSchema>;

// Content type data types
export type ParagraphData = z.infer<typeof paragraphDataSchema>;
export type InfoBoxData = z.infer<typeof infoBoxDataSchema>;
export type BulletListData = z.infer<typeof bulletListDataSchema>;
export type NumberedListData = z.infer<typeof numberedListDataSchema>;
export type GridData = z.infer<typeof gridDataSchema>;
export type ComparisonData = z.infer<typeof comparisonDataSchema>;
export type TableData = z.infer<typeof tableDataSchema>;
export type ScatterPlotData = z.infer<typeof scatterPlotDataSchema>;
export type KeyValueData = z.infer<typeof keyValueDataSchema>;
