/**
 * Zod schemas and TypeScript interfaces for quiz generation
 * Supports 7 quiz types: flashcards, MCQ, true/false, fill-in-blank, matching, free text, ordering
 */

import { z } from 'zod';

// Quiz difficulty levels
export const quizDifficultySchema = z.enum(['easy', 'medium', 'hard']);
export type QuizDifficulty = z.infer<typeof quizDifficultySchema>;

// Quiz types
export const quizTypeSchema = z.enum([
  'flashcard',
  'multipleChoice',
  'trueFalse',
  'fillInBlank',
  'matching',
  'freeText',
  'ordering'
]);
export type QuizType = z.infer<typeof quizTypeSchema>;

// Base quiz question interface
export const baseQuestionSchema = z.object({
  id: z.string(),
  type: quizTypeSchema,
  difficulty: quizDifficultySchema,
  sourceStepId: z.string(), // Reference to the step this question was generated from
  sourceContent: z.string(), // Exact content excerpt used to generate this question
  points: z.number().min(1).max(10).default(1),
});

// Flashcard question schema
export const flashcardQuestionSchema = baseQuestionSchema.extend({
  type: z.literal('flashcard'),
  front: z.string().min(1).max(500), // Term or question
  back: z.string().min(1).max(1000), // Definition or answer
  hint: z.string().max(200).optional(),
});

// Multiple choice question schema
export const multipleChoiceQuestionSchema = baseQuestionSchema.extend({
  type: z.literal('multipleChoice'),
  question: z.string().min(1).max(1000),
  options: z.array(z.string().min(1).max(300)).length(4),
  correctAnswerIndex: z.number().min(0).max(3),
  explanation: z.string().max(500).optional(),
});

// True/False question schema
export const trueFalseQuestionSchema = baseQuestionSchema.extend({
  type: z.literal('trueFalse'),
  statement: z.string().min(1).max(500),
  correctAnswer: z.boolean(),
  explanation: z.string().max(500).optional(),
});

// Fill in the blank question schema
export const fillInBlankQuestionSchema = baseQuestionSchema.extend({
  type: z.literal('fillInBlank'),
  text: z.string().min(1).max(1000), // Text with _____ placeholders
  blanks: z.array(z.object({
    position: z.number().min(0), // Position of blank in text
    correctAnswer: z.string().min(1).max(100),
    acceptableAnswers: z.array(z.string()).optional(), // Alternative correct answers
    caseSensitive: z.boolean().default(false),
  })).min(1).max(5),
  hint: z.string().max(200).optional(),
});

// Matching pairs question schema
export const matchingQuestionSchema = baseQuestionSchema.extend({
  type: z.literal('matching'),
  instruction: z.string().max(200).default('Match the following items:'),
  pairs: z.array(z.object({
    left: z.string().min(1).max(200), // Term or concept
    right: z.string().min(1).max(300), // Definition or description
  })).min(3).max(8),
});

// Free text question schema
export const freeTextQuestionSchema = baseQuestionSchema.extend({
  type: z.literal('freeText'),
  question: z.string().min(1).max(1000),
  answerType: z.enum(['short', 'long']), // short: 1-2 sentences, long: paragraph
  maxLength: z.number().min(50).max(2000),
  sampleAnswer: z.string().min(1).max(2000), // Example of a good answer
  evaluationCriteria: z.array(z.string()).min(1).max(5), // Key points to look for
});

// Ordering/Sequencing question schema
export const orderingQuestionSchema = baseQuestionSchema.extend({
  type: z.literal('ordering'),
  instruction: z.string().max(200).default('Arrange the following items in the correct order:'),
  items: z.array(z.string().min(1).max(300)).min(3).max(8),
  correctOrder: z.array(z.number()).min(3).max(8), // Indices representing correct order
  orderType: z.enum(['chronological', 'logical', 'priority', 'process']).default('logical'),
});

// Union of all question types
export const quizQuestionSchema = z.discriminatedUnion('type', [
  flashcardQuestionSchema,
  multipleChoiceQuestionSchema,
  trueFalseQuestionSchema,
  fillInBlankQuestionSchema,
  matchingQuestionSchema,
  freeTextQuestionSchema,
  orderingQuestionSchema,
]);

export type QuizQuestion = z.infer<typeof quizQuestionSchema>;
export type FlashcardQuestion = z.infer<typeof flashcardQuestionSchema>;
export type MultipleChoiceQuestion = z.infer<typeof multipleChoiceQuestionSchema>;
export type TrueFalseQuestion = z.infer<typeof trueFalseQuestionSchema>;
export type FillInBlankQuestion = z.infer<typeof fillInBlankQuestionSchema>;
export type MatchingQuestion = z.infer<typeof matchingQuestionSchema>;
export type FreeTextQuestion = z.infer<typeof freeTextQuestionSchema>;
export type OrderingQuestion = z.infer<typeof orderingQuestionSchema>;

// Quiz generation input schema
export const quizGenerationInputSchema = z.object({
  learningContentId: z.string(),
  quizTypes: z.array(quizTypeSchema).min(1).max(7),
  difficulty: quizDifficultySchema.default('medium'),
  questionsPerType: z.number().min(1).max(10).default(3),
  includeHints: z.boolean().default(true),
  includeExplanations: z.boolean().default(true),
});

export type QuizGenerationInput = z.infer<typeof quizGenerationInputSchema>;

// Generated quiz schema
export const generatedQuizSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string(),
  learningContentId: z.string(),
  questions: z.array(quizQuestionSchema).min(1).max(50),
  estimatedDuration: z.number().min(1), // in minutes
  totalPoints: z.number().min(1),
  metadata: z.object({
    generatedAt: z.string(),
    aiModel: z.string(),
    sourceStepsUsed: z.array(z.string()), // Step IDs that were used for generation
    difficultyDistribution: z.record(quizDifficultySchema, z.number()),
    typeDistribution: z.record(quizTypeSchema, z.number()),
  }),
});

export type GeneratedQuiz = z.infer<typeof generatedQuizSchema>;

// Utility function to calculate dynamic max questions based on request parameters
export function calculateMaxQuestions(quizTypes: QuizType[], questionsPerType: number): number {
  const baseExpected = quizTypes.length * questionsPerType;
  const withBuffer = Math.ceil(baseExpected * 1.2); // 20% buffer for AI variance
  const safeMax = Math.min(withBuffer, 50); // Cap at 50 (from generatedQuizSchema limit)
  return Math.max(safeMax, 1); // Minimum 1 question
}

// Factory function to create dynamic quiz schemas based on expected question count
export function createDynamicQuizSchemas(maxQuestions: number) {
  const questionObjectSchema = z.object({
    id: z.string(),
    type: quizTypeSchema,
    difficulty: quizDifficultySchema,
    points: z.number().min(1).max(10).default(1),

    // Flashcard fields (when type === 'flashcard')
    front: z.string().optional(),
    back: z.string().optional(),
    hint: z.string().optional(),

    // Multiple choice fields (when type === 'multipleChoice')
    question: z.string().optional(),
    options: z.array(z.string()).optional(),
    correctAnswerIndex: z.number().optional(),
    explanation: z.string().optional(),

    // True/False fields (when type === 'trueFalse')
    statement: z.string().optional(),
    correctAnswer: z.boolean().optional(),

    // Fill in blank fields (when type === 'fillInBlank')
    text: z.string().optional(),
    blanks: z.array(z.object({
      position: z.number(),
      correctAnswer: z.string(),
      acceptableAnswers: z.array(z.string()).optional(),
      caseSensitive: z.boolean().default(false),
    })).optional(),

    // Matching fields (when type === 'matching')
    pairs: z.array(z.object({
      left: z.string(),
      right: z.string(),
    })).optional(),

    // Free text fields (when type === 'freeText')
    answerType: z.enum(['short', 'long']).optional(),
    maxLength: z.number().optional(),
    sampleAnswer: z.string().optional(),
    evaluationCriteria: z.array(z.string()).optional(),

    // Ordering fields (when type === 'ordering')
    items: z.array(z.string()).optional(),
    correctOrder: z.array(z.number()).optional(),
    orderType: z.enum(['chronological', 'logical', 'priority', 'process']).optional(),

    // Shared instruction field (used by matching, ordering)
    instruction: z.string().optional(),
  });

  return {
    aiQuizGenerationResponseSchema: z.object({
      questions: z.array(questionObjectSchema).min(1).max(maxQuestions)
    }),
    metaCompatibleQuizSchema: z.object({
      questions: z.array(z.object({
        id: z.string(),
        type: quizTypeSchema,
        difficulty: quizDifficultySchema,
        points: z.number().min(1).max(10).default(1),

        // Flashcard fields (when type === 'flashcard')
        front: z.string().optional(),
        back: z.string().optional(),
        hint: z.string().optional(),

        // Multiple choice fields (when type === 'multipleChoice')
        question: z.string().optional(),
        optionsJson: z.string().optional(), // JSON array of strings
        correctAnswerIndex: z.number().optional(),
        explanation: z.string().optional(),

        // True/False fields (when type === 'trueFalse')
        statement: z.string().optional(),
        correctAnswer: z.boolean().optional(),

        // Fill in blank fields (when type === 'fillInBlank')
        text: z.string().optional(),
        blanksJson: z.string().optional(), // JSON array of blank objects

        // Matching fields (when type === 'matching')
        instruction: z.string().optional(),
        pairsJson: z.string().optional(), // JSON array of pair objects

        // Free text fields (when type === 'freeText')
        answerType: z.enum(['short', 'long']).optional(),
        maxLength: z.number().optional(),
        sampleAnswer: z.string().optional(),
        evaluationCriteriaJson: z.string().optional(), // JSON array of strings

        // Ordering fields (when type === 'ordering')
        itemsJson: z.string().optional(), // JSON array of strings
        correctOrderJson: z.string().optional(), // JSON array of numbers
        orderType: z.enum(['chronological', 'logical', 'priority', 'process']).optional(),
      })).min(1).max(maxQuestions)
    })
  };
}

// Simplified schema for AI generation to avoid JSON Schema conversion issues
// This schema uses a flattened structure that's easier for AI to generate
export const aiQuizGenerationResponseSchema = z.object({
  questions: z.array(z.object({
    id: z.string(),
    type: quizTypeSchema,
    difficulty: quizDifficultySchema,
    points: z.number().min(1).max(10).default(1),

    // Flashcard fields (when type === 'flashcard')
    front: z.string().optional(),
    back: z.string().optional(),
    hint: z.string().optional(),

    // Multiple choice fields (when type === 'multipleChoice')
    question: z.string().optional(),
    options: z.array(z.string()).optional(),
    correctAnswerIndex: z.number().optional(),
    explanation: z.string().optional(),

    // True/False fields (when type === 'trueFalse')
    statement: z.string().optional(),
    correctAnswer: z.boolean().optional(),

    // Fill in blank fields (when type === 'fillInBlank')
    text: z.string().optional(),
    blanks: z.array(z.object({
      position: z.number(),
      correctAnswer: z.string(),
      acceptableAnswers: z.array(z.string()).optional(),
      caseSensitive: z.boolean().default(false),
    })).optional(),

    // Matching fields (when type === 'matching')
    instruction: z.string().optional(),
    pairs: z.array(z.object({
      left: z.string(),
      right: z.string(),
    })).optional(),

    // Free text fields (when type === 'freeText')
    answerType: z.enum(['short', 'long']).optional(),
    maxLength: z.number().optional(),
    sampleAnswer: z.string().optional(),
    evaluationCriteria: z.array(z.string()).optional(),

    // Ordering fields (when type === 'ordering')
    items: z.array(z.string()).optional(),
    correctOrder: z.array(z.number()).optional(),
    orderType: z.enum(['chronological', 'logical', 'priority', 'process']).optional(),
  })).min(1).max(10)
});

export type AIQuizGenerationResponse = z.infer<typeof aiQuizGenerationResponseSchema>;

// Meta-compatible schema with flattened structure to avoid "tools field exceeds max depth limit" error
// Uses JSON strings for complex nested data to stay within 2-3 level depth limit
export const metaCompatibleQuizSchema = z.object({
  questions: z.array(z.object({
    id: z.string(),
    type: quizTypeSchema,
    difficulty: quizDifficultySchema,
    points: z.number().min(1).max(10).default(1),

    // Flashcard fields (when type === 'flashcard')
    front: z.string().optional(),
    back: z.string().optional(),
    hint: z.string().optional(),

    // Multiple choice fields (when type === 'multipleChoice')
    question: z.string().optional(),
    optionsJson: z.string().optional(), // JSON array of strings
    correctAnswerIndex: z.number().optional(),
    explanation: z.string().optional(),

    // True/False fields (when type === 'trueFalse')
    statement: z.string().optional(),
    correctAnswer: z.boolean().optional(),

    // Fill in blank fields (when type === 'fillInBlank')
    text: z.string().optional(),
    blanksJson: z.string().optional(), // JSON array of blank objects

    // Matching fields (when type === 'matching')
    instruction: z.string().optional(),
    pairsJson: z.string().optional(), // JSON array of pair objects

    // Free text fields (when type === 'freeText')
    answerType: z.enum(['short', 'long']).optional(),
    maxLength: z.number().optional(),
    sampleAnswer: z.string().optional(),
    evaluationCriteriaJson: z.string().optional(), // JSON array of strings

    // Ordering fields (when type === 'ordering')
    itemsJson: z.string().optional(), // JSON array of strings
    correctOrderJson: z.string().optional(), // JSON array of numbers
    orderType: z.enum(['chronological', 'logical', 'priority', 'process']).optional(),
  })).min(1).max(10)
});

export type MetaCompatibleQuizResponse = z.infer<typeof metaCompatibleQuizSchema>;

// Quiz answer submission schemas
export const quizAnswerSchema = z.object({
  questionId: z.string(),
  questionType: quizTypeSchema,
  answer: z.union([
    z.string(), // For text-based answers
    z.number(), // For MCQ index
    z.boolean(), // For true/false
    z.array(z.string()), // For fill-in-blank multiple answers
    z.array(z.object({ left: z.string(), right: z.string() })), // For matching pairs
    z.array(z.number()), // For ordering (indices)
  ]),
  timeSpent: z.number().min(0), // seconds spent on this question
});

export const quizSubmissionSchema = z.object({
  quizId: z.string(),
  answers: z.array(quizAnswerSchema),
  totalTimeSpent: z.number().min(0), // total seconds spent on quiz
  completedAt: z.string(),
});

export type QuizAnswer = z.infer<typeof quizAnswerSchema>;
export type QuizSubmission = z.infer<typeof quizSubmissionSchema>;

// Quiz result schema
export const quizResultSchema = z.object({
  quizId: z.string(),
  userId: z.string(),
  submission: quizSubmissionSchema,
  score: z.object({
    totalPoints: z.number(),
    earnedPoints: z.number(),
    percentage: z.number().min(0).max(100),
    correctAnswers: z.number(),
    totalQuestions: z.number(),
  }),
  questionResults: z.array(z.object({
    questionId: z.string(),
    isCorrect: z.boolean(),
    pointsEarned: z.number(),
    feedback: z.string().optional(),
  })),
  completedAt: z.string(),
  timeSpent: z.number(),
});

export type QuizResult = z.infer<typeof quizResultSchema>;

// Content validation schema for ensuring no hallucination
export const contentValidationSchema = z.object({
  sourceContent: z.string(),
  generatedQuestion: quizQuestionSchema,
  validationChecks: z.object({
    contentMatch: z.boolean(), // Question content matches source
    noHallucination: z.boolean(), // No invented facts or details
    appropriateDifficulty: z.boolean(), // Difficulty matches content complexity
    clearlyAnswerable: z.boolean(), // Question can be answered from source
  }),
  confidence: z.number().min(0).max(1), // AI confidence in validation
});

export type ContentValidation = z.infer<typeof contentValidationSchema>;

// Utility functions for converting between Meta-compatible and full schemas
export function convertMetaResponseToFull(metaResponse: MetaCompatibleQuizResponse): AIQuizGenerationResponse {
  // Helper function to safely parse JSON with error handling
  function safeJsonParse(jsonString: string | undefined, fallback: any = undefined): any {
    if (!jsonString) return fallback;
    try {
      return JSON.parse(jsonString);
    } catch (error) {
      console.warn('Failed to parse JSON string:', jsonString, error);
      return fallback;
    }
  }

  return {
    questions: metaResponse.questions.map(q => {
      const baseQuestion: any = {
        id: q.id,
        type: q.type,
        difficulty: q.difficulty,
        points: q.points,
      };

      // Add type-specific fields with safe JSON parsing
      if (q.type === 'flashcard') {
        baseQuestion.front = q.front;
        baseQuestion.back = q.back;
        baseQuestion.hint = q.hint;
      } else if (q.type === 'multipleChoice') {
        baseQuestion.question = q.question;
        baseQuestion.options = safeJsonParse(q.optionsJson, []);
        baseQuestion.correctAnswerIndex = q.correctAnswerIndex;
        baseQuestion.explanation = q.explanation;
      } else if (q.type === 'trueFalse') {
        baseQuestion.statement = q.statement;
        baseQuestion.correctAnswer = q.correctAnswer;
        baseQuestion.explanation = q.explanation;
      } else if (q.type === 'fillInBlank') {
        baseQuestion.text = q.text;
        baseQuestion.blanks = safeJsonParse(q.blanksJson, []);
        baseQuestion.hint = q.hint;
      } else if (q.type === 'matching') {
        baseQuestion.instruction = q.instruction;
        baseQuestion.pairs = safeJsonParse(q.pairsJson, []);
      } else if (q.type === 'freeText') {
        baseQuestion.question = q.question;
        baseQuestion.answerType = q.answerType;
        baseQuestion.maxLength = q.maxLength;
        baseQuestion.sampleAnswer = q.sampleAnswer;
        baseQuestion.evaluationCriteria = safeJsonParse(q.evaluationCriteriaJson, []);
      } else if (q.type === 'ordering') {
        baseQuestion.instruction = q.instruction;
        baseQuestion.items = safeJsonParse(q.itemsJson, []);
        baseQuestion.correctOrder = safeJsonParse(q.correctOrderJson, []);
        baseQuestion.orderType = q.orderType;
      }

      return baseQuestion;
    })
  };
}
