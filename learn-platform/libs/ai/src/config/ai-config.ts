/**
 * AI service configuration for OpenRouter and Vercel AI SDK
 */

import { createOpenRouter } from '@openrouter/ai-sdk-provider';
import type { ContentType, LearningLevel } from '../schemas/content-generation';

// Environment variables with defaults
export const AI_CONFIG = {
  // OpenRouter configuration
  apiKey: process.env['OPENROUTER_API_KEY'] || '',
  appName: process.env['OPENROUTER_APP_NAME'] || 'Learning Platform',
  siteUrl: process.env['OPENROUTER_SITE_URL'] || 'http://localhost:3000',

  // Default model settings - prioritize free models that support structured outputs
  defaultModel: 'openai/gpt-4o-mini' as const,
  fallbackModel: 'openai/gpt-3.5-turbo' as const,

  // Generation parameters
  temperature: 0.7,
  maxTokens: 4000,
  maxSteps: 10,

  // Timeouts (in milliseconds)
  requestTimeout: 60000, // 60 seconds
  retryDelay: 2000, // 2 seconds
  maxRetries: 3,
} as const;

// Model configurations for different use cases
export const MODEL_CONFIGS = {
  // FREE MODELS - Most don't support structured output, but some newer ones do
  'openai/gpt-4o-mini': {
    temperature: 0.7,
    maxTokens: 4000,
    strengths: ['reasoning', 'structured-output', 'cost-effective', 'tool-calling'] as ModelStrength[],
    costTier: 'free' as CostTier,
    pricing: { prompt: '0', completion: '0' },
    supportsStructuredOutput: true, // This free model supports tool calling and structured outputs
  },

  'deepseek/deepseek-r1:free': {
    temperature: 0.7,
    maxTokens: 4000,
    strengths: ['reasoning', 'cost-effective'] as ModelStrength[],
    costTier: 'free' as CostTier,
    pricing: { prompt: '0', completion: '0' },
    supportsStructuredOutput: false, // Most free models don't support structured outputs on OpenRouter
  },

  'meta-llama/llama-3.1-8b-instruct:free': {
    temperature: 0.8,
    maxTokens: 3000,
    strengths: ['general-purpose', 'cost-effective'] as ModelStrength[],
    costTier: 'free' as CostTier,
    pricing: { prompt: '0', completion: '0' },
    supportsStructuredOutput: false, // Most free models don't support structured outputs on OpenRouter
  },

  'mistralai/mistral-7b-instruct:free': {
    temperature: 0.7,
    maxTokens: 3000,
    strengths: ['fast', 'cost-effective'] as ModelStrength[],
    costTier: 'free' as CostTier,
    pricing: { prompt: '0', completion: '0' },
    supportsStructuredOutput: false, // Most free models don't support structured outputs on OpenRouter
  },

  // PREMIUM MODELS - Fallback for complex tasks
  'anthropic/claude-3-sonnet': {
    temperature: 0.7,
    maxTokens: 4000,
    strengths: ['reasoning', 'explanations', 'structured-output'] as ModelStrength[],
    costTier: 'medium' as CostTier,
    pricing: { prompt: '0.000003', completion: '0.000015' },
    supportsStructuredOutput: true,
  },

  // Most capable for complex topics
  'anthropic/claude-3-opus': {
    temperature: 0.6,
    maxTokens: 4000,
    strengths: ['complex-reasoning', 'detailed-explanations', 'accuracy'] as ModelStrength[],
    costTier: 'high' as CostTier,
    pricing: { prompt: '0.000015', completion: '0.000075' },
    supportsStructuredOutput: true,
  },

  // Reliable structured outputs
  'openai/gpt-4o': {
    temperature: 0.7,
    maxTokens: 4000,
    strengths: ['structured-output', 'consistency', 'speed'] as ModelStrength[],
    costTier: 'medium' as CostTier,
    pricing: { prompt: '0.0000025', completion: '0.00001' },
    supportsStructuredOutput: true,
  },

  // Fast and capable
  'openai/gpt-4-turbo': {
    temperature: 0.7,
    maxTokens: 4000,
    strengths: ['speed', 'general-purpose', 'cost-effective'] as ModelStrength[],
    costTier: 'medium' as CostTier,
    pricing: { prompt: '0.00001', completion: '0.00003' },
    supportsStructuredOutput: true,
  },

  // Cost-effective option - but doesn't support structured outputs reliably
  'meta-llama/llama-3.1-70b-instruct': {
    temperature: 0.8,
    maxTokens: 3000,
    strengths: ['cost-effective', 'open-source', 'general-purpose'] as ModelStrength[],
    costTier: 'low' as CostTier,
    pricing: { prompt: '0.00000088', completion: '0.00000088' },
    supportsStructuredOutput: false, // Fails with AI_NoObjectGeneratedError
  },

  // Most cost-effective reliable structured output model
  'openai/gpt-3.5-turbo': {
    temperature: 0.7,
    maxTokens: 3000,
    strengths: ['speed', 'cost-effective', 'reliable', 'structured-output'] as ModelStrength[],
    costTier: 'low' as CostTier,
    pricing: { prompt: '0.0000005', completion: '0.0000015' },
    supportsStructuredOutput: true, // Verified working with OpenRouter
  },
} as const;

// Helper functions for model selection
export function getFreeModels(): (keyof typeof MODEL_CONFIGS)[] {
  return Object.keys(MODEL_CONFIGS).filter(
    model => MODEL_CONFIGS[model as keyof typeof MODEL_CONFIGS].costTier === 'free'
  ) as (keyof typeof MODEL_CONFIGS)[];
}

export function getCheapestModels(): (keyof typeof MODEL_CONFIGS)[] {
  return Object.keys(MODEL_CONFIGS)
    .filter(model => MODEL_CONFIGS[model as keyof typeof MODEL_CONFIGS].costTier === 'low')
    .sort((a, b) => {
      const aConfig = MODEL_CONFIGS[a as keyof typeof MODEL_CONFIGS];
      const bConfig = MODEL_CONFIGS[b as keyof typeof MODEL_CONFIGS];
      const aPrice = parseFloat(aConfig.pricing?.prompt || '0') + parseFloat(aConfig.pricing?.completion || '0');
      const bPrice = parseFloat(bConfig.pricing?.prompt || '0') + parseFloat(bConfig.pricing?.completion || '0');
      return aPrice - bPrice;
    }) as (keyof typeof MODEL_CONFIGS)[];
}

export function getModelsByStructuredOutputSupport(): (keyof typeof MODEL_CONFIGS)[] {
  return Object.keys(MODEL_CONFIGS).filter(
    model => MODEL_CONFIGS[model as keyof typeof MODEL_CONFIGS].supportsStructuredOutput
  ) as (keyof typeof MODEL_CONFIGS)[];
}

// Get cost-effective models that reliably support structured outputs
export function getReliableStructuredOutputModels(): (keyof typeof MODEL_CONFIGS)[] {
  return getModelsByStructuredOutputSupport()
    .filter(model => MODEL_CONFIGS[model].costTier === 'low' || MODEL_CONFIGS[model].costTier === 'medium')
    .sort((a, b) => {
      // Sort by cost (cheapest first)
      const aPrice = parseFloat(MODEL_CONFIGS[a].pricing?.prompt || '0') + parseFloat(MODEL_CONFIGS[a].pricing?.completion || '0');
      const bPrice = parseFloat(MODEL_CONFIGS[b].pricing?.prompt || '0') + parseFloat(MODEL_CONFIGS[b].pricing?.completion || '0');
      return aPrice - bPrice;
    });
}

// Model selection based on content types and learning level
export function selectOptimalModel(
  contentTypes: ContentType[],
  learningLevel: LearningLevel,
  preferCostEffective = true // Default to cost-effective
): keyof typeof MODEL_CONFIGS {
  // For complex content types or advanced level, use more capable models
  const complexTypes = ['scatterPlot', 'table', 'comparison', 'grid'];
  const hasComplexContent = contentTypes.some(type => complexTypes.includes(type));

  // For structured output tasks, prioritize free models that support it, then reliable paid models
  if (preferCostEffective) {
    // First check for free models that support structured outputs
    const freeStructuredModels = getFreeModels().filter(model =>
      MODEL_CONFIGS[model].supportsStructuredOutput
    );

    if (freeStructuredModels.length > 0) {
      // For complex content, use the most capable free model
      if (learningLevel === 'advanced' || hasComplexContent) {
        return freeStructuredModels.find(model =>
          MODEL_CONFIGS[model].strengths.includes('reasoning') ||
          MODEL_CONFIGS[model].strengths.includes('tool-calling')
        ) || freeStructuredModels[0];
      }

      // Default to first free model that supports structured outputs
      return freeStructuredModels[0];
    }

    // Fallback to paid reliable models
    const reliableModels = getReliableStructuredOutputModels();
    if (reliableModels.length > 0) {
      return reliableModels[0];
    }
  }

  // Fallback to premium models if free models are not preferred or available
  if (learningLevel === 'advanced' || hasComplexContent) {
    return 'anthropic/claude-3-opus';
  }

  if (learningLevel === 'intermediate') {
    return 'anthropic/claude-3-sonnet';
  }

  // Default for beginners and simple content
  return 'openai/gpt-4o';
}

// Smart model selection with fallback logic
export function selectModelWithFallback(
  contentTypes: ContentType[],
  learningLevel: LearningLevel,
  options: {
    preferCostEffective?: boolean;
    requireStructuredOutput?: boolean;
    maxRetries?: number;
  } = {}
): { primary: keyof typeof MODEL_CONFIGS; fallbacks: (keyof typeof MODEL_CONFIGS)[] } {
  const {
    preferCostEffective = true,
    requireStructuredOutput = true,
    maxRetries = 3
  } = options;

  // Get available models based on requirements
  let availableModels = Object.keys(MODEL_CONFIGS) as (keyof typeof MODEL_CONFIGS)[];

  if (requireStructuredOutput) {
    availableModels = availableModels.filter(
      model => MODEL_CONFIGS[model].supportsStructuredOutput
    );
  }

  // Sort models by cost preference, prioritizing free structured output models
  const freeStructuredModels = availableModels.filter(model =>
    MODEL_CONFIGS[model].costTier === 'free' && MODEL_CONFIGS[model].supportsStructuredOutput
  );
  const freeNonStructuredModels = availableModels.filter(model =>
    MODEL_CONFIGS[model].costTier === 'free' && !MODEL_CONFIGS[model].supportsStructuredOutput
  );
  const reliableStructuredModels = availableModels.filter(model =>
    MODEL_CONFIGS[model].supportsStructuredOutput &&
    (MODEL_CONFIGS[model].costTier === 'low' || MODEL_CONFIGS[model].costTier === 'medium')
  );
  const lowCostModels = availableModels.filter(model => MODEL_CONFIGS[model].costTier === 'low');
  const mediumCostModels = availableModels.filter(model => MODEL_CONFIGS[model].costTier === 'medium');
  const highCostModels = availableModels.filter(model => MODEL_CONFIGS[model].costTier === 'high');

  // Build priority list
  let priorityList: (keyof typeof MODEL_CONFIGS)[] = [];

  if (preferCostEffective && requireStructuredOutput) {
    // For structured output: free structured models first, then reliable paid models
    priorityList = [...freeStructuredModels, ...reliableStructuredModels, ...highCostModels];
  } else if (preferCostEffective) {
    // Standard cost preference: all free models first, then low cost, then medium, then high
    priorityList = [...freeStructuredModels, ...freeNonStructuredModels, ...lowCostModels, ...mediumCostModels, ...highCostModels];
  } else {
    // Quality first: high, medium, low, then free
    priorityList = [...highCostModels, ...mediumCostModels, ...lowCostModels, ...freeStructuredModels, ...freeNonStructuredModels];
  }

  // Select primary model based on content complexity
  const complexTypes = ['scatterPlot', 'table', 'comparison', 'grid'];
  const hasComplexContent = contentTypes.some(type => complexTypes.includes(type));

  let primaryModel: keyof typeof MODEL_CONFIGS;

  if (preferCostEffective) {
    if (requireStructuredOutput) {
      // For structured output, prioritize free structured models first
      if (learningLevel === 'advanced' || hasComplexContent) {
        primaryModel = freeStructuredModels.find(model =>
          MODEL_CONFIGS[model].strengths.includes('reasoning') ||
          MODEL_CONFIGS[model].strengths.includes('tool-calling')
        ) || reliableStructuredModels.find(model =>
          MODEL_CONFIGS[model].strengths.includes('reasoning') ||
          MODEL_CONFIGS[model].strengths.includes('complex-reasoning')
        ) || priorityList[0];
      } else {
        // Use most cost-effective structured output model (free first, then paid)
        primaryModel = freeStructuredModels[0] || reliableStructuredModels[0] || priorityList[0];
      }
    } else if (learningLevel === 'advanced' || hasComplexContent) {
      // Use best free model for complex content (non-structured output)
      const allFreeModels = [...freeStructuredModels, ...freeNonStructuredModels];
      primaryModel = allFreeModels.find(model =>
        MODEL_CONFIGS[model].strengths.includes('reasoning')
      ) || allFreeModels[0] || priorityList[0];
    } else {
      // Use any suitable free model
      const allFreeModels = [...freeStructuredModels, ...freeNonStructuredModels];
      primaryModel = allFreeModels[0] || priorityList[0];
    }
  } else {
    // Use selectOptimalModel for quality-first approach
    primaryModel = selectOptimalModel(contentTypes, learningLevel, false);
  }

  // Create fallback list (excluding the primary model)
  const fallbacks = priorityList
    .filter(model => model !== primaryModel)
    .slice(0, maxRetries);

  return {
    primary: primaryModel,
    fallbacks
  };
}

// Create OpenRouter instance with configuration
export function createAIProvider(env?: Record<string, any>) {
  // Use environment variables from Cloudflare Workers context if provided
  const apiKey = env?.['OPENROUTER_API_KEY'] || AI_CONFIG.apiKey;

  if (!apiKey) {
    throw new Error('OPENROUTER_API_KEY environment variable is required');
  }

  return createOpenRouter({
    apiKey,
    extraBody: {
      // Optional: Add any OpenRouter-specific parameters
    },
  });
}

// Get model configuration
export function getModelConfig(model: keyof typeof MODEL_CONFIGS) {
  return MODEL_CONFIGS[model];
}

// Validate environment configuration
export function validateAIConfig(): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!AI_CONFIG.apiKey) {
    errors.push('OPENROUTER_API_KEY is required');
  }

  if (!AI_CONFIG.appName) {
    errors.push('OPENROUTER_APP_NAME is required');
  }

  if (!AI_CONFIG.siteUrl) {
    errors.push('OPENROUTER_SITE_URL is required');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Content type to model mapping for specialized use cases
export const CONTENT_TYPE_MODEL_PREFERENCES = {
  paragraph: ['anthropic/claude-3-sonnet', 'openai/gpt-4o'],
  infoBox: ['anthropic/claude-3-sonnet', 'openai/gpt-4o'],
  bulletList: ['openai/gpt-4o', 'anthropic/claude-3-sonnet'],
  numberedList: ['openai/gpt-4o', 'anthropic/claude-3-sonnet'],
  grid: ['anthropic/claude-3-opus', 'anthropic/claude-3-sonnet'],
  comparison: ['anthropic/claude-3-opus', 'anthropic/claude-3-sonnet'],
  table: ['openai/gpt-4o', 'anthropic/claude-3-opus'],
  scatterPlot: ['anthropic/claude-3-opus', 'openai/gpt-4o'],
  keyValueGrid: ['openai/gpt-4o', 'anthropic/claude-3-sonnet'],
} as const;

// Learning level to model mapping
export const LEARNING_LEVEL_MODEL_PREFERENCES = {
  beginner: ['openai/gpt-4o', 'anthropic/claude-3-sonnet'],
  intermediate: ['anthropic/claude-3-sonnet', 'openai/gpt-4o'],
  advanced: ['anthropic/claude-3-opus', 'anthropic/claude-3-sonnet'],
} as const;

// Generation options based on learning level
export const LEARNING_LEVEL_GENERATION_OPTIONS = {
  beginner: {
    temperature: 0.6, // More consistent
    maxSteps: 6, // Shorter explanations
    includeExamples: true,
    simplifyLanguage: true,
  },
  intermediate: {
    temperature: 0.7, // Balanced
    maxSteps: 8, // Medium length
    includeExamples: true,
    simplifyLanguage: false,
  },
  advanced: {
    temperature: 0.8, // More creative
    maxSteps: 12, // Longer, detailed explanations
    includeExamples: false, // Assume prior knowledge
    simplifyLanguage: false,
  },
} as const;

// Export types
export type AIModel = keyof typeof MODEL_CONFIGS;
export type ModelConfig = typeof MODEL_CONFIGS[AIModel];
export type AIConfigType = typeof AI_CONFIG;
export type CostTier = 'free' | 'low' | 'medium' | 'high';
export type ModelStrength = 'reasoning' | 'explanations' | 'structured-output' | 'cost-effective' |
  'complex-reasoning' | 'detailed-explanations' | 'accuracy' | 'consistency' | 'speed' |
  'general-purpose' | 'open-source' | 'fast' | 'reliable' | 'tool-calling';
