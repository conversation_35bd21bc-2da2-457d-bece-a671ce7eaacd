/**
 * Prompt templates for AI learning content generation
 */

import type { LearningLevel, ContentType } from '../schemas/content-generation';

// System prompt for learning content generation
export const LEARNING_CONTENT_SYSTEM_PROMPT = `You are an expert educational content creator specializing in generating structured, engaging learning materials. Your goal is to create comprehensive, step-by-step explanations that help learners understand complex topics.

## Your Expertise:
- Educational pedagogy and learning theory
- Content structuring and information architecture
- Adapting content complexity to different learning levels
- Creating engaging, interactive learning experiences

## Content Generation Guidelines:

### 1. Learning Levels:
- **Beginner**: Use simple language, provide context, include basic examples, avoid jargon
- **Intermediate**: Assume some background knowledge, use moderate complexity, include practical applications
- **Advanced**: Use technical language, focus on nuanced concepts, include complex examples and edge cases

### 2. Content Types and Best Practices:

**paragraph**: Clear, concise explanations. Use multiple paragraphs for complex concepts.
**infoBox**: Highlight key information, warnings, or important notes. Include a descriptive heading.
**bulletList**: Break down concepts into digestible points. Use parallel structure.
**numberedList**: Sequential steps or processes. Ensure logical order.
**grid**: Organize related concepts or categories. Each item should have a clear title and description.
**comparison**: Show before/after, pros/cons, or different approaches. Use clear labels.
**table**: Present structured data or comparisons. Include descriptive headers.
**scatterPlot**: Visualize relationships between variables. Include meaningful labels.
**keyValueGrid**: Define terms, concepts, or provide quick reference information.

### 3. Content Structure:
- Create 4-10 steps for comprehensive coverage
- Use varied content types to maintain engagement
- Ensure logical progression from basic to advanced concepts
- Include practical examples and real-world applications
- Balance text-heavy and visual content types

### 4. Step Titles and Icons:
- Create descriptive, engaging step titles
- Use appropriate icon names from lucide-react library
- Icons should be relevant to the content and visually distinct
- Common icons: Brain, BookOpen, Lightbulb, Target, Zap, Settings, Users, TrendingUp, CheckCircle

### 5. Content Quality:
- Ensure accuracy and factual correctness
- Use clear, engaging language appropriate to the learning level
- Provide sufficient detail without overwhelming the learner
- Include practical examples and applications
- Maintain consistency in tone and style throughout

## Output Requirements:
You must generate a JSON object that exactly matches the required schema with:
- A descriptive title for the entire learning content
- A brief description summarizing what the learner will gain
- An estimated reading time in minutes
- An array of steps with varied content types
- Metadata including the learning level and content types used

## CRITICAL: Data Structure Examples
For each content type, the "data" field must follow these EXACT formats:

**paragraph**: Use string or array of strings directly (NOT objects):
✅ Correct: "This is a paragraph"
✅ Correct: ["First paragraph", "Second paragraph"]
❌ Wrong: {"content": "text"} or {"text": "text"}

**infoBox**: Use object with heading and lines:
✅ Correct: {"heading": "Important", "lines": ["Point 1", "Point 2"]}

**bulletList**: Use array of strings directly:
✅ Correct: ["Item 1", "Item 2", "Item 3"]
❌ Wrong: {"items": ["Item 1", "Item 2"]}

**numberedList**: Use array of strings directly:
✅ Correct: ["Step 1", "Step 2", "Step 3"]

**grid**: Use array of objects with title and content:
✅ Correct: [{"title": "Title 1", "content": "Description 1"}]

**comparison**: Use array of objects with label, before, after:
✅ Correct: [{"label": "Feature", "before": "Old way", "after": "New way"}]

**table**: Use object with headers and rows:
✅ Correct: {"headers": ["Col1", "Col2"], "rows": [["A", "B"], ["C", "D"]]}

**keyValueGrid**: Use array of objects with key and value:
✅ Correct: [{"key": "Term", "value": "Definition"}]

Remember: Your goal is to create educational content that is both informative and engaging, helping learners build understanding progressively through well-structured, varied content formats.`;

// User prompt template
export function createUserPrompt(
  topic: string,
  learningLevel: LearningLevel,
  preferredContentTypes: ContentType[],
  focusAreas?: string
): string {
  const levelDescriptions = {
    beginner: 'someone new to this topic with little to no prior knowledge',
    intermediate: 'someone with basic understanding who wants to deepen their knowledge',
    advanced: 'someone with solid foundation who wants expert-level insights'
  };

  const contentTypeInstructions = preferredContentTypes.length > 0
    ? `\n\nPreferred content types to emphasize: ${preferredContentTypes.join(', ')}`
    : '';

  const focusInstruction = focusAreas
    ? `\n\nSpecific focus areas: ${focusAreas}`
    : '';

  return `Create comprehensive learning content about: "${topic}"

Target audience: ${levelDescriptions[learningLevel]}
Learning level: ${learningLevel}${contentTypeInstructions}${focusInstruction}

Requirements:
1. Generate 4-8 learning steps with varied content types
2. Use clear, ${learningLevel}-appropriate language
3. Include practical examples and real-world applications
4. Ensure logical progression from basic concepts to more complex ideas
5. Create engaging step titles with appropriate lucide-react icon names
6. Balance different content types for optimal learning experience

Focus on creating content that helps the learner build understanding progressively, with each step building upon the previous ones.`;
}

// Content type specific instructions
export const CONTENT_TYPE_INSTRUCTIONS = {
  paragraph: 'Use clear, well-structured paragraphs. For complex topics, break into multiple paragraphs with logical flow.',

  infoBox: 'Create highlighted information boxes with descriptive headings. Use for key concepts, warnings, tips, or important notes.',

  bulletList: 'Use bullet points for listing key concepts, features, benefits, or important points. Keep items concise and parallel in structure.',

  numberedList: 'Use numbered lists for sequential steps, processes, or prioritized items. Ensure logical order and clear instructions.',

  grid: 'Organize related concepts into a grid format. Each item should have a clear title and descriptive content. Ideal for categories, features, or components.',

  comparison: 'Create before/after comparisons, pros/cons, or different approaches. Use clear labels and ensure balanced presentation.',

  table: 'Present structured data with clear headers and organized rows. Ideal for specifications, comparisons, or reference information.',

  scatterPlot: 'Visualize relationships between two variables with labeled data points. Include meaningful axis labels and point descriptions.',

  keyValueGrid: 'Define terms, concepts, or provide quick reference information in key-value format. Keep definitions clear and concise.'
};

// Icon suggestions for different topics
export const TOPIC_ICON_SUGGESTIONS = {
  technology: ['Cpu', 'Monitor', 'Smartphone', 'Wifi', 'Database', 'Cloud', 'Code'],
  science: ['Atom', 'Microscope', 'FlaskConical', 'Dna', 'Telescope', 'Zap', 'Beaker'],
  business: ['TrendingUp', 'BarChart', 'PieChart', 'DollarSign', 'Briefcase', 'Target', 'Users'],
  education: ['BookOpen', 'GraduationCap', 'Brain', 'Lightbulb', 'PenTool', 'FileText', 'Award'],
  health: ['Heart', 'Activity', 'Stethoscope', 'Pill', 'Shield', 'Thermometer', 'Cross'],
  general: ['Info', 'HelpCircle', 'CheckCircle', 'AlertCircle', 'Star', 'Flag', 'Bookmark']
};

// Learning level specific prompt adjustments
export const LEVEL_SPECIFIC_ADJUSTMENTS = {
  beginner: {
    languageStyle: 'simple, clear, and jargon-free',
    exampleTypes: 'everyday, relatable examples',
    depth: 'foundational concepts with clear explanations',
    assumptions: 'no prior knowledge of the topic'
  },

  intermediate: {
    languageStyle: 'moderately technical with explanations of key terms',
    exampleTypes: 'practical, real-world applications',
    depth: 'detailed explanations with some technical depth',
    assumptions: 'basic familiarity with related concepts'
  },

  advanced: {
    languageStyle: 'technical and precise terminology',
    exampleTypes: 'complex scenarios and edge cases',
    depth: 'comprehensive coverage with nuanced insights',
    assumptions: 'solid foundation in the subject area'
  }
};

// Helper function to generate content type distribution
export function generateContentTypeDistribution(
  preferredTypes: ContentType[],
  totalSteps: number
): ContentType[] {
  if (preferredTypes.length === 0) {
    // Default distribution for balanced learning
    const defaultTypes: ContentType[] = ['paragraph', 'bulletList', 'infoBox', 'grid'];
    return Array.from({ length: totalSteps }, (_, i) =>
      defaultTypes[i % defaultTypes.length]
    );
  }

  // Distribute preferred types across steps
  const distribution: ContentType[] = [];
  for (let i = 0; i < totalSteps; i++) {
    distribution.push(preferredTypes[i % preferredTypes.length]);
  }

  return distribution;
}

// Helper function to suggest icons based on topic keywords
export function suggestIconsForTopic(topic: string): string[] {
  const topicLower = topic.toLowerCase();

  if (topicLower.includes('tech') || topicLower.includes('computer') || topicLower.includes('software')) {
    return TOPIC_ICON_SUGGESTIONS.technology;
  }

  if (topicLower.includes('science') || topicLower.includes('physics') || topicLower.includes('chemistry')) {
    return TOPIC_ICON_SUGGESTIONS.science;
  }

  if (topicLower.includes('business') || topicLower.includes('market') || topicLower.includes('finance')) {
    return TOPIC_ICON_SUGGESTIONS.business;
  }

  if (topicLower.includes('learn') || topicLower.includes('education') || topicLower.includes('study')) {
    return TOPIC_ICON_SUGGESTIONS.education;
  }

  if (topicLower.includes('health') || topicLower.includes('medical') || topicLower.includes('body')) {
    return TOPIC_ICON_SUGGESTIONS.health;
  }

  return TOPIC_ICON_SUGGESTIONS.general;
}
