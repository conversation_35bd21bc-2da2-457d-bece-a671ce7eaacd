/**
 * AI prompts for content-based quiz generation
 * These prompts ensure questions are generated ONLY from provided source content
 * with strict validation to prevent hallucination
 */

import type { QuizType, QuizDifficulty } from '../schemas/quiz-generation';

// System prompt for quiz generation with anti-hallucination measures
export const QUIZ_GENERATION_SYSTEM_PROMPT = `You are an expert educational assessment creator specializing in generating quiz questions EXCLUSIVELY from provided source content. Your primary responsibility is to prevent hallucination and ensure all questions derive directly from the given material.

## CRITICAL ANTI-HALLUCINATION RULES:
1. **SOURCE-ONLY GENERATION**: Generate questions ONLY from the explicitly provided content
2. **NO EXTERNAL KNOWLEDGE**: Do not use any knowledge beyond what is provided in the source material
3. **EXACT CONTENT REFERENCE**: Every question must reference specific text from the source
4. **NO ASSUMPTIONS**: Do not make assumptions or inferences beyond what is explicitly stated
5. **CONTENT VALIDATION**: Include the exact source text excerpt for each generated question

## QUIZ GENERATION EXPERTISE:
- Educational assessment design and question taxonomy
- <PERSON>'s taxonomy application for different difficulty levels
- Content analysis and key concept extraction
- Question clarity and unambiguous answer validation

## QUESTION QUALITY STANDARDS:
- **Clear and Unambiguous**: Questions must have one clearly correct answer
- **Appropriate Difficulty**: Match the specified difficulty level
- **Content-Aligned**: Directly test understanding of the provided material
- **Pedagogically Sound**: Follow best practices for educational assessment
- **Source-Traceable**: Every element must be traceable to the source content

## DIFFICULTY LEVEL GUIDELINES:
- **Easy**: Direct recall of explicitly stated facts and definitions
- **Medium**: Understanding and application of concepts from the content
- **Hard**: Analysis and synthesis of multiple concepts from the content

## VALIDATION REQUIREMENTS:
For each generated question, you must:
1. Identify the exact source text used
2. Verify the question can be answered solely from that text
3. Confirm no external knowledge is required
4. Ensure the answer is explicitly or implicitly stated in the source

Remember: It is better to generate fewer high-quality, source-based questions than to risk any hallucination.`;

// Create user prompt for quiz generation
export function createQuizGenerationPrompt(
  sourceContent: string,
  stepTitle: string,
  stepId: string,
  quizTypes: QuizType[],
  difficulty: QuizDifficulty,
  questionsPerType: number
): string {
  const difficultyInstructions = {
    easy: 'Focus on direct recall of facts, definitions, and explicitly stated information',
    medium: 'Test understanding and application of concepts, requiring some interpretation of the content',
    hard: 'Require analysis, synthesis, or evaluation of multiple concepts from the content'
  };

  const typeInstructions = {
    flashcard: `Create term/definition or question/answer pairs using exact terminology from the content.
    REQUIRED FIELDS: "front" (term/question), "back" (definition/answer), optional "hint"
    IMPORTANT: Front and back MUST contain different content - never duplicate the same text.`,

    multipleChoice: `Generate questions with 4 options where only one is clearly correct based on the content.
    REQUIRED FIELDS: "question", "options" (array of exactly 4 strings), "correctAnswerIndex" (0-3), optional "explanation"`,

    trueFalse: `Create statements that are definitively true or false according to the source material.
    REQUIRED FIELDS: "statement", "correctAnswer" (boolean), optional "explanation"`,

    fillInBlank: `Remove key terms or phrases that are explicitly mentioned in the content.
    REQUIRED FIELDS: "text" (with _____ placeholders), "blanks" (array with position, correctAnswer, optional acceptableAnswers, caseSensitive), optional "hint"
    EXAMPLE: {"text": "The _____ is responsible for _____", "blanks": [{"position": 0, "correctAnswer": "CPU", "caseSensitive": false}, {"position": 1, "correctAnswer": "processing", "caseSensitive": false}]}`,

    matching: `Pair terms with their definitions or concepts with their descriptions from the content.
    REQUIRED FIELDS: "pairs" (array of at least 3 objects with "left" and "right" properties), optional "instruction"
    EXAMPLE: {"pairs": [{"left": "Term1", "right": "Definition1"}, {"left": "Term2", "right": "Definition2"}, {"left": "Term3", "right": "Definition3"}]}`,

    freeText: `Ask questions that require explanation using concepts and information from the content.
    REQUIRED FIELDS: "question", "answerType" ("short" or "long"), "maxLength" (number), "sampleAnswer", "evaluationCriteria" (array of strings)
    EXAMPLE: {"question": "Explain...", "answerType": "short", "maxLength": 200, "sampleAnswer": "Sample response...", "evaluationCriteria": ["Mentions key concept", "Explains relationship"]}`,

    ordering: `Sequence steps, processes, or concepts in the order they appear or are logically arranged in the content.
    REQUIRED FIELDS: "items" (array of at least 3 strings), "correctOrder" (array of indices), optional "instruction", optional "orderType"
    EXAMPLE: {"items": ["Step A", "Step B", "Step C"], "correctOrder": [0, 1, 2], "orderType": "logical"}`
  };

  return `Generate quiz questions from the following learning content step:

**Step Title**: ${stepTitle}
**Step ID**: ${stepId}
**Target Difficulty**: ${difficulty}

**SOURCE CONTENT** (Generate questions ONLY from this content):
"""
${sourceContent}
"""

**GENERATION REQUIREMENTS**:
- Difficulty Level: ${difficulty} - ${difficultyInstructions[difficulty]}
- Generate ${questionsPerType} question(s) for each requested type: ${quizTypes.join(', ')}
- Total questions to generate: ${quizTypes.length * questionsPerType}

**QUESTION TYPE INSTRUCTIONS**:
${quizTypes.map(type => `- **${type}**: ${typeInstructions[type]}`).join('\n')}

${quizTypes.includes('flashcard') ? `
**SPECIAL FLASHCARD REQUIREMENTS**:
- The "front" field must contain a term, concept, or question from the source
- The "back" field must contain the corresponding definition, explanation, or answer
- Front and back MUST contain different content - never duplicate the same text
- Example: front: "Machine Learning", back: "A type of artificial intelligence that enables computers to learn without being explicitly programmed"
` : ''}

**MANDATORY VALIDATION FOR EACH QUESTION**:
1. Include the exact text excerpt from the source content that supports the question
2. Verify the question can be answered using only the provided content
3. Ensure no external knowledge or assumptions are required
4. Confirm the answer is explicitly or clearly implied in the source

**CRITICAL FIELD REQUIREMENTS BY TYPE**:
${quizTypes.includes('matching') ? `
**MATCHING Questions MUST include**:
- "pairs": Array of at least 3 objects, each with "left" and "right" properties
- Example: [{"left": "CPU", "right": "Central Processing Unit"}, {"left": "RAM", "right": "Random Access Memory"}, {"left": "GPU", "right": "Graphics Processing Unit"}]
` : ''}${quizTypes.includes('fillInBlank') ? `
**FILL-IN-BLANK Questions MUST include**:
- "text": String with _____ placeholders where answers go
- "blanks": Array with objects containing "position" (number), "correctAnswer" (string), optional "acceptableAnswers" (array), "caseSensitive" (boolean)
- Example: {"text": "The _____ processes data", "blanks": [{"position": 0, "correctAnswer": "CPU", "caseSensitive": false}]}
` : ''}${quizTypes.includes('freeText') ? `
**FREE TEXT Questions MUST include**:
- "question": The question text
- "answerType": Either "short" or "long"
- "maxLength": Number (e.g., 200 for short, 500 for long)
- "sampleAnswer": Example of a good answer
- "evaluationCriteria": Array of strings describing what makes a good answer
- Example: {"question": "Explain...", "answerType": "short", "maxLength": 200, "sampleAnswer": "A good answer would...", "evaluationCriteria": ["Mentions key concept", "Provides example"]}
` : ''}

**OUTPUT FORMAT REQUIREMENTS**:
- Generate exactly ${quizTypes.length * questionsPerType} questions total
- Each question MUST include ALL required fields for its type
- Set sourceStepId to: "${stepId}"
- Set sourceContent to the exact text excerpt used for each question
- Ensure all content references are accurate and traceable
- Follow the provided schema exactly

**COMPLETE EXAMPLES FOR EACH TYPE**:
${quizTypes.includes('matching') ? `
MATCHING Example:
{
  "id": "q1",
  "type": "matching",
  "difficulty": "${difficulty}",
  "points": 1,
  "instruction": "Match the following terms with their definitions:",
  "pairs": [
    {"left": "Algorithm", "right": "A step-by-step procedure for solving a problem"},
    {"left": "Variable", "right": "A storage location with an associated name"},
    {"left": "Function", "right": "A reusable block of code that performs a specific task"}
  ]
}
` : ''}${quizTypes.includes('fillInBlank') ? `
FILL-IN-BLANK Example:
{
  "id": "q2",
  "type": "fillInBlank",
  "difficulty": "${difficulty}",
  "points": 1,
  "text": "A _____ is a step-by-step procedure for solving a problem, while a _____ stores data.",
  "blanks": [
    {"position": 0, "correctAnswer": "algorithm", "caseSensitive": false},
    {"position": 1, "correctAnswer": "variable", "caseSensitive": false}
  ]
}
` : ''}${quizTypes.includes('freeText') ? `
FREE TEXT Example:
{
  "id": "q3",
  "type": "freeText",
  "difficulty": "${difficulty}",
  "points": 1,
  "question": "Explain the relationship between algorithms and programming.",
  "answerType": "short",
  "maxLength": 200,
  "sampleAnswer": "Algorithms provide the logical steps that programmers implement in code to solve specific problems.",
  "evaluationCriteria": ["Mentions algorithms as logical steps", "Connects to programming implementation", "Shows understanding of problem-solving"]
}
` : ''}

**QUALITY CHECKLIST** (verify each question):
✓ Can be answered solely from the provided content
✓ Uses exact terminology from the source
✓ Requires no external knowledge
✓ Has a clear, unambiguous correct answer
✓ Matches the specified difficulty level
✓ Includes accurate source content reference
✓ Contains ALL required fields for the question type

Generate high-quality, content-based quiz questions that test understanding of the provided material without any hallucination.`;
}

/**
 * Create quiz generation prompt with specific type quotas
 */
export function createQuizGenerationPromptWithQuotas(
  content: string,
  title: string,
  stepId: string,
  typeQuotas: Map<QuizType, number>,
  difficulty: QuizDifficulty
): string {
  const quotaEntries = Array.from(typeQuotas.entries());
  const totalQuestions = Array.from(typeQuotas.values()).reduce((sum, count) => sum + count, 0);

  // Define type instructions for this function
  const typeInstructions = {
    flashcard: `Create term/definition or question/answer pairs using exact terminology from the content.
    REQUIRED FIELDS: "front" (term/question), "back" (definition/answer), optional "hint"
    IMPORTANT: Front and back MUST contain different content - never duplicate the same text.`,

    multipleChoice: `Generate questions with 4 options where only one is clearly correct based on the content.
    REQUIRED FIELDS: "question", "options" (array of exactly 4 strings), "correctAnswerIndex" (0-3), optional "explanation"`,

    trueFalse: `Create statements that are definitively true or false according to the source material.
    REQUIRED FIELDS: "statement", "correctAnswer" (boolean), optional "explanation"`,

    fillInBlank: `Remove key terms or phrases that are explicitly mentioned in the content.
    REQUIRED FIELDS: "text" (with _____ placeholders), "blanks" (array with position, correctAnswer, optional acceptableAnswers, caseSensitive), optional "hint"
    EXAMPLE: {"text": "The _____ is responsible for _____", "blanks": [{"position": 0, "correctAnswer": "CPU", "caseSensitive": false}, {"position": 1, "correctAnswer": "processing", "caseSensitive": false}]}`,

    matching: `Pair terms with their definitions or concepts with their descriptions from the content.
    REQUIRED FIELDS: "pairs" (array of at least 3 objects with "left" and "right" properties), optional "instruction"
    EXAMPLE: {"pairs": [{"left": "Term1", "right": "Definition1"}, {"left": "Term2", "right": "Definition2"}, {"left": "Term3", "right": "Definition3"}]}`,

    freeText: `Ask questions that require explanation using concepts and information from the content.
    REQUIRED FIELDS: "question", "answerType" ("short" or "long"), "maxLength" (number), "sampleAnswer", "evaluationCriteria" (array of strings)
    EXAMPLE: {"question": "Explain...", "answerType": "short", "maxLength": 200, "sampleAnswer": "Sample response...", "evaluationCriteria": ["Mentions key concept", "Explains relationship"]}`,

    ordering: `Sequence steps, processes, or concepts in the order they appear or are logically arranged in the content.
    REQUIRED FIELDS: "items" (array of at least 3 strings), "correctOrder" (array of indices), optional "instruction", optional "orderType"
    EXAMPLE: {"items": ["Step A", "Step B", "Step C"], "correctOrder": [0, 1, 2], "orderType": "logical"}`
  };

  const quotaInstructions = quotaEntries.map(([type, count]) => {
    const typeInfo = typeInstructions[type];
    return `**${count} ${type.toUpperCase()} question${count > 1 ? 's' : ''}**:
${typeInfo}`;
  }).join('\n\n');

  return `**LEARNING CONTENT**: "${title}"

**CONTENT TO ANALYZE**:
${content}

**QUIZ GENERATION REQUIREMENTS**:
Generate EXACTLY ${totalQuestions} questions with the following specific distribution:

${quotaInstructions}

**CRITICAL TYPE QUOTA REQUIREMENTS**:
${quotaEntries.map(([type, count]) => `- Generate EXACTLY ${count} ${type} question${count > 1 ? 's' : ''}`).join('\n')}

**DIFFICULTY LEVEL**: ${difficulty}

**STRICT QUOTA ENFORCEMENT**:
- You MUST generate the exact number specified for each question type
- Do NOT generate extra questions of any type
- Do NOT skip any required question types
- Total questions must equal exactly ${totalQuestions}

${quotaEntries.includes(['matching', 1]) || quotaEntries.some(([type]) => type === 'matching') ? `
**MATCHING Questions MUST include**:
- "pairs": Array of at least 3 objects, each with "left" and "right" properties
- Example: [{"left": "CPU", "right": "Central Processing Unit"}, {"left": "RAM", "right": "Random Access Memory"}, {"left": "GPU", "right": "Graphics Processing Unit"}]
` : ''}${quotaEntries.includes(['fillInBlank', 1]) || quotaEntries.some(([type]) => type === 'fillInBlank') ? `
**FILL-IN-BLANK Questions MUST include**:
- "text": String with _____ placeholders where answers go
- "blanks": Array with objects containing "position" (number), "correctAnswer" (string), optional "acceptableAnswers" (array), "caseSensitive" (boolean)
- Example: {"text": "The _____ processes data", "blanks": [{"position": 0, "correctAnswer": "CPU", "caseSensitive": false}]}
` : ''}${quotaEntries.includes(['freeText', 1]) || quotaEntries.some(([type]) => type === 'freeText') ? `
**FREE TEXT Questions MUST include**:
- "question": The question text
- "answerType": Either "short" or "long"
- "maxLength": Number (e.g., 200 for short, 500 for long)
- "sampleAnswer": Example of a good answer
- "evaluationCriteria": Array of strings describing what makes a good answer
- Example: {"question": "Explain...", "answerType": "short", "maxLength": 200, "sampleAnswer": "A good answer would...", "evaluationCriteria": ["Mentions key concept", "Provides example"]}
` : ''}

**OUTPUT FORMAT REQUIREMENTS**:
- Generate exactly ${totalQuestions} questions total
- Each question MUST include ALL required fields for its type
- Set sourceStepId to: "${stepId}"
- Set sourceContent to the exact text excerpt used for each question
- Ensure all content references are accurate and traceable
- Follow the provided schema exactly

**QUALITY CHECKLIST** (verify each question):
✓ Can be answered solely from the provided content
✓ Uses exact terminology from the source
✓ Requires no external knowledge
✓ Has a clear, unambiguous correct answer
✓ Matches the specified difficulty level
✓ Includes accurate source content reference
✓ Contains ALL required fields for the question type
✓ Meets the exact quota for its type

Generate high-quality, content-based quiz questions that test understanding of the provided material without any hallucination.`;
}

// Prompt for content validation to prevent hallucination
export function createContentValidationPrompt(
  sourceContent: string,
  generatedQuestion: any
): string {
  const questionType = generatedQuestion.type;

  return `Validate that the following quiz question was generated exclusively from the provided source content with no hallucination:

**SOURCE CONTENT**:
"""
${sourceContent}
"""

**GENERATED QUESTION**:
"""
${JSON.stringify(generatedQuestion, null, 2)}
"""

**VALIDATION CHECKLIST**:
Evaluate each aspect and provide a boolean result:

1. **Content Match**: Does the question content directly relate to information in the source?
2. **No Hallucination**: Are there any facts, details, or information not present in the source?
3. **Appropriate Difficulty**: Does the question difficulty match the complexity of the source content?
4. **Clearly Answerable**: Can the question be answered using only the provided source content?

**QUESTION TYPE SPECIFIC GUIDANCE**:
${getQuestionTypeValidationGuidance(questionType)}

**VALIDATION CRITERIA**:
- ✓ PASS: All question elements are traceable to the source content
- ✗ FAIL: Any element requires external knowledge or contains invented information

**IMPORTANT DISTINCTIONS**:
- **NOT Hallucination**: Logical ordering/sequencing of items mentioned in source content
- **NOT Hallucination**: Asking for explanations of concepts described in the source
- **IS Hallucination**: Adding specific facts, dates, numbers, or names not in the source
- **IS Hallucination**: Requiring knowledge from outside the provided content

**CONFIDENCE SCORING**:
Provide a confidence score (0.0 to 1.0) for the overall validation:
- 1.0: Completely confident the question is source-based with no hallucination
- 0.8-0.9: High confidence with minor concerns
- 0.6-0.7: Moderate confidence, some elements unclear
- 0.4-0.5: Low confidence, potential issues detected
- 0.0-0.3: Very low confidence, likely hallucination present

Provide detailed reasoning for any validation failures and suggest improvements.`;
}

// Prompt for quiz difficulty adjustment
export function createDifficultyAdjustmentPrompt(
  sourceContent: string,
  currentQuestion: any,
  targetDifficulty: QuizDifficulty
): string {
  const adjustmentGuidelines = {
    easy: {
      focus: 'Direct recall and recognition',
      verbs: 'identify, list, define, state, recall, recognize',
      approach: 'Ask for explicitly stated facts and definitions'
    },
    medium: {
      focus: 'Understanding and application',
      verbs: 'explain, describe, compare, classify, summarize, interpret',
      approach: 'Test comprehension and ability to apply concepts'
    },
    hard: {
      focus: 'Analysis and evaluation',
      verbs: 'analyze, evaluate, synthesize, critique, justify, predict',
      approach: 'Require critical thinking and connection of multiple concepts'
    }
  };

  const guidelines = adjustmentGuidelines[targetDifficulty];

  return `Adjust the following quiz question to match the target difficulty level while maintaining strict adherence to the source content:

**SOURCE CONTENT**:
"""
${sourceContent}
"""

**CURRENT QUESTION**:
"""
${JSON.stringify(currentQuestion, null, 2)}
"""

**TARGET DIFFICULTY**: ${targetDifficulty}

**ADJUSTMENT GUIDELINES FOR ${targetDifficulty.toUpperCase()}**:
- **Focus**: ${guidelines.focus}
- **Action Verbs**: ${guidelines.verbs}
- **Approach**: ${guidelines.approach}

**ADJUSTMENT REQUIREMENTS**:
1. Maintain the same question type
2. Use only information from the provided source content
3. Adjust cognitive complexity to match target difficulty
4. Ensure the question remains clearly answerable from the source
5. Update the sourceContent field with the exact text excerpt used

**DIFFICULTY EXAMPLES**:
- **Easy**: "What is [term] as defined in the content?"
- **Medium**: "How does [concept A] relate to [concept B] according to the content?"
- **Hard**: "Based on the content, what would be the implications if [scenario]?"

Generate the adjusted question that matches the target difficulty while remaining strictly source-based.`;
}

// Prompt for quiz question refinement
export function createQuestionRefinementPrompt(
  sourceContent: string,
  question: any,
  issues: string[]
): string {
  return `Refine the following quiz question to address the identified issues while maintaining strict adherence to the source content:

**SOURCE CONTENT**:
"""
${sourceContent}
"""

**CURRENT QUESTION**:
"""
${JSON.stringify(question, null, 2)}
"""

**IDENTIFIED ISSUES**:
${issues.map((issue, index) => `${index + 1}. ${issue}`).join('\n')}

**REFINEMENT REQUIREMENTS**:
1. Address all identified issues
2. Maintain the same question type and difficulty level
3. Use only information from the provided source content
4. Ensure the question has a clear, unambiguous answer
5. Update the sourceContent field with the exact text excerpt used
6. Preserve the educational value of the question

**QUALITY STANDARDS**:
- Clear and unambiguous wording
- Single correct answer (for objective questions)
- Appropriate difficulty level
- Direct relationship to source content
- No external knowledge required

Generate the refined question that addresses all issues while remaining strictly source-based and educationally sound.`;
}

/**
 * Get question type specific validation guidance
 */
function getQuestionTypeValidationGuidance(questionType: string): string {
  switch (questionType) {
    case 'ordering':
      return `**For ORDERING questions**:
- ✓ VALID: Items are all mentioned in the source content
- ✓ VALID: Logical sequence can be inferred from the content (e.g., process steps)
- ✗ INVALID: Items not mentioned in source or requiring external knowledge
- Note: Ordering questions often require reasonable inference about sequence/priority`;

    case 'freeText':
      return `**For FREE TEXT questions**:
- ✓ VALID: Question asks for explanation/description of concepts in the source
- ✓ VALID: Expected answer can be derived from the provided content
- ✗ INVALID: Question requires knowledge not available in the source
- Note: Free text questions test understanding, not just recall`;

    case 'multipleChoice':
      return `**For MULTIPLE CHOICE questions**:
- ✓ VALID: All options and correct answer are based on source content
- ✓ VALID: Distractors are plausible but clearly incorrect based on source
- ✗ INVALID: Options include information not in the source`;

    case 'matching':
      return `**For MATCHING questions**:
- ✓ VALID: All terms and definitions/descriptions are from the source
- ✗ INVALID: Any term or definition not mentioned in the source`;

    case 'fillInBlank':
      return `**For FILL IN BLANK questions**:
- ✓ VALID: Missing words/phrases are explicitly stated in the source
- ✗ INVALID: Expected answers require external knowledge`;

    case 'trueFalse':
      return `**For TRUE/FALSE questions**:
- ✓ VALID: Statement can be verified as true or false using only the source
- ✗ INVALID: Statement requires external knowledge to evaluate`;

    case 'flashcard':
      return `**For FLASHCARD questions**:
- ✓ VALID: Both front and back content are directly from the source AND are different from each other
- ✓ VALID: Front contains a term/concept/question, back contains definition/explanation/answer
- ✗ INVALID: Either side contains information not in the source
- ✗ INVALID: Front and back contain identical or nearly identical content`;

    default:
      return `**For this question type**:
- ✓ VALID: All content is traceable to the source material
- ✗ INVALID: Any content requires external knowledge`;
  }
}
