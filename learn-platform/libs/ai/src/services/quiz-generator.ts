/**
 * Quiz Generation Service
 * Generates quiz questions exclusively from MultiStepExplain content
 * with strict validation to prevent hallucination
 */

import { generateObject, generateText } from 'ai';
import { createAIProvider, selectModelWithFallback } from '../config/ai-config';
import {
  quizGenerationInputSchema,
  generatedQuizSchema,
  quizQuestionSchema,
  contentValidationSchema,
  convertMetaResponseToFull,
  createDynamicQuizSchemas,
  type QuizGenerationInput,
  type GeneratedQuiz,
  type QuizQuestion,
  type ContentValidation,
  type QuizType,
  type QuizDifficulty,
} from '../schemas/quiz-generation';
import {
  QUIZ_GENERATION_SYSTEM_PROMPT,
  createQuizGenerationPromptWithQuotas,
} from '../prompts/quiz-generation';

// Interface for learning content step
interface LearningStep {
  id: string;
  title: string;
  icon: string;
  blocks: Array<{
    id: string;
    type: string;
    data: any;
    isEditing?: boolean;
  }>;
}

// Interface for learning content
interface LearningContent {
  id: string;
  title: string;
  description: string;
  steps: LearningStep[];
  learningLevel: 'beginner' | 'intermediate' | 'advanced';
}

// Quiz generation options
interface QuizGenerationOptions {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  validateContent?: boolean;
  retryOnFailure?: boolean;
  preferCostEffective?: boolean; // Default to true for cost optimization
  env?: Record<string, any>; // Cloudflare Workers environment variables
}

/**
 * Extract text content from a learning step block
 */
function extractTextFromBlock(block: any): string {
  const { type, data } = block;

  switch (type) {
    case 'paragraph':
      // Handle both formats: direct string/array or nested in data.text
      if (typeof data === 'string') {
        return data;
      } else if (Array.isArray(data)) {
        return data.join('\n');
      } else if (data && typeof data.text === 'string') {
        return data.text;
      } else if (data && Array.isArray(data.text)) {
        return data.text.join('\n');
      }
      return '';

    case 'infoBox':
      // Handle both formats: direct object or nested in data
      const infoData = data.title ? data : (data.data || data);
      const heading = infoData.title || infoData.heading ? `${infoData.title || infoData.heading}\n` : '';
      const content = infoData.content || '';
      const lines = Array.isArray(infoData.lines) ? infoData.lines.join('\n') : '';
      return heading + content + lines;

    case 'bulletList':
    case 'numberedList':
      // Handle both formats: direct array or nested in data.items
      if (Array.isArray(data)) {
        return data.join('\n');
      } else if (data && Array.isArray(data.items)) {
        return data.items.join('\n');
      }
      return '';

    case 'grid':
      const gridData = Array.isArray(data) ? data : (data.items || []);
      if (Array.isArray(gridData)) {
        return gridData.map((item: any) => `${item.title}: ${item.content}`).join('\n');
      }
      return '';

    case 'comparison':
      const comparisonData = Array.isArray(data) ? data : (data.items || []);
      if (Array.isArray(comparisonData)) {
        return comparisonData.map((item: any) =>
          `${item.label}: Before - ${item.before}, After - ${item.after}`
        ).join('\n');
      }
      return '';

    case 'table':
      const tableData = data.headers ? data : (data.data || data);
      if (tableData.headers && tableData.rows) {
        const headerRow = tableData.headers.join(' | ');
        const dataRows = tableData.rows.map((row: any[]) => row.join(' | ')).join('\n');
        return `${headerRow}\n${dataRows}`;
      }
      return '';

    case 'keyValueGrid':
      const kvData = Array.isArray(data) ? data : (data.items || []);
      if (Array.isArray(kvData)) {
        return kvData.map((item: any) => `${item.key}: ${item.value}`).join('\n');
      }
      return '';

    case 'scatterPlot':
      const plotData = data.data ? data : (data.config || data);
      if (plotData.data && Array.isArray(plotData.data)) {
        return plotData.data.map((point: any) =>
          `${point.label}: (${point.x}, ${point.y})`
        ).join('\n');
      }
      return '';

    default:
      return '';
  }
}

/**
 * Extract all text content from a learning step
 */
function extractStepContent(step: LearningStep): string {
  const title = step.title;
  const blockContents = step.blocks
    .map(block => extractTextFromBlock(block))
    .filter(content => content.trim().length > 0);

  return [title, ...blockContents].join('\n\n');
}

/**
 * Validate generated quiz question against source content
 */
async function validateQuizQuestion(
  sourceContent: string,
  question: QuizQuestion,
  options: QuizGenerationOptions = {}
): Promise<ContentValidation> {
  const openrouter = createAIProvider(options.env);
  const model = openrouter(options.model || 'openai/gpt-4o');

  const validationPrompt = `Analyze if this quiz question can be answered solely from the provided content:

**SOURCE CONTENT**:
${sourceContent}

**QUESTION TO VALIDATE**:
${JSON.stringify(question, null, 2)}

**VALIDATION CRITERIA**:
- Can the question be answered using only the provided content?
- Are all terms and concepts mentioned in the question present in the source?
- Does the question require external knowledge not provided in the content?

Respond with a validation assessment.`;

  try {
    const result = await generateObject({
      model,
      schema: contentValidationSchema,
      system: 'You are a content validation expert. Analyze quiz questions for hallucination and source adherence.',
      prompt: validationPrompt,
      temperature: 0.1, // Low temperature for consistent validation
      // Add provider routing to ensure structured output support
      experimental_providerMetadata: {
        openrouter: {
          provider: {
            require_parameters: true, // Only use providers that support all parameters
            data_collection: 'allow', // Allow data collection for better model availability
          }
        }
      }
    });

    return result.object;
  } catch (error) {
    console.error('Quiz question validation failed:', error);
    console.error('Question details:', {
      id: question.id,
      type: question.type,
      sourceStepId: question.sourceStepId
    });

    // Return conservative validation result
    return {
      sourceContent,
      generatedQuestion: question,
      validationChecks: {
        contentMatch: false,
        noHallucination: false,
        appropriateDifficulty: false,
        clearlyAnswerable: false,
      },
      confidence: 0.0,
    };
  }
}

/**
 * Validate question based on its type with appropriate criteria
 */
function validateQuestionByType(question: QuizQuestion, validation: ContentValidation): boolean {
  const { confidence, validationChecks } = validation;
  const { contentMatch, noHallucination, clearlyAnswerable } = validationChecks;

  // Core requirements for all question types
  if (!contentMatch) {
    return false;
  }

  // Special validation for flashcards to prevent identical front/back
  if (question.type === 'flashcard') {
    const flashcard = question as any; // Type assertion for flashcard
    if (flashcard.front && flashcard.back) {
      // Check if front and back are identical or too similar
      const frontText = flashcard.front.trim().toLowerCase();
      const backText = flashcard.back.trim().toLowerCase();

      if (frontText === backText) {
        console.warn('Flashcard validation failed: identical front and back content', {
          questionId: question.id,
          front: flashcard.front,
          back: flashcard.back
        });
        return false;
      }

      // Check if they're too similar (more than 80% overlap)
      const similarity = calculateTextSimilarity(frontText, backText);
      if (similarity > 0.8) {
        console.warn('Flashcard validation failed: front and back too similar', {
          questionId: question.id,
          similarity,
          front: flashcard.front,
          back: flashcard.back
        });
        return false;
      }
    }
  }

  // Question-type-specific validation criteria
  switch (question.type) {
    case 'ordering':
    case 'freeText':
      // More lenient criteria for complex question types
      // These types often require inference and interpretation
      return confidence >= 0.5 &&
             (noHallucination || clearlyAnswerable); // Either check can pass

    case 'flashcard':
    case 'multipleChoice':
    case 'trueFalse':
    case 'fillInBlank':
    case 'matching':
      // Stricter criteria for objective question types
      return confidence >= 0.6 &&
             noHallucination &&
             clearlyAnswerable;

    default:
      // Default validation for any new question types
      return confidence >= 0.6 &&
             noHallucination &&
             clearlyAnswerable;
  }
}

/**
 * Calculate text similarity using simple word overlap
 */
function calculateTextSimilarity(text1: string, text2: string): number {
  const words1 = new Set(text1.split(/\s+/));
  const words2 = new Set(text2.split(/\s+/));

  const intersection = new Set([...words1].filter(word => words2.has(word)));
  const union = new Set([...words1, ...words2]);

  return union.size > 0 ? intersection.size / union.size : 0;
}

/**
 * Check if a model is from Meta provider (which has depth limitations)
 */
function isMetaProvider(modelName: string): boolean {
  return modelName.includes('meta-llama') || modelName.includes('llama');
}

/**
 * Transform AI-generated question to match strict schema requirements
 */
function transformAIQuestionToStrict(rawQuestion: any, stepId: string, stepContent: string): QuizQuestion | null {
  try {
    // Base fields that all questions need
    // Always generate a new UUID to ensure uniqueness, regardless of AI-provided ID
    const baseQuestion = {
      id: crypto.randomUUID(),
      type: rawQuestion.type,
      difficulty: rawQuestion.difficulty || 'medium',
      sourceStepId: stepId,
      sourceContent: stepContent.substring(0, 500),
      points: rawQuestion.points || 1,
    };

    // Transform based on question type with proper validation
    switch (rawQuestion.type) {
      case 'flashcard':
        if (!rawQuestion.front || !rawQuestion.back) {
          console.warn(`Flashcard question missing required fields: front=${!!rawQuestion.front}, back=${!!rawQuestion.back}`);
          return null;
        }
        return {
          ...baseQuestion,
          type: 'flashcard',
          front: rawQuestion.front,
          back: rawQuestion.back,
          hint: rawQuestion.hint,
        };

      case 'multipleChoice':
        if (!rawQuestion.question || !rawQuestion.options || rawQuestion.options.length !== 4 || rawQuestion.correctAnswerIndex === undefined) {
          console.warn(`Multiple choice question missing required fields or invalid structure`);
          return null;
        }
        return {
          ...baseQuestion,
          type: 'multipleChoice',
          question: rawQuestion.question,
          options: rawQuestion.options,
          correctAnswerIndex: rawQuestion.correctAnswerIndex,
          explanation: rawQuestion.explanation,
        };

      case 'trueFalse':
        if (!rawQuestion.statement || rawQuestion.correctAnswer === undefined) {
          console.warn(`True/false question missing required fields`);
          return null;
        }
        return {
          ...baseQuestion,
          type: 'trueFalse',
          statement: rawQuestion.statement,
          correctAnswer: rawQuestion.correctAnswer,
          explanation: rawQuestion.explanation,
        };

      case 'fillInBlank':
        console.debug(`Fill in blank question raw data:`, {
          id: rawQuestion.id,
          text: rawQuestion.text,
          blanks: rawQuestion.blanks,
          blanksLength: rawQuestion.blanks?.length,
          hint: rawQuestion.hint,
          allFields: Object.keys(rawQuestion)
        });

        // Try to extract blanks from JSON if needed
        let blanks = rawQuestion.blanks;
        if (!blanks && rawQuestion.blanksJson) {
          try {
            blanks = JSON.parse(rawQuestion.blanksJson);
          } catch (e) {
            console.warn('Failed to parse blanksJson:', rawQuestion.blanksJson);
          }
        }

        if (!rawQuestion.text || !blanks || blanks.length === 0) {
          console.warn(`Fill in blank question missing required fields`, {
            hasText: !!rawQuestion.text,
            hasBlanks: !!blanks,
            blanksLength: blanks?.length || 0,
            actualBlanks: blanks,
            rawBlanks: rawQuestion.blanks,
            rawBlanksJson: rawQuestion.blanksJson,
            allFields: Object.keys(rawQuestion)
          });
          return null;
        }
        return {
          ...baseQuestion,
          type: 'fillInBlank',
          text: rawQuestion.text,
          blanks: blanks,
          hint: rawQuestion.hint,
        };

      case 'matching':
        console.debug(`Matching question raw data:`, {
          id: rawQuestion.id,
          pairs: rawQuestion.pairs,
          pairsLength: rawQuestion.pairs?.length,
          instruction: rawQuestion.instruction,
          allFields: Object.keys(rawQuestion)
        });

        // Try to extract pairs from other possible field names
        let pairs = rawQuestion.pairs;
        if (!pairs && rawQuestion.pairsJson) {
          try {
            pairs = JSON.parse(rawQuestion.pairsJson);
          } catch (e) {
            console.warn('Failed to parse pairsJson:', rawQuestion.pairsJson);
          }
        }

        if (!pairs || pairs.length < 3) {
          console.warn(`Matching question missing required pairs or insufficient pairs (need at least 3)`, {
            hasPairs: !!pairs,
            pairsLength: pairs?.length || 0,
            actualPairs: pairs,
            rawPairs: rawQuestion.pairs,
            rawPairsJson: rawQuestion.pairsJson,
            allFields: Object.keys(rawQuestion)
          });
          return null;
        }
        return {
          ...baseQuestion,
          type: 'matching',
          instruction: rawQuestion.instruction || 'Match the following items:',
          pairs: pairs,
        };

      case 'freeText':
        console.debug(`Free text question raw data:`, {
          id: rawQuestion.id,
          question: rawQuestion.question,
          answerType: rawQuestion.answerType,
          maxLength: rawQuestion.maxLength,
          sampleAnswer: rawQuestion.sampleAnswer,
          evaluationCriteria: rawQuestion.evaluationCriteria,
          allFields: Object.keys(rawQuestion)
        });

        // Try to provide fallback values for missing fields
        const question = rawQuestion.question;
        const answerType = rawQuestion.answerType || 'short';
        const maxLength = rawQuestion.maxLength || (answerType === 'short' ? 200 : 500);
        const sampleAnswer = rawQuestion.sampleAnswer || 'A comprehensive answer based on the provided content.';
        let evaluationCriteria = rawQuestion.evaluationCriteria;

        // Try to parse evaluationCriteria from JSON if it's a string
        if (!evaluationCriteria && rawQuestion.evaluationCriteriaJson) {
          try {
            evaluationCriteria = JSON.parse(rawQuestion.evaluationCriteriaJson);
          } catch (e) {
            console.warn('Failed to parse evaluationCriteriaJson:', rawQuestion.evaluationCriteriaJson);
          }
        }

        // Provide default evaluation criteria if still missing
        if (!evaluationCriteria) {
          evaluationCriteria = ['Demonstrates understanding of key concepts', 'Uses terminology from the content', 'Provides clear explanation'];
        }

        if (!question) {
          console.warn(`Free text question missing required question field`, {
            hasQuestion: !!question,
            allFields: Object.keys(rawQuestion)
          });
          return null;
        }

        return {
          ...baseQuestion,
          type: 'freeText',
          question: question,
          answerType: answerType,
          maxLength: maxLength,
          sampleAnswer: sampleAnswer,
          evaluationCriteria: evaluationCriteria,
        };

      case 'ordering':
        if (!rawQuestion.items || !rawQuestion.correctOrder || rawQuestion.items.length < 3) {
          console.warn(`Ordering question missing required fields or insufficient items (need at least 3)`);
          return null;
        }
        return {
          ...baseQuestion,
          type: 'ordering',
          instruction: rawQuestion.instruction || 'Arrange the following items in the correct order:',
          items: rawQuestion.items,
          correctOrder: rawQuestion.correctOrder,
          orderType: rawQuestion.orderType || 'logical',
        };

      default:
        console.warn(`Unknown question type: ${rawQuestion.type}`);
        return null;
    }
  } catch (error) {
    console.warn(`Failed to transform AI question:`, error);
    return null;
  }
}

/**
 * Fallback text-based generation when structured output fails
 */
async function generateQuestionsWithTextFallback(
  model: any,
  userPrompt: string,
  stepContent: string,
  stepId: string,
  options: QuizGenerationOptions
): Promise<QuizQuestion[]> {
  console.info('Attempting text-based generation as fallback...');

  const textPrompt = `${QUIZ_GENERATION_SYSTEM_PROMPT}

${userPrompt}

Please respond with valid JSON in the following format:
{
  "questions": [
    {
      "id": "unique-id",
      "type": "multipleChoice|flashcard|trueFalse|fillInBlank|matching|freeText|ordering",
      "difficulty": "easy|medium|hard",
      "points": 1,
      // Add appropriate fields based on question type
    }
  ]
}`;

  try {
    const result = await generateText({
      model,
      prompt: textPrompt,
      temperature: options.temperature || 0.3,
      maxTokens: options.maxTokens || 4000,
    });

    // Try to parse the JSON response
    const jsonMatch = result.text.match(/\{[\s\S]*\}/);
    if (!jsonMatch) {
      throw new Error('No JSON found in text response');
    }

    const parsedResponse = JSON.parse(jsonMatch[0]);

    // Validate and convert to proper format
    const questions: QuizQuestion[] = [];
    for (const rawQuestion of parsedResponse.questions || []) {
      try {
        const fullQuestion = {
          ...rawQuestion,
          sourceStepId: stepId,
          sourceContent: stepContent.substring(0, 500),
        };

        const validatedQuestion = quizQuestionSchema.parse(fullQuestion);
        questions.push(validatedQuestion);
      } catch (validationError) {
        console.warn('Text fallback question validation failed:', validationError);
      }
    }

    return questions;
  } catch (error) {
    console.error('Text-based fallback generation failed:', error);
    return [];
  }
}

/**
 * Fallback validation for edge cases where strict validation might be too harsh
 */
function validateQuestionFallback(question: QuizQuestion, validation: ContentValidation): boolean {
  const { confidence, validationChecks } = validation;
  const { contentMatch, appropriateDifficulty } = validationChecks;

  // Very lenient fallback criteria
  // Only require content match and reasonable confidence
  if (!contentMatch || confidence < 0.4) {
    return false;
  }

  // For ordering and freeText, be very lenient as they often require interpretation
  if (question.type === 'ordering' || question.type === 'freeText') {
    return confidence >= 0.4 && appropriateDifficulty;
  }

  // For other types, require slightly higher confidence
  return confidence >= 0.5 && appropriateDifficulty;
}

/**
 * Generate quiz questions for a single step with quota tracking
 */
async function generateQuestionsForStepWithQuotas(
  step: LearningStep,
  quizTypes: QuizType[],
  typeQuotas: Map<QuizType, number>,
  difficulty: QuizDifficulty,
  options: QuizGenerationOptions = {}
): Promise<QuizQuestion[]> {
  const stepContent = extractStepContent(step);

  if (stepContent.trim().length < 50) {
    console.warn(`Step ${step.id} has insufficient content for quiz generation`);
    return [];
  }

  const openrouter = createAIProvider(options.env);

  // Use only models that reliably support structured outputs for quiz generation
  const modelSelection = selectModelWithFallback(
    ['paragraph'], // Default content type for quiz generation
    difficulty === 'easy' ? 'beginner' : difficulty === 'medium' ? 'intermediate' : 'advanced',
    {
      preferCostEffective: options.preferCostEffective !== false, // Default to true
      requireStructuredOutput: true,
      maxRetries: 3
    }
  );

  const primaryModel = options.model || modelSelection.primary;

  // Calculate total questions needed for this step
  const totalQuestionsForStep = Array.from(typeQuotas.values()).reduce((sum, count) => sum + count, 0);
  const dynamicSchemas = createDynamicQuizSchemas(Math.max(totalQuestionsForStep, 1));

  console.info(`Dynamic schema limits for step ${step.id}: maxQuestions=${totalQuestionsForStep} with quotas:`, Object.fromEntries(typeQuotas));

  const userPrompt = createQuizGenerationPromptWithQuotas(
    stepContent,
    step.title,
    step.id,
    typeQuotas,
    difficulty
  );

  // Try primary model first, then fallback models if needed
  let questions: QuizQuestion[] = [];
  let lastError: Error | null = null;

  const modelsToTry = [primaryModel, ...modelSelection.fallbacks];

  console.info(`Quiz generation model selection: primary=${primaryModel}, fallbacks=[${modelSelection.fallbacks.join(', ')}]`);

  for (const modelToTry of modelsToTry) {
    try {
      console.info(`Attempting quiz generation with model: ${modelToTry}`);

      const currentModel = openrouter(modelToTry);
      const isMetaModel = isMetaProvider(modelToTry);

      // Use dynamic schemas with calculated max questions
      const schema = isMetaModel ? dynamicSchemas.metaCompatibleQuizSchema : dynamicSchemas.aiQuizGenerationResponseSchema;

      console.info(`Using ${isMetaModel ? 'Meta-compatible' : 'standard'} schema for model: ${modelToTry}`);

      const result = await generateObject({
        model: currentModel,
        schema,
        system: QUIZ_GENERATION_SYSTEM_PROMPT,
        prompt: userPrompt,
        temperature: options.temperature || 0.3,
        maxTokens: options.maxTokens || 4000,
        // Add provider routing to ensure structured output support
        experimental_providerMetadata: {
          openrouter: {
            provider: {
              require_parameters: true, // Only use providers that support all parameters
              data_collection: 'allow', // Allow data collection for better model availability
            }
          }
        }
      });

      // Convert AI response to full quiz question schema
      let aiResponse;
      if (isMetaModel) {
        // Convert Meta-compatible response to standard format
        aiResponse = convertMetaResponseToFull(result.object);
      } else {
        aiResponse = result.object;
      }

      // Log the full AI response for debugging
      console.info(`AI Response for step ${step.id}:`, {
        totalQuestions: aiResponse.questions.length,
        questionTypes: aiResponse.questions.map((q: any) => q.type),
        questionIds: aiResponse.questions.map((q: any) => q.id),
        requestedQuotas: Object.fromEntries(typeQuotas)
      });

      const validatedQuestions: QuizQuestion[] = [];

      for (const rawQuestion of aiResponse.questions) {
        try {
          // Transform AI question to match strict schema requirements
          const transformedQuestion = transformAIQuestionToStrict(rawQuestion, step.id, stepContent);

          if (transformedQuestion) {
            // Validate the transformed question against the full schema
            const validatedQuestion = quizQuestionSchema.parse(transformedQuestion);
            validatedQuestions.push(validatedQuestion);
            console.debug(`Successfully validated ${rawQuestion.type} question: ${rawQuestion.id}`);
          } else {
            console.warn(`Question transformation failed for step ${step.id}:`, {
              questionId: rawQuestion.id,
              questionType: rawQuestion.type,
              reason: 'Missing required fields or invalid structure'
            });
          }
        } catch (validationError) {
          console.warn(`Question failed schema validation for step ${step.id}:`, {
            questionId: rawQuestion.id,
            questionType: rawQuestion.type,
            error: validationError,
          });
          // Skip invalid questions rather than failing the entire generation
        }
      }

      questions = validatedQuestions;
      console.info(`Successfully generated ${questions.length} questions using model: ${modelToTry}`);
      break; // Success, exit the retry loop

    } catch (error) {
      lastError = error as Error;
      console.warn(`Structured output failed with model ${modelToTry}:`, error);

      // If this is not the last model to try, continue to the next one
      if (modelToTry !== modelsToTry[modelsToTry.length - 1]) {
        console.info(`Trying fallback model...`);
        continue;
      }
    }
  }

  // If all models failed, throw the last error
  if (questions.length === 0 && lastError) {
    throw new Error(`All models failed to generate questions. Last error: ${lastError.message}`);
  }

  // Validate questions if enabled
  if (options.validateContent !== false) {
    const validatedQuestions: QuizQuestion[] = [];

    for (const question of questions) {
      const validation = await validateQuizQuestion(stepContent, question, options);

      // Question-type-specific validation criteria
      const isValidQuestion = validateQuestionByType(question, validation);

      if (isValidQuestion) {
        validatedQuestions.push(question);
      } else {
        // Try fallback validation for edge cases
        const fallbackValid = validateQuestionFallback(question, validation);

        if (fallbackValid) {
          console.info(`Question passed fallback validation for step ${step.id}:`, {
            questionId: question.id,
            questionType: question.type,
            confidence: validation.confidence,
            validationChecks: validation.validationChecks
          });
          validatedQuestions.push(question);
        } else {
          console.warn(`Question failed validation for step ${step.id}:`, {
            questionId: question.id,
            questionType: question.type,
            confidence: validation.confidence,
            validationChecks: validation.validationChecks,
            sourceStepId: step.id
          });
        }
      }
    }

    return validatedQuestions;
  }

  return questions;
}

/**
 * Generate quiz questions for a single step with cost-optimized model selection (legacy)
 */
async function generateQuestionsForStep(
  step: LearningStep,
  quizTypes: QuizType[],
  difficulty: QuizDifficulty,
  questionsPerType: number,
  options: QuizGenerationOptions = {}
): Promise<QuizQuestion[]> {
  // Convert to quota-based approach
  const typeQuotas = createQuestionTypeQuotas(quizTypes, questionsPerType);
  return generateQuestionsForStepWithQuotas(step, quizTypes, typeQuotas, difficulty, options);
}

/**
 * Calculate estimated quiz duration based on question types and count
 */
function calculateEstimatedDuration(questions: QuizQuestion[]): number {
  const timePerQuestion = {
    flashcard: 0.5,      // 30 seconds
    multipleChoice: 1.0,  // 1 minute
    trueFalse: 0.5,      // 30 seconds
    fillInBlank: 1.5,    // 1.5 minutes
    matching: 2.0,       // 2 minutes
    freeText: 3.0,       // 3 minutes
    ordering: 2.0,       // 2 minutes
  };

  const totalMinutes = questions.reduce((total, question) => {
    return total + (timePerQuestion[question.type] || 1.0);
  }, 0);

  return Math.ceil(totalMinutes);
}

/**
 * Create a quota tracking system for question types
 */
function createQuestionTypeQuotas(
  quizTypes: QuizType[],
  questionsPerType: number
): Map<QuizType, number> {
  const quotas = new Map<QuizType, number>();
  quizTypes.forEach(type => {
    quotas.set(type, questionsPerType);
  });
  return quotas;
}

/**
 * Distribute questions across steps with proper type quota tracking
 */
function distributeQuestionsAcrossSteps(
  totalQuestions: number,
  stepCount: number,
  quizTypes: QuizType[],
  questionsPerType: number
): Array<{ questionsToGenerate: number; typesToUse: QuizType[]; typeQuotas: Map<QuizType, number> }> {
  const distributions: Array<{ questionsToGenerate: number; typesToUse: QuizType[]; typeQuotas: Map<QuizType, number> }> = [];

  // Create master quota tracker
  const masterQuotas = createQuestionTypeQuotas(quizTypes, questionsPerType);

  console.info('Initial type quotas:', Object.fromEntries(masterQuotas));

  // Create a round-robin distribution of types across steps
  const typeAssignments: QuizType[] = [];

  // Fill typeAssignments with proper distribution
  for (let i = 0; i < totalQuestions; i++) {
    const typeIndex = i % quizTypes.length;
    typeAssignments.push(quizTypes[typeIndex]);
  }

  console.info('Type assignments across questions:', typeAssignments);

  if (stepCount <= totalQuestions) {
    // Distribute questions evenly across steps
    const baseQuestionsPerStep = Math.floor(totalQuestions / stepCount);
    const remainderQuestions = totalQuestions % stepCount;

    let assignmentIndex = 0;

    for (let i = 0; i < stepCount; i++) {
      const questionsForThisStep = baseQuestionsPerStep + (i < remainderQuestions ? 1 : 0);

      // Get the specific types for this step from our assignments
      const typesForThisStep: QuizType[] = [];
      const stepQuotas = new Map<QuizType, number>();

      for (let j = 0; j < questionsForThisStep; j++) {
        if (assignmentIndex < typeAssignments.length) {
          const assignedType = typeAssignments[assignmentIndex];
          typesForThisStep.push(assignedType);
          stepQuotas.set(assignedType, (stepQuotas.get(assignedType) || 0) + 1);
          assignmentIndex++;
        }
      }

      distributions.push({
        questionsToGenerate: questionsForThisStep,
        typesToUse: Array.from(new Set(typesForThisStep)), // Remove duplicates for AI prompt
        typeQuotas: stepQuotas
      });
    }
  } else {
    // More steps than questions - one question per step
    for (let i = 0; i < stepCount; i++) {
      if (i < totalQuestions) {
        const assignedType = typeAssignments[i];
        const stepQuotas = new Map<QuizType, number>();
        stepQuotas.set(assignedType, 1);

        distributions.push({
          questionsToGenerate: 1,
          typesToUse: [assignedType],
          typeQuotas: stepQuotas
        });
      } else {
        distributions.push({
          questionsToGenerate: 0,
          typesToUse: [],
          typeQuotas: new Map()
        });
      }
    }
  }

  return distributions;
}

/**
 * Generate quiz from learning content
 */
export async function generateQuizFromContent(
  learningContent: LearningContent,
  input: QuizGenerationInput,
  options: QuizGenerationOptions = {}
): Promise<GeneratedQuiz> {
  // Validate input
  const validatedInput = quizGenerationInputSchema.parse(input);

  if (learningContent.steps.length === 0) {
    throw new Error('Learning content must have at least one step');
  }

  // Calculate total target questions
  const totalTargetQuestions = validatedInput.quizTypes.length * validatedInput.questionsPerType;

  console.info(`Quiz generation strategy:`, {
    totalSteps: learningContent.steps.length,
    targetQuestions: totalTargetQuestions,
    quizTypes: validatedInput.quizTypes,
    questionsPerType: validatedInput.questionsPerType
  });

  // Distribute questions across steps to achieve exact target total
  const questionDistribution = distributeQuestionsAcrossSteps(
    totalTargetQuestions,
    learningContent.steps.length,
    validatedInput.quizTypes,
    validatedInput.questionsPerType
  );

  console.info(`Question distribution across steps:`, questionDistribution.map((dist, i) => ({
    stepIndex: i,
    questionsToGenerate: dist.questionsToGenerate,
    typesToUse: dist.typesToUse,
    typeQuotas: Object.fromEntries(dist.typeQuotas)
  })));

  const allQuestions: QuizQuestion[] = [];
  const sourceStepsUsed: string[] = [];
  const globalTypeTracker = createQuestionTypeQuotas(validatedInput.quizTypes, validatedInput.questionsPerType);

  // Generate questions for each step according to distribution
  for (let i = 0; i < learningContent.steps.length; i++) {
    const step = learningContent.steps[i];
    const distribution = questionDistribution[i];

    if (distribution.questionsToGenerate === 0) {
      console.info(`Skipping step ${step.id} - no questions allocated`);
      continue;
    }

    console.info(`Generating ${distribution.questionsToGenerate} questions for step ${step.id}:`, {
      typesToUse: distribution.typesToUse,
      typeQuotas: Object.fromEntries(distribution.typeQuotas),
      remainingGlobalQuotas: Object.fromEntries(globalTypeTracker)
    });

    const stepQuestions = await generateQuestionsForStepWithQuotas(
      step,
      distribution.typesToUse,
      distribution.typeQuotas,
      validatedInput.difficulty,
      options
    );

    if (stepQuestions.length > 0) {
      // Update global tracker and add questions
      stepQuestions.forEach(question => {
        const currentQuota = globalTypeTracker.get(question.type) || 0;
        if (currentQuota > 0) {
          globalTypeTracker.set(question.type, currentQuota - 1);
          allQuestions.push(question);
        }
      });

      sourceStepsUsed.push(step.id);

      console.info(`Added ${stepQuestions.length} questions from step ${step.id}:`, {
        questionTypes: stepQuestions.map(q => q.type),
        updatedGlobalQuotas: Object.fromEntries(globalTypeTracker)
      });
    } else {
      console.warn(`No valid questions generated for step ${step.id}`);
    }
  }

  if (allQuestions.length === 0) {
    console.error('Quiz generation failed - no valid questions generated');
    console.error('Steps processed:', learningContent.steps.length);
    console.error('Source steps used:', sourceStepsUsed.length);
    throw new Error('Failed to generate any valid quiz questions from the content');
  }

  console.info(`Quiz generation successful: ${allQuestions.length} questions generated from ${sourceStepsUsed.length} steps (target was ${totalTargetQuestions})`);

  // Calculate metadata
  const totalPoints = allQuestions.reduce((sum, q) => sum + q.points, 0);
  const estimatedDuration = calculateEstimatedDuration(allQuestions);

  // Calculate distributions
  const difficultyDistribution: Record<string, number> = {};
  const typeDistribution: Record<string, number> = {};

  allQuestions.forEach(question => {
    difficultyDistribution[question.difficulty] = (difficultyDistribution[question.difficulty] || 0) + 1;
    typeDistribution[question.type] = (typeDistribution[question.type] || 0) + 1;
  });

  const quiz: GeneratedQuiz = {
    id: crypto.randomUUID(),
    title: `Quiz: ${learningContent.title}`,
    description: `Generated quiz based on "${learningContent.title}" learning content`,
    learningContentId: learningContent.id,
    questions: allQuestions,
    estimatedDuration,
    totalPoints,
    metadata: {
      generatedAt: new Date().toISOString(),
      aiModel: options.model || 'openai/gpt-4o-mini', // Updated default to gpt-4o-mini
      sourceStepsUsed,
      difficultyDistribution,
      typeDistribution,
    },
  };

  return generatedQuizSchema.parse(quiz);
}

/**
 * Get available quiz types with descriptions
 */
export function getAvailableQuizTypes(): Array<{
  type: QuizType;
  name: string;
  description: string;
  estimatedTime: string;
}> {
  return [
    {
      type: 'flashcard',
      name: 'Flashcards',
      description: 'Term and definition or question and answer cards',
      estimatedTime: '30 seconds per card'
    },
    {
      type: 'multipleChoice',
      name: 'Multiple Choice',
      description: 'Questions with 4 options, one correct answer',
      estimatedTime: '1 minute per question'
    },
    {
      type: 'trueFalse',
      name: 'True or False',
      description: 'Statements to evaluate as true or false',
      estimatedTime: '30 seconds per question'
    },
    {
      type: 'fillInBlank',
      name: 'Fill in the Blank',
      description: 'Complete sentences with missing words or phrases',
      estimatedTime: '1.5 minutes per question'
    },
    {
      type: 'matching',
      name: 'Matching Pairs',
      description: 'Match terms with their definitions or descriptions',
      estimatedTime: '2 minutes per set'
    },
    {
      type: 'freeText',
      name: 'Free Text Answer',
      description: 'Short or long-form written responses',
      estimatedTime: '3 minutes per question'
    },
    {
      type: 'ordering',
      name: 'Ordering/Sequencing',
      description: 'Arrange items in the correct order',
      estimatedTime: '2 minutes per question'
    }
  ];
}
