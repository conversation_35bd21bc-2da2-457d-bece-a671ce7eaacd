/**
 * Integration test to verify the validation fix works end-to-end
 */

import { describe, it, expect } from '@jest/globals';
import {
  transformAIGeneratedData,
  validateStepDataWithDetails,
  generatedLearningContentSchema
} from '../schemas/content-generation';

describe('Validation Integration Test', () => {
  describe('End-to-end validation workflow', () => {
    it('should handle a complete AI-generated content structure with transformation', () => {
      // Simulate AI-generated content with common problematic formats
      const aiGeneratedContent = {
        title: "Understanding Machine Learning",
        description: "Learn the basics of machine learning",
        estimatedReadingTime: 5,
        steps: [
          {
            title: "Introduction",
            icon: "Brain",
            type: "paragraph",
            data: { content: "Machine learning is a subset of artificial intelligence." } // Problematic format
          },
          {
            title: "Key Concepts",
            icon: "BookOpen",
            type: "bulletList",
            data: { items: ["Supervised learning", "Unsupervised learning", "Reinforcement learning"] } // Problematic format
          },
          {
            title: "Important Note",
            icon: "AlertCircle",
            type: "infoBox",
            data: {
              heading: "Remember",
              lines: ["Practice is key", "Start with simple examples"]
            } // Correct format
          },
          {
            title: "Multiple Paragraphs",
            icon: "FileText",
            type: "paragraph",
            data: { paragraphs: ["First paragraph about ML.", "Second paragraph with more details."] } // Problematic format
          }
        ],
        metadata: {
          aiModel: "test-model",
          generatedAt: "2024-01-01T00:00:00Z",
          contentTypes: ["paragraph", "bulletList", "infoBox"],
          learningLevel: "beginner",
          topic: "Machine Learning"
        }
      };

      // Step 1: Transform each step's data
      const transformedContent = {
        ...aiGeneratedContent,
        steps: aiGeneratedContent.steps.map(step => ({
          ...step,
          data: transformAIGeneratedData(step.type, step.data)
        }))
      };

      // Step 2: Validate the overall structure
      const structureValidation = generatedLearningContentSchema.safeParse(transformedContent);
      expect(structureValidation.success).toBe(true);

      // Step 3: Validate each step's data individually
      transformedContent.steps.forEach((step, index) => {
        const validation = validateStepDataWithDetails(step.type, step.data);
        expect(validation.isValid).toBe(true);

        console.log(`Step ${index + 1} (${step.type}):`, {
          original: aiGeneratedContent.steps[index].data,
          transformed: step.data,
          valid: validation.isValid
        });
      });

      // Step 4: Verify the transformed data is in the correct format
      expect(transformedContent.steps[0].data).toBe("Machine learning is a subset of artificial intelligence.");
      expect(transformedContent.steps[1].data).toEqual(["Supervised learning", "Unsupervised learning", "Reinforcement learning"]);
      expect(transformedContent.steps[2].data).toEqual({
        heading: "Remember",
        lines: ["Practice is key", "Start with simple examples"]
      });
      expect(transformedContent.steps[3].data).toEqual(["First paragraph about ML.", "Second paragraph with more details."]);
    });

    it('should handle edge cases in transformation', () => {
      const edgeCases = [
        {
          type: 'paragraph',
          input: null,
          expected: null
        },
        {
          type: 'paragraph',
          input: undefined,
          expected: undefined
        },
        {
          type: 'paragraph',
          input: { data: { data: { content: "Deeply nested" } } },
          expected: "Deeply nested"
        },
        {
          type: 'bulletList',
          input: { items: ["Item 1", "Item 2"] },
          expected: ["Item 1", "Item 2"]
        },
        {
          type: 'bulletList',
          input: { list: ["Point 1", "Point 2"] },
          expected: ["Point 1", "Point 2"]
        }
      ];

      edgeCases.forEach((testCase, index) => {
        const transformed = transformAIGeneratedData(testCase.type, testCase.input);
        expect(transformed).toEqual(testCase.expected);

        console.log(`Edge case ${index + 1}:`, {
          type: testCase.type,
          input: testCase.input,
          expected: testCase.expected,
          transformed: transformed
        });
      });
    });

    it('should provide detailed error information for truly invalid data', () => {
      const invalidData = {
        type: 'paragraph',
        data: 12345 // Number is not valid for paragraph
      };

      const transformed = transformAIGeneratedData(invalidData.type, invalidData.data);
      const validation = validateStepDataWithDetails(invalidData.type, transformed);

      expect(validation.isValid).toBe(false);
      expect(validation.error).toBeDefined();
      expect(validation.expectedFormat).toContain('string | string[]');
      expect(validation.actualData).toBe(12345);

      console.log('Invalid data validation result:', validation);
    });
  });

  describe('Transformation coverage', () => {
    it('should handle all common AI-generated paragraph formats', () => {
      const formats = [
        { content: "Content property" },
        { text: "Text property" },
        { paragraph: "Paragraph property" },
        { paragraphs: ["Para 1", "Para 2"] },
        { body: "Body property" },
        { description: "Description property" },
        { data: { content: "Nested content" } }
      ];

      formats.forEach((format, index) => {
        const transformed = transformAIGeneratedData('paragraph', format);
        const validation = validateStepDataWithDetails('paragraph', transformed);

        expect(validation.isValid).toBe(true);
        console.log(`Format ${index + 1} transformation:`, {
          input: format,
          output: transformed,
          valid: validation.isValid
        });
      });
    });

    it('should handle all content types with common AI formats', () => {
      const testCases = [
        // bulletList transformations
        {
          type: 'bulletList',
          input: { items: ["Item 1", "Item 2"] },
          expected: ["Item 1", "Item 2"]
        },
        {
          type: 'bulletList',
          input: { list: ["Point 1", "Point 2"] },
          expected: ["Point 1", "Point 2"]
        },

        // infoBox transformations
        {
          type: 'infoBox',
          input: { heading: "Important", items: ["Note 1", "Note 2"] },
          expected: { heading: "Important", lines: ["Note 1", "Note 2"] }
        },

        // grid transformations
        {
          type: 'grid',
          input: { items: [{ title: "Title 1", content: "Content 1" }] },
          expected: [{ title: "Title 1", content: "Content 1" }]
        },

        // keyValueGrid transformations
        {
          type: 'keyValueGrid',
          input: { items: [{ key: "Term", value: "Definition" }] },
          expected: [{ key: "Term", value: "Definition" }]
        }
      ];

      testCases.forEach((testCase, index) => {
        const transformed = transformAIGeneratedData(testCase.type, testCase.input);
        const validation = validateStepDataWithDetails(testCase.type, transformed);

        expect(transformed).toEqual(testCase.expected);
        expect(validation.isValid).toBe(true);

        console.log(`Content type ${testCase.type} transformation ${index + 1}:`, {
          input: testCase.input,
          expected: testCase.expected,
          transformed: transformed,
          valid: validation.isValid
        });
      });
    });
  });
});
