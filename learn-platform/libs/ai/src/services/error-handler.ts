/**
 * Comprehensive error handling for AI service
 */

import { AI_CONFIG } from '../config/ai-config';

// Base error class for AI service
export class AIServiceError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any,
    public userMessage?: string
  ) {
    super(message);
    this.name = 'AIServiceError';
  }

  // Get user-friendly error message
  getUserMessage(): string {
    return this.userMessage || this.message;
  }

  // Check if error is retryable
  isRetryable(): boolean {
    const retryableCodes = [
      'NETWORK_ERROR',
      'TIMEOUT_ERROR',
      'RATE_LIMIT',
      'SERVER_ERROR',
      'TEMPORARY_UNAVAILABLE'
    ];
    return retryableCodes.includes(this.code);
  }
}

// Specific error types
export class RateLimitError extends AIServiceError {
  constructor(message: string, details?: any) {
    super(
      message,
      'RATE_LIMIT',
      details,
      'Too many requests. Please wait a moment and try again.'
    );
    this.name = 'RateLimitError';
  }
}

export class QuotaExceededError extends AIServiceError {
  constructor(message: string, details?: any) {
    super(
      message,
      'QUOTA_EXCEEDED',
      details,
      'API quota exceeded. Please check your account or try again later.'
    );
    this.name = 'QuotaExceededError';
  }
}

export class InvalidInputError extends AIServiceError {
  constructor(message: string, details?: any) {
    super(
      message,
      'INVALID_INPUT',
      details,
      'Invalid input provided. Please check your request and try again.'
    );
    this.name = 'InvalidInputError';
  }
}

export class ModelError extends AIServiceError {
  constructor(message: string, details?: any) {
    super(
      message,
      'MODEL_ERROR',
      details,
      'AI model error. Please try again or contact support if the issue persists.'
    );
    this.name = 'ModelError';
  }
}

export class NetworkError extends AIServiceError {
  constructor(message: string, details?: any) {
    super(
      message,
      'NETWORK_ERROR',
      details,
      'Network connection error. Please check your internet connection and try again.'
    );
    this.name = 'NetworkError';
  }
}

export class TimeoutError extends AIServiceError {
  constructor(message: string, details?: any) {
    super(
      message,
      'TIMEOUT_ERROR',
      details,
      'Request timed out. Please try again with a simpler request or contact support.'
    );
    this.name = 'TimeoutError';
  }
}

export class ValidationError extends AIServiceError {
  constructor(message: string, details?: any) {
    super(
      message,
      'VALIDATION_ERROR',
      details,
      'Generated content validation failed. Please try again.'
    );
    this.name = 'ValidationError';
  }
}

export class ConfigurationError extends AIServiceError {
  constructor(message: string, details?: any) {
    super(
      message,
      'CONFIG_ERROR',
      details,
      'Service configuration error. Please contact support.'
    );
    this.name = 'ConfigurationError';
  }
}

// Error classification function
export function classifyError(error: any): AIServiceError {
  // If it's already our error type, return as-is
  if (error instanceof AIServiceError) {
    return error;
  }

  const errorMessage = error?.message || 'Unknown error';
  const errorCode = error?.code || error?.status;

  // Rate limiting errors
  if (
    errorMessage.includes('rate limit') ||
    errorMessage.includes('429') ||
    errorCode === 429
  ) {
    return new RateLimitError(errorMessage, { originalError: error });
  }

  // Quota/billing errors
  if (
    errorMessage.includes('quota') ||
    errorMessage.includes('billing') ||
    errorMessage.includes('insufficient') ||
    errorCode === 402
  ) {
    return new QuotaExceededError(errorMessage, { originalError: error });
  }

  // Network errors
  if (
    errorMessage.includes('network') ||
    errorMessage.includes('connection') ||
    errorMessage.includes('ECONNREFUSED') ||
    errorMessage.includes('ENOTFOUND') ||
    errorCode === 'NETWORK_ERROR'
  ) {
    return new NetworkError(errorMessage, { originalError: error });
  }

  // Timeout errors
  if (
    errorMessage.includes('timeout') ||
    errorMessage.includes('ETIMEDOUT') ||
    errorCode === 'TIMEOUT'
  ) {
    return new TimeoutError(errorMessage, { originalError: error });
  }

  // Validation errors
  if (
    errorMessage.includes('validation') ||
    errorMessage.includes('schema') ||
    errorMessage.includes('invalid format')
  ) {
    return new ValidationError(errorMessage, { originalError: error });
  }

  // Model/API errors
  if (
    errorCode >= 500 ||
    errorMessage.includes('internal server') ||
    errorMessage.includes('service unavailable')
  ) {
    return new ModelError(errorMessage, { originalError: error });
  }

  // Input validation errors
  if (
    errorCode >= 400 && errorCode < 500 ||
    errorMessage.includes('bad request') ||
    errorMessage.includes('invalid input')
  ) {
    return new InvalidInputError(errorMessage, { originalError: error });
  }

  // Default to generic AI service error
  return new AIServiceError(
    errorMessage,
    'UNKNOWN_ERROR',
    { originalError: error },
    'An unexpected error occurred. Please try again.'
  );
}

// Retry logic with exponential backoff
export async function withRetry<T>(
  operation: () => Promise<T>,
  options: {
    maxRetries?: number;
    baseDelay?: number;
    maxDelay?: number;
    shouldRetry?: (error: AIServiceError) => boolean;
  } = {}
): Promise<T> {
  const {
    maxRetries = AI_CONFIG.maxRetries,
    baseDelay = AI_CONFIG.retryDelay,
    maxDelay = 30000, // 30 seconds max
    shouldRetry = (error) => error.isRetryable()
  } = options;

  let lastError: AIServiceError;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = classifyError(error);

      // Don't retry on the last attempt or if error is not retryable
      if (attempt === maxRetries || !shouldRetry(lastError)) {
        throw lastError;
      }

      // Calculate delay with exponential backoff
      const delay = Math.min(baseDelay * Math.pow(2, attempt - 1), maxDelay);
      
      // Add some jitter to prevent thundering herd
      const jitter = Math.random() * 0.1 * delay;
      const finalDelay = delay + jitter;

      console.warn(
        `AI service attempt ${attempt} failed, retrying in ${Math.round(finalDelay)}ms:`,
        lastError.message
      );

      await new Promise(resolve => setTimeout(resolve, finalDelay));
    }
  }

  throw lastError!;
}

// Timeout wrapper
export async function withTimeout<T>(
  operation: () => Promise<T>,
  timeoutMs: number = AI_CONFIG.requestTimeout
): Promise<T> {
  return Promise.race([
    operation(),
    new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new TimeoutError(
          `Operation timed out after ${timeoutMs}ms`,
          { timeoutMs }
        ));
      }, timeoutMs);
    })
  ]);
}

// Error logging utility
export function logError(error: AIServiceError, context?: any): void {
  const logData = {
    error: {
      name: error.name,
      message: error.message,
      code: error.code,
      details: error.details
    },
    context,
    timestamp: new Date().toISOString()
  };

  // In production, you might want to send this to a logging service
  console.error('AI Service Error:', JSON.stringify(logData, null, 2));
}

// User-friendly error messages
export function getErrorMessage(error: any): string {
  if (error instanceof AIServiceError) {
    return error.getUserMessage();
  }

  // Fallback for unknown errors
  return 'An unexpected error occurred. Please try again.';
}

// Error recovery suggestions
export function getErrorRecoveryActions(error: AIServiceError): string[] {
  const actions: string[] = [];

  switch (error.code) {
    case 'RATE_LIMIT':
      actions.push('Wait a few minutes before trying again');
      actions.push('Try using a simpler topic or fewer content types');
      break;

    case 'QUOTA_EXCEEDED':
      actions.push('Check your API account status');
      actions.push('Contact support if you believe this is an error');
      break;

    case 'INVALID_INPUT':
      actions.push('Check that your topic is clear and specific');
      actions.push('Ensure you have selected at least one content type');
      actions.push('Try shortening your focus areas description');
      break;

    case 'MODEL_ERROR':
      actions.push('Try again in a few minutes');
      actions.push('Try using a different AI model');
      actions.push('Simplify your request');
      break;

    case 'NETWORK_ERROR':
      actions.push('Check your internet connection');
      actions.push('Try again in a few moments');
      break;

    case 'TIMEOUT_ERROR':
      actions.push('Try with a simpler topic');
      actions.push('Reduce the number of content types');
      actions.push('Try again later when the service is less busy');
      break;

    case 'VALIDATION_ERROR':
      actions.push('Try again with a different topic');
      actions.push('Contact support if the issue persists');
      break;

    default:
      actions.push('Try again in a few minutes');
      actions.push('Contact support if the problem continues');
  }

  return actions;
}
