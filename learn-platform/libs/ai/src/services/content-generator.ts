/**
 * AI Content Generation Service
 * Main service for generating structured learning content using OpenRouter and Vercel AI SDK
 */

import { generateObject } from 'ai';
import {
  createAIProvider,
  selectOptimalModel,
  getModelConfig,
  LEARNING_LEVEL_GENERATION_OPTIONS,
  validateAIConfig,
  AI_CONFIG
} from '../config/ai-config';
import {
  learningContentInputSchema,
  generatedLearningContentSchema,
  validateStepDataWithDetails,
  transformAIGeneratedData,
  type LearningContentInput,
  type GeneratedLearningContent
} from '../schemas/content-generation';
import {
  LEARNING_CONTENT_SYSTEM_PROMPT,
  createUserPrompt,
  generateContentTypeDistribution,
  suggestIconsForTopic
} from '../prompts/learning-content';

// Import error handling utilities
import {
  AIServiceError,
  RateLimitError,
  InvalidInputError,
  ModelError,
  ConfigurationError,
  ValidationError,
  classifyError,
  withRetry,
  withTimeout,
  logError
} from './error-handler';

// Content generation options
export interface ContentGenerationOptions {
  model?: string;
  temperature?: number;
  maxTokens?: number;
  maxSteps?: number;
  includeExamples?: boolean;
  preferCostEffective?: boolean;
  retryOnFailure?: boolean;
  env?: Record<string, any>; // Cloudflare Workers environment variables
}

/**
 * Validate AI configuration with optional environment variables
 */
function validateAIConfigWithEnv(env?: Record<string, any>): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Check API key from env or fallback to static config
  const apiKey = env?.['OPENROUTER_API_KEY'] || AI_CONFIG.apiKey;
  if (!apiKey) {
    errors.push('OPENROUTER_API_KEY is required');
  }

  // Check app name from env or fallback to static config
  const appName = env?.['OPENROUTER_APP_NAME'] || AI_CONFIG.appName;
  if (!appName) {
    errors.push('OPENROUTER_APP_NAME is required');
  }

  // Check site URL from env or fallback to static config
  const siteUrl = env?.['OPENROUTER_SITE_URL'] || AI_CONFIG.siteUrl;
  if (!siteUrl) {
    errors.push('OPENROUTER_SITE_URL is required');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Main function to generate learning content
 */
export async function generateLearningContent(
  input: LearningContentInput,
  options: ContentGenerationOptions = {}
): Promise<GeneratedLearningContent> {
  // Validate configuration with optional environment variables
  const configValidation = validateAIConfigWithEnv(options.env);
  if (!configValidation.isValid) {
    throw new ConfigurationError(
      `Configuration error: ${configValidation.errors.join(', ')}`,
      configValidation.errors
    );
  }

  // Validate input
  try {
    learningContentInputSchema.parse(input);
  } catch (error) {
    throw new InvalidInputError(
      'Invalid input provided',
      { originalError: error, input }
    );
  }

  // Select optimal model
  const selectedModel = options.model || selectOptimalModel(
    input.preferredContentTypes,
    input.learningLevel,
    options.preferCostEffective
  );

  // Get model configuration
  const modelConfig = getModelConfig(selectedModel as any);
  const levelOptions = LEARNING_LEVEL_GENERATION_OPTIONS[input.learningLevel];

  // Merge options with defaults
  const finalOptions = {
    temperature: options.temperature ?? levelOptions.temperature,
    maxTokens: options.maxTokens ?? modelConfig.maxTokens,
    maxSteps: options.maxSteps ?? levelOptions.maxSteps,
    includeExamples: options.includeExamples ?? levelOptions.includeExamples,
    retryOnFailure: options.retryOnFailure ?? true,
  };

  // Create AI provider with environment variables
  const openrouter = createAIProvider(options.env);
  const model = openrouter(selectedModel);

  // Generate content type distribution
  const contentTypeDistribution = generateContentTypeDistribution(
    input.preferredContentTypes,
    finalOptions.maxSteps
  );

  // Create prompts
  const systemPrompt = LEARNING_CONTENT_SYSTEM_PROMPT;
  const userPrompt = createUserPrompt(
    input.topic,
    input.learningLevel,
    input.preferredContentTypes,
    input.focusAreas
  );

  try {
    // Generate content with enhanced error handling and retry logic
    const generateContent = async () => {
      const result = await withTimeout(async () => {
        return await generateObject({
          model,
          schema: generatedLearningContentSchema,
          system: systemPrompt,
          prompt: userPrompt,
          temperature: finalOptions.temperature,
          maxTokens: finalOptions.maxTokens,
        });
      }, AI_CONFIG.requestTimeout);

      // Validate the generated content
      return await validateAndEnhanceContent(
        result.object,
        input,
        selectedModel
      );
    };

    // Use retry logic if enabled
    if (finalOptions.retryOnFailure) {
      return await withRetry(generateContent, {
        maxRetries: AI_CONFIG.maxRetries,
        baseDelay: AI_CONFIG.retryDelay,
      });
    } else {
      return await generateContent();
    }

  } catch (error) {
    // Classify and log the error
    const classifiedError = classifyError(error);
    logError(classifiedError, {
      input,
      model: selectedModel,
      options: finalOptions
    });

    throw classifiedError;
  }
}

/**
 * Validate and enhance generated content
 */
async function validateAndEnhanceContent(
  content: any,
  originalInput: LearningContentInput,
  model: string
): Promise<GeneratedLearningContent> {
  try {
    // Parse and validate the content structure
    const validatedContent = generatedLearningContentSchema.parse(content);

    // Transform and validate each step's data structure
    for (let i = 0; i < validatedContent.steps.length; i++) {
      const step = validatedContent.steps[i];

      // First, try to transform AI-generated data to expected format
      const transformedData = transformAIGeneratedData(step.type, step.data);

      // Validate the transformed data
      const validation = validateStepDataWithDetails(step.type, transformedData);

      if (!validation.isValid) {
        console.error(`Validation failed for step ${i + 1} (${step.type}):`, {
          error: validation.error,
          expectedFormat: validation.expectedFormat,
          originalData: step.data,
          transformedData: transformedData,
          stepTitle: step.title
        });

        throw new Error(
          `Invalid data structure for step ${i + 1} "${step.title}" (type: ${step.type}). ` +
          `Expected: ${validation.expectedFormat}. ` +
          `Error: ${validation.error}. ` +
          `Original data: ${JSON.stringify(step.data)}. ` +
          `Transformed data: ${JSON.stringify(transformedData)}`
        );
      }

      // Update the step with the transformed data
      validatedContent.steps[i].data = transformedData;
    }

    // Enhance metadata
    const enhancedContent: GeneratedLearningContent = {
      ...validatedContent,
      metadata: {
        ...validatedContent.metadata,
        aiModel: model,
        generatedAt: new Date().toISOString(),
        contentTypes: validatedContent.steps.map(step => step.type),
        learningLevel: originalInput.learningLevel,
        topic: originalInput.topic,
      }
    };

    // Ensure reasonable reading time
    if (enhancedContent.estimatedReadingTime < 1) {
      enhancedContent.estimatedReadingTime = Math.max(
        1,
        Math.ceil(enhancedContent.steps.length * 1.5)
      );
    }

    return enhancedContent;

  } catch (error) {
    throw new ValidationError(
      'Generated content validation failed',
      { originalError: error, content }
    );
  }
}

/**
 * Get available models for content generation
 */
export function getAvailableModels(): Array<{
  id: string;
  name: string;
  description: string;
  costTier: string;
  strengths: string[];
}> {
  return [
    {
      id: 'anthropic/claude-3-sonnet',
      name: 'Claude 3 Sonnet',
      description: 'Best for reasoning and explanations',
      costTier: 'medium',
      strengths: ['reasoning', 'explanations', 'structured-output']
    },
    {
      id: 'anthropic/claude-3-opus',
      name: 'Claude 3 Opus',
      description: 'Most capable for complex topics',
      costTier: 'high',
      strengths: ['complex-reasoning', 'detailed-explanations', 'accuracy']
    },
    {
      id: 'openai/gpt-4o',
      name: 'GPT-4o',
      description: 'Reliable structured outputs',
      costTier: 'medium',
      strengths: ['structured-output', 'consistency', 'speed']
    },
    {
      id: 'meta-llama/llama-3.1-70b-instruct',
      name: 'Llama 3.1 70B',
      description: 'Cost-effective option',
      costTier: 'low',
      strengths: ['cost-effective', 'open-source', 'general-purpose']
    }
  ];
}

/**
 * Estimate content generation cost (placeholder - would need actual pricing data)
 */
export function estimateGenerationCost(
  input: LearningContentInput,
  options: ContentGenerationOptions = {}
): { estimatedCost: number; currency: string; model: string } {
  const model = options.model || selectOptimalModel(
    input.preferredContentTypes,
    input.learningLevel,
    options.preferCostEffective
  );

  // Placeholder cost estimation - would need real pricing data
  const baseCosts = {
    'anthropic/claude-3-opus': 0.15,
    'anthropic/claude-3-sonnet': 0.08,
    'openai/gpt-4o': 0.10,
    'meta-llama/llama-3.1-70b-instruct': 0.03,
  };

  const baseCost = baseCosts[model as keyof typeof baseCosts] || 0.08;
  const stepMultiplier = (options.maxSteps || 6) / 6;

  return {
    estimatedCost: Number((baseCost * stepMultiplier).toFixed(3)),
    currency: 'USD',
    model
  };
}
