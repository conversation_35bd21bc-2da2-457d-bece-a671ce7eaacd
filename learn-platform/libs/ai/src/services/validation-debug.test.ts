/**
 * Debug test to investigate validation issues with AI-generated content
 */

import { describe, it, expect } from '@jest/globals';
import {
  validateStepDataWithDetails,
  transformAIGeneratedData,
  paragraphDataSchema,
  type StepConfig
} from '../schemas/content-generation';

describe('AI Content Validation Debug', () => {
  describe('Paragraph validation', () => {
    it('should accept valid string paragraph', () => {
      const data = "This is a valid paragraph.";
      const result = validateStepDataWithDetails('paragraph', data);

      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should accept valid array of strings paragraph', () => {
      const data = ["First paragraph.", "Second paragraph."];
      const result = validateStepDataWithDetails('paragraph', data);

      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should reject object with content property (common AI mistake)', () => {
      const data = { content: "This is paragraph content" };
      const result = validateStepDataWithDetails('paragraph', data);

      expect(result.isValid).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.actualData).toEqual(data);
      expect(result.expectedFormat).toContain('string | string[]');

      console.log('Object with content property validation result:', result);
    });

    it('should reject object with text property (common AI mistake)', () => {
      const data = { text: "This is paragraph text" };
      const result = validateStepDataWithDetails('paragraph', data);

      expect(result.isValid).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.actualData).toEqual(data);

      console.log('Object with text property validation result:', result);
    });

    it('should reject object with paragraphs array property (common AI mistake)', () => {
      const data = { paragraphs: ["First paragraph", "Second paragraph"] };
      const result = validateStepDataWithDetails('paragraph', data);

      expect(result.isValid).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.actualData).toEqual(data);

      console.log('Object with paragraphs property validation result:', result);
    });

    it('should reject number', () => {
      const data = 123;
      const result = validateStepDataWithDetails('paragraph', data);

      expect(result.isValid).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('should reject null', () => {
      const data = null;
      const result = validateStepDataWithDetails('paragraph', data);

      expect(result.isValid).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('should reject undefined', () => {
      const data = undefined;
      const result = validateStepDataWithDetails('paragraph', data);

      expect(result.isValid).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('should reject array with non-string elements', () => {
      const data = ["Valid string", 123, "Another valid string"];
      const result = validateStepDataWithDetails('paragraph', data);

      expect(result.isValid).toBe(false);
      expect(result.error).toBeDefined();
    });
  });

  describe('Common AI-generated formats', () => {
    it('should test various formats AI might generate', () => {
      const commonAIFormats = [
        // Format 1: Object with content
        { content: "Paragraph content here" },

        // Format 2: Object with text
        { text: "Paragraph text here" },

        // Format 3: Object with paragraph
        { paragraph: "Paragraph content here" },

        // Format 4: Object with paragraphs array
        { paragraphs: ["Para 1", "Para 2"] },

        // Format 5: Object with body
        { body: "Paragraph body here" },

        // Format 6: Object with description
        { description: "Paragraph description here" },

        // Format 7: Nested object
        { data: { content: "Nested content" } },

        // Format 8: Array of objects
        [{ text: "Para 1" }, { text: "Para 2" }],
      ];

      console.log('\n=== Testing Common AI-Generated Formats ===');

      commonAIFormats.forEach((format, index) => {
        const result = validateStepDataWithDetails('paragraph', format);
        console.log(`\nFormat ${index + 1}:`, JSON.stringify(format));
        console.log('Valid:', result.isValid);
        if (!result.isValid) {
          console.log('Error:', result.error);
          console.log('Expected:', result.expectedFormat);
        }
      });
    });
  });

  describe('Schema direct testing', () => {
    it('should test paragraph schema directly', () => {
      const testCases = [
        "Simple string",
        ["Array", "of", "strings"],
        { content: "Object with content" },
        123,
        null,
        undefined
      ];

      console.log('\n=== Direct Schema Testing ===');

      testCases.forEach((testCase, index) => {
        try {
          const result = paragraphDataSchema.parse(testCase);
          console.log(`\nTest ${index + 1}: VALID`);
          console.log('Input:', JSON.stringify(testCase));
          console.log('Parsed:', JSON.stringify(result));
        } catch (error: any) {
          console.log(`\nTest ${index + 1}: INVALID`);
          console.log('Input:', JSON.stringify(testCase));
          console.log('Error:', error.message);
        }
      });
    });
  });

  describe('Data transformation', () => {
    it('should transform common AI-generated paragraph formats', () => {
      const testCases = [
        // Format 1: Object with content
        { input: { content: "Paragraph content here" }, expected: "Paragraph content here" },

        // Format 2: Object with text
        { input: { text: "Paragraph text here" }, expected: "Paragraph text here" },

        // Format 3: Object with paragraph
        { input: { paragraph: "Paragraph content here" }, expected: "Paragraph content here" },

        // Format 4: Object with paragraphs array
        { input: { paragraphs: ["Para 1", "Para 2"] }, expected: ["Para 1", "Para 2"] },

        // Format 5: Object with body
        { input: { body: "Paragraph body here" }, expected: "Paragraph body here" },

        // Format 6: Object with description
        { input: { description: "Paragraph description here" }, expected: "Paragraph description here" },

        // Format 7: Nested object
        { input: { data: { content: "Nested content" } }, expected: "Nested content" },

        // Format 8: Already correct string
        { input: "Already correct", expected: "Already correct" },

        // Format 9: Already correct array
        { input: ["Para 1", "Para 2"], expected: ["Para 1", "Para 2"] },
      ];

      console.log('\n=== Testing Data Transformation ===');

      testCases.forEach((testCase, index) => {
        const transformed = transformAIGeneratedData('paragraph', testCase.input);
        console.log(`\nTest ${index + 1}:`);
        console.log('Input:', JSON.stringify(testCase.input));
        console.log('Expected:', JSON.stringify(testCase.expected));
        console.log('Transformed:', JSON.stringify(transformed));

        expect(transformed).toEqual(testCase.expected);

        // Test that transformed data validates correctly
        const validation = validateStepDataWithDetails('paragraph', transformed);
        expect(validation.isValid).toBe(true);
      });
    });

    it('should handle transformation + validation workflow', () => {
      const aiGeneratedData = { content: "This is AI-generated paragraph content" };

      // Step 1: Transform
      const transformed = transformAIGeneratedData('paragraph', aiGeneratedData);
      expect(transformed).toBe("This is AI-generated paragraph content");

      // Step 2: Validate
      const validation = validateStepDataWithDetails('paragraph', transformed);
      expect(validation.isValid).toBe(true);
      expect(validation.error).toBeUndefined();
    });
  });
});
