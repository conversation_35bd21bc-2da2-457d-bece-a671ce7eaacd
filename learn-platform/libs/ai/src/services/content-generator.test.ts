/**
 * Tests for AI content generation service
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { generateLearningContent, getAvailableModels, estimateGenerationCost } from './content-generator';
import { AIServiceError, ConfigurationError, InvalidInputError } from './error-handler';
import type { LearningContentInput } from '../schemas/content-generation';

// Mock the AI SDK
jest.mock('ai', () => ({
  generateObject: jest.fn(),
}));

// Mock the OpenRouter provider
jest.mock('@openrouter/ai-sdk-provider', () => ({
  createOpenRouter: jest.fn(() => jest.fn()),
}));

describe('Content Generator', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Set up environment variables for tests
    process.env['OPENROUTER_API_KEY'] = 'test-api-key';
    process.env['OPENROUTER_APP_NAME'] = 'Test Learning Platform';
    process.env['OPENROUTER_SITE_URL'] = 'http://localhost:3000';
  });

  describe('generateLearningContent', () => {
    const validInput: LearningContentInput = {
      topic: 'How does machine learning work?',
      learningLevel: 'beginner',
      preferredContentTypes: ['paragraph', 'bulletList'],
      focusAreas: 'practical examples'
    };

    it('should throw ConfigurationError when API key is missing', async () => {
      delete process.env['OPENROUTER_API_KEY'];

      await expect(generateLearningContent(validInput))
        .rejects
        .toThrow('Configuration error');
    });

    it('should throw InvalidInputError for invalid input', async () => {
      const invalidInput = {
        topic: '', // Empty topic
        learningLevel: 'beginner',
        preferredContentTypes: [],
      } as any;

      await expect(generateLearningContent(invalidInput))
        .rejects
        .toThrow(InvalidInputError);
    });

    it('should validate input schema', async () => {
      const invalidInput = {
        topic: 'Valid topic',
        learningLevel: 'invalid-level', // Invalid learning level
        preferredContentTypes: ['paragraph'],
      } as any;

      await expect(generateLearningContent(invalidInput))
        .rejects
        .toThrow(InvalidInputError);
    });

    it('should handle empty preferred content types', async () => {
      const inputWithEmptyTypes = {
        ...validInput,
        preferredContentTypes: []
      } as any;

      await expect(generateLearningContent(inputWithEmptyTypes))
        .rejects
        .toThrow(InvalidInputError);
    });
  });

  describe('getAvailableModels', () => {
    it('should return list of available models', () => {
      const models = getAvailableModels();

      expect(Array.isArray(models)).toBe(true);
      expect(models.length).toBeGreaterThan(0);

      // Check structure of first model
      const firstModel = models[0];
      expect(firstModel).toHaveProperty('id');
      expect(firstModel).toHaveProperty('name');
      expect(firstModel).toHaveProperty('description');
      expect(firstModel).toHaveProperty('costTier');
      expect(firstModel).toHaveProperty('strengths');
      expect(Array.isArray(firstModel.strengths)).toBe(true);
    });

    it('should include expected models', () => {
      const models = getAvailableModels();
      const modelIds = models.map(m => m.id);

      expect(modelIds).toContain('anthropic/claude-3-sonnet');
      expect(modelIds).toContain('openai/gpt-4o');
      expect(modelIds).toContain('meta-llama/llama-3.1-70b-instruct');
    });
  });

  describe('estimateGenerationCost', () => {
    const validInput: LearningContentInput = {
      topic: 'Test topic',
      learningLevel: 'beginner',
      preferredContentTypes: ['paragraph'],
    };

    it('should return cost estimation', () => {
      const estimate = estimateGenerationCost(validInput);

      expect(estimate).toHaveProperty('estimatedCost');
      expect(estimate).toHaveProperty('currency');
      expect(estimate).toHaveProperty('model');

      expect(typeof estimate.estimatedCost).toBe('number');
      expect(estimate.estimatedCost).toBeGreaterThan(0);
      expect(estimate.currency).toBe('USD');
    });

    it('should adjust cost based on options', () => {
      const baseEstimate = estimateGenerationCost(validInput);
      const extendedEstimate = estimateGenerationCost(validInput, { maxSteps: 12 });

      expect(extendedEstimate.estimatedCost).toBeGreaterThan(baseEstimate.estimatedCost);
    });

    it('should use cost-effective model when requested', () => {
      const estimate = estimateGenerationCost(validInput, { preferCostEffective: true });

      expect(estimate.model).toBe('meta-llama/llama-3.1-70b-instruct');
    });
  });

  describe('Input validation edge cases', () => {
    it('should handle very long topics', async () => {
      const longTopic = 'a'.repeat(300); // Exceeds 200 char limit
      const input = {
        topic: longTopic,
        learningLevel: 'beginner',
        preferredContentTypes: ['paragraph'],
      } as any;

      await expect(generateLearningContent(input))
        .rejects
        .toThrow(InvalidInputError);
    });

    it('should handle very long focus areas', async () => {
      const longFocusAreas = 'a'.repeat(600); // Exceeds 500 char limit
      const input = {
        topic: 'Valid topic',
        learningLevel: 'beginner',
        preferredContentTypes: ['paragraph'],
        focusAreas: longFocusAreas,
      } as any;

      await expect(generateLearningContent(input))
        .rejects
        .toThrow(InvalidInputError);
    });

    it('should handle invalid content types', async () => {
      const input = {
        topic: 'Valid topic',
        learningLevel: 'beginner',
        preferredContentTypes: ['invalidType'],
      } as any;

      await expect(generateLearningContent(input))
        .rejects
        .toThrow(InvalidInputError);
    });
  });
});
