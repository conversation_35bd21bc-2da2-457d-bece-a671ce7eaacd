/**
 * Content Validation Service
 * Ensures quiz questions are generated exclusively from source content
 * with comprehensive validation to prevent hallucination
 */

import { generateObject } from 'ai';
import { createAIProvider } from '../config/ai-config';
import {
  contentValidationSchema,
  quizQuestionSchema,
  type ContentValidation,
  type QuizQuestion,
  type QuizType
} from '../schemas/quiz-generation';
import { createContentValidationPrompt } from '../prompts/quiz-generation';

// Validation result interface
export interface ValidationResult {
  isValid: boolean;
  confidence: number;
  issues: string[];
  suggestions: string[];
  validationDetails: ContentValidation;
}

// Validation configuration
export interface ValidationConfig {
  model?: string;
  strictMode?: boolean;
  minConfidence?: number;
  checkExternalKnowledge?: boolean;
  validateAnswers?: boolean;
  env?: Record<string, any>; // Cloudflare Workers environment variables
}

/**
 * Text similarity checker using simple string matching
 */
function calculateTextSimilarity(source: string, target: string): number {
  const sourceWords = source.toLowerCase().split(/\s+/);
  const targetWords = target.toLowerCase().split(/\s+/);

  const sourceSet = new Set(sourceWords);
  const targetSet = new Set(targetWords);

  const intersection = new Set([...sourceSet].filter(word => targetSet.has(word)));
  const union = new Set([...sourceSet, ...targetSet]);

  return intersection.size / union.size;
}

/**
 * Extract key terms and concepts from text
 */
function extractKeyTerms(text: string): string[] {
  // Simple extraction - in production, could use NLP libraries
  const words = text.toLowerCase()
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(word => word.length > 3);

  // Remove common words
  const stopWords = new Set(['this', 'that', 'with', 'have', 'will', 'from', 'they', 'been', 'were', 'said', 'each', 'which', 'their', 'time', 'would', 'there', 'could', 'other']);

  return [...new Set(words.filter(word => !stopWords.has(word)))];
}

/**
 * Check if question content exists in source material
 */
function validateContentMatch(sourceContent: string, question: QuizQuestion): {
  isValid: boolean;
  similarity: number;
  missingTerms: string[];
} {
  const questionText = extractQuestionText(question);
  const similarity = calculateTextSimilarity(sourceContent, questionText);

  const questionTerms = extractKeyTerms(questionText);
  const sourceTerms = extractKeyTerms(sourceContent);
  const sourceTermsSet = new Set(sourceTerms);

  const missingTerms = questionTerms.filter(term => !sourceTermsSet.has(term));

  return {
    isValid: similarity > 0.3 && missingTerms.length < questionTerms.length * 0.5,
    similarity,
    missingTerms
  };
}

/**
 * Extract text content from different question types
 */
function extractQuestionText(question: QuizQuestion): string {
  switch (question.type) {
    case 'flashcard':
      return `${question.front} ${question.back}`;

    case 'multipleChoice':
      return `${question.question} ${question.options.join(' ')}`;

    case 'trueFalse':
      return question.statement;

    case 'fillInBlank':
      return `${question.text} ${question.blanks.map(b => b.correctAnswer).join(' ')}`;

    case 'matching':
      return question.pairs.map(p => `${p.left} ${p.right}`).join(' ');

    case 'freeText':
      return `${question.question} ${question.sampleAnswer}`;

    case 'ordering':
      return question.items.join(' ');

    default:
      return '';
  }
}

/**
 * Validate question answers against source content
 */
function validateAnswers(sourceContent: string, question: QuizQuestion): {
  isValid: boolean;
  issues: string[];
} {
  const issues: string[] = [];

  switch (question.type) {
    case 'multipleChoice':
      const correctOption = question.options[question.correctAnswerIndex];
      if (!sourceContent.toLowerCase().includes(correctOption.toLowerCase())) {
        const similarity = calculateTextSimilarity(sourceContent, correctOption);
        if (similarity < 0.2) {
          issues.push(`Correct answer "${correctOption}" not found in source content`);
        }
      }
      break;

    case 'trueFalse':
      // Check if the statement can be verified from source
      const statementSimilarity = calculateTextSimilarity(sourceContent, question.statement);
      if (statementSimilarity < 0.3) {
        issues.push('True/false statement cannot be verified from source content');
      }
      break;

    case 'fillInBlank':
      for (const blank of question.blanks) {
        if (!sourceContent.toLowerCase().includes(blank.correctAnswer.toLowerCase())) {
          issues.push(`Fill-in-blank answer "${blank.correctAnswer}" not found in source`);
        }
      }
      break;

    case 'matching':
      for (const pair of question.pairs) {
        const leftFound = sourceContent.toLowerCase().includes(pair.left.toLowerCase());
        const rightFound = sourceContent.toLowerCase().includes(pair.right.toLowerCase());
        if (!leftFound || !rightFound) {
          issues.push(`Matching pair "${pair.left}" - "${pair.right}" not fully supported by source`);
        }
      }
      break;
  }

  return {
    isValid: issues.length === 0,
    issues
  };
}

/**
 * Check for potential hallucination indicators
 */
function checkForHallucination(sourceContent: string, question: QuizQuestion): {
  hasHallucination: boolean;
  indicators: string[];
} {
  const indicators: string[] = [];
  const questionText = extractQuestionText(question);

  // Check for specific dates, numbers, or names not in source
  const datePattern = /\b\d{4}\b|\b\d{1,2}\/\d{1,2}\/\d{2,4}\b/g;
  const questionDates = questionText.match(datePattern) || [];
  const sourceDates = sourceContent.match(datePattern) || [];

  for (const date of questionDates) {
    if (!sourceDates.includes(date)) {
      indicators.push(`Specific date "${date}" not found in source content`);
    }
  }

  // Check for proper nouns (capitalized words) not in source
  const properNounPattern = /\b[A-Z][a-z]+\b/g;
  const questionProperNouns = questionText.match(properNounPattern) || [];
  const sourceProperNouns = sourceContent.match(properNounPattern) || [];

  for (const noun of questionProperNouns) {
    if (!sourceProperNouns.includes(noun) && noun.length > 3) {
      indicators.push(`Proper noun "${noun}" not found in source content`);
    }
  }

  // Check for specific technical terms or jargon
  const questionTerms = extractKeyTerms(questionText);
  const sourceTerms = extractKeyTerms(sourceContent);
  const sourceTermsSet = new Set(sourceTerms);

  const uniqueTerms = questionTerms.filter(term =>
    !sourceTermsSet.has(term) && term.length > 5
  );

  if (uniqueTerms.length > 2) {
    indicators.push(`Multiple technical terms not found in source: ${uniqueTerms.join(', ')}`);
  }

  return {
    hasHallucination: indicators.length > 0,
    indicators
  };
}

/**
 * Perform comprehensive content validation
 */
export async function validateQuizContent(
  sourceContent: string,
  question: QuizQuestion,
  config: ValidationConfig = {}
): Promise<ValidationResult> {
  const {
    model = 'openai/gpt-4o',
    strictMode = true,
    minConfidence = 0.7,
    checkExternalKnowledge = true,
    validateAnswers = true,
    env
  } = config;

  const issues: string[] = [];
  const suggestions: string[] = [];

  // 1. Basic content matching
  const contentMatch = validateContentMatch(sourceContent, question);
  if (!contentMatch.isValid) {
    issues.push(`Low content similarity (${(contentMatch.similarity * 100).toFixed(1)}%)`);
    if (contentMatch.missingTerms.length > 0) {
      issues.push(`Terms not found in source: ${contentMatch.missingTerms.join(', ')}`);
    }
  }

  // 2. Answer validation
  if (validateAnswers) {
    const answerValidation = validateAnswers(sourceContent, question);
    if (!answerValidation.isValid) {
      issues.push(...answerValidation.issues);
    }
  }

  // 3. Hallucination detection
  if (checkExternalKnowledge) {
    const hallucinationCheck = checkForHallucination(sourceContent, question);
    if (hallucinationCheck.hasHallucination) {
      issues.push(...hallucinationCheck.indicators);
    }
  }

  // 4. AI-powered validation
  let aiValidation: ContentValidation;
  try {
    const openrouter = createAIProvider(env);
    const aiModel = openrouter(model);

    const validationPrompt = createContentValidationPrompt(sourceContent, question);

    const result = await generateObject({
      model: aiModel,
      schema: contentValidationSchema,
      system: 'You are a content validation expert. Analyze quiz questions for hallucination and source adherence.',
      prompt: validationPrompt,
      temperature: 0.1,
    });

    aiValidation = result.object;
  } catch (error) {
    console.error('AI validation failed:', error);
    aiValidation = {
      sourceContent,
      generatedQuestion: question,
      validationChecks: {
        contentMatch: contentMatch.isValid,
        noHallucination: !checkExternalKnowledge || issues.length === 0,
        appropriateDifficulty: true, // Default assumption
        clearlyAnswerable: true, // Default assumption
      },
      confidence: Math.max(0.1, contentMatch.similarity),
    };
  }

  // 5. Combine validation results
  const overallConfidence = Math.min(
    aiValidation.confidence,
    contentMatch.similarity,
    issues.length === 0 ? 1.0 : 0.5
  );

  const isValid = strictMode
    ? overallConfidence >= minConfidence &&
      aiValidation.validationChecks.contentMatch &&
      aiValidation.validationChecks.noHallucination &&
      issues.length === 0
    : overallConfidence >= minConfidence * 0.8;

  // 6. Generate suggestions for improvement
  if (!isValid) {
    suggestions.push('Ensure all question content is directly derived from the source material');

    if (contentMatch.similarity < 0.5) {
      suggestions.push('Increase alignment between question content and source text');
    }

    if (contentMatch.missingTerms.length > 0) {
      suggestions.push('Use terminology that appears in the source content');
    }

    if (!aiValidation.validationChecks.clearlyAnswerable) {
      suggestions.push('Make sure the question can be answered using only the provided content');
    }
  }

  return {
    isValid,
    confidence: overallConfidence,
    issues,
    suggestions,
    validationDetails: aiValidation,
  };
}

/**
 * Batch validate multiple quiz questions
 */
export async function validateQuizBatch(
  sourceContent: string,
  questions: QuizQuestion[],
  config: ValidationConfig = {}
): Promise<{
  validQuestions: QuizQuestion[];
  invalidQuestions: Array<{ question: QuizQuestion; validation: ValidationResult }>;
  overallStats: {
    totalQuestions: number;
    validQuestions: number;
    averageConfidence: number;
    commonIssues: string[];
  };
}> {
  const validQuestions: QuizQuestion[] = [];
  const invalidQuestions: Array<{ question: QuizQuestion; validation: ValidationResult }> = [];
  const allIssues: string[] = [];
  let totalConfidence = 0;

  for (const question of questions) {
    const validation = await validateQuizContent(sourceContent, question, config);
    totalConfidence += validation.confidence;

    if (validation.isValid) {
      validQuestions.push(question);
    } else {
      invalidQuestions.push({ question, validation });
      allIssues.push(...validation.issues);
    }
  }

  // Calculate common issues
  const issueCounts = allIssues.reduce((counts, issue) => {
    counts[issue] = (counts[issue] || 0) + 1;
    return counts;
  }, {} as Record<string, number>);

  const commonIssues = Object.entries(issueCounts)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 5)
    .map(([issue]) => issue);

  return {
    validQuestions,
    invalidQuestions,
    overallStats: {
      totalQuestions: questions.length,
      validQuestions: validQuestions.length,
      averageConfidence: totalConfidence / questions.length,
      commonIssues,
    },
  };
}
