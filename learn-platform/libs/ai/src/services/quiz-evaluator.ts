/**
 * Quiz Evaluation Service
 * Evaluates quiz answers and calculates scores
 */

import { generateObject } from 'ai';
import { createAIProvider } from '../config/ai-config';
import { z } from 'zod';
import type { QuizQuestion, QuizAnswer } from '../schemas/quiz-generation';

// Evaluation result schemas
const questionEvaluationSchema = z.object({
  questionId: z.string(),
  isCorrect: z.boolean(),
  pointsEarned: z.number(),
  feedback: z.string().optional(),
  explanation: z.string().optional(),
});

const quizEvaluationSchema = z.object({
  score: z.object({
    totalPoints: z.number(),
    earnedPoints: z.number(),
    percentage: z.number().min(0).max(100),
    correctAnswers: z.number(),
    totalQuestions: z.number(),
  }),
  questionResults: z.array(questionEvaluationSchema),
});

export type QuizEvaluation = z.infer<typeof quizEvaluationSchema>;
export type QuestionEvaluation = z.infer<typeof questionEvaluationSchema>;

/**
 * Evaluate a single question answer
 */
function evaluateObjectiveQuestion(question: QuizQuestion, userAnswer: any): QuestionEvaluation {
  let isCorrect = false;
  let feedback = '';

  switch (question.type) {
    case 'multipleChoice':
      const correctIndex = question.correctAnswerIndex;
      const userIndex = typeof userAnswer === 'number' ? userAnswer : -1;
      isCorrect = userIndex === correctIndex;

      if (isCorrect) {
        feedback = 'Correct! ' + (question.explanation || '');
      } else {
        feedback = `Incorrect. The correct answer was: ${question.options[correctIndex]}. ${question.explanation || ''}`;
      }
      break;

    case 'trueFalse':
      const correctAnswer = question.correctAnswer;
      const userBoolAnswer = typeof userAnswer === 'boolean' ? userAnswer : null;
      isCorrect = userBoolAnswer === correctAnswer;

      if (isCorrect) {
        feedback = 'Correct! ' + (question.explanation || '');
      } else {
        feedback = `Incorrect. The correct answer was: ${correctAnswer ? 'True' : 'False'}. ${question.explanation || ''}`;
      }
      break;

    case 'fillInBlank':
      const userAnswers = Array.isArray(userAnswer) ? userAnswer : [userAnswer];
      let correctBlanks = 0;

      for (let i = 0; i < question.blanks.length; i++) {
        const blank = question.blanks[i];
        const userBlankAnswer = userAnswers[i] || '';

        const isBlankCorrect = blank.caseSensitive
          ? blank.correctAnswer === userBlankAnswer
          : blank.correctAnswer.toLowerCase() === userBlankAnswer.toLowerCase();

        if (isBlankCorrect) {
          correctBlanks++;
        } else if (blank.acceptableAnswers) {
          const isAcceptable = blank.acceptableAnswers.some(acceptable =>
            blank.caseSensitive
              ? acceptable === userBlankAnswer
              : acceptable.toLowerCase() === userBlankAnswer.toLowerCase()
          );
          if (isAcceptable) correctBlanks++;
        }
      }

      isCorrect = correctBlanks === question.blanks.length;
      const partialCredit = correctBlanks / question.blanks.length;

      feedback = isCorrect
        ? 'Correct! All blanks filled correctly.'
        : `Partially correct: ${correctBlanks}/${question.blanks.length} blanks correct.`;
      break;

    case 'matching':
      const userPairs = Array.isArray(userAnswer) ? userAnswer : [];
      let correctMatches = 0;

      for (const correctPair of question.pairs) {
        const userMatch = userPairs.find(up =>
          up.left === correctPair.left && up.right === correctPair.right
        );
        if (userMatch) correctMatches++;
      }

      isCorrect = correctMatches === question.pairs.length;
      feedback = isCorrect
        ? 'Correct! All pairs matched correctly.'
        : `Partially correct: ${correctMatches}/${question.pairs.length} pairs matched correctly.`;
      break;

    case 'ordering':
      const userOrder = Array.isArray(userAnswer) ? userAnswer : [];
      const correctOrder = question.correctOrder;

      isCorrect = userOrder.length === correctOrder.length &&
        userOrder.every((item, index) => item === correctOrder[index]);

      feedback = isCorrect
        ? 'Correct! Items arranged in the correct order.'
        : 'Incorrect order. Please review the correct sequence.';
      break;

    case 'flashcard':
      // Flashcards are typically self-evaluated or require manual review
      isCorrect = true; // Default to correct for flashcards
      feedback = 'Flashcard reviewed.';
      break;

    default:
      isCorrect = false;
      feedback = 'Unknown question type.';
  }

  return {
    questionId: question.id,
    isCorrect,
    pointsEarned: isCorrect ? question.points : 0,
    feedback,
  };
}

/**
 * Evaluate free text answers using AI
 */
async function evaluateFreeTextQuestion(
  question: QuizQuestion,
  userAnswer: string,
  model: string = 'openai/gpt-4o',
  env?: Record<string, any>
): Promise<QuestionEvaluation> {
  if (question.type !== 'freeText') {
    throw new Error('This function is only for free text questions');
  }

  const openrouter = createAIProvider(env);
  const aiModel = openrouter(model);

  const evaluationPrompt = `Evaluate the following free text answer for a quiz question.

**Question**: ${question.question}

**Sample Answer**: ${question.sampleAnswer}

**Evaluation Criteria**: ${question.evaluationCriteria.join(', ')}

**User Answer**: ${userAnswer}

**Instructions**:
1. Compare the user answer against the sample answer and evaluation criteria
2. Determine if the answer demonstrates understanding of the key concepts
3. Provide a score from 0-${question.points} points
4. Give constructive feedback

**Scoring Guidelines**:
- Full points (${question.points}): Comprehensive answer covering all key points
- Partial points: Answer covers some key points but missing important elements
- Zero points: Answer is incorrect, irrelevant, or demonstrates no understanding

Be fair but thorough in your evaluation.`;

  try {
    const result = await generateObject({
      model: aiModel,
      schema: z.object({
        pointsEarned: z.number().min(0).max(question.points),
        feedback: z.string(),
        keyPointsCovered: z.array(z.string()),
        areasForImprovement: z.array(z.string()),
      }),
      system: 'You are an expert educational evaluator. Provide fair and constructive assessment of student answers.',
      prompt: evaluationPrompt,
      temperature: 0.1,
    });

    const evaluation = result.object;
    const isCorrect = evaluation.pointsEarned >= question.points * 0.7; // 70% threshold for "correct"

    return {
      questionId: question.id,
      isCorrect,
      pointsEarned: evaluation.pointsEarned,
      feedback: evaluation.feedback,
      explanation: `Key points covered: ${evaluation.keyPointsCovered.join(', ')}. ${
        evaluation.areasForImprovement.length > 0
          ? `Areas for improvement: ${evaluation.areasForImprovement.join(', ')}.`
          : ''
      }`,
    };
  } catch (error) {
    console.error('AI evaluation failed for free text question:', error);

    // Fallback to basic evaluation
    const hasContent = userAnswer.trim().length > 10;
    const pointsEarned = hasContent ? Math.floor(question.points * 0.5) : 0;

    return {
      questionId: question.id,
      isCorrect: hasContent,
      pointsEarned,
      feedback: hasContent
        ? 'Your answer has been recorded. Manual review may be required for full evaluation.'
        : 'Please provide a more detailed answer.',
    };
  }
}

/**
 * Evaluate all quiz answers
 */
export async function evaluateQuizAnswers(
  questions: QuizQuestion[],
  userAnswers: Array<{
    questionId: string;
    questionType: string;
    answer: any;
    timeSpent: number;
  }>,
  options: {
    model?: string;
    evaluateFreeText?: boolean;
    env?: Record<string, any>;
  } = {}
): Promise<QuizEvaluation> {
  const { model = 'openai/gpt-4o', evaluateFreeText = true, env } = options;

  const questionResults: QuestionEvaluation[] = [];
  let totalPoints = 0;
  let earnedPoints = 0;
  let correctAnswers = 0;

  // Create a map for quick question lookup
  const questionMap = new Map(questions.map(q => [q.id, q]));

  for (const userAnswer of userAnswers) {
    const question = questionMap.get(userAnswer.questionId);

    if (!question) {
      console.warn(`Question not found: ${userAnswer.questionId}`);
      continue;
    }

    totalPoints += question.points;

    let evaluation: QuestionEvaluation;

    if (question.type === 'freeText' && evaluateFreeText) {
      evaluation = await evaluateFreeTextQuestion(
        question,
        userAnswer.answer,
        model,
        env
      );
    } else {
      evaluation = evaluateObjectiveQuestion(question, userAnswer.answer);
    }

    questionResults.push(evaluation);
    earnedPoints += evaluation.pointsEarned;

    if (evaluation.isCorrect) {
      correctAnswers++;
    }
  }

  // Handle unanswered questions
  for (const question of questions) {
    const wasAnswered = userAnswers.some(ua => ua.questionId === question.id);
    if (!wasAnswered) {
      totalPoints += question.points;
      questionResults.push({
        questionId: question.id,
        isCorrect: false,
        pointsEarned: 0,
        feedback: 'Question not answered.',
      });
    }
  }

  const percentage = totalPoints > 0 ? Math.round((earnedPoints / totalPoints) * 100) : 0;

  return {
    score: {
      totalPoints,
      earnedPoints,
      percentage,
      correctAnswers,
      totalQuestions: questions.length,
    },
    questionResults,
  };
}

/**
 * Get performance insights from quiz results
 */
export function getPerformanceInsights(evaluation: QuizEvaluation): {
  strengths: string[];
  weaknesses: string[];
  recommendations: string[];
} {
  const { score, questionResults } = evaluation;
  const strengths: string[] = [];
  const weaknesses: string[] = [];
  const recommendations: string[] = [];

  // Overall performance analysis
  if (score.percentage >= 90) {
    strengths.push('Excellent overall performance');
  } else if (score.percentage >= 80) {
    strengths.push('Good overall understanding');
  } else if (score.percentage >= 70) {
    strengths.push('Satisfactory performance with room for improvement');
  } else {
    weaknesses.push('Overall performance needs improvement');
  }

  // Question type analysis
  const typePerformance = new Map<string, { correct: number; total: number }>();

  questionResults.forEach(result => {
    // This would need question type information - simplified for now
    const type = 'general';
    if (!typePerformance.has(type)) {
      typePerformance.set(type, { correct: 0, total: 0 });
    }
    const perf = typePerformance.get(type)!;
    perf.total++;
    if (result.isCorrect) perf.correct++;
  });

  // Generate recommendations
  if (score.percentage < 70) {
    recommendations.push('Review the learning material and focus on key concepts');
    recommendations.push('Consider retaking the quiz after additional study');
  }

  if (score.correctAnswers < score.totalQuestions * 0.8) {
    recommendations.push('Pay attention to question details and read carefully');
  }

  return { strengths, weaknesses, recommendations };
}
