/**
 * @jest-environment node
 */

import { jest } from '@jest/globals';
import { validateQuizContent, validateQuizBatch } from '../content-validator';
import type { QuizQuestion } from '../../schemas/quiz-generation';

// Mock the AI generation
jest.mock('ai', () => ({
  generateObject: jest.fn(),
}));

// Mock the AI config
jest.mock('../../config/ai-config', () => ({
  createAIProvider: jest.fn(() => () => 'mock-model'),
}));

const { generateObject } = await import('ai');

const sourceContent = `
JavaScript is a programming language that supports multiple data types including:
- String: Text data enclosed in quotes like "hello world"
- Number: Numeric values such as 42 or 3.14
- Boolean: True or false values
- Object: Complex data structures like {name: "John", age: 30}

Variables can be declared using var, let, or const keywords. The const keyword creates immutable bindings.
Functions are reusable blocks of code that can accept parameters and return values.
`;

const validQuestion: QuizQuestion = {
  id: 'q1',
  type: 'multipleChoice',
  difficulty: 'medium',
  sourceStepId: 'step-1',
  sourceContent: 'JavaScript supports multiple data types including String, Number, Boolean, and Object.',
  points: 10,
  question: 'Which of the following are JavaScript data types?',
  options: ['String', 'Number', 'Boolean', 'All of the above'],
  correctAnswerIndex: 3,
  explanation: 'JavaScript supports string, number, boolean, and object data types.',
};

const hallucinatedQuestion: QuizQuestion = {
  id: 'q2',
  type: 'multipleChoice',
  difficulty: 'medium',
  sourceStepId: 'step-1',
  sourceContent: 'JavaScript supports multiple data types.',
  points: 10,
  question: 'What year was JavaScript invented?',
  options: ['1995', '1996', '1997', '1998'],
  correctAnswerIndex: 0,
  explanation: 'JavaScript was invented by Brendan Eich in 1995.',
};

const questionWithWrongAnswer: QuizQuestion = {
  id: 'q3',
  type: 'trueFalse',
  difficulty: 'easy',
  sourceStepId: 'step-1',
  sourceContent: 'Variables can be declared using var, let, or const keywords.',
  points: 5,
  statement: 'Variables in JavaScript can only be declared using the var keyword.',
  correctAnswer: true, // This is wrong based on the source content
  explanation: 'JavaScript only supports var for variable declaration.',
};

describe('Content Validator Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('validateQuizContent', () => {
    it('validates correct content-based question', async () => {
      (generateObject as jest.MockedFunction<typeof generateObject>).mockResolvedValue({
        object: {
          sourceContent,
          generatedQuestion: validQuestion,
          validationChecks: {
            contentMatch: true,
            noHallucination: true,
            appropriateDifficulty: true,
            clearlyAnswerable: true,
          },
          confidence: 0.9,
        },
      });

      const result = await validateQuizContent(sourceContent, validQuestion);

      expect(result.isValid).toBe(true);
      expect(result.confidence).toBeGreaterThan(0.8);
      expect(result.issues).toHaveLength(0);
      expect(result.validationDetails.validationChecks.contentMatch).toBe(true);
      expect(result.validationDetails.validationChecks.noHallucination).toBe(true);
    });

    it('detects hallucinated content', async () => {
      (generateObject as jest.MockedFunction<typeof generateObject>).mockResolvedValue({
        object: {
          sourceContent,
          generatedQuestion: hallucinatedQuestion,
          validationChecks: {
            contentMatch: false,
            noHallucination: false,
            appropriateDifficulty: true,
            clearlyAnswerable: false,
          },
          confidence: 0.2,
        },
      });

      const result = await validateQuizContent(sourceContent, hallucinatedQuestion);

      expect(result.isValid).toBe(false);
      expect(result.confidence).toBeLessThan(0.5);
      expect(result.issues.length).toBeGreaterThan(0);
      expect(result.suggestions.length).toBeGreaterThan(0);
    });

    it('detects incorrect answers based on source content', async () => {
      (generateObject as jest.MockedFunction<typeof generateObject>).mockResolvedValue({
        object: {
          sourceContent,
          generatedQuestion: questionWithWrongAnswer,
          validationChecks: {
            contentMatch: true,
            noHallucination: true,
            appropriateDifficulty: true,
            clearlyAnswerable: true,
          },
          confidence: 0.8,
        },
      });

      const result = await validateQuizContent(
        sourceContent, 
        questionWithWrongAnswer,
        { validateAnswers: true }
      );

      expect(result.isValid).toBe(false);
      expect(result.issues).toContain(
        expect.stringContaining('statement cannot be verified from source content')
      );
    });

    it('handles AI validation failure gracefully', async () => {
      (generateObject as jest.MockedFunction<typeof generateObject>).mockRejectedValue(
        new Error('AI service unavailable')
      );

      const result = await validateQuizContent(sourceContent, validQuestion);

      // Should still provide basic validation even if AI fails
      expect(result).toMatchObject({
        isValid: expect.any(Boolean),
        confidence: expect.any(Number),
        issues: expect.any(Array),
        suggestions: expect.any(Array),
        validationDetails: expect.any(Object),
      });
    });

    it('validates multiple choice answers against source', async () => {
      const mcqWithWrongAnswer: QuizQuestion = {
        ...validQuestion,
        options: ['Python', 'Java', 'C++', 'Ruby'], // None of these are mentioned in source
        correctAnswerIndex: 0,
      };

      (generateObject as jest.MockedFunction<typeof generateObject>).mockResolvedValue({
        object: {
          sourceContent,
          generatedQuestion: mcqWithWrongAnswer,
          validationChecks: {
            contentMatch: true,
            noHallucination: true,
            appropriateDifficulty: true,
            clearlyAnswerable: true,
          },
          confidence: 0.8,
        },
      });

      const result = await validateQuizContent(
        sourceContent, 
        mcqWithWrongAnswer,
        { validateAnswers: true }
      );

      expect(result.isValid).toBe(false);
      expect(result.issues).toContain(
        expect.stringContaining('not found in source content')
      );
    });

    it('detects proper nouns not in source content', async () => {
      const questionWithProperNouns: QuizQuestion = {
        ...validQuestion,
        question: 'Who created JavaScript at Netscape?',
        options: ['Brendan Eich', 'Douglas Crockford', 'John Resig', 'Ryan Dahl'],
        correctAnswerIndex: 0,
      };

      (generateObject as jest.MockedFunction<typeof generateObject>).mockResolvedValue({
        object: {
          sourceContent,
          generatedQuestion: questionWithProperNouns,
          validationChecks: {
            contentMatch: false,
            noHallucination: false,
            appropriateDifficulty: true,
            clearlyAnswerable: false,
          },
          confidence: 0.3,
        },
      });

      const result = await validateQuizContent(sourceContent, questionWithProperNouns);

      expect(result.isValid).toBe(false);
      expect(result.issues).toContain(
        expect.stringContaining('Proper noun')
      );
    });

    it('validates fill-in-blank answers', async () => {
      const fillInBlankQuestion: QuizQuestion = {
        id: 'q4',
        type: 'fillInBlank',
        difficulty: 'medium',
        sourceStepId: 'step-1',
        sourceContent: 'Variables can be declared using var, let, or const keywords.',
        points: 10,
        text: 'Variables can be declared using _____, _____, or _____ keywords.',
        blanks: [
          { position: 0, correctAnswer: 'var', caseSensitive: false },
          { position: 1, correctAnswer: 'let', caseSensitive: false },
          { position: 2, correctAnswer: 'const', caseSensitive: false },
        ],
      };

      (generateObject as jest.MockedFunction<typeof generateObject>).mockResolvedValue({
        object: {
          sourceContent,
          generatedQuestion: fillInBlankQuestion,
          validationChecks: {
            contentMatch: true,
            noHallucination: true,
            appropriateDifficulty: true,
            clearlyAnswerable: true,
          },
          confidence: 0.9,
        },
      });

      const result = await validateQuizContent(
        sourceContent, 
        fillInBlankQuestion,
        { validateAnswers: true }
      );

      expect(result.isValid).toBe(true);
      expect(result.issues).toHaveLength(0);
    });

    it('uses strict mode validation', async () => {
      (generateObject as jest.MockedFunction<typeof generateObject>).mockResolvedValue({
        object: {
          sourceContent,
          generatedQuestion: validQuestion,
          validationChecks: {
            contentMatch: true,
            noHallucination: true,
            appropriateDifficulty: true,
            clearlyAnswerable: true,
          },
          confidence: 0.65, // Below strict threshold but above relaxed
        },
      });

      const strictResult = await validateQuizContent(
        sourceContent, 
        validQuestion,
        { strictMode: true, minConfidence: 0.7 }
      );

      const relaxedResult = await validateQuizContent(
        sourceContent, 
        validQuestion,
        { strictMode: false, minConfidence: 0.7 }
      );

      expect(strictResult.isValid).toBe(false);
      expect(relaxedResult.isValid).toBe(true);
    });
  });

  describe('validateQuizBatch', () => {
    it('validates multiple questions and provides statistics', async () => {
      const questions = [validQuestion, hallucinatedQuestion, questionWithWrongAnswer];

      (generateObject as jest.MockedFunction<typeof generateObject>)
        .mockResolvedValueOnce({
          object: {
            validationChecks: {
              contentMatch: true,
              noHallucination: true,
              appropriateDifficulty: true,
              clearlyAnswerable: true,
            },
            confidence: 0.9,
          },
        })
        .mockResolvedValueOnce({
          object: {
            validationChecks: {
              contentMatch: false,
              noHallucination: false,
              appropriateDifficulty: true,
              clearlyAnswerable: false,
            },
            confidence: 0.2,
          },
        })
        .mockResolvedValueOnce({
          object: {
            validationChecks: {
              contentMatch: true,
              noHallucination: true,
              appropriateDifficulty: true,
              clearlyAnswerable: true,
            },
            confidence: 0.8,
          },
        });

      const result = await validateQuizBatch(sourceContent, questions);

      expect(result.validQuestions).toHaveLength(1); // Only the first question should pass
      expect(result.invalidQuestions).toHaveLength(2);
      expect(result.overallStats.totalQuestions).toBe(3);
      expect(result.overallStats.validQuestions).toBe(1);
      expect(result.overallStats.averageConfidence).toBeCloseTo(0.63, 1);
      expect(result.overallStats.commonIssues.length).toBeGreaterThan(0);
    });

    it('handles empty question array', async () => {
      const result = await validateQuizBatch(sourceContent, []);

      expect(result.validQuestions).toHaveLength(0);
      expect(result.invalidQuestions).toHaveLength(0);
      expect(result.overallStats.totalQuestions).toBe(0);
      expect(result.overallStats.averageConfidence).toBe(0);
    });

    it('tracks common validation issues', async () => {
      const questions = [hallucinatedQuestion, questionWithWrongAnswer];

      (generateObject as jest.MockedFunction<typeof generateObject>)
        .mockResolvedValue({
          object: {
            validationChecks: {
              contentMatch: false,
              noHallucination: false,
              appropriateDifficulty: true,
              clearlyAnswerable: false,
            },
            confidence: 0.2,
          },
        });

      const result = await validateQuizBatch(sourceContent, questions);

      expect(result.overallStats.commonIssues).toContain(
        expect.stringContaining('content similarity')
      );
    });
  });
});
