/**
 * @jest-environment node
 */

import { jest } from '@jest/globals';
import { generateQuizFromContent, getAvailableQuizTypes } from '../quiz-generator';
import type { QuizGenerationInput } from '../../schemas/quiz-generation';

// Mock the AI generation
jest.mock('ai', () => ({
  generateObject: jest.fn(),
}));

// Mock the AI config
jest.mock('../../config/ai-config', () => ({
  createAIProvider: jest.fn(() => () => 'mock-model'),
  selectOptimalModel: jest.fn(() => 'openai/gpt-4o'),
  selectModelWithFallback: jest.fn(() => ({
    primary: 'deepseek/deepseek-r1:free',
    fallbacks: ['meta-llama/llama-3.1-8b-instruct:free', 'openai/gpt-4o']
  })),
  AI_CONFIG: {},
}));

// Mock crypto for UUID generation
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: jest.fn(() => 'mock-uuid-123'),
  },
});

const { generateObject } = await import('ai');

// Mock learning content
const mockLearningContent = {
  id: 'content-1',
  title: 'JavaScript Fundamentals',
  description: 'Learn the basics of JavaScript programming',
  steps: [
    {
      id: 'step-1',
      title: 'Variables and Data Types',
      icon: 'Brain',
      blocks: [
        {
          id: 'block-1',
          type: 'paragraph',
          data: 'JavaScript has several data types including strings, numbers, booleans, and objects. Variables can be declared using var, let, or const keywords.',
        },
        {
          id: 'block-2',
          type: 'bulletList',
          data: [
            'String: Text data enclosed in quotes',
            'Number: Numeric values including integers and floats',
            'Boolean: True or false values',
            'Object: Complex data structures'
          ],
        },
      ],
    },
    {
      id: 'step-2',
      title: 'Functions',
      icon: 'Zap',
      blocks: [
        {
          id: 'block-3',
          type: 'paragraph',
          data: 'Functions are reusable blocks of code that perform specific tasks. They can accept parameters and return values.',
        },
      ],
    },
  ],
  learningLevel: 'beginner' as const,
};

const mockQuizInput: QuizGenerationInput = {
  learningContentId: 'content-1',
  quizTypes: ['multipleChoice', 'trueFalse'],
  difficulty: 'medium',
  questionsPerType: 2,
  includeHints: true,
  includeExplanations: true,
};

const mockGeneratedQuestions = {
  questions: [
    {
      id: 'q1',
      type: 'multipleChoice',
      difficulty: 'medium',
      sourceStepId: 'step-1',
      sourceContent: 'JavaScript has several data types including strings, numbers, booleans, and objects.',
      points: 10,
      question: 'Which of the following are JavaScript data types?',
      options: ['String', 'Number', 'Boolean', 'All of the above'],
      correctAnswerIndex: 3,
      explanation: 'JavaScript supports string, number, boolean, and object data types.',
    },
    {
      id: 'q2',
      type: 'trueFalse',
      difficulty: 'medium',
      sourceStepId: 'step-1',
      sourceContent: 'Variables can be declared using var, let, or const keywords.',
      points: 5,
      statement: 'Variables in JavaScript can only be declared using the var keyword.',
      correctAnswer: false,
      explanation: 'JavaScript supports var, let, and const for variable declaration.',
    },
  ],
};

describe('Quiz Generator Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('generateQuizFromContent', () => {
    it('generates quiz successfully with valid input', async () => {
      (generateObject as jest.MockedFunction<typeof generateObject>).mockResolvedValue({
        object: mockGeneratedQuestions,
      });

      const result = await generateQuizFromContent(
        mockLearningContent,
        mockQuizInput
      );

      expect(result).toMatchObject({
        id: expect.any(String),
        title: 'Quiz: JavaScript Fundamentals',
        description: 'Generated quiz based on "JavaScript Fundamentals" learning content',
        learningContentId: 'content-1',
        questions: expect.arrayContaining([
          expect.objectContaining({
            type: 'multipleChoice',
            sourceStepId: 'step-1',
          }),
          expect.objectContaining({
            type: 'trueFalse',
            sourceStepId: 'step-1',
          }),
        ]),
        estimatedDuration: expect.any(Number),
        totalPoints: 15,
        metadata: expect.objectContaining({
          generatedAt: expect.any(String),
          aiModel: expect.any(String),
          modelsUsed: expect.any(Array),
          costOptimized: true,
          sourceStepsUsed: expect.arrayContaining(['step-1']),
        }),
      });
    });

    it('throws error when no steps provided', async () => {
      const emptyContent = { ...mockLearningContent, steps: [] };

      await expect(
        generateQuizFromContent(emptyContent, mockQuizInput)
      ).rejects.toThrow('Learning content must have at least one step');
    });

    it('filters out steps with insufficient content', async () => {
      const contentWithEmptyStep = {
        ...mockLearningContent,
        steps: [
          {
            id: 'empty-step',
            title: 'Empty Step',
            icon: 'Brain',
            blocks: [
              {
                id: 'empty-block',
                type: 'paragraph',
                data: 'Short', // Too short for quiz generation
              },
            ],
          },
          mockLearningContent.steps[0], // Valid step
        ],
      };

      (generateObject as jest.MockedFunction<typeof generateObject>).mockResolvedValue({
        object: { questions: [mockGeneratedQuestions.questions[0]] },
      });

      const result = await generateQuizFromContent(
        contentWithEmptyStep,
        mockQuizInput
      );

      expect(result.questions).toHaveLength(1);
      expect(result.metadata.sourceStepsUsed).toEqual(['step-1']);
    });

    it('calculates estimated duration correctly', async () => {
      (generateObject as jest.MockedFunction<typeof generateObject>).mockResolvedValue({
        object: mockGeneratedQuestions,
      });

      const result = await generateQuizFromContent(
        mockLearningContent,
        mockQuizInput
      );

      // Should calculate based on question types and count
      expect(result.estimatedDuration).toBeGreaterThan(0);
      expect(result.estimatedDuration).toBeLessThan(10); // Reasonable duration
    });

    it('handles AI generation failure gracefully', async () => {
      (generateObject as jest.MockedFunction<typeof generateObject>).mockRejectedValue(
        new Error('AI service unavailable')
      );

      await expect(
        generateQuizFromContent(mockLearningContent, mockQuizInput)
      ).rejects.toThrow('Failed to generate any valid quiz questions from the content');
    });

    it('validates input parameters', async () => {
      const invalidInput = {
        ...mockQuizInput,
        quizTypes: [], // Empty array should be invalid
      };

      await expect(
        generateQuizFromContent(mockLearningContent, invalidInput as any)
      ).rejects.toThrow();
    });

    it('includes content validation when enabled', async () => {
      (generateObject as jest.MockedFunction<typeof generateObject>)
        .mockResolvedValueOnce({ object: mockGeneratedQuestions })
        .mockResolvedValue({
          object: {
            sourceContent: 'test content',
            generatedQuestion: mockGeneratedQuestions.questions[0],
            validationChecks: {
              contentMatch: true,
              noHallucination: true,
              appropriateDifficulty: true,
              clearlyAnswerable: true,
            },
            confidence: 0.9,
          },
        });

      const result = await generateQuizFromContent(
        mockLearningContent,
        mockQuizInput,
        { validateContent: true }
      );

      expect(result.questions).toHaveLength(2);
      // Validation calls should have been made
      expect(generateObject).toHaveBeenCalledTimes(3); // 1 generation + 2 validations
    });

    it('filters out questions that fail validation', async () => {
      (generateObject as jest.MockedFunction<typeof generateObject>)
        .mockResolvedValueOnce({ object: mockGeneratedQuestions })
        .mockResolvedValueOnce({
          object: {
            validationChecks: {
              contentMatch: false,
              noHallucination: false,
              appropriateDifficulty: true,
              clearlyAnswerable: true,
            },
            confidence: 0.3,
          },
        })
        .mockResolvedValueOnce({
          object: {
            validationChecks: {
              contentMatch: true,
              noHallucination: true,
              appropriateDifficulty: true,
              clearlyAnswerable: true,
            },
            confidence: 0.9,
          },
        });

      const result = await generateQuizFromContent(
        mockLearningContent,
        mockQuizInput,
        { validateContent: true }
      );

      // Should only include the question that passed validation
      expect(result.questions).toHaveLength(1);
    });

    it('calculates distributions correctly', async () => {
      (generateObject as jest.MockedFunction<typeof generateObject>).mockResolvedValue({
        object: mockGeneratedQuestions,
      });

      const result = await generateQuizFromContent(
        mockLearningContent,
        mockQuizInput
      );

      expect(result.metadata.difficultyDistribution).toEqual({
        medium: 2,
      });

      expect(result.metadata.typeDistribution).toEqual({
        multipleChoice: 1,
        trueFalse: 1,
      });
    });

    it('uses cost-optimized model selection by default', async () => {
      const { selectModelWithFallback } = await import('../../config/ai-config');

      (generateObject as jest.MockedFunction<typeof generateObject>).mockResolvedValue({
        object: mockGeneratedQuestions,
      });

      await generateQuizFromContent(
        mockLearningContent,
        mockQuizInput,
        { preferCostEffective: true }
      );

      expect(selectModelWithFallback).toHaveBeenCalledWith(
        ['paragraph'],
        'intermediate',
        {
          preferCostEffective: true,
          requireStructuredOutput: true,
          maxRetries: 3
        }
      );
    });

    it('includes model metadata in generated questions', async () => {
      (generateObject as jest.MockedFunction<typeof generateObject>).mockResolvedValue({
        object: mockGeneratedQuestions,
      });

      const result = await generateQuizFromContent(
        mockLearningContent,
        mockQuizInput,
        { preferCostEffective: true }
      );

      expect(result.metadata).toMatchObject({
        costOptimized: true,
        modelsUsed: expect.any(Array),
      });

      // Check that questions have model metadata
      result.questions.forEach(question => {
        expect(question.metadata).toMatchObject({
          generatedByModel: expect.any(String),
          fallbackUsed: expect.any(Boolean),
        });
      });
    });
  });

  describe('getAvailableQuizTypes', () => {
    it('returns all available quiz types with metadata', () => {
      const types = getAvailableQuizTypes();

      expect(types).toHaveLength(7);
      expect(types).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            type: 'flashcard',
            name: 'Flashcards',
            description: expect.any(String),
            estimatedTime: expect.any(String),
          }),
          expect.objectContaining({
            type: 'multipleChoice',
            name: 'Multiple Choice',
            description: expect.any(String),
            estimatedTime: expect.any(String),
          }),
          expect.objectContaining({
            type: 'trueFalse',
            name: 'True or False',
            description: expect.any(String),
            estimatedTime: expect.any(String),
          }),
        ])
      );
    });

    it('includes all required quiz types', () => {
      const types = getAvailableQuizTypes();
      const typeNames = types.map(t => t.type);

      expect(typeNames).toContain('flashcard');
      expect(typeNames).toContain('multipleChoice');
      expect(typeNames).toContain('trueFalse');
      expect(typeNames).toContain('fillInBlank');
      expect(typeNames).toContain('matching');
      expect(typeNames).toContain('freeText');
      expect(typeNames).toContain('ordering');
    });
  });
});
