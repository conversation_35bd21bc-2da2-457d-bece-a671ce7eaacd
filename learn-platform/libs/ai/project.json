{"name": "ai", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/ai/src", "projectType": "library", "tags": ["scope:shared", "type:lib"], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/ai", "main": "libs/ai/src/index.ts", "tsConfig": "libs/ai/tsconfig.lib.json", "assets": ["libs/ai/*.md"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/ai/jest.config.ts"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/ai/**/*.ts"]}}}}