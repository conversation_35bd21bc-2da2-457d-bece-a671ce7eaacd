# AI Library Integration Example

This document shows how to integrate the `@learn-platform/ai` library into your application.

## Basic Usage

```typescript
import { generateLearningContent } from '@learn-platform/ai';

// Basic content generation
const content = await generateLearningContent({
  topic: 'How does machine learning work?',
  learningLevel: 'beginner',
  preferredContentTypes: ['paragraph', 'bulletList', 'infoBox'],
  focusAreas: 'practical examples and real-world applications'
});

console.log(content.title);
console.log(content.description);
console.log(`Estimated reading time: ${content.estimatedReadingTime} minutes`);

// Display the steps
content.steps.forEach((step, index) => {
  console.log(`Step ${index + 1}: ${step.title}`);
  console.log(`Type: ${step.type}`);
  console.log(`Icon: ${step.icon}`);
  console.log('Data:', step.data);
});
```

## Advanced Usage with Options

```typescript
import { 
  generateLearningContent, 
  getAvailableModels, 
  estimateGenerationCost 
} from '@learn-platform/ai';

// Get available models
const models = getAvailableModels();
console.log('Available models:', models);

// Estimate cost before generation
const input = {
  topic: 'Advanced React patterns',
  learningLevel: 'advanced',
  preferredContentTypes: ['paragraph', 'grid', 'comparison']
};

const costEstimate = estimateGenerationCost(input, {
  model: 'anthropic/claude-3-opus',
  maxSteps: 10
});

console.log(`Estimated cost: $${costEstimate.estimatedCost} ${costEstimate.currency}`);

// Generate with custom options
const content = await generateLearningContent(input, {
  model: 'anthropic/claude-3-opus',
  temperature: 0.8,
  maxSteps: 10,
  includeExamples: true,
  preferCostEffective: false,
  retryOnFailure: true
});
```

## Error Handling

```typescript
import { 
  generateLearningContent,
  AIServiceError,
  RateLimitError,
  InvalidInputError,
  ModelError,
  getErrorMessage,
  getErrorRecoveryActions
} from '@learn-platform/ai';

try {
  const content = await generateLearningContent({
    topic: 'JavaScript async/await',
    learningLevel: 'intermediate',
    preferredContentTypes: ['paragraph', 'numberedList']
  });
  
  // Success - use the content
  console.log('Generated content:', content);
  
} catch (error) {
  // Get user-friendly error message
  const userMessage = getErrorMessage(error);
  console.error('User message:', userMessage);
  
  // Handle specific error types
  if (error instanceof RateLimitError) {
    console.log('Rate limited - waiting before retry...');
    // Implement retry logic with delay
    
  } else if (error instanceof InvalidInputError) {
    console.log('Invalid input provided');
    // Show form validation errors
    
  } else if (error instanceof ModelError) {
    console.log('AI model error - trying different model...');
    // Retry with different model
    
  } else if (error instanceof AIServiceError) {
    // Get recovery suggestions
    const actions = getErrorRecoveryActions(error);
    console.log('Suggested actions:', actions);
  }
}
```

## Integration with tRPC

```typescript
// In your tRPC procedure
import { generateLearningContent } from '@learn-platform/ai';
import { z } from 'zod';

export const aiGenerationRouter = router({
  generateContent: protectedProcedure
    .input(z.object({
      topic: z.string().min(3).max(200),
      learningLevel: z.enum(['beginner', 'intermediate', 'advanced']),
      preferredContentTypes: z.array(z.string()).min(1),
      focusAreas: z.string().optional(),
    }))
    .mutation(async ({ input, ctx }) => {
      try {
        // Generate content using AI
        const generatedContent = await generateLearningContent({
          topic: input.topic,
          learningLevel: input.learningLevel,
          preferredContentTypes: input.preferredContentTypes as any,
          focusAreas: input.focusAreas,
        });

        // Save to database (implement this based on your schema)
        const savedContent = await ctx.db.learningContent.create({
          data: {
            title: generatedContent.title,
            description: generatedContent.description,
            steps: generatedContent.steps,
            metadata: generatedContent.metadata,
            userId: ctx.user.id,
          }
        });

        return {
          success: true,
          contentId: savedContent.id,
          content: generatedContent
        };

      } catch (error) {
        // Log error for debugging
        console.error('AI generation failed:', error);
        
        // Return user-friendly error
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: getErrorMessage(error),
          cause: error
        });
      }
    }),
});
```

## React Component Integration

```typescript
// React component using the AI service
import { useState } from 'react';
import { trpc } from '@/lib/trpc';
import { getErrorMessage } from '@learn-platform/ai';

export function LearningContentGenerator() {
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const generateMutation = trpc.ai.generateContent.useMutation({
    onSuccess: (data) => {
      console.log('Content generated:', data);
      // Redirect to content view or update UI
    },
    onError: (error) => {
      setError(getErrorMessage(error));
    },
    onSettled: () => {
      setIsGenerating(false);
    }
  });

  const handleSubmit = async (formData: any) => {
    setIsGenerating(true);
    setError(null);
    
    generateMutation.mutate(formData);
  };

  return (
    <div>
      {/* Your form component */}
      <form onSubmit={handleSubmit}>
        {/* Form fields */}
      </form>
      
      {isGenerating && (
        <div>Generating content... This may take a moment.</div>
      )}
      
      {error && (
        <div className="error">
          {error}
        </div>
      )}
    </div>
  );
}
```

## Environment Setup

Make sure to set these environment variables:

```bash
# Required
OPENROUTER_API_KEY=your-openrouter-api-key-here

# Optional (with defaults)
OPENROUTER_APP_NAME=Learning Platform
OPENROUTER_SITE_URL=http://localhost:3000
```

## Testing

```typescript
// Mock the AI service in tests
jest.mock('@learn-platform/ai', () => ({
  generateLearningContent: jest.fn(),
  getErrorMessage: jest.fn(),
}));

// In your test
import { generateLearningContent } from '@learn-platform/ai';

const mockGenerateContent = generateLearningContent as jest.MockedFunction<typeof generateLearningContent>;

test('should handle content generation', async () => {
  mockGenerateContent.mockResolvedValue({
    title: 'Test Content',
    description: 'Test Description',
    steps: [],
    estimatedReadingTime: 5,
    metadata: {
      aiModel: 'test-model',
      generatedAt: new Date().toISOString(),
      contentTypes: ['paragraph'],
      learningLevel: 'beginner',
      topic: 'test'
    }
  });

  // Your test logic here
});
```

## Best Practices

1. **Always handle errors gracefully** - AI services can fail for various reasons
2. **Show loading states** - Content generation can take 10-30 seconds
3. **Validate input** - Use the provided schemas for input validation
4. **Monitor costs** - Use `estimateGenerationCost` to track usage
5. **Cache results** - Store generated content to avoid regenerating the same content
6. **Provide fallbacks** - Have backup content or alternative flows when AI fails
7. **Rate limiting** - Implement client-side rate limiting to prevent abuse
