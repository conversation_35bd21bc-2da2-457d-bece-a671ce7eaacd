# @learn-platform/ai

AI service library for generating structured learning content using OpenRouter and Vercel AI SDK.

## Features

- 🤖 **OpenRouter Integration**: Access to 100+ AI models through a single API
- 📝 **Structured Content Generation**: Generate learning content matching MultiStepExplain component schema
- 🔧 **TypeScript Support**: Full type safety with Zod schema validation
- ⚡ **Cloudflare Workers Compatible**: Designed for edge environments
- 🎯 **Educational Focus**: Specialized prompts for learning content generation
- 🔄 **Multiple Content Types**: Support for 9 different content formats

## Installation

This library is part of the learn-platform monorepo and is automatically available to other workspace packages.

## Environment Variables

```bash
OPENROUTER_API_KEY=your-openrouter-api-key
OPENROUTER_APP_NAME=Learning Platform
OPENROUTER_SITE_URL=https://your-domain.com
```

## Usage

### Basic Content Generation

```typescript
import { generateLearningContent } from '@learn-platform/ai';

const content = await generateLearningContent({
  topic: 'How does machine learning work?',
  learningLevel: 'beginner',
  preferredContentTypes: ['paragraph', 'bulletList', 'infoBox'],
  focusAreas: 'practical examples and real-world applications'
});
```

### Custom Configuration

```typescript
import { generateLearningContent } from '@learn-platform/ai';

const content = await generateLearningContent(
  {
    topic: 'Advanced React patterns',
    learningLevel: 'advanced',
    preferredContentTypes: ['paragraph', 'grid', 'comparison']
  },
  {
    model: 'anthropic/claude-3-sonnet',
    temperature: 0.7,
    maxSteps: 8,
    includeExamples: true
  }
);
```

## Supported Content Types

- `paragraph` - Text explanations
- `infoBox` - Highlighted information boxes
- `bulletList` - Bullet point lists
- `numberedList` - Numbered step lists
- `grid` - Visual grid layouts
- `comparison` - Before/after comparisons
- `table` - Data tables
- `scatterPlot` - Data visualizations
- `keyValueGrid` - Definition-style content

## Supported Models

- `anthropic/claude-3-sonnet` - Best for reasoning and explanations
- `anthropic/claude-3-opus` - Most capable for complex topics
- `openai/gpt-4o` - Reliable structured outputs
- `openai/gpt-4-turbo` - Fast and capable
- `meta-llama/llama-3.1-70b-instruct` - Cost-effective option

## Error Handling

The library provides specific error types for different failure scenarios:

```typescript
import { 
  generateLearningContent, 
  AIServiceError, 
  RateLimitError, 
  InvalidInputError 
} from '@learn-platform/ai';

try {
  const content = await generateLearningContent(input);
} catch (error) {
  if (error instanceof RateLimitError) {
    // Handle rate limiting
  } else if (error instanceof InvalidInputError) {
    // Handle invalid input
  } else if (error instanceof AIServiceError) {
    // Handle other AI service errors
  }
}
```

## Testing

```bash
# Run tests
nx test ai

# Run tests with coverage
nx test ai --coverage
```

## Development

The library follows the monorepo conventions and integrates with:

- **Database**: Uses `@learn-platform/db` for content storage
- **tRPC**: Integrates with `@learn-platform/trpc` for API endpoints
- **Authentication**: Works with `@learn-platform/auth` for user context

## Architecture

```
libs/ai/src/
├── config/          # AI service configuration
├── schemas/         # Zod schemas for validation
├── prompts/         # Prompt templates
├── services/        # Core AI services
├── types.ts         # TypeScript definitions
└── index.ts         # Public API exports
```
