{"name": "trpc", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/trpc/src", "projectType": "library", "tags": ["scope:shared", "type:api"], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/trpc", "main": "libs/trpc/src/index.ts", "tsConfig": "libs/trpc/tsconfig.lib.json", "assets": []}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/trpc/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "libs/trpc/jest.config.ts"}}}}