/**
 * tRPC context creation for Cloudflare Workers
 * This context will be available in all tRPC procedures
 */



export interface CreateContextOptions {
  request: Request;
  env?: any; // Cloudflare Worker environment bindings
}

/**
 * User session information extracted from authentication
 */
export interface UserSession {
  isAuthenticated: boolean;
  userId: string | null;
  roles: string[];
  email?: string;
  name?: string;
}

/**
 * Request metadata extracted from headers and Cloudflare
 */
export interface RequestMetadata {
  ip: string;
  country: string;
  userAgent: string;
  timestamp: string;
  requestId: string;
}

/**
 * Creates the tRPC context from the incoming request
 * This is called for every request to the tRPC server
 */
export async function createContext(opts: CreateContextOptions) {
  const { request, env } = opts;

  // Extract URL information
  const url = new URL(request.url);

  // Extract Cloudflare-specific headers and request metadata
  const requestMetadata: RequestMetadata = {
    ip: request.headers.get('CF-Connecting-IP') ||
        request.headers.get('X-Forwarded-For') ||
        request.headers.get('X-Real-IP') ||
        'unknown',
    country: request.headers.get('CF-IPCountry') || 'unknown',
    userAgent: request.headers.get('user-agent') || '',
    timestamp: new Date().toISOString(),
    requestId: request.headers.get('CF-Ray') ||
               request.headers.get('X-Request-ID') ||
               crypto.randomUUID(),
  };

  // Get authentication context using better-auth
  // Use the Cloudflare Workers environment if available
  const { createAuthContext, createAuthContextWithEnv } = await import('@learn-platform/auth');
  const authContext = env
    ? await createAuthContextWithEnv(request, env)
    : await createAuthContext(request);

  return {
    // Core request information
    request,
    env,
    url,

    // Enhanced metadata
    ...requestMetadata,

    // Authentication context from better-auth
    session: authContext.session,
    isAuthenticated: authContext.isAuthenticated,
    userId: authContext.userId,
    user: authContext.user,

    // Legacy userSession for backward compatibility
    userSession: {
      isAuthenticated: authContext.isAuthenticated,
      userId: authContext.userId,
      roles: [], // TODO: Implement role system
      email: authContext.user?.email,
      name: authContext.user?.name,
    },

    // Utility functions
    getUserId: () => authContext.userId,
    getUserRoles: () => [], // TODO: Implement role system
    hasRole: (role: string) => false, // TODO: Implement role system

    // Request timing for performance monitoring
    startTime: Date.now(),
  };
}

// Remove the parseUserSession function as we're now using better-auth

export type Context = Awaited<ReturnType<typeof createContext>>;
