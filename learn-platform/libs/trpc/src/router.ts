import { initTRPC, TRPCError } from '@trpc/server';
import { z } from 'zod';

import { templatesRouter } from './procedures/templates';
import { learningContentRouter } from './procedures/learning-content';
import { learningProgressRouter } from './procedures/learning-progress';
import { learningFeedbackRouter } from './procedures/learning-feedback';
import { releasesRouter } from './procedures/releases';
import { quizRouter } from './procedures/quiz';
import type { Context } from './context';

/**
 * Initialize tRPC with context and enhanced error formatting
 */
const t = initTRPC.context<Context>().create({
  errorFormatter({ shape, error }) {
    // Check if we're in development mode (Cloudflare Workers compatible)
    const isDevelopment = typeof globalThis !== 'undefined' &&
                         (globalThis as any).ENVIRONMENT === 'development';

    return {
      ...shape,
      data: {
        ...shape.data,
        code: error.code,
        stack: isDevelopment ? error.stack : undefined,
      },
    };
  },
});

/**
 * Export reusable router and procedure helpers
 */
export const router = t.router;
export const publicProcedure = t.procedure;

/**
 * Protected procedure that requires authentication
 * This middleware checks if the user is authenticated before proceeding
 */
export const protectedProcedure = publicProcedure.use(({ ctx, next }) => {
  if (!ctx.isAuthenticated) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'You must be logged in to access this resource',
    });
  }

  return next({
    ctx: {
      ...ctx,
      // Ensure user is available in protected procedures
      user: ctx.user,
    },
  });
});

/**
 * Admin procedure that requires admin role
 * This middleware checks if the user has admin privileges
 */
export const adminProcedure = protectedProcedure.use(({ ctx, next }) => {
  // TODO: Implement proper role checking
  // For now, we'll just check if user is authenticated
  // In a real app, you would check user roles from database
  if (!ctx.isAuthenticated) {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'You must have admin privileges to access this resource',
    });
  }

  return next({
    ctx,
  });
});

/**
 * Input/Output validation schemas
 */
const getUserInput = z.object({
  userId: z.string().min(1, 'User ID is required'),
});

const updateUserInput = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  email: z.string().email('Invalid email format').optional(),
});

// Authentication schemas
const signUpInput = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  name: z.string().min(1, 'Name is required'),
  avatar: z.string().optional(), // Optional avatar URL or path
});

const signInInput = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required'),
});

/**
 * Main application router with enhanced procedures
 */
export const appRouter = router({
  // Authentication procedures
  auth: router({
    // Sign up new user
    signUp: publicProcedure
      .input(signUpInput)
      .mutation(async ({ input, ctx }) => {
        try {
          // Create auth instance with Cloudflare Workers environment
          const { createAuth } = await import('@learn-platform/auth');
          const auth = createAuth(ctx.env);
          const result = await auth.api.signUpEmail({
            body: {
              email: input.email,
              password: input.password,
              name: input.name,
              avatar: input.avatar || '', // Use provided avatar or empty string as fallback
            },
            headers: ctx.request.headers,
          });

          return {
            success: true,
            message: 'User created successfully',
            user: result.user,
          };
        } catch (error) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: error instanceof Error ? error.message : 'Failed to create user',
          });
        }
      }),

    // Sign in user
    signIn: publicProcedure
      .input(signInInput)
      .mutation(async ({ input, ctx }) => {
        try {
          // Create auth instance with Cloudflare Workers environment
          const { createAuth } = await import('@learn-platform/auth');
          const auth = createAuth(ctx.env);
          const result = await auth.api.signInEmail({
            body: {
              email: input.email,
              password: input.password,
            },
            headers: ctx.request.headers,
          });

          return {
            success: true,
            message: 'Signed in successfully',
            user: result.user,
          };
        } catch (error) {
          throw new TRPCError({
            code: 'UNAUTHORIZED',
            message: error instanceof Error ? error.message : 'Invalid credentials',
          });
        }
      }),

    // Sign out user
    signOut: protectedProcedure
      .mutation(async ({ ctx }) => {
        try {
          // Create auth instance with Cloudflare Workers environment
          const { createAuth } = await import('@learn-platform/auth');
          const auth = createAuth(ctx.env);
          await auth.api.signOut({
            headers: ctx.request.headers,
          });

          return {
            success: true,
            message: 'Signed out successfully',
          };
        } catch (error) {
          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: 'Failed to sign out',
          });
        }
      }),

    // Get current session
    getSession: publicProcedure
      .query(({ ctx }) => {
        return {
          session: ctx.session,
          isAuthenticated: ctx.isAuthenticated,
          user: ctx.user,
        };
      }),
  }),

  // Health check endpoint with enhanced information
  health: publicProcedure
    .query(({ ctx }) => {
      return {
        status: 'ok',
        timestamp: ctx.timestamp,
        message: 'tRPC server is running on Cloudflare Workers',
        requestId: ctx.requestId,
        country: ctx.country,
        version: '1.0.0',
      };
    }),

  // Get current user information (requires authentication)
  getUser: protectedProcedure
    .input(getUserInput)
    .query(({ input, ctx }) => {
      // In a real application, this would fetch user data from the database
      // Example: const user = await ctx.db.select().from(users).where(eq(users.id, input.userId));

      if (input.userId !== ctx.userId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'You can only access your own user information',
        });
      }

      return {
        id: ctx.userId,
        name: ctx.user?.name || 'Demo User',
        email: ctx.user?.email || '<EMAIL>',
        roles: [], // TODO: Implement role system
        isAuthenticated: ctx.isAuthenticated,
        lastLogin: new Date().toISOString(),
        metadata: {
          country: ctx.country,
          userAgent: ctx.userAgent,
          ip: ctx.ip,
        },
      };
    }),

  // Enhanced user profile endpoint (requires authentication)
  getUserProfile: protectedProcedure
    .query(({ ctx }) => {
      // Return comprehensive user profile information
      // In a real application, this would fetch additional data from the database
      return {
        id: ctx.userId,
        email: ctx.user?.email || '',
        name: ctx.user?.name || '',
        avatar: ctx.user?.avatar || null,
        emailVerified: ctx.user?.emailVerified || false,
        createdAt: ctx.user?.createdAt?.toISOString() || new Date().toISOString(),
        updatedAt: ctx.user?.updatedAt?.toISOString() || new Date().toISOString(),
        lastLoginAt: new Date().toISOString(),
        preferences: {
          theme: 'system' as const,
          language: 'en',
          timezone: 'UTC',
          notifications: {
            email: true,
            push: true,
            marketing: false,
          },
          privacy: {
            profileVisibility: 'public' as const,
            showEmail: false,
            showLastSeen: true,
          },
        },
        metadata: {
          totalLogins: 42, // Would come from database
          lastLoginIp: ctx.ip,
          lastLoginCountry: ctx.country,
          lastLoginUserAgent: ctx.userAgent,
          accountStatus: 'active' as const,
          roles: [],
          permissions: [],
        },
      };
    }),

  // Get user dashboard data (requires authentication)
  getDashboard: protectedProcedure
    .query(({ ctx }) => {
      // Return dashboard data with stats, activity, and recommendations
      // In a real application, this would aggregate data from multiple database tables
      return {
        user: {
          id: ctx.userId,
          email: ctx.user?.email || '',
          name: ctx.user?.name || '',
          avatar: ctx.user?.avatar || null,
          emailVerified: ctx.user?.emailVerified || false,
          createdAt: ctx.user?.createdAt?.toISOString() || new Date().toISOString(),
          updatedAt: ctx.user?.updatedAt?.toISOString() || new Date().toISOString(),
          preferences: {
            theme: 'system' as const,
            language: 'en',
            timezone: 'UTC',
            notifications: { email: true, push: true, marketing: false },
            privacy: { profileVisibility: 'public' as const, showEmail: false, showLastSeen: true },
          },
          metadata: {
            totalLogins: 42,
            accountStatus: 'active' as const,
            roles: [],
            permissions: [],
          },
        },
        stats: {
          coursesEnrolled: 5,
          coursesCompleted: 2,
          totalLearningTime: 1440, // 24 hours in minutes
          currentStreak: 7, // 7 days
          achievements: [
            {
              id: 'first_course',
              title: 'First Steps',
              description: 'Completed your first course',
              icon: '🎯',
              unlockedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
              rarity: 'common' as const,
            },
            {
              id: 'week_streak',
              title: 'Week Warrior',
              description: 'Maintained a 7-day learning streak',
              icon: '🔥',
              unlockedAt: new Date().toISOString(),
              rarity: 'rare' as const,
            },
          ],
        },
        recentActivity: [
          {
            id: 'activity_1',
            type: 'achievement_unlocked' as const,
            title: 'Achievement Unlocked: Week Warrior',
            description: 'You maintained a 7-day learning streak!',
            timestamp: new Date().toISOString(),
            metadata: { achievementId: 'week_streak' },
          },
          {
            id: 'activity_2',
            type: 'lesson_completed' as const,
            title: 'Completed: Advanced TypeScript Patterns',
            description: 'Lesson 3 of TypeScript Mastery course',
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            metadata: { courseId: 'typescript_mastery', lessonId: 'lesson_3' },
          },
        ],
        recommendations: [
          {
            id: 'course_react_advanced',
            title: 'Advanced React Patterns',
            description: 'Master advanced React concepts and patterns',
            thumbnail: 'https://example.com/react-course.jpg',
            difficulty: 'advanced' as const,
            estimatedDuration: 480, // 8 hours
            rating: 4.8,
            enrollmentCount: 1250,
            tags: ['React', 'JavaScript', 'Frontend'],
            reason: 'Based on your TypeScript progress',
          },
        ],
      };
    }),

  // Update user preferences (requires authentication)
  updatePreferences: protectedProcedure
    .input(z.object({
      theme: z.enum(['light', 'dark', 'system']).optional(),
      language: z.string().optional(),
      timezone: z.string().optional(),
      notifications: z.object({
        email: z.boolean().optional(),
        push: z.boolean().optional(),
        marketing: z.boolean().optional(),
      }).optional(),
      privacy: z.object({
        profileVisibility: z.enum(['public', 'private', 'friends']).optional(),
        showEmail: z.boolean().optional(),
        showLastSeen: z.boolean().optional(),
      }).optional(),
    }))
    .mutation(({ input, ctx }) => {
      // In a real application, this would update the user preferences in the database
      console.log(`Updating preferences for user ${ctx.userId}:`, input);

      // Return updated preferences
      return {
        theme: input.theme || 'system',
        language: input.language || 'en',
        timezone: input.timezone || 'UTC',
        notifications: {
          email: input.notifications?.email ?? true,
          push: input.notifications?.push ?? true,
          marketing: input.notifications?.marketing ?? false,
        },
        privacy: {
          profileVisibility: input.privacy?.profileVisibility || 'public',
          showEmail: input.privacy?.showEmail ?? false,
          showLastSeen: input.privacy?.showLastSeen ?? true,
        },
      };
    }),

  // Get current authenticated user (simplified version)
  me: protectedProcedure
    .query(({ ctx }) => {
      return {
        id: ctx.userId,
        name: ctx.user?.name || 'Demo User',
        email: ctx.user?.email || '<EMAIL>',
        roles: [], // TODO: Implement role system
        isAuthenticated: ctx.isAuthenticated,
      };
    }),

  // Update user profile (protected mutation)
  updateUser: protectedProcedure
    .input(updateUserInput)
    .mutation(({ input, ctx }) => {
      // In a real application, this would update the user in the database
      // Example: await ctx.db.update(users).set(input).where(eq(users.id, ctx.userId));

      return {
        id: ctx.userId,
        name: input.name || ctx.user?.name || 'Demo User',
        email: input.email || ctx.user?.email || '<EMAIL>',
        updatedAt: new Date().toISOString(),
        message: 'User profile updated successfully',
      };
    }),

  // Example greeting endpoint with enhanced context
  greeting: publicProcedure
    .input(
      z.object({
        name: z.string().min(1, 'Name is required'),
      })
    )
    .query(({ input, ctx }) => {
      return {
        message: `Hello, ${input.name}!`,
        userAgent: ctx.userAgent,
        timestamp: ctx.timestamp,
        country: ctx.country,
        isAuthenticated: ctx.isAuthenticated,
        requestId: ctx.requestId,
      };
    }),

  // Example mutation endpoint with enhanced validation
  echo: publicProcedure
    .input(
      z.object({
        message: z.string().min(1, 'Message cannot be empty').max(1000, 'Message too long'),
      })
    )
    .mutation(({ input, ctx }) => {
      return {
        echo: input.message,
        timestamp: ctx.timestamp,
        requestId: ctx.requestId,
        metadata: {
          userAgent: ctx.userAgent,
          country: ctx.country,
        },
      };
    }),

  // Admin-only procedure example
  adminStats: adminProcedure
    .query(({ ctx }) => {
      return {
        message: 'Admin statistics',
        timestamp: ctx.timestamp,
        requestId: ctx.requestId,
        adminUser: ctx.userId,
        // In a real app, this would return actual admin statistics
        stats: {
          totalUsers: 1000,
          activeUsers: 250,
          totalRequests: 50000,
        },
      };
    }),

  // Templates management
  templates: templatesRouter,

  // Learning content management
  learningContent: learningContentRouter,

  // Learning progress tracking
  learningProgress: learningProgressRouter,

  // Learning content feedback
  learningFeedback: learningFeedbackRouter,

  // Release notifications management
  releases: releasesRouter,

  // Quiz management
  quiz: quizRouter,
});

/**
 * Export the router type for client-side type safety
 */
export type AppRouter = typeof appRouter;
