/**
 * Main exports for the tRPC library
 * This file exports all the public APIs that can be used by other applications
 */

// Export the main router and its type
export { appRouter, type AppRouter } from './router';

// Export context creation utilities and types
export {
  createContext,
  type Context,
  type CreateContextOptions,
  type UserSession,
  type RequestMetadata,
} from './context';

// Export tRPC building blocks for extending the router
export {
  router,
  publicProcedure,
  protectedProcedure,
  adminProcedure
} from './router';
