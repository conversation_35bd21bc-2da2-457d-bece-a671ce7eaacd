{"version": 3, "file": "router.js", "sourceRoot": "", "sources": ["router.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAC;AACxC,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AAGxB;;GAEG;AACH,MAAM,CAAC,GAAG,QAAQ,CAAC,OAAO,EAAW,CAAC,MAAM,CAAC;IAC3C,cAAc,CAAC,EAAE,KAAK,EAAE;QACtB,OAAO,KAAK,CAAC;IACf,CAAC;CACF,CAAC,CAAC;AAEH;;GAEG;AACH,MAAM,CAAC,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;AAC/B,MAAM,CAAC,MAAM,eAAe,GAAG,CAAC,CAAC,SAAS,CAAC;AAE3C;;GAEG;AACH,MAAM,CAAC,MAAM,SAAS,GAAG,MAAM,CAAC;IAC9B,wBAAwB;IACxB,MAAM,EAAE,eAAe;SACpB,KAAK,CAAC,GAAG,EAAE;QACV,OAAO;YACL,MAAM,EAAE,IAAI;YACZ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,OAAO,EAAE,8CAA8C;SACxD,CAAC;IACJ,CAAC,CAAC;IAEJ,kDAAkD;IAClD,QAAQ,EAAE,eAAe;SACtB,KAAK,CACJ,CAAC,CAAC,MAAM,CAAC;QACP,IAAI,EAAE,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,kBAAkB,CAAC;KAC5C,CAAC,CACH;SACA,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE;QACxB,OAAO;YACL,OAAO,EAAE,UAAU,KAAK,CAAC,IAAI,GAAG;YAChC,SAAS,EAAE,GAAG,CAAC,SAAS;YACxB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;IACJ,CAAC,CAAC;IAEJ,4BAA4B;IAC5B,IAAI,EAAE,eAAe;SAClB,KAAK,CACJ,CAAC,CAAC,MAAM,CAAC;QACP,OAAO,EAAE,CAAC,CAAC,MAAM,EAAE;KACpB,CAAC,CACH;SACA,QAAQ,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE;QACtB,OAAO;YACL,IAAI,EAAE,KAAK,CAAC,OAAO;YACnB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;IACJ,CAAC,CAAC;CACL,CAAC,CAAC"}