/**
 * tRPC context creation for Cloudflare Workers
 * This context will be available in all tRPC procedures
 */
/**
 * Creates the tRPC context from the incoming request
 * This is called for every request to the tRPC server
 */
export function createContext(opts) {
    const { request, env } = opts;
    // Extract useful information from the request
    const url = new URL(request.url);
    const userAgent = request.headers.get('user-agent') || '';
    return {
        request,
        env,
        url,
        userAgent,
        // Add more context properties as needed
        // For example: user authentication, database connections, etc.
    };
}
