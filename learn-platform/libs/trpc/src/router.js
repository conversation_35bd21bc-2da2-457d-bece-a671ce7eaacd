import { initTRPC } from '@trpc/server';
import { z } from 'zod';
/**
 * Initialize tRPC with context
 */
const t = initTRPC.context().create({
    errorFormatter({ shape }) {
        return shape;
    },
});
/**
 * Export reusable router and procedure helpers
 */
export const router = t.router;
export const publicProcedure = t.procedure;
/**
 * Example procedures - replace with your actual API endpoints
 */
export const appRouter = router({
    // Health check endpoint
    health: publicProcedure
        .query(() => {
        return {
            status: 'ok',
            timestamp: new Date().toISOString(),
            message: 'tRPC server is running on Cloudflare Workers',
        };
    }),
    // Example greeting endpoint with input validation
    greeting: publicProcedure
        .input(z.object({
        name: z.string().min(1, 'Name is required'),
    }))
        .query(({ input, ctx }) => {
        return {
            message: `Hello, ${input.name}!`,
            userAgent: ctx.userAgent,
            timestamp: new Date().toISOString(),
        };
    }),
    // Example mutation endpoint
    echo: publicProcedure
        .input(z.object({
        message: z.string(),
    }))
        .mutation(({ input }) => {
        return {
            echo: input.message,
            timestamp: new Date().toISOString(),
        };
    }),
});
