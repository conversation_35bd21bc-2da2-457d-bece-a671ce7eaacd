/**
 * Comprehensive test suite for tRPC router
 * Tests all procedures, authentication, and error handling
 */

import { TRPCError } from '@trpc/server';
import { appRouter } from './router';
import { createContext } from './context';

// Mock the auth library
jest.mock('@learn-platform/auth', () => ({
  createAuth: jest.fn(() => ({
    api: {
      signUpEmail: jest.fn(),
      signInEmail: jest.fn(),
      signOut: jest.fn(),
    },
  })),
  getSession: jest.fn(),
  createAuthContext: jest.fn(),
  createAuthContextWithEnv: jest.fn(),
}));

describe('tRPC Router', () => {
  let mockContext: any;
  let mockAuthenticatedContext: any;
  let caller: any;
  let authenticatedCaller: any;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock unauthenticated context
    mockContext = {
      request: new Request('http://localhost/test'),
      env: {},
      url: new URL('http://localhost/test'),
      ip: '127.0.0.1',
      country: 'US',
      userAgent: 'test-agent',
      timestamp: '2024-01-01T00:00:00.000Z',
      requestId: 'test-request-id',
      session: null,
      isAuthenticated: false,
      userId: null,
      user: null,
      userSession: {
        isAuthenticated: false,
        userId: null,
        roles: [],
      },
      getUserId: () => null,
      getUserRoles: () => [],
      hasRole: () => false,
      startTime: Date.now(),
    };

    // Mock authenticated context
    mockAuthenticatedContext = {
      ...mockContext,
      session: { id: 'session-123' },
      isAuthenticated: true,
      userId: 'user-123',
      user: {
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User',
      },
      userSession: {
        isAuthenticated: true,
        userId: 'user-123',
        roles: ['user'],
        email: '<EMAIL>',
        name: 'Test User',
      },
      getUserId: () => 'user-123',
      getUserRoles: () => ['user'],
      hasRole: (role: string) => role === 'user',
    };

    caller = appRouter.createCaller(mockContext);
    authenticatedCaller = appRouter.createCaller(mockAuthenticatedContext);
  });

  describe('Public Procedures', () => {
    describe('health', () => {
      it('should return health status', async () => {
        const result = await caller.health();

        expect(result).toEqual({
          status: 'ok',
          timestamp: '2024-01-01T00:00:00.000Z',
          message: 'tRPC server is running on Cloudflare Workers',
          requestId: 'test-request-id',
          country: 'US',
          version: '1.0.0',
        });
      });
    });

    describe('greeting', () => {
      it('should return greeting with context information', async () => {
        const result = await caller.greeting({ name: 'John' });

        expect(result).toEqual({
          message: 'Hello, John!',
          userAgent: 'test-agent',
          timestamp: '2024-01-01T00:00:00.000Z',
          country: 'US',
          isAuthenticated: false,
          requestId: 'test-request-id',
        });
      });

      it('should validate input name', async () => {
        await expect(caller.greeting({ name: '' })).rejects.toThrow();
      });
    });

    describe('echo', () => {
      it('should echo message with metadata', async () => {
        const result = await caller.echo({ message: 'Hello World' });

        expect(result).toEqual({
          echo: 'Hello World',
          timestamp: '2024-01-01T00:00:00.000Z',
          requestId: 'test-request-id',
          metadata: {
            userAgent: 'test-agent',
            country: 'US',
          },
        });
      });

      it('should validate message length', async () => {
        await expect(caller.echo({ message: '' })).rejects.toThrow();

        const longMessage = 'a'.repeat(1001);
        await expect(caller.echo({ message: longMessage })).rejects.toThrow();
      });
    });
  });

  describe('Authentication Procedures', () => {
    describe('auth.signUp', () => {
      it('should create new user successfully', async () => {
        const { createAuth } = require('@learn-platform/auth');
        const mockAuth = {
          api: {
            signUpEmail: jest.fn().mockResolvedValue({
              user: { id: 'user-123', email: '<EMAIL>', name: 'Test User' },
            }),
          },
        };
        (createAuth as jest.MockedFunction<typeof createAuth>).mockReturnValue(mockAuth);

        const result = await caller.auth.signUp({
          email: '<EMAIL>',
          password: 'password123',
          name: 'Test User',
        });

        expect(result).toEqual({
          success: true,
          message: 'User created successfully',
          user: { id: 'user-123', email: '<EMAIL>', name: 'Test User' },
        });

        expect(mockAuth.api.signUpEmail).toHaveBeenCalledWith({
          body: {
            email: '<EMAIL>',
            password: 'password123',
            name: 'Test User',
            avatar: '', // Avatar field is now included with empty string as default
          },
          headers: mockContext.request.headers,
        });
      });

      it('should handle signup errors', async () => {
        const { createAuth } = require('@learn-platform/auth');
        const mockAuth = {
          api: {
            signUpEmail: jest.fn().mockRejectedValue(new Error('Email already exists')),
          },
        };
        (createAuth as jest.MockedFunction<typeof createAuth>).mockReturnValue(mockAuth);

        await expect(caller.auth.signUp({
          email: '<EMAIL>',
          password: 'password123',
          name: 'Test User',
        })).rejects.toThrow('Email already exists');
      });

      it('should validate input', async () => {
        await expect(caller.auth.signUp({
          email: 'invalid-email',
          password: 'short',
          name: '',
        })).rejects.toThrow();
      });
    });

    describe('auth.signIn', () => {
      it('should sign in user successfully', async () => {
        const { createAuth } = require('@learn-platform/auth');
        const mockAuth = {
          api: {
            signInEmail: jest.fn().mockResolvedValue({
              user: { id: 'user-123', email: '<EMAIL>', name: 'Test User' },
            }),
          },
        };
        (createAuth as jest.MockedFunction<typeof createAuth>).mockReturnValue(mockAuth);

        const result = await caller.auth.signIn({
          email: '<EMAIL>',
          password: 'password123',
        });

        expect(result).toEqual({
          success: true,
          message: 'Signed in successfully',
          user: { id: 'user-123', email: '<EMAIL>', name: 'Test User' },
        });
      });

      it('should handle invalid credentials', async () => {
        const { createAuth } = require('@learn-platform/auth');
        const mockAuth = {
          api: {
            signInEmail: jest.fn().mockRejectedValue(new Error('Invalid credentials')),
          },
        };
        (createAuth as jest.MockedFunction<typeof createAuth>).mockReturnValue(mockAuth);

        await expect(caller.auth.signIn({
          email: '<EMAIL>',
          password: 'wrongpassword',
        })).rejects.toThrow('Invalid credentials');
      });
    });

    describe('auth.getSession', () => {
      it('should return session information for unauthenticated user', async () => {
        const result = await caller.auth.getSession();

        expect(result).toEqual({
          session: null,
          isAuthenticated: false,
          user: null,
        });
      });

      it('should return session information for authenticated user', async () => {
        const result = await authenticatedCaller.auth.getSession();

        expect(result).toEqual({
          session: { id: 'session-123' },
          isAuthenticated: true,
          user: {
            id: 'user-123',
            email: '<EMAIL>',
            name: 'Test User',
          },
        });
      });
    });
  });

  describe('Protected Procedures', () => {
    describe('getUser', () => {
      it('should require authentication', async () => {
        await expect(caller.getUser({ userId: 'user-123' })).rejects.toThrow('You must be logged in to access this resource');
      });

      it('should return user information for authenticated user', async () => {
        const result = await authenticatedCaller.getUser({ userId: 'user-123' });

        expect(result).toEqual({
          id: 'user-123',
          name: 'Test User',
          email: '<EMAIL>',
          roles: [],
          isAuthenticated: true,
          lastLogin: expect.any(String),
          metadata: {
            country: 'US',
            userAgent: 'test-agent',
            ip: '127.0.0.1',
          },
        });
      });

      it('should prevent accessing other users data', async () => {
        await expect(authenticatedCaller.getUser({ userId: 'other-user' })).rejects.toThrow('You can only access your own user information');
      });
    });

    describe('me', () => {
      it('should require authentication', async () => {
        await expect(caller.me()).rejects.toThrow('You must be logged in to access this resource');
      });

      it('should return current user information', async () => {
        const result = await authenticatedCaller.me();

        expect(result).toEqual({
          id: 'user-123',
          name: 'Test User',
          email: '<EMAIL>',
          roles: [],
          isAuthenticated: true,
        });
      });
    });

    describe('updateUser', () => {
      it('should require authentication', async () => {
        await expect(caller.updateUser({ name: 'New Name' })).rejects.toThrow('You must be logged in to access this resource');
      });

      it('should update user information', async () => {
        const result = await authenticatedCaller.updateUser({
          name: 'Updated Name',
          email: '<EMAIL>',
        });

        expect(result).toEqual({
          id: 'user-123',
          name: 'Updated Name',
          email: '<EMAIL>',
          updatedAt: expect.any(String),
          message: 'User profile updated successfully',
        });
      });
    });

    describe('auth.signOut', () => {
      it('should require authentication', async () => {
        await expect(caller.auth.signOut()).rejects.toThrow('You must be logged in to access this resource');
      });

      it('should sign out authenticated user', async () => {
        const { createAuth } = require('@learn-platform/auth');
        const mockAuth = {
          api: {
            signOut: jest.fn().mockResolvedValue({}),
          },
        };
        (createAuth as jest.MockedFunction<typeof createAuth>).mockReturnValue(mockAuth);

        const result = await authenticatedCaller.auth.signOut();

        expect(result).toEqual({
          success: true,
          message: 'Signed out successfully',
        });

        expect(mockAuth.api.signOut).toHaveBeenCalledWith({
          headers: mockAuthenticatedContext.request.headers,
        });
      });
    });
  });

  describe('Admin Procedures', () => {
    describe('adminStats', () => {
      it('should require authentication', async () => {
        await expect(caller.adminStats()).rejects.toThrow('You must be logged in to access this resource');
      });

      it('should return admin statistics for authenticated user', async () => {
        // Note: Current implementation doesn't check for admin role
        const result = await authenticatedCaller.adminStats();

        expect(result).toEqual({
          message: 'Admin statistics',
          timestamp: '2024-01-01T00:00:00.000Z',
          requestId: 'test-request-id',
          adminUser: 'user-123',
          stats: {
            totalUsers: 1000,
            activeUsers: 250,
            totalRequests: 50000,
          },
        });
      });
    });
  });

  describe('Error Handling', () => {
    it('should format errors properly', async () => {
      try {
        await caller.getUser({ userId: 'user-123' });
      } catch (error: any) {
        expect(error).toBeInstanceOf(TRPCError);
        expect(error.code).toBe('UNAUTHORIZED');
        expect(error.message).toBe('You must be logged in to access this resource');
      }
    });
  });
});
