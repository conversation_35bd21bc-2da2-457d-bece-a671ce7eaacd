/**
 * Test suite for tRPC context creation
 * Tests context creation, authentication integration, and metadata extraction
 */

import { createContext } from './context';

// Mock the auth library
jest.mock('@learn-platform/auth', () => ({
  createAuthContext: jest.fn(),
  createAuthContextWithEnv: jest.fn(),
}));

describe('tRPC Context', () => {
  let mockRequest: Request;
  let mockEnv: any;

  beforeEach(() => {
    jest.clearAllMocks();

    // Create mock request with headers
    mockRequest = new Request('http://localhost:8787/trpc/health', {
      method: 'GET',
      headers: {
        'CF-Connecting-IP': '***********',
        'CF-IPCountry': 'US',
        'user-agent': 'Mozilla/5.0 (Test Browser)',
        'CF-Ray': 'test-ray-id-123',
      },
    });

    mockEnv = {
      BETTER_AUTH_SECRET: 'test-secret',
      BETTER_AUTH_URL: 'http://localhost:8787',
    };
  });

  describe('createContext', () => {
    it('should create context with request metadata', async () => {
      const { createAuthContextWithEnv } = require('@learn-platform/auth');
      createAuthContextWithEnv.mockResolvedValue({
        session: null,
        isAuthenticated: false,
        userId: null,
        user: null,
      });

      const context = await createContext({
        request: mockRequest,
        env: mockEnv,
      });

      expect(context).toMatchObject({
        request: mockRequest,
        env: mockEnv,
        url: new URL('http://localhost:8787/trpc/health'),
        ip: '***********',
        country: 'US',
        userAgent: 'Mozilla/5.0 (Test Browser)',
        requestId: 'test-ray-id-123',
        timestamp: expect.any(String),
        session: null,
        isAuthenticated: false,
        userId: null,
        user: null,
        startTime: expect.any(Number),
      });
    });

    it('should extract IP from different headers', async () => {
      const { createAuthContext } = require('@learn-platform/auth');
      createAuthContext.mockResolvedValue({
        session: null,
        isAuthenticated: false,
        userId: null,
        user: null,
      });

      // Test X-Forwarded-For header
      const requestWithXFF = new Request('http://localhost:8787/test', {
        headers: {
          'X-Forwarded-For': '***********',
        },
      });

      const context = await createContext({
        request: requestWithXFF,
      });

      expect(context.ip).toBe('***********');
    });

    it('should handle missing headers gracefully', async () => {
      const { createAuthContext } = require('@learn-platform/auth');
      createAuthContext.mockResolvedValue({
        session: null,
        isAuthenticated: false,
        userId: null,
        user: null,
      });

      const requestWithoutHeaders = new Request('http://localhost:8787/test');

      const context = await createContext({
        request: requestWithoutHeaders,
      });

      expect(context).toMatchObject({
        ip: 'unknown',
        country: 'unknown',
        userAgent: '',
        requestId: expect.any(String), // Should generate UUID
      });
    });

    it('should use createAuthContextWithEnv when env is provided', async () => {
      const { createAuthContextWithEnv } = require('@learn-platform/auth');
      createAuthContextWithEnv.mockResolvedValue({
        session: { id: 'session-123' },
        isAuthenticated: true,
        userId: 'user-123',
        user: {
          id: 'user-123',
          email: '<EMAIL>',
          name: 'Test User',
        },
      });

      const context = await createContext({
        request: mockRequest,
        env: mockEnv,
      });

      expect(createAuthContextWithEnv).toHaveBeenCalledWith(mockRequest, mockEnv);
      expect(context.isAuthenticated).toBe(true);
      expect(context.userId).toBe('user-123');
      expect(context.user).toEqual({
        id: 'user-123',
        email: '<EMAIL>',
        name: 'Test User',
      });
    });

    it('should use createAuthContext when env is not provided', async () => {
      const { createAuthContext } = require('@learn-platform/auth');
      createAuthContext.mockResolvedValue({
        session: null,
        isAuthenticated: false,
        userId: null,
        user: null,
      });

      const context = await createContext({
        request: mockRequest,
      });

      expect(createAuthContext).toHaveBeenCalledWith(mockRequest);
      expect(context.isAuthenticated).toBe(false);
    });

    it('should create userSession for backward compatibility', async () => {
      const { createAuthContextWithEnv } = require('@learn-platform/auth');
      createAuthContextWithEnv.mockResolvedValue({
        session: { id: 'session-123' },
        isAuthenticated: true,
        userId: 'user-123',
        user: {
          id: 'user-123',
          email: '<EMAIL>',
          name: 'Test User',
        },
      });

      const context = await createContext({
        request: mockRequest,
        env: mockEnv,
      });

      expect(context.userSession).toEqual({
        isAuthenticated: true,
        userId: 'user-123',
        roles: [],
        email: '<EMAIL>',
        name: 'Test User',
      });
    });

    it('should provide utility functions', async () => {
      const { createAuthContextWithEnv } = require('@learn-platform/auth');
      createAuthContextWithEnv.mockResolvedValue({
        session: { id: 'session-123' },
        isAuthenticated: true,
        userId: 'user-123',
        user: {
          id: 'user-123',
          email: '<EMAIL>',
          name: 'Test User',
        },
      });

      const context = await createContext({
        request: mockRequest,
        env: mockEnv,
      });

      expect(context.getUserId()).toBe('user-123');
      expect(context.getUserRoles()).toEqual([]);
      expect(context.hasRole('admin')).toBe(false);
    });

    it('should generate UUID for requestId when CF-Ray is not available', async () => {
      const { createAuthContext } = require('@learn-platform/auth');
      createAuthContext.mockResolvedValue({
        session: null,
        isAuthenticated: false,
        userId: null,
        user: null,
      });

      // Mock crypto.randomUUID
      const mockUUID = 'mock-uuid-123';
      global.crypto = {
        randomUUID: jest.fn().mockReturnValue(mockUUID),
      } as any;

      const requestWithoutRay = new Request('http://localhost:8787/test');

      const context = await createContext({
        request: requestWithoutRay,
      });

      expect(context.requestId).toBe(mockUUID);
      expect(crypto.randomUUID).toHaveBeenCalled();
    });

    it('should handle X-Request-ID header', async () => {
      const { createAuthContext } = require('@learn-platform/auth');
      createAuthContext.mockResolvedValue({
        session: null,
        isAuthenticated: false,
        userId: null,
        user: null,
      });

      const requestWithXRequestId = new Request('http://localhost:8787/test', {
        headers: {
          'X-Request-ID': 'custom-request-id-456',
        },
      });

      const context = await createContext({
        request: requestWithXRequestId,
      });

      expect(context.requestId).toBe('custom-request-id-456');
    });

    it('should set timestamp as ISO string', async () => {
      const { createAuthContext } = require('@learn-platform/auth');
      createAuthContext.mockResolvedValue({
        session: null,
        isAuthenticated: false,
        userId: null,
        user: null,
      });

      const context = await createContext({
        request: mockRequest,
      });

      expect(context.timestamp).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/);
      expect(new Date(context.timestamp)).toBeInstanceOf(Date);
    });

    it('should set startTime for performance monitoring', async () => {
      const { createAuthContext } = require('@learn-platform/auth');
      createAuthContext.mockResolvedValue({
        session: null,
        isAuthenticated: false,
        userId: null,
        user: null,
      });

      const beforeTime = Date.now();
      const context = await createContext({
        request: mockRequest,
      });
      const afterTime = Date.now();

      expect(context.startTime).toBeGreaterThanOrEqual(beforeTime);
      expect(context.startTime).toBeLessThanOrEqual(afterTime);
    });
  });

  describe('Context Type', () => {
    it('should have correct TypeScript types', async () => {
      const { createAuthContext } = require('@learn-platform/auth');
      createAuthContext.mockResolvedValue({
        session: null,
        isAuthenticated: false,
        userId: null,
        user: null,
      });

      const context = await createContext({
        request: mockRequest,
        env: mockEnv,
      });

      // Type assertions to ensure TypeScript compatibility
      expect(typeof context.request).toBe('object');
      expect(typeof context.env).toBe('object');
      expect(typeof context.url).toBe('object');
      expect(typeof context.ip).toBe('string');
      expect(typeof context.country).toBe('string');
      expect(typeof context.userAgent).toBe('string');
      expect(typeof context.timestamp).toBe('string');
      expect(typeof context.requestId).toBe('string');
      expect(typeof context.isAuthenticated).toBe('boolean');
      expect(typeof context.getUserId).toBe('function');
      expect(typeof context.getUserRoles).toBe('function');
      expect(typeof context.hasRole).toBe('function');
      expect(typeof context.startTime).toBe('number');
    });
  });
});
