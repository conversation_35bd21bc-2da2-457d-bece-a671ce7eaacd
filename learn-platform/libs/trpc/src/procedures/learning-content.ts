/**
 * tRPC procedures for learning content management
 * Handles user-generated learning content (separate from admin templates)
 */

import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { router, publicProcedure, protectedProcedure } from '../router';
import { createDatabaseConnectionFromEnv } from '@learn-platform/db';
import {
  learningContent,
  learningProgress,
  eq,
  and,
  or,
  type LearningContent,
} from '@learn-platform/db';
import { desc, gte, lte, ilike } from 'drizzle-orm';

// Validation schemas
const stepSchema = z.object({
  id: z.string(),
  title: z.string(),
  icon: z.string(),
  blocks: z.array(
    z.object({
      id: z.string(),
      type: z.string(),
      data: z.any(),
      isEditing: z.boolean().optional(),
    })
  ),
});

const createLearningContentSchema = z.object({
  title: z.string().min(1).max(200),
  description: z.string().max(1000).optional(),
  steps: z.array(stepSchema).min(1).max(15),
  learningLevel: z.enum(['beginner', 'intermediate', 'advanced']),
  estimatedReadingTime: z.number().min(1).max(300),
  isPublic: z.boolean().default(false),
  tags: z.array(z.string()).default([]),
  aiMetadata: z
    .object({
      aiModel: z.string().optional(),
      generatedAt: z.string().optional(),
      contentTypes: z.array(z.string()).optional(),
      originalPrompt: z.string().optional(),
    })
    .optional(),
});

const updateLearningContentSchema = z.object({
  id: z.string(),
  title: z.string().min(1).max(200).optional(),
  description: z.string().max(1000).optional(),
  steps: z.array(stepSchema).optional(),
  learningLevel: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
  estimatedReadingTime: z.number().min(1).max(300).optional(),
  isPublic: z.boolean().optional(),
  tags: z.array(z.string()).optional(),
});

const getLearningContentSchema = z.object({
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0),
  learningLevel: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
  isPublic: z.boolean().optional(),
  tags: z.array(z.string()).optional(),
  search: z.string().optional(),
  dateRange: z
    .object({
      from: z.string().optional(),
      to: z.string().optional(),
    })
    .optional(),
  contentTypes: z.array(z.string()).optional(),
  readingTimeRange: z
    .object({
      min: z.number().optional(),
      max: z.number().optional(),
    })
    .optional(),
});

// AI generation input schema
const aiGenerationInputSchema = z.object({
  topic: z.string().min(3).max(200),
  learningLevel: z.enum(['beginner', 'intermediate', 'advanced']),
  preferredContentTypes: z.array(z.string()).min(1),
  focusAreas: z.string().max(500).optional(),
});

// Transform AI-generated content to database format
function transformAIContentToDBFormat(aiContent: any): any[] {
  return aiContent.steps.map((step: any) => ({
    id: crypto.randomUUID(),
    title: step.title,
    icon: step.icon,
    blocks: [
      {
        id: crypto.randomUUID(),
        type: step.type,
        data: step.data,
        isEditing: false,
      },
    ],
  }));
}

/**
 * Learning content router with CRUD operations
 */
export const learningContentRouter = router({
  /**
   * Get learning content with filtering and pagination
   * Public content visible to all, private content only to owner
   */
  getAll: publicProcedure
    .input(getLearningContentSchema)
    .query(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        // Build where conditions
        const whereConditions = [];

        // If user is authenticated, show their private content + all public content
        // If not authenticated, only show public content
        if (ctx.isAuthenticated) {
          whereConditions.push(
            or(
              eq(learningContent.isPublic, true),
              eq(learningContent.userId, ctx.userId!)
            )
          );
        } else {
          whereConditions.push(eq(learningContent.isPublic, true));
        }

        // Add filters
        if (input.learningLevel) {
          whereConditions.push(
            eq(learningContent.learningLevel, input.learningLevel)
          );
        }

        if (input.isPublic !== undefined) {
          whereConditions.push(eq(learningContent.isPublic, input.isPublic));
        }

        const content = await db
          .select({
            id: learningContent.id,
            title: learningContent.title,
            description: learningContent.description,
            learningLevel: learningContent.learningLevel,
            estimatedReadingTime: learningContent.estimatedReadingTime,
            isPublic: learningContent.isPublic,
            tags: learningContent.tags,
            userId: learningContent.userId,
            createdAt: learningContent.createdAt,
            updatedAt: learningContent.updatedAt,
          })
          .from(learningContent)
          .where(and(...whereConditions))
          .orderBy(desc(learningContent.updatedAt))
          .limit(input.limit)
          .offset(input.offset);

        return {
          success: true,
          content,
          pagination: {
            limit: input.limit,
            offset: input.offset,
            hasMore: content.length === input.limit,
          },
        };
      } catch (error) {
        console.error('Error fetching learning content:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch learning content',
        });
      }
    }),

  /**
   * Get specific learning content by ID
   */
  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        const content = await db
          .select()
          .from(learningContent)
          .where(eq(learningContent.id, input.id))
          .limit(1);

        if (!content.length) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Learning content not found',
          });
        }

        const contentItem = content[0];

        // Check if user can access this content
        if (!contentItem.isPublic && contentItem.userId !== ctx.userId) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'Access denied to private content',
          });
        }

        return {
          success: true,
          content: contentItem,
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        console.error('Error fetching learning content:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch learning content',
        });
      }
    }),

  /**
   * Create new learning content
   */
  create: protectedProcedure
    .input(createLearningContentSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        const contentId = crypto.randomUUID();
        const now = new Date();

        const newContent = {
          id: contentId,
          title: input.title,
          description: input.description || '',
          steps: input.steps,
          learningLevel: input.learningLevel,
          estimatedReadingTime: input.estimatedReadingTime,
          isPublic: input.isPublic,
          tags: input.tags,
          aiMetadata: input.aiMetadata || null,
          userId: ctx.userId!,
          createdAt: now,
          updatedAt: now,
        };

        await db.insert(learningContent).values(newContent);

        return {
          success: true,
          content: newContent,
          message: 'Learning content created successfully',
        };
      } catch (error) {
        console.error('Error creating learning content:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to create learning content',
        });
      }
    }),

  /**
   * Update existing learning content
   */
  update: protectedProcedure
    .input(updateLearningContentSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        // First verify the content exists and belongs to the user
        const existingContent = await db
          .select()
          .from(learningContent)
          .where(
            and(
              eq(learningContent.id, input.id),
              eq(learningContent.userId, ctx.userId!)
            )
          )
          .limit(1);

        if (!existingContent.length) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Learning content not found',
          });
        }

        // Prepare update data
        const updateData: any = {
          updatedAt: new Date(),
        };

        if (input.title !== undefined) updateData.title = input.title;
        if (input.description !== undefined)
          updateData.description = input.description;
        if (input.steps !== undefined) updateData.steps = input.steps;
        if (input.learningLevel !== undefined)
          updateData.learningLevel = input.learningLevel;
        if (input.estimatedReadingTime !== undefined)
          updateData.estimatedReadingTime = input.estimatedReadingTime;
        if (input.isPublic !== undefined) updateData.isPublic = input.isPublic;
        if (input.tags !== undefined) updateData.tags = input.tags;

        await db
          .update(learningContent)
          .set(updateData)
          .where(eq(learningContent.id, input.id));

        return {
          success: true,
          message: 'Learning content updated successfully',
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        console.error('Error updating learning content:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to update learning content',
        });
      }
    }),

  /**
   * Delete learning content
   */
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        // First verify the content exists and belongs to the user
        const existingContent = await db
          .select()
          .from(learningContent)
          .where(
            and(
              eq(learningContent.id, input.id),
              eq(learningContent.userId, ctx.userId!)
            )
          )
          .limit(1);

        if (!existingContent.length) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Learning content not found',
          });
        }

        await db
          .delete(learningContent)
          .where(eq(learningContent.id, input.id));

        return {
          success: true,
          message: 'Learning content deleted successfully',
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        console.error('Error deleting learning content:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to delete learning content',
        });
      }
    }),

  /**
   * Get user's own learning content
   */
  getMy: protectedProcedure
    .input(getLearningContentSchema.omit({ isPublic: true }))
    .query(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        const whereConditions = [eq(learningContent.userId, ctx.userId!)];

        // Learning level filter
        if (input.learningLevel) {
          whereConditions.push(
            eq(learningContent.learningLevel, input.learningLevel)
          );
        }

        // Search filter (title and description)
        if (input.search) {
          const searchCondition = or(
            // Note: Using LIKE for basic search - in production you might want to use full-text search
            ilike(learningContent.title, `%${input.search}%`),
            ilike(learningContent.description, `%${input.search}%`)
          );
          if (searchCondition) {
            whereConditions.push(searchCondition);
          }
        }

        // Date range filter
        if (input.dateRange?.from) {
          whereConditions.push(
            gte(learningContent.createdAt, new Date(input.dateRange.from))
          );
        }
        if (input.dateRange?.to) {
          const toDate = new Date(input.dateRange.to);
          toDate.setHours(23, 59, 59, 999); // End of day
          whereConditions.push(lte(learningContent.createdAt, toDate));
        }

        // Reading time range filter
        if (input.readingTimeRange?.min !== undefined) {
          whereConditions.push(
            gte(
              learningContent.estimatedReadingTime,
              input.readingTimeRange.min
            )
          );
        }
        if (input.readingTimeRange?.max !== undefined) {
          whereConditions.push(
            lte(
              learningContent.estimatedReadingTime,
              input.readingTimeRange.max
            )
          );
        }

        // Tags filter (if tags are provided, content must have at least one matching tag)
        // Note: For now, we'll skip complex tag filtering in the database query
        // and handle it in post-processing to avoid SQL complexity issues

        // Join learning content with progress data
        // Note: Using regular select() instead of selectDistinct() because JSON columns
        // don't have equality operators in PostgreSQL. The unique constraint on
        // learningProgress (contentId, userId) ensures no duplicates.
        const contentWithProgress = await db
          .select({
            // Learning content fields
            id: learningContent.id,
            title: learningContent.title,
            description: learningContent.description,
            steps: learningContent.steps,
            learningLevel: learningContent.learningLevel,
            estimatedReadingTime: learningContent.estimatedReadingTime,
            isPublic: learningContent.isPublic,
            tags: learningContent.tags,
            aiMetadata: learningContent.aiMetadata,
            userId: learningContent.userId,
            createdAt: learningContent.createdAt,
            updatedAt: learningContent.updatedAt,
            // Progress fields (nullable)
            progressId: learningProgress.id,
            currentStepIndex: learningProgress.currentStepIndex,
            completedSteps: learningProgress.completedSteps,
            totalTimeSpent: learningProgress.totalTimeSpent,
            completionPercentage: learningProgress.completionPercentage,
            isCompleted: learningProgress.isCompleted,
            lastAccessedAt: learningProgress.lastAccessedAt,
          })
          .from(learningContent)
          .leftJoin(
            learningProgress,
            and(
              eq(learningProgress.contentId, learningContent.id),
              eq(learningProgress.userId, ctx.userId!)
            )
          )
          .where(and(...whereConditions))
          .orderBy(desc(learningContent.updatedAt))
          .limit(input.limit)
          .offset(input.offset);

        // Transform the result to include progress as a nested object
        const content = contentWithProgress.map((item: any) => ({
          id: item.id,
          title: item.title,
          description: item.description,
          steps: item.steps,
          learningLevel: item.learningLevel,
          estimatedReadingTime: item.estimatedReadingTime,
          isPublic: item.isPublic,
          tags: item.tags,
          aiMetadata: item.aiMetadata,
          userId: item.userId,
          createdAt: item.createdAt,
          updatedAt: item.updatedAt,
          // Include progress data if it exists
          progress: item.progressId ? {
            id: item.progressId,
            currentStepIndex: item.currentStepIndex || 0,
            completedSteps: item.completedSteps || [],
            totalTimeSpent: item.totalTimeSpent || 0,
            completionPercentage: item.completionPercentage || 0,
            isCompleted: item.isCompleted || false,
            lastAccessedAt: item.lastAccessedAt,
          } : null,
        }));

        // Post-process for content type and tags filtering (since they're stored in JSON)
        let filteredContent = content;

        // Filter by content types
        if (input.contentTypes && input.contentTypes.length > 0) {
          filteredContent = filteredContent.filter((item: LearningContent) => {
            if (!item.steps || !Array.isArray(item.steps)) return false;

            // Check if any step contains the requested content types
            return item.steps.some((step: any) => {
              if (!step.blocks || !Array.isArray(step.blocks)) return false;
              return step.blocks.some((block: any) =>
                input.contentTypes!.includes(block.type)
              );
            });
          });
        }

        // Filter by tags
        if (input.tags && input.tags.length > 0) {
          filteredContent = filteredContent.filter((item: LearningContent) => {
            if (!item.tags || !Array.isArray(item.tags)) return false;
            // Check if content has at least one matching tag
            return input.tags!.some((tag) =>
              item.tags!.some((contentTag: string) =>
                contentTag.toLowerCase().includes(tag.toLowerCase())
              )
            );
          });
        }

        return {
          success: true,
          content: filteredContent,
          pagination: {
            limit: input.limit,
            offset: input.offset,
            hasMore: content.length === input.limit,
          },
        };
      } catch (error) {
        console.error('Error fetching user learning content:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch user learning content',
        });
      }
    }),

  /**
   * Generate AI-powered learning content and save to database
   */
  generateWithAI: protectedProcedure
    .input(aiGenerationInputSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        // Import AI generation service
        const { generateLearningContent } = await import('@learn-platform/ai');

        // Generate content using AI
        const aiGeneratedContent = await generateLearningContent(
          {
            topic: input.topic,
            learningLevel: input.learningLevel,
            preferredContentTypes: input.preferredContentTypes as any,
            focusAreas: input.focusAreas,
          },
          { model: 'openai/gpt-4o' }
        );

        // Transform AI content to database format
        const dbSteps = transformAIContentToDBFormat(aiGeneratedContent);

        // Save to database
        const { db } = createDatabaseConnectionFromEnv(ctx.env);
        const contentId = crypto.randomUUID();
        const now = new Date();

        const newContent = {
          id: contentId,
          title: aiGeneratedContent.title,
          description: aiGeneratedContent.description,
          steps: dbSteps,
          learningLevel: input.learningLevel,
          estimatedReadingTime: aiGeneratedContent.estimatedReadingTime,
          isPublic: false, // Default to private
          tags: [], // TODO: Extract tags from content
          aiMetadata: {
            aiModel: aiGeneratedContent.metadata.aiModel,
            generatedAt: aiGeneratedContent.metadata.generatedAt,
            contentTypes: aiGeneratedContent.metadata.contentTypes,
            originalPrompt: input.topic,
          },
          userId: ctx.userId!,
          createdAt: now,
          updatedAt: now,
        };

        await db.insert(learningContent).values(newContent);

        return {
          success: true,
          contentId,
          content: newContent,
          message: 'Learning content generated and saved successfully',
        };
      } catch (error) {
        console.error('Error generating AI content:', error);

        // Handle specific AI errors
        if (error instanceof Error) {
          if (error.message.includes('Configuration error')) {
            throw new TRPCError({
              code: 'INTERNAL_SERVER_ERROR',
              message:
                'AI service is not properly configured. Please try again later.',
            });
          }
          if (error.message.includes('rate limit')) {
            throw new TRPCError({
              code: 'TOO_MANY_REQUESTS',
              message:
                'AI service rate limit exceeded. Please try again in a few minutes.',
            });
          }
        }

        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to generate learning content. Please try again.',
        });
      }
    }),

  /**
   * Toggle public/private status of learning content
   */
  togglePublic: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        isPublic: z.boolean(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        // First verify the content exists and belongs to the user
        const existingContent = await db
          .select()
          .from(learningContent)
          .where(
            and(
              eq(learningContent.id, input.id),
              eq(learningContent.userId, ctx.userId!)
            )
          )
          .limit(1);

        if (!existingContent.length) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Learning content not found',
          });
        }

        // Update the public status
        await db
          .update(learningContent)
          .set({
            isPublic: input.isPublic,
            updatedAt: new Date(),
          })
          .where(eq(learningContent.id, input.id));

        return {
          success: true,
          isPublic: input.isPublic,
          message: `Content is now ${input.isPublic ? 'public' : 'private'}`,
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        console.error('Error toggling content visibility:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to update content visibility',
        });
      }
    }),

  /**
   * Duplicate learning content
   */
  duplicate: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        newTitle: z.string().min(1).max(200).optional(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        // First get the original content and verify ownership
        const originalContent = await db
          .select()
          .from(learningContent)
          .where(
            and(
              eq(learningContent.id, input.id),
              eq(learningContent.userId, ctx.userId!)
            )
          )
          .limit(1);

        if (!originalContent.length) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Learning content not found or access denied',
          });
        }

        const original = originalContent[0];
        const contentId = crypto.randomUUID();
        const now = new Date();

        // Create duplicate with new ID and title
        const duplicatedContent = {
          id: contentId,
          title: input.newTitle || `${original.title} (Copy)`,
          description: original.description,
          steps: original.steps,
          learningLevel: original.learningLevel,
          estimatedReadingTime: original.estimatedReadingTime,
          isPublic: false, // Always start as private
          tags: original.tags,
          aiMetadata: original.aiMetadata,
          userId: ctx.userId!,
          createdAt: now,
          updatedAt: now,
        };

        await db.insert(learningContent).values(duplicatedContent);

        return {
          success: true,
          content: duplicatedContent,
          message: 'Learning content duplicated successfully',
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        console.error('Error duplicating learning content:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to duplicate learning content',
        });
      }
    }),

  /**
   * Get learning content statistics for the authenticated user
   */
  getStats: protectedProcedure.query(async ({ ctx }) => {
    try {
      const { db } = createDatabaseConnectionFromEnv(ctx.env);

      // Get all user's content
      const userContent = await db
        .select()
        .from(learningContent)
        .where(eq(learningContent.userId, ctx.userId!));

      // Calculate statistics
      const totalContent = userContent.length;
      const publicContent = userContent.filter(
        (item: LearningContent) => item.isPublic
      ).length;
      const privateContent = totalContent - publicContent;

      // Calculate by learning level
      const beginnerCount = userContent.filter(
        (item: LearningContent) => item.learningLevel === 'beginner'
      ).length;
      const intermediateCount = userContent.filter(
        (item: LearningContent) => item.learningLevel === 'intermediate'
      ).length;
      const advancedCount = userContent.filter(
        (item: LearningContent) => item.learningLevel === 'advanced'
      ).length;

      // Calculate total estimated reading time
      const totalReadingTime = userContent.reduce(
        (sum: number, item: LearningContent) => sum + item.estimatedReadingTime,
        0
      );

      // Get AI-generated content count
      const aiGeneratedCount = userContent.filter(
        (item: LearningContent) => item.aiMetadata
      ).length;

      const stats = {
        totalContent,
        publicContent,
        privateContent,
        byLearningLevel: {
          beginner: beginnerCount,
          intermediate: intermediateCount,
          advanced: advancedCount,
        },
        totalReadingTime,
        aiGeneratedCount,
        averageReadingTime:
          totalContent > 0 ? Math.round(totalReadingTime / totalContent) : 0,
      };

      return {
        success: true,
        stats,
      };
    } catch (error) {
      console.error('Error fetching learning content stats:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch learning content statistics',
      });
    }
  }),
});
