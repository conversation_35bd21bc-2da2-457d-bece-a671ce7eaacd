/**
 * Quiz tRPC procedures
 * Handles quiz generation, retrieval, submission, and progress tracking
 */

import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { router, publicProcedure, protectedProcedure } from '../trpc';
import { createDatabaseConnectionFromEnv } from '@learn-platform/db/connection';
import {
  quiz,
  quizAttempt,
  quizProgress,
  quizAnalytics,
  quizFeedback,
  learningContent,
  eq,
  and,
  desc,
  or
} from '@learn-platform/db';

// Input schemas
const generateQuizInputSchema = z.object({
  learningContentId: z.string(),
  quizTypes: z.array(z.enum(['flashcard', 'multipleChoice', 'trueFalse', 'fillInBlank', 'matching', 'freeText', 'ordering'])).min(1).max(7),
  difficulty: z.enum(['easy', 'medium', 'hard']).default('medium'),
  questionsPerType: z.number().min(1).max(10).default(3),
  includeHints: z.boolean().default(true),
  includeExplanations: z.boolean().default(true),
  timeLimit: z.number().optional(), // in minutes
  shuffleQuestions: z.boolean().default(false),
});

const getQuizzesInputSchema = z.object({
  learningContentId: z.string().optional(),
  difficulty: z.enum(['easy', 'medium', 'hard']).optional(),
  search: z.string().optional(),
  limit: z.number().min(1).max(50).default(20),
  offset: z.number().min(0).default(0),
  includePublic: z.boolean().default(true),
});

const startQuizInputSchema = z.object({
  quizId: z.string(),
});

const submitAnswerInputSchema = z.object({
  attemptId: z.string(),
  questionId: z.string(),
  answer: z.any(), // Union type based on question type
  timeSpent: z.number().min(0), // seconds
  isTemporary: z.boolean().default(false), // For draft answers
});

const completeQuizInputSchema = z.object({
  attemptId: z.string(),
});

const quizFeedbackInputSchema = z.object({
  quizId: z.string(),
  attemptId: z.string().optional(),
  difficultyRating: z.number().min(1).max(5).optional(),
  clarityRating: z.number().min(1).max(5).optional(),
  relevanceRating: z.number().min(1).max(5).optional(),
  overallRating: z.number().min(1).max(5).optional(),
  comments: z.string().max(1000).optional(),
  suggestions: z.string().max(1000).optional(),
  questionFeedback: z.array(z.object({
    questionId: z.string(),
    rating: z.number().min(1).max(5),
    comment: z.string().max(500).optional(),
    reportedIssue: z.enum(['unclear', 'incorrect', 'too_hard', 'too_easy', 'irrelevant']).optional(),
  })).default([]),
  isAnonymous: z.boolean().default(false),
});

const getQuizHistoryInputSchema = z.object({
  learningContentId: z.string(),
  limit: z.number().min(1).max(50).default(20),
  offset: z.number().min(0).default(0),
});

/**
 * Quiz router with CRUD operations and quiz-taking functionality
 */
export const quizRouter = router({
  /**
   * Generate a new quiz from learning content
   */
  generate: protectedProcedure
    .input(generateQuizInputSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        // Verify learning content exists and user has access
        const content = await db
          .select()
          .from(learningContent)
          .where(
            and(
              eq(learningContent.id, input.learningContentId),
              or(
                eq(learningContent.isPublic, true),
                eq(learningContent.userId, ctx.userId!)
              )
            )
          )
          .limit(1);

        if (content.length === 0) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Learning content not found or access denied',
          });
        }

        // Import quiz generation service
        const { generateQuizFromContent } = await import('@learn-platform/ai');

        // Generate quiz using AI
        console.info('Starting quiz generation with AI...');
        const generatedQuiz = await generateQuizFromContent(
          content[0],
          {
            learningContentId: input.learningContentId,
            quizTypes: input.quizTypes,
            difficulty: input.difficulty,
            questionsPerType: input.questionsPerType,
            includeHints: input.includeHints,
            includeExplanations: input.includeExplanations,
          },
          {
            validateContent: true,
            retryOnFailure: true,
          }
        );

        console.info('Quiz generation completed successfully:', {
          questionsGenerated: generatedQuiz.questions.length,
          totalPoints: generatedQuiz.totalPoints,
          estimatedDuration: generatedQuiz.estimatedDuration
        });

        // Save quiz to database
        const quizId = crypto.randomUUID();
        const now = new Date();

        const newQuiz = {
          id: quizId,
          title: generatedQuiz.title,
          description: generatedQuiz.description,
          learningContentId: input.learningContentId,
          difficulty: input.difficulty,
          estimatedDuration: generatedQuiz.estimatedDuration,
          totalPoints: generatedQuiz.totalPoints,
          questions: generatedQuiz.questions,
          metadata: generatedQuiz.metadata,
          isPublic: false, // Default to private
          allowRetakes: true,
          showCorrectAnswers: input.includeExplanations,
          shuffleQuestions: input.shuffleQuestions,
          timeLimit: input.timeLimit || null,
          createdBy: ctx.userId!,
          createdAt: now,
          updatedAt: now,
        };

        console.info('Attempting to save quiz to database...', {
          quizId,
          questionsCount: newQuiz.questions.length,
          userId: ctx.userId
        });

        try {
          await db.insert(quiz).values(newQuiz);
          console.info('Quiz saved to database successfully:', { quizId });
        } catch (dbError) {
          console.error('Database insertion failed:', dbError);
          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: `Failed to save quiz to database: ${dbError instanceof Error ? dbError.message : 'Unknown database error'}`,
          });
        }

        console.info('Returning quiz generation response...', { quizId });
        return {
          success: true,
          quiz: newQuiz,
          message: 'Quiz generated successfully',
        };
      } catch (error) {
        console.error('Quiz generation failed:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: error instanceof Error ? error.message : 'Failed to generate quiz',
        });
      }
    }),

  /**
   * Get quizzes with filtering
   */
  getAll: publicProcedure
    .input(getQuizzesInputSchema)
    .query(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        // Build where conditions
        const whereConditions = [];

        if (input.learningContentId) {
          whereConditions.push(eq(quiz.learningContentId, input.learningContentId));
        }

        if (input.difficulty) {
          whereConditions.push(eq(quiz.difficulty, input.difficulty));
        }

        // Search filter (title and description)
        if (input.search) {
          const searchCondition = or(
            ilike(quiz.title, `%${input.search}%`),
            ilike(quiz.description, `%${input.search}%`)
          );
          if (searchCondition) {
            whereConditions.push(searchCondition);
          }
        }

        // Access control
        if (ctx.isAuthenticated) {
          if (input.includePublic) {
            whereConditions.push(
              or(
                eq(quiz.isPublic, true),
                eq(quiz.createdBy, ctx.userId!)
              )
            );
          } else {
            whereConditions.push(eq(quiz.createdBy, ctx.userId!));
          }
        } else {
          whereConditions.push(eq(quiz.isPublic, true));
        }

        const quizzes = await db
          .select({
            id: quiz.id,
            title: quiz.title,
            description: quiz.description,
            learningContentId: quiz.learningContentId,
            difficulty: quiz.difficulty,
            estimatedDuration: quiz.estimatedDuration,
            totalPoints: quiz.totalPoints,
            questionCount: quiz.questions, // Will be transformed
            isPublic: quiz.isPublic,
            allowRetakes: quiz.allowRetakes,
            timeLimit: quiz.timeLimit,
            createdAt: quiz.createdAt,
            metadata: quiz.metadata,
          })
          .from(quiz)
          .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
          .orderBy(desc(quiz.createdAt))
          .limit(input.limit)
          .offset(input.offset);

        // Transform question count
        const transformedQuizzes = quizzes.map(q => ({
          ...q,
          questionCount: Array.isArray(q.questionCount) ? q.questionCount.length : 0,
        }));

        return {
          quizzes: transformedQuizzes,
          hasMore: quizzes.length === input.limit,
        };
      } catch (error) {
        console.error('Failed to fetch quizzes:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch quizzes',
        });
      }
    }),

  /**
   * Get quiz by ID with questions
   */
  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        const quizData = await db
          .select()
          .from(quiz)
          .where(eq(quiz.id, input.id))
          .limit(1);

        if (quizData.length === 0) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Quiz not found',
          });
        }

        const quizItem = quizData[0];

        // Check access permissions
        if (!quizItem.isPublic && (!ctx.isAuthenticated || quizItem.createdBy !== ctx.userId)) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'Access denied to this quiz',
          });
        }

        return {
          quiz: quizItem,
        };
      } catch (error) {
        if (error instanceof TRPCError) throw error;
        console.error('Failed to fetch quiz:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch quiz',
        });
      }
    }),

  /**
   * Start a quiz attempt
   */
  startAttempt: protectedProcedure
    .input(startQuizInputSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        // Get quiz details
        const quizData = await db
          .select()
          .from(quiz)
          .where(eq(quiz.id, input.quizId))
          .limit(1);

        if (quizData.length === 0) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Quiz not found',
          });
        }

        const quizItem = quizData[0];

        // Check access permissions
        if (!quizItem.isPublic && quizItem.createdBy !== ctx.userId) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'Access denied to this quiz',
          });
        }

        // Check if retakes are allowed
        if (!quizItem.allowRetakes) {
          const existingAttempts = await db
            .select()
            .from(quizAttempt)
            .where(
              and(
                eq(quizAttempt.quizId, input.quizId),
                eq(quizAttempt.userId, ctx.userId!),
                eq(quizAttempt.isCompleted, true)
              )
            )
            .limit(1);

          if (existingAttempts.length > 0) {
            throw new TRPCError({
              code: 'FORBIDDEN',
              message: 'Retakes are not allowed for this quiz',
            });
          }
        }

        // Create new attempt
        const attemptId = crypto.randomUUID();
        const progressId = crypto.randomUUID();
        const now = new Date();

        const newAttempt = {
          id: attemptId,
          quizId: input.quizId,
          userId: ctx.userId!,
          startedAt: now,
          completedAt: null,
          isCompleted: false,
          answers: [],
          totalTimeSpent: 0,
          score: null,
          questionResults: null,
          metadata: {
            questionOrder: quizItem.shuffleQuestions
              ? shuffleArray(quizItem.questions.map((_, i) => i))
              : quizItem.questions.map((_, i) => i),
          },
        };

        const newProgress = {
          id: progressId,
          attemptId,
          currentQuestionIndex: 0,
          questionsAnswered: 0,
          totalQuestions: quizItem.questions.length,
          timeSpentSoFar: 0,
          lastActiveAt: now,
          currentAnswers: [],
          bookmarkedQuestions: [],
          questionNotes: {},
          updatedAt: now,
        };

        await db.insert(quizAttempt).values(newAttempt);
        await db.insert(quizProgress).values(newProgress);

        return {
          success: true,
          attemptId,
          progressId,
          message: 'Quiz attempt started successfully',
        };
      } catch (error) {
        if (error instanceof TRPCError) throw error;
        console.error('Failed to start quiz attempt:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to start quiz attempt',
        });
      }
    }),

  /**
   * Submit an answer for a question
   */
  submitAnswer: protectedProcedure
    .input(submitAnswerInputSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        // Verify attempt belongs to user
        const attempt = await db
          .select()
          .from(quizAttempt)
          .where(
            and(
              eq(quizAttempt.id, input.attemptId),
              eq(quizAttempt.userId, ctx.userId!)
            )
          )
          .limit(1);

        if (attempt.length === 0) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Quiz attempt not found',
          });
        }

        if (attempt[0].isCompleted) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'Cannot submit answer to completed quiz',
          });
        }

        // Update answers and progress
        const currentAnswers = attempt[0].answers || [];
        const existingAnswerIndex = currentAnswers.findIndex(a => a.questionId === input.questionId);

        if (existingAnswerIndex >= 0) {
          // Update existing answer
          currentAnswers[existingAnswerIndex] = {
            questionId: input.questionId,
            questionType: currentAnswers[existingAnswerIndex].questionType,
            answer: input.answer,
            timeSpent: input.timeSpent,
          };
        } else {
          // Add new answer
          currentAnswers.push({
            questionId: input.questionId,
            questionType: 'unknown', // Will be determined during scoring
            answer: input.answer,
            timeSpent: input.timeSpent,
          });
        }

        await db
          .update(quizAttempt)
          .set({
            answers: currentAnswers,
            totalTimeSpent: (attempt[0].totalTimeSpent || 0) + input.timeSpent,
          })
          .where(eq(quizAttempt.id, input.attemptId));

        // Update progress if not temporary
        if (!input.isTemporary) {
          await db
            .update(quizProgress)
            .set({
              questionsAnswered: currentAnswers.length,
              timeSpentSoFar: (attempt[0].totalTimeSpent || 0) + input.timeSpent,
              lastActiveAt: new Date(),
              updatedAt: new Date(),
            })
            .where(eq(quizProgress.attemptId, input.attemptId));
        }

        return {
          success: true,
          message: 'Answer submitted successfully',
        };
      } catch (error) {
        if (error instanceof TRPCError) throw error;
        console.error('Failed to submit answer:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to submit answer',
        });
      }
    }),

  /**
   * Complete quiz and calculate score
   */
  complete: protectedProcedure
    .input(completeQuizInputSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        // Get attempt and quiz data
        const attemptData = await db
          .select({
            attempt: quizAttempt,
            quiz: quiz,
          })
          .from(quizAttempt)
          .innerJoin(quiz, eq(quizAttempt.quizId, quiz.id))
          .where(
            and(
              eq(quizAttempt.id, input.attemptId),
              eq(quizAttempt.userId, ctx.userId!)
            )
          )
          .limit(1);

        if (attemptData.length === 0) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Quiz attempt not found',
          });
        }

        const { attempt, quiz: quizData } = attemptData[0];

        if (attempt.isCompleted) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: 'Quiz is already completed',
          });
        }

        // Calculate score using AI evaluation service
        const { evaluateQuizAnswers } = await import('@learn-platform/ai');

        const evaluation = await evaluateQuizAnswers(
          quizData.questions,
          attempt.answers || [],
          {
            env: ctx.env // Pass environment for API keys
          }
        );

        const now = new Date();

        // Update attempt with results
        await db
          .update(quizAttempt)
          .set({
            completedAt: now,
            isCompleted: true,
            score: evaluation.score,
            questionResults: evaluation.questionResults,
          })
          .where(eq(quizAttempt.id, input.attemptId));

        return {
          success: true,
          score: evaluation.score,
          questionResults: evaluation.questionResults,
          message: 'Quiz completed successfully',
        };
      } catch (error) {
        if (error instanceof TRPCError) throw error;
        console.error('Failed to complete quiz:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to complete quiz',
        });
      }
    }),

  /**
   * Submit quiz feedback
   */
  submitFeedback: protectedProcedure
    .input(quizFeedbackInputSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        const feedbackId = crypto.randomUUID();
        const now = new Date();

        const newFeedback = {
          id: feedbackId,
          quizId: input.quizId,
          userId: ctx.userId!,
          attemptId: input.attemptId || null,
          difficultyRating: input.difficultyRating || null,
          clarityRating: input.clarityRating || null,
          relevanceRating: input.relevanceRating || null,
          overallRating: input.overallRating || null,
          comments: input.comments || null,
          suggestions: input.suggestions || null,
          questionFeedback: input.questionFeedback,
          isAnonymous: input.isAnonymous,
          createdAt: now,
        };

        await db.insert(quizFeedback).values(newFeedback);

        return {
          success: true,
          message: 'Feedback submitted successfully',
        };
      } catch (error) {
        console.error('Failed to submit feedback:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to submit feedback',
        });
      }
    }),

  /**
   * Get user's quiz attempts
   */
  getMyAttempts: protectedProcedure
    .input(z.object({
      quizId: z.string().optional(),
      limit: z.number().min(1).max(50).default(20),
      offset: z.number().min(0).default(0),
    }))
    .query(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        const whereConditions = [eq(quizAttempt.userId, ctx.userId!)];

        if (input.quizId) {
          whereConditions.push(eq(quizAttempt.quizId, input.quizId));
        }

        const attempts = await db
          .select({
            id: quizAttempt.id,
            quizId: quizAttempt.quizId,
            startedAt: quizAttempt.startedAt,
            completedAt: quizAttempt.completedAt,
            isCompleted: quizAttempt.isCompleted,
            score: quizAttempt.score,
            totalTimeSpent: quizAttempt.totalTimeSpent,
            quizTitle: quiz.title,
            quizDifficulty: quiz.difficulty,
          })
          .from(quizAttempt)
          .innerJoin(quiz, eq(quizAttempt.quizId, quiz.id))
          .where(and(...whereConditions))
          .orderBy(desc(quizAttempt.startedAt))
          .limit(input.limit)
          .offset(input.offset);

        return {
          attempts,
          hasMore: attempts.length === input.limit,
        };
      } catch (error) {
        console.error('Failed to fetch attempts:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch attempts',
        });
      }
    }),

  /**
   * Get current incomplete attempt for a quiz
   */
  getCurrentAttempt: protectedProcedure
    .input(z.object({ quizId: z.string() }))
    .query(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        // Get the most recent incomplete attempt for this quiz and user
        const attempt = await db
          .select()
          .from(quizAttempt)
          .where(
            and(
              eq(quizAttempt.quizId, input.quizId),
              eq(quizAttempt.userId, ctx.userId!),
              eq(quizAttempt.isCompleted, false)
            )
          )
          .orderBy(desc(quizAttempt.startedAt))
          .limit(1);

        if (attempt.length === 0) {
          return {
            success: true,
            attempt: null,
            progress: null,
          };
        }

        // Get progress data for this attempt
        const progress = await db
          .select()
          .from(quizProgress)
          .where(eq(quizProgress.attemptId, attempt[0].id))
          .limit(1);

        return {
          success: true,
          attempt: attempt[0],
          progress: progress[0] || null,
        };
      } catch (error) {
        console.error('Failed to fetch current attempt:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch current attempt',
        });
      }
    }),

  /**
   * Get quiz history for a specific learning content
   */
  getHistoryByLearningContent: protectedProcedure
    .input(getQuizHistoryInputSchema)
    .query(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        // Get quiz attempts with quiz details for the specific learning content
        const attempts = await db
          .select({
            id: quizAttempt.id,
            quizId: quizAttempt.quizId,
            quizTitle: quiz.title,
            startedAt: quizAttempt.startedAt,
            completedAt: quizAttempt.completedAt,
            isCompleted: quizAttempt.isCompleted,
            score: quizAttempt.score,
            totalTimeSpent: quizAttempt.totalTimeSpent,
            difficulty: quiz.difficulty,
          })
          .from(quizAttempt)
          .innerJoin(quiz, eq(quizAttempt.quizId, quiz.id))
          .where(
            and(
              eq(quiz.learningContentId, input.learningContentId),
              eq(quizAttempt.userId, ctx.userId!)
            )
          )
          .orderBy(desc(quizAttempt.startedAt))
          .limit(input.limit)
          .offset(input.offset);

        // Calculate statistics for all attempts for this learning content
        const statsQuery = await db
          .select({
            totalAttempts: quizAttempt.id,
            completedAttempts: quizAttempt.isCompleted,
            score: quizAttempt.score,
            totalTimeSpent: quizAttempt.totalTimeSpent,
            startedAt: quizAttempt.startedAt,
          })
          .from(quizAttempt)
          .innerJoin(quiz, eq(quizAttempt.quizId, quiz.id))
          .where(
            and(
              eq(quiz.learningContentId, input.learningContentId),
              eq(quizAttempt.userId, ctx.userId!)
            )
          );

        // Calculate stats from the raw data
        const totalAttempts = statsQuery.length;
        const completedAttempts = statsQuery.filter(a => a.completedAttempts).length;
        const completedAttemptsWithScores = statsQuery.filter(a => a.completedAttempts && a.score);

        const averageScore = completedAttemptsWithScores.length > 0
          ? completedAttemptsWithScores.reduce((sum, a) => sum + (a.score?.percentage || 0), 0) / completedAttemptsWithScores.length
          : 0;

        const averageTimeSpent = completedAttempts > 0
          ? statsQuery.filter(a => a.completedAttempts).reduce((sum, a) => sum + (a.totalTimeSpent || 0), 0) / completedAttempts
          : 0;

        const bestScore = completedAttemptsWithScores.length > 0
          ? Math.max(...completedAttemptsWithScores.map(a => a.score?.percentage || 0))
          : 0;

        const lastAttemptDate = totalAttempts > 0
          ? Math.max(...statsQuery.map(a => new Date(a.startedAt).getTime()))
          : undefined;

        const stats = {
          totalAttempts,
          completedAttempts,
          averageScore,
          averageTimeSpent,
          bestScore,
          lastAttemptDate: lastAttemptDate ? new Date(lastAttemptDate).toISOString() : undefined,
        };

        // Transform attempts to match expected interface
        const transformedAttempts = attempts.map(attempt => ({
          id: attempt.id,
          quizId: attempt.quizId,
          quizTitle: attempt.quizTitle,
          startedAt: attempt.startedAt.toISOString(),
          completedAt: attempt.completedAt?.toISOString(),
          isCompleted: attempt.isCompleted,
          score: attempt.score || undefined,
          totalTimeSpent: attempt.totalTimeSpent || 0,
          difficulty: attempt.difficulty as 'easy' | 'medium' | 'hard',
        }));

        return {
          attempts: transformedAttempts,
          stats,
          hasMore: attempts.length === input.limit,
        };
      } catch (error) {
        console.error('Failed to fetch quiz history:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch quiz history',
        });
      }
    }),

  /**
   * Get the latest in-progress quiz attempt for the user
   */
  getLatestInProgress: protectedProcedure
    .query(async ({ ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        // Get the most recent incomplete quiz attempt
        const inProgressAttempt = await db
          .select({
            id: quizAttempt.id,
            quizId: quizAttempt.quizId,
            startedAt: quizAttempt.startedAt,
            isCompleted: quizAttempt.isCompleted,
            totalTimeSpent: quizAttempt.totalTimeSpent,
            quizTitle: quiz.title,
            quizDifficulty: quiz.difficulty,
          })
          .from(quizAttempt)
          .innerJoin(quiz, eq(quizAttempt.quizId, quiz.id))
          .where(
            and(
              eq(quizAttempt.userId, ctx.userId!),
              eq(quizAttempt.isCompleted, false)
            )
          )
          .orderBy(desc(quizAttempt.startedAt))
          .limit(1);

        if (inProgressAttempt.length === 0) {
          return {
            success: true,
            attempt: null,
          };
        }

        return {
          success: true,
          attempt: inProgressAttempt[0],
        };
      } catch (error) {
        console.error('Failed to fetch latest in-progress quiz:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch latest in-progress quiz',
        });
      }
    }),
});

// Utility function to shuffle array
function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}
