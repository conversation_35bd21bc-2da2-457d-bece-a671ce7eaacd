import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { router, protectedProcedure } from '../router';
import { createDatabaseConnectionFromEnv } from '@learn-platform/db';
import { learningProgress, learningContent, eq, and, desc, type LearningProgress } from '@learn-platform/db';

// Input schemas
const updateProgressSchema = z.object({
  contentId: z.string(),
  currentStepIndex: z.number().min(0),
  timeSpent: z.number().min(0).optional(), // Time spent in this session (seconds)
  completedSteps: z.array(z.number()).optional(),
});

const addBookmarkSchema = z.object({
  contentId: z.string(),
  stepIndex: z.number().min(0),
  note: z.string().optional(),
});

const addNoteSchema = z.object({
  contentId: z.string(),
  stepIndex: z.number().min(0),
  content: z.string().min(1),
});

const removeBookmarkSchema = z.object({
  contentId: z.string(),
  stepIndex: z.number().min(0),
});

const updateNoteSchema = z.object({
  contentId: z.string(),
  stepIndex: z.number().min(0),
  noteIndex: z.number().min(0),
  content: z.string().min(1),
});

const deleteNoteSchema = z.object({
  contentId: z.string(),
  stepIndex: z.number().min(0),
  noteIndex: z.number().min(0),
});

export const learningProgressRouter = router({
  /**
   * Get progress for a specific learning content
   */
  getProgress: protectedProcedure
    .input(z.object({ contentId: z.string() }))
    .query(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        const progress = await db
          .select()
          .from(learningProgress)
          .where(
            and(
              eq(learningProgress.contentId, input.contentId),
              eq(learningProgress.userId, ctx.userId as string)
            )
          )
          .limit(1);

        return {
          success: true,
          progress: progress[0] || null,
        };
      } catch (error) {
        console.error('Error fetching learning progress:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch learning progress',
        });
      }
    }),

  /**
   * Update learning progress
   */
  updateProgress: protectedProcedure
    .input(updateProgressSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        // First, check if progress record exists
        const existingProgress = await db
          .select()
          .from(learningProgress)
          .where(
            and(
              eq(learningProgress.contentId, input.contentId),
              eq(learningProgress.userId, ctx.userId as string)
            )
          )
          .limit(1);

        // Get content info to calculate total steps
        const content = await db
          .select()
          .from(learningContent)
          .where(eq(learningContent.id, input.contentId))
          .limit(1);

        if (!content.length) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Learning content not found',
          });
        }

        const totalSteps = content[0].steps ? (content[0].steps as any[]).length : 0;
        const completedSteps = input.completedSteps || existingProgress[0]?.completedSteps || [];
        const completionPercentage = totalSteps > 0 ? Math.round((completedSteps.length / totalSteps) * 100) : 0;
        const isCompleted = completionPercentage >= 100;

        const now = new Date();

        if (existingProgress.length > 0) {
          // Update existing progress
          const currentProgress = existingProgress[0];
          const newTotalTimeSpent = (currentProgress.totalTimeSpent || 0) + (input.timeSpent || 0);
          const newSessionCount = (currentProgress.sessionCount || 1) + 1;

          await db
            .update(learningProgress)
            .set({
              currentStepIndex: input.currentStepIndex,
              completedSteps: completedSteps as any,
              totalTimeSpent: newTotalTimeSpent,
              completionPercentage,
              isCompleted,
              lastAccessedAt: now,
              sessionCount: newSessionCount,
              updatedAt: now,
            })
            .where(eq(learningProgress.id, currentProgress.id));

          return {
            success: true,
            progress: {
              ...currentProgress,
              currentStepIndex: input.currentStepIndex,
              completedSteps,
              totalTimeSpent: newTotalTimeSpent,
              completionPercentage,
              isCompleted,
              sessionCount: newSessionCount,
            },
          };
        } else {
          // Create new progress record
          const newProgress = {
            id: crypto.randomUUID(),
            contentId: input.contentId,
            userId: ctx.userId as string,
            currentStepIndex: input.currentStepIndex,
            completedSteps: completedSteps as any,
            totalTimeSpent: input.timeSpent || 0,
            completionPercentage,
            isCompleted,
            bookmarks: [],
            notes: [],
            lastAccessedAt: now,
            sessionCount: 1,
            createdAt: now,
            updatedAt: now,
          };

          await db.insert(learningProgress).values(newProgress);

          return {
            success: true,
            progress: newProgress,
          };
        }
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        console.error('Error updating learning progress:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to update learning progress',
        });
      }
    }),

  /**
   * Add bookmark to a step
   */
  addBookmark: protectedProcedure
    .input(addBookmarkSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        // Get existing progress
        const progress = await db
          .select()
          .from(learningProgress)
          .where(
            and(
              eq(learningProgress.contentId, input.contentId),
              eq(learningProgress.userId, ctx.userId!)
            )
          )
          .limit(1);

        if (!progress.length) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Progress record not found. Start learning first.',
          });
        }

        const currentProgress = progress[0];
        const existingBookmarks = currentProgress.bookmarks || [];

        // Remove existing bookmark for this step if any
        const filteredBookmarks = existingBookmarks.filter(
          (bookmark: any) => bookmark.stepIndex !== input.stepIndex
        );

        // Add new bookmark
        const newBookmark = {
          stepIndex: input.stepIndex,
          note: input.note,
          timestamp: new Date().toISOString(),
        };

        const updatedBookmarks = [...filteredBookmarks, newBookmark];

        await db
          .update(learningProgress)
          .set({
            bookmarks: updatedBookmarks as any,
            updatedAt: new Date(),
          })
          .where(eq(learningProgress.id, currentProgress.id));

        return {
          success: true,
          bookmark: newBookmark,
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        console.error('Error adding bookmark:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to add bookmark',
        });
      }
    }),

  /**
   * Add note to a step
   */
  addNote: protectedProcedure
    .input(addNoteSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        // Get existing progress
        const progress = await db
          .select()
          .from(learningProgress)
          .where(
            and(
              eq(learningProgress.contentId, input.contentId),
              eq(learningProgress.userId, ctx.userId!)
            )
          )
          .limit(1);

        if (!progress.length) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Progress record not found. Start learning first.',
          });
        }

        const currentProgress = progress[0];
        const existingNotes = currentProgress.notes || [];

        // Add new note
        const newNote = {
          stepIndex: input.stepIndex,
          content: input.content,
          timestamp: new Date().toISOString(),
        };

        const updatedNotes = [...existingNotes, newNote];

        await db
          .update(learningProgress)
          .set({
            notes: updatedNotes as unknown,
            updatedAt: new Date(),
          })
          .where(eq(learningProgress.id, currentProgress.id));

        return {
          success: true,
          note: newNote,
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        console.error('Error adding note:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to add note',
        });
      }
    }),

  /**
   * Remove bookmark from a step
   */
  removeBookmark: protectedProcedure
    .input(removeBookmarkSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        // Get existing progress
        const progress = await db
          .select()
          .from(learningProgress)
          .where(
            and(
              eq(learningProgress.contentId, input.contentId),
              eq(learningProgress.userId, ctx.userId!)
            )
          )
          .limit(1);

        if (!progress.length) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Progress record not found.',
          });
        }

        const currentProgress = progress[0];
        const existingBookmarks = currentProgress.bookmarks || [];

        // Remove bookmark for this step
        const filteredBookmarks = existingBookmarks.filter(
          (bookmark: any) => bookmark.stepIndex !== input.stepIndex
        );

        await db
          .update(learningProgress)
          .set({
            bookmarks: filteredBookmarks as any,
            updatedAt: new Date(),
          })
          .where(eq(learningProgress.id, currentProgress.id));

        return {
          success: true,
          message: 'Bookmark removed successfully',
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        console.error('Error removing bookmark:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to remove bookmark',
        });
      }
    }),

  /**
   * Update an existing note
   */
  updateNote: protectedProcedure
    .input(updateNoteSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        // Get existing progress
        const progress = await db
          .select()
          .from(learningProgress)
          .where(
            and(
              eq(learningProgress.contentId, input.contentId),
              eq(learningProgress.userId, ctx.userId!)
            )
          )
          .limit(1);

        if (!progress.length) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Progress record not found.',
          });
        }

        const currentProgress = progress[0];
        const existingNotes = currentProgress.notes || [];

        // Find notes for this step
        const stepNotes = existingNotes.filter((note: unknown) => (note as any).stepIndex === input.stepIndex);

        if (input.noteIndex >= stepNotes.length) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Note not found.',
          });
        }

        // Update the specific note
        const updatedNotes = existingNotes.map((note: unknown) => {
          if ((note as any).stepIndex === input.stepIndex) {
            const stepNoteIndex = stepNotes.findIndex((sn: unknown) => sn === note);
            if (stepNoteIndex === input.noteIndex) {
              return {
                ...(note as any),
                content: input.content,
                updatedAt: new Date().toISOString(),
              };
            }
          }
          return note;
        });

        await db
          .update(learningProgress)
          .set({
            notes: updatedNotes as unknown,
            updatedAt: new Date(),
          })
          .where(eq(learningProgress.id, currentProgress.id));

        return {
          success: true,
          message: 'Note updated successfully',
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        console.error('Error updating note:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to update note',
        });
      }
    }),

  /**
   * Delete a note
   */
  deleteNote: protectedProcedure
    .input(deleteNoteSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        // Get existing progress
        const progress = await db
          .select()
          .from(learningProgress)
          .where(
            and(
              eq(learningProgress.contentId, input.contentId),
              eq(learningProgress.userId, ctx.userId!)
            )
          )
          .limit(1);

        if (!progress.length) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Progress record not found.',
          });
        }

        const currentProgress = progress[0];
        const existingNotes = currentProgress.notes || [];

        // Find notes for this step
        const stepNotes = existingNotes.filter((note: unknown) => (note as any).stepIndex === input.stepIndex);

        if (input.noteIndex >= stepNotes.length) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Note not found.',
          });
        }

        // Remove the specific note
        const noteToRemove = stepNotes[input.noteIndex];
        const updatedNotes = existingNotes.filter((note: unknown) => note !== noteToRemove);

        await db
          .update(learningProgress)
          .set({
            notes: updatedNotes as unknown,
            updatedAt: new Date(),
          })
          .where(eq(learningProgress.id, currentProgress.id));

        return {
          success: true,
          message: 'Note deleted successfully',
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        console.error('Error deleting note:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to delete note',
        });
      }
    }),

  /**
   * Get user's overall learning statistics
   */
  getStats: protectedProcedure
    .query(async ({ ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        const allProgress = await db
          .select()
          .from(learningProgress)
          .where(eq(learningProgress.userId, ctx.userId as string))
          .orderBy(desc(learningProgress.updatedAt));

        const totalContent = allProgress.length;
        const completedContent = allProgress.filter((p: LearningProgress) => p.isCompleted).length;
        const inProgressContent = allProgress.filter((p: LearningProgress) => !p.isCompleted && (p.currentStepIndex ?? 0) > 0).length;
        const totalTimeSpent = allProgress.reduce((sum: number, p: LearningProgress) => sum + (p.totalTimeSpent || 0), 0);
        const averageCompletion = totalContent > 0
          ? Math.round(allProgress.reduce((sum: number, p: LearningProgress) => sum + (p.completionPercentage || 0), 0) / totalContent)
          : 0;

        return {
          success: true,
          stats: {
            totalContent,
            completedContent,
            inProgressContent,
            totalTimeSpent, // in seconds
            averageCompletion,
            recentActivity: allProgress.slice(0, 5), // Last 5 activities
          },
        };
      } catch (error) {
        console.error('Error fetching learning stats:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch learning statistics',
        });
      }
    }),

  /**
   * Get the latest in-progress learning content for the user
   */
  getLatestInProgress: protectedProcedure
    .query(async ({ ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        // Get the most recent in-progress learning activity
        const inProgressActivity = await db
          .select({
            id: learningProgress.id,
            contentId: learningProgress.contentId,
            currentStepIndex: learningProgress.currentStepIndex,
            completionPercentage: learningProgress.completionPercentage,
            isCompleted: learningProgress.isCompleted,
            updatedAt: learningProgress.updatedAt,
            totalTimeSpent: learningProgress.totalTimeSpent,
          })
          .from(learningProgress)
          .where(
            and(
              eq(learningProgress.userId, ctx.userId as string),
              eq(learningProgress.isCompleted, false),
              // Only include activities that have actually started (currentStepIndex > 0)
              // This filters out "not started" activities
              // Note: Using SQL function to handle potential null values
            )
          )
          .orderBy(desc(learningProgress.updatedAt))
          .limit(1);

        // Filter for activities where currentStepIndex > 0 (actually in progress)
        const filteredActivity = inProgressActivity.filter(activity =>
          (activity.currentStepIndex ?? 0) > 0
        );

        if (filteredActivity.length === 0) {
          return {
            success: true,
            activity: null,
          };
        }

        const activity = filteredActivity[0];

        // Get the associated learning content details
        const content = await db
          .select({
            id: learningContent.id,
            title: learningContent.title,
            description: learningContent.description,
            learningLevel: learningContent.learningLevel,
            estimatedReadingTime: learningContent.estimatedReadingTime,
          })
          .from(learningContent)
          .where(eq(learningContent.id, activity.contentId))
          .limit(1);

        if (content.length === 0) {
          return {
            success: true,
            activity: null,
          };
        }

        return {
          success: true,
          activity: {
            ...activity,
            content: content[0],
          },
        };
      } catch (error) {
        console.error('Error fetching latest in-progress learning:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch latest in-progress learning',
        });
      }
    }),
});
