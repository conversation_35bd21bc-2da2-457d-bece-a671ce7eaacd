/**
 * Integration tests for learning content tRPC procedures
 * Tests authentication, input validation, AI service integration, database operations, and error scenarios
 */

// Mock external dependencies BEFORE any imports
jest.mock('@learn-platform/db/connection', () => ({
  createDatabaseConnectionFromEnv: jest.fn(() => ({
    db: {
      select: jest.fn().mockReturnThis(),
      from: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      values: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      set: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      offset: jest.fn().mockReturnThis(),
      execute: jest.fn().mockResolvedValue([]),
    },
  })),
}));

jest.mock('@learn-platform/db/schema', () => ({
  learningContent: {
    id: 'id',
    title: 'title',
    description: 'description',
    steps: 'steps',
    learningLevel: 'learningLevel',
    estimatedReadingTime: 'estimatedReadingTime',
    isPublic: 'isPublic',
    tags: 'tags',
    aiMetadata: 'aiMetadata',
    userId: 'userId',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
  },
  eq: jest.fn(),
  and: jest.fn(),
  or: jest.fn(),
  desc: jest.fn(),
  gte: jest.fn(),
  lte: jest.fn(),
  sql: jest.fn(),
}));

jest.mock('@learn-platform/ai', () => ({
  generateLearningContent: jest.fn(),
}));

jest.mock('@learn-platform/db/services/learning-content', () => ({
  duplicateLearningContent: jest.fn(),
  getLearningContentStats: jest.fn(),
}));

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { TRPCError } from '@trpc/server';
import { learningContentRouter } from './learning-content';

// Helper functions for creating test data
function createMockContext(isAuthenticated = true, userId = 'test-user-id') {
  return {
    isAuthenticated,
    userId,
    user: isAuthenticated ? { id: userId, email: '<EMAIL>', name: 'Test User' } : null,
    session: isAuthenticated ? { id: 'session-id', userId } : null,
    env: {
      DATABASE_URL: 'postgresql://test:test@localhost:5432/test',
    },
    request: new Request('http://localhost:3000'),
    timestamp: new Date().toISOString(),
    requestId: 'test-request-id',
    country: 'US',
  };
}

function createMockLearningContent(overrides = {}) {
  return {
    id: 'content-id-1',
    title: 'Test Learning Content',
    description: 'Test description',
    steps: [
      {
        id: 'step-1',
        title: 'Introduction',
        icon: '📚',
        blocks: [
          {
            id: 'block-1',
            type: 'paragraph',
            data: 'This is a test paragraph.',
            isEditing: false,
          },
        ],
      },
    ],
    learningLevel: 'beginner' as const,
    estimatedReadingTime: 5,
    isPublic: false,
    tags: ['test'],
    aiMetadata: {
      aiModel: 'openai/gpt-4o',
      generatedAt: '2024-01-01T00:00:00Z',
      contentTypes: ['paragraph'],
      originalPrompt: 'Test topic',
    },
    userId: 'test-user-id',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    ...overrides,
  };
}

describe('Learning Content tRPC Procedures', () => {
  let mockDb: any;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Get the mocked database connection
    const { createDatabaseConnectionFromEnv } = require('@learn-platform/db/connection');
    mockDb = createDatabaseConnectionFromEnv().db;
  });

  describe('getAll procedure', () => {
    it('should return public content for unauthenticated users', async () => {
      const mockContent = [createMockLearningContent({ isPublic: true })];
      mockDb.select.mockReturnValue(mockDb);
      mockDb.from.mockReturnValue(mockDb);
      mockDb.where.mockReturnValue(mockDb);
      mockDb.orderBy.mockReturnValue(mockDb);
      mockDb.limit.mockReturnValue(mockDb);
      mockDb.offset.mockResolvedValue(mockContent);

      const ctx = createMockContext(false);
      const input = { limit: 20, offset: 0 };

      const caller = learningContentRouter.createCaller(ctx);
      const result = await caller.getAll(input);

      expect(result.success).toBe(true);
      expect(result.content).toEqual(mockContent);
    });

    it('should return public and user content for authenticated users', async () => {
      const mockContent = [
        createMockLearningContent({ isPublic: true }),
        createMockLearningContent({ isPublic: false, userId: 'test-user-id' }),
      ];
      mockDb.offset.mockResolvedValue(mockContent);

      const ctx = createMockContext(true);
      const input = { limit: 20, offset: 0 };

      const caller = learningContentRouter.createCaller(ctx);
      const result = await caller.getAll(input);

      expect(result.success).toBe(true);
      expect(result.content).toEqual(mockContent);
    });

    it('should handle filtering by learning level', async () => {
      const mockContent = [createMockLearningContent({ learningLevel: 'beginner' })];
      mockDb.offset.mockResolvedValue(mockContent);

      const ctx = createMockContext(true);
      const input = { limit: 20, offset: 0, learningLevel: 'beginner' as const };

      const caller = learningContentRouter.createCaller(ctx);
      const result = await caller.getAll(input);

      expect(result.success).toBe(true);
      expect(result.content).toEqual(mockContent);
    });
  });

  describe('getById procedure', () => {
    it('should return content by ID for public content', async () => {
      const mockContent = createMockLearningContent({ isPublic: true });
      mockDb.limit.mockResolvedValue([mockContent]);

      const ctx = createMockContext(false);
      const input = { id: 'content-id-1' };

      const caller = learningContentRouter.createCaller(ctx);
      const result = await caller.getById(input);

      expect(result.success).toBe(true);
      expect(result.content).toEqual(mockContent);
    });

    it('should return content by ID for owner', async () => {
      const mockContent = createMockLearningContent({ isPublic: false, userId: 'test-user-id' });
      mockDb.limit.mockResolvedValue([mockContent]);

      const ctx = createMockContext(true, 'test-user-id');
      const input = { id: 'content-id-1' };

      const caller = learningContentRouter.createCaller(ctx);
      const result = await caller.getById(input);

      expect(result.success).toBe(true);
      expect(result.content).toEqual(mockContent);
    });

    it('should throw FORBIDDEN for private content accessed by non-owner', async () => {
      const mockContent = createMockLearningContent({ isPublic: false, userId: 'other-user-id' });
      mockDb.limit.mockResolvedValue([mockContent]);

      const ctx = createMockContext(true, 'test-user-id');
      const input = { id: 'content-id-1' };

      const caller = learningContentRouter.createCaller(ctx);
      
      await expect(caller.getById(input)).rejects.toThrow(TRPCError);
    });

    it('should throw NOT_FOUND for non-existent content', async () => {
      mockDb.limit.mockResolvedValue([]);

      const ctx = createMockContext(true);
      const input = { id: 'non-existent-id' };

      const caller = learningContentRouter.createCaller(ctx);
      
      await expect(caller.getById(input)).rejects.toThrow(TRPCError);
    });
  });

  describe('create procedure', () => {
    it('should create new learning content for authenticated user', async () => {
      const mockContent = createMockLearningContent();
      mockDb.insert.mockReturnValue(mockDb);
      mockDb.values.mockResolvedValue(undefined);

      const ctx = createMockContext(true);
      const input = {
        title: 'Test Content',
        description: 'Test description',
        steps: mockContent.steps,
        learningLevel: 'beginner' as const,
        estimatedReadingTime: 5,
        isPublic: false,
        tags: ['test'],
      };

      const caller = learningContentRouter.createCaller(ctx);
      const result = await caller.create(input);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Learning content created successfully');
      expect(mockDb.insert).toHaveBeenCalled();
      expect(mockDb.values).toHaveBeenCalled();
    });

    it('should require authentication', async () => {
      const ctx = createMockContext(false);
      const input = {
        title: 'Test Content',
        steps: [],
        learningLevel: 'beginner' as const,
        estimatedReadingTime: 5,
      };

      const caller = learningContentRouter.createCaller(ctx);
      
      await expect(caller.create(input)).rejects.toThrow(TRPCError);
    });
  });

  describe('update procedure', () => {
    it('should update learning content for owner', async () => {
      const mockContent = createMockLearningContent({ userId: 'test-user-id' });
      mockDb.limit.mockResolvedValue([mockContent]);
      mockDb.set.mockReturnValue(mockDb);
      mockDb.where.mockResolvedValue(undefined);

      const ctx = createMockContext(true, 'test-user-id');
      const input = {
        id: 'content-id-1',
        title: 'Updated Title',
      };

      const caller = learningContentRouter.createCaller(ctx);
      const result = await caller.update(input);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Learning content updated successfully');
      expect(mockDb.update).toHaveBeenCalled();
    });

    it('should throw NOT_FOUND for non-existent content', async () => {
      mockDb.limit.mockResolvedValue([]);

      const ctx = createMockContext(true);
      const input = {
        id: 'non-existent-id',
        title: 'Updated Title',
      };

      const caller = learningContentRouter.createCaller(ctx);
      
      await expect(caller.update(input)).rejects.toThrow(TRPCError);
    });
  });

  describe('delete procedure', () => {
    it('should delete learning content for owner', async () => {
      const mockContent = createMockLearningContent({ userId: 'test-user-id' });
      mockDb.limit.mockResolvedValue([mockContent]);
      mockDb.where.mockResolvedValue(undefined);

      const ctx = createMockContext(true, 'test-user-id');
      const input = { id: 'content-id-1' };

      const caller = learningContentRouter.createCaller(ctx);
      const result = await caller.delete(input);

      expect(result.success).toBe(true);
      expect(result.message).toBe('Learning content deleted successfully');
      expect(mockDb.delete).toHaveBeenCalled();
    });
  });

  describe('duplicate procedure', () => {
    it('should duplicate learning content', async () => {
      const { duplicateLearningContent } = require('@learn-platform/db/services/learning-content');
      const mockDuplicatedContent = createMockLearningContent({ title: 'Test Content (Copy)' });
      duplicateLearningContent.mockResolvedValue(mockDuplicatedContent);

      const ctx = createMockContext(true);
      const input = { id: 'content-id-1', newTitle: 'Custom Copy Title' };

      const caller = learningContentRouter.createCaller(ctx);
      const result = await caller.duplicate(input);

      expect(result.success).toBe(true);
      expect(result.content).toEqual(mockDuplicatedContent);
      expect(duplicateLearningContent).toHaveBeenCalledWith(
        expect.anything(),
        'content-id-1',
        'test-user-id',
        'Custom Copy Title'
      );
    });
  });

  describe('getStats procedure', () => {
    it('should return user learning content statistics', async () => {
      const { getLearningContentStats } = require('@learn-platform/db/services/learning-content');
      const mockStats = {
        totalContent: 5,
        publicContent: 2,
        privateContent: 3,
        contentByLevel: {
          beginner: 2,
          intermediate: 2,
          advanced: 1,
        },
      };
      getLearningContentStats.mockResolvedValue(mockStats);

      const ctx = createMockContext(true);

      const caller = learningContentRouter.createCaller(ctx);
      const result = await caller.getStats();

      expect(result.success).toBe(true);
      expect(result.stats).toEqual(mockStats);
      expect(getLearningContentStats).toHaveBeenCalledWith(
        expect.anything(),
        'test-user-id'
      );
    });
  });
});
