import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { router, protectedProcedure } from '../router';
import { createDatabaseConnectionFromEnv } from '@learn-platform/db/connection';
import { learningContentFeedback, learningContent, eq, and, desc } from '@learn-platform/db/schema';

// Input schemas
const submitFeedbackSchema = z.object({
  contentId: z.string(),
  rating: z.number().min(1).max(5),
  feedbackText: z.string().optional(),
  isHelpful: z.boolean().optional(),
  suggestedImprovements: z.object({
    contentQuality: z.boolean().optional(),
    clarity: z.boolean().optional(),
    examples: z.boolean().optional(),
    length: z.boolean().optional(),
    difficulty: z.boolean().optional(),
    other: z.string().optional(),
  }).optional(),
  requestRegeneration: z.boolean().optional(),
  regenerationReason: z.string().optional(),
});

const updateFeedbackSchema = z.object({
  feedbackId: z.string(),
  rating: z.number().min(1).max(5).optional(),
  feedbackText: z.string().optional(),
  isHelpful: z.boolean().optional(),
  suggestedImprovements: z.object({
    contentQuality: z.boolean().optional(),
    clarity: z.boolean().optional(),
    examples: z.boolean().optional(),
    length: z.boolean().optional(),
    difficulty: z.boolean().optional(),
    other: z.string().optional(),
  }).optional(),
  requestRegeneration: z.boolean().optional(),
  regenerationReason: z.string().optional(),
});

export const learningFeedbackRouter = router({
  /**
   * Submit feedback for learning content
   */
  submitFeedback: protectedProcedure
    .input(submitFeedbackSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        // Verify the content exists and user has access
        const content = await db
          .select()
          .from(learningContent)
          .where(eq(learningContent.id, input.contentId))
          .limit(1);

        if (!content.length) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Learning content not found',
          });
        }

        // Check if user already submitted feedback for this content
        const existingFeedback = await db
          .select()
          .from(learningContentFeedback)
          .where(
            and(
              eq(learningContentFeedback.contentId, input.contentId),
              eq(learningContentFeedback.userId, ctx.userId!)
            )
          )
          .limit(1);

        if (existingFeedback.length > 0) {
          throw new TRPCError({
            code: 'CONFLICT',
            message: 'You have already submitted feedback for this content. Use update instead.',
          });
        }

        const now = new Date();
        const feedbackId = crypto.randomUUID();

        const newFeedback = {
          id: feedbackId,
          contentId: input.contentId,
          userId: ctx.userId!,
          rating: input.rating,
          feedbackText: input.feedbackText,
          isHelpful: input.isHelpful,
          suggestedImprovements: input.suggestedImprovements as any,
          requestRegeneration: input.requestRegeneration || false,
          regenerationReason: input.regenerationReason,
          createdAt: now,
          updatedAt: now,
        };

        await db.insert(learningContentFeedback).values(newFeedback);

        return {
          success: true,
          feedback: newFeedback,
          message: 'Feedback submitted successfully',
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        console.error('Error submitting feedback:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to submit feedback',
        });
      }
    }),

  /**
   * Update existing feedback
   */
  updateFeedback: protectedProcedure
    .input(updateFeedbackSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        // Verify the feedback exists and belongs to the user
        const existingFeedback = await db
          .select()
          .from(learningContentFeedback)
          .where(
            and(
              eq(learningContentFeedback.id, input.feedbackId),
              eq(learningContentFeedback.userId, ctx.userId!)
            )
          )
          .limit(1);

        if (!existingFeedback.length) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Feedback not found or you do not have permission to update it',
          });
        }

        const updateData: any = {
          updatedAt: new Date(),
        };

        // Only update provided fields
        if (input.rating !== undefined) updateData.rating = input.rating;
        if (input.feedbackText !== undefined) updateData.feedbackText = input.feedbackText;
        if (input.isHelpful !== undefined) updateData.isHelpful = input.isHelpful;
        if (input.suggestedImprovements !== undefined) updateData.suggestedImprovements = input.suggestedImprovements;
        if (input.requestRegeneration !== undefined) updateData.requestRegeneration = input.requestRegeneration;
        if (input.regenerationReason !== undefined) updateData.regenerationReason = input.regenerationReason;

        await db
          .update(learningContentFeedback)
          .set(updateData)
          .where(eq(learningContentFeedback.id, input.feedbackId));

        return {
          success: true,
          message: 'Feedback updated successfully',
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        console.error('Error updating feedback:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to update feedback',
        });
      }
    }),

  /**
   * Get feedback for specific content
   */
  getFeedback: protectedProcedure
    .input(z.object({ contentId: z.string() }))
    .query(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        const feedback = await db
          .select()
          .from(learningContentFeedback)
          .where(
            and(
              eq(learningContentFeedback.contentId, input.contentId),
              eq(learningContentFeedback.userId, ctx.userId!)
            )
          )
          .limit(1);

        return {
          success: true,
          feedback: feedback[0] || null,
        };
      } catch (error) {
        console.error('Error fetching feedback:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch feedback',
        });
      }
    }),

  /**
   * Get user's feedback history
   */
  getFeedbackHistory: protectedProcedure
    .input(z.object({
      limit: z.number().min(1).max(100).default(20),
      offset: z.number().min(0).default(0),
    }))
    .query(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        const feedbackHistory = await db
          .select({
            feedback: learningContentFeedback,
            content: {
              id: learningContent.id,
              title: learningContent.title,
              description: learningContent.description,
            },
          })
          .from(learningContentFeedback)
          .leftJoin(learningContent, eq(learningContentFeedback.contentId, learningContent.id))
          .where(eq(learningContentFeedback.userId, ctx.userId!))
          .orderBy(desc(learningContentFeedback.updatedAt))
          .limit(input.limit)
          .offset(input.offset);

        return {
          success: true,
          feedback: feedbackHistory,
          pagination: {
            limit: input.limit,
            offset: input.offset,
            hasMore: feedbackHistory.length === input.limit,
          },
        };
      } catch (error) {
        console.error('Error fetching feedback history:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch feedback history',
        });
      }
    }),

  /**
   * Delete feedback
   */
  deleteFeedback: protectedProcedure
    .input(z.object({ feedbackId: z.string() }))
    .mutation(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        // Verify the feedback exists and belongs to the user
        const existingFeedback = await db
          .select()
          .from(learningContentFeedback)
          .where(
            and(
              eq(learningContentFeedback.id, input.feedbackId),
              eq(learningContentFeedback.userId, ctx.userId!)
            )
          )
          .limit(1);

        if (!existingFeedback.length) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Feedback not found or you do not have permission to delete it',
          });
        }

        await db
          .delete(learningContentFeedback)
          .where(eq(learningContentFeedback.id, input.feedbackId));

        return {
          success: true,
          message: 'Feedback deleted successfully',
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        console.error('Error deleting feedback:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to delete feedback',
        });
      }
    }),

  /**
   * Get content feedback statistics (for content creators)
   */
  getContentStats: protectedProcedure
    .input(z.object({ contentId: z.string() }))
    .query(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        // Verify user owns the content
        const content = await db
          .select()
          .from(learningContent)
          .where(
            and(
              eq(learningContent.id, input.contentId),
              eq(learningContent.userId, ctx.userId!)
            )
          )
          .limit(1);

        if (!content.length) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Content not found or you do not have permission to view its statistics',
          });
        }

        const allFeedback = await db
          .select()
          .from(learningContentFeedback)
          .where(eq(learningContentFeedback.contentId, input.contentId));

        const totalFeedback = allFeedback.length;
        const averageRating = totalFeedback > 0 
          ? allFeedback.reduce((sum, f) => sum + f.rating, 0) / totalFeedback 
          : 0;
        
        const helpfulCount = allFeedback.filter(f => f.isHelpful === true).length;
        const regenerationRequests = allFeedback.filter(f => f.requestRegeneration === true).length;

        // Count improvement suggestions
        const improvementCounts = {
          contentQuality: 0,
          clarity: 0,
          examples: 0,
          length: 0,
          difficulty: 0,
          other: 0,
        };

        allFeedback.forEach(f => {
          if (f.suggestedImprovements) {
            const improvements = f.suggestedImprovements as any;
            Object.keys(improvementCounts).forEach(key => {
              if (improvements[key]) {
                improvementCounts[key as keyof typeof improvementCounts]++;
              }
            });
          }
        });

        return {
          success: true,
          stats: {
            totalFeedback,
            averageRating: Math.round(averageRating * 10) / 10,
            helpfulCount,
            regenerationRequests,
            improvementCounts,
            ratingDistribution: {
              1: allFeedback.filter(f => f.rating === 1).length,
              2: allFeedback.filter(f => f.rating === 2).length,
              3: allFeedback.filter(f => f.rating === 3).length,
              4: allFeedback.filter(f => f.rating === 4).length,
              5: allFeedback.filter(f => f.rating === 5).length,
            },
          },
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }

        console.error('Error fetching content stats:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch content statistics',
        });
      }
    }),
});
