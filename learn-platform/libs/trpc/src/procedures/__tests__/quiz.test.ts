/**
 * @jest-environment node
 */

import { jest } from '@jest/globals';
import { TRPCError } from '@trpc/server';
import { quizRouter } from '../quiz';
import { createCallerFactory } from '../../trpc';

// Mock database connection
const mockDb = {
  select: jest.fn(),
  insert: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
};

const mockQuery = {
  from: jest.fn(() => mockQuery),
  where: jest.fn(() => mockQuery),
  innerJoin: jest.fn(() => mockQuery),
  leftJoin: jest.fn(() => mockQuery),
  orderBy: jest.fn(() => mockQuery),
  limit: jest.fn(() => mockQuery),
  offset: jest.fn(() => mockQuery),
  set: jest.fn(() => mockQuery),
  values: jest.fn(() => mockQuery),
};

// Setup mock returns
mockDb.select.mockReturnValue(mockQuery);
mockDb.insert.mockReturnValue(mockQuery);
mockDb.update.mockReturnValue(mockQuery);

jest.mock('@learn-platform/db/connection', () => ({
  createDatabaseConnectionFromEnv: jest.fn(() => ({ db: mockDb })),
}));

jest.mock('@learn-platform/db', () => ({
  quiz: {
    id: 'quiz.id',
    title: 'quiz.title',
    learningContentId: 'quiz.learningContentId',
    createdBy: 'quiz.createdBy',
    isPublic: 'quiz.isPublic',
  },
  quizAttempt: {
    id: 'quizAttempt.id',
    quizId: 'quizAttempt.quizId',
    userId: 'quizAttempt.userId',
    isCompleted: 'quizAttempt.isCompleted',
    answers: 'quizAttempt.answers',
  },
  quizProgress: {
    id: 'quizProgress.id',
    attemptId: 'quizProgress.attemptId',
  },
  quizFeedback: {
    id: 'quizFeedback.id',
    quizId: 'quizFeedback.quizId',
    userId: 'quizFeedback.userId',
  },
  learningContent: {
    id: 'learningContent.id',
    isPublic: 'learningContent.isPublic',
    userId: 'learningContent.userId',
  },
  eq: jest.fn(),
  and: jest.fn(),
  or: jest.fn(),
  desc: jest.fn(),
}));

// Mock AI services
jest.mock('@learn-platform/ai', () => ({
  generateQuizFromContent: jest.fn(),
  evaluateQuizAnswers: jest.fn(),
}));

// Mock crypto
Object.defineProperty(global, 'crypto', {
  value: {
    randomUUID: jest.fn(() => 'mock-uuid-123'),
  },
});

const createCaller = createCallerFactory(quizRouter);

// Mock context
const mockContext = {
  env: {
    DATABASE_URL: 'mock-db-url',
  },
  userId: 'user-123',
  isAuthenticated: true,
};

const mockLearningContent = {
  id: 'content-1',
  title: 'Test Content',
  isPublic: true,
  userId: 'user-123',
  steps: [
    {
      id: 'step-1',
      title: 'Test Step',
      blocks: [
        { id: 'block-1', type: 'paragraph', data: 'Test content for quiz generation' }
      ],
    },
  ],
};

const mockGeneratedQuiz = {
  id: 'quiz-123',
  title: 'Generated Quiz',
  description: 'Test quiz',
  questions: [
    {
      id: 'q1',
      type: 'multipleChoice',
      question: 'Test question?',
      options: ['A', 'B', 'C', 'D'],
      correctAnswerIndex: 1,
      points: 10,
    },
  ],
  estimatedDuration: 5,
  totalPoints: 10,
  metadata: {
    generatedAt: '2024-01-01T00:00:00Z',
    aiModel: 'test-model',
    sourceStepsUsed: ['step-1'],
    difficultyDistribution: { medium: 1 },
    typeDistribution: { multipleChoice: 1 },
  },
};

describe('Quiz tRPC Procedures', () => {
  let caller: ReturnType<typeof createCaller>;

  beforeEach(() => {
    jest.clearAllMocks();
    caller = createCaller(mockContext);
  });

  describe('generate', () => {
    it('generates quiz successfully for authenticated user', async () => {
      // Mock learning content query
      mockQuery.limit.mockResolvedValueOnce([mockLearningContent]);
      
      // Mock AI generation
      const { generateQuizFromContent } = await import('@learn-platform/ai');
      (generateQuizFromContent as jest.MockedFunction<typeof generateQuizFromContent>)
        .mockResolvedValue(mockGeneratedQuiz);

      // Mock quiz insertion
      mockQuery.values.mockResolvedValueOnce(undefined);

      const input = {
        learningContentId: 'content-1',
        quizTypes: ['multipleChoice' as const],
        difficulty: 'medium' as const,
        questionsPerType: 1,
        includeHints: true,
        includeExplanations: true,
      };

      const result = await caller.generate(input);

      expect(result.success).toBe(true);
      expect(result.quiz).toMatchObject({
        id: expect.any(String),
        title: 'Generated Quiz',
        learningContentId: 'content-1',
        createdBy: 'user-123',
      });

      expect(generateQuizFromContent).toHaveBeenCalledWith(
        mockLearningContent,
        expect.objectContaining(input),
        expect.objectContaining({
          validateContent: true,
          retryOnFailure: true,
        })
      );
    });

    it('throws error when learning content not found', async () => {
      mockQuery.limit.mockResolvedValueOnce([]);

      const input = {
        learningContentId: 'nonexistent',
        quizTypes: ['multipleChoice' as const],
        difficulty: 'medium' as const,
        questionsPerType: 1,
        includeHints: true,
        includeExplanations: true,
      };

      await expect(caller.generate(input)).rejects.toThrow(
        expect.objectContaining({
          code: 'NOT_FOUND',
          message: 'Learning content not found or access denied',
        })
      );
    });

    it('throws error when AI generation fails', async () => {
      mockQuery.limit.mockResolvedValueOnce([mockLearningContent]);
      
      const { generateQuizFromContent } = await import('@learn-platform/ai');
      (generateQuizFromContent as jest.MockedFunction<typeof generateQuizFromContent>)
        .mockRejectedValue(new Error('AI service error'));

      const input = {
        learningContentId: 'content-1',
        quizTypes: ['multipleChoice' as const],
        difficulty: 'medium' as const,
        questionsPerType: 1,
        includeHints: true,
        includeExplanations: true,
      };

      await expect(caller.generate(input)).rejects.toThrow(
        expect.objectContaining({
          code: 'INTERNAL_SERVER_ERROR',
        })
      );
    });
  });

  describe('getAll', () => {
    it('returns quizzes for authenticated user', async () => {
      const mockQuizzes = [
        {
          id: 'quiz-1',
          title: 'Test Quiz 1',
          difficulty: 'medium',
          questions: [{ id: 'q1' }, { id: 'q2' }], // Mock questions array
          createdAt: '2024-01-01T00:00:00Z',
        },
        {
          id: 'quiz-2',
          title: 'Test Quiz 2',
          difficulty: 'hard',
          questions: [{ id: 'q3' }],
          createdAt: '2024-01-02T00:00:00Z',
        },
      ];

      mockQuery.offset.mockResolvedValueOnce(mockQuizzes);

      const result = await caller.getAll({
        limit: 20,
        offset: 0,
        includePublic: true,
      });

      expect(result.quizzes).toHaveLength(2);
      expect(result.quizzes[0]).toMatchObject({
        id: 'quiz-1',
        title: 'Test Quiz 1',
        questionCount: 2,
      });
      expect(result.hasMore).toBe(false);
    });

    it('filters quizzes by learning content ID', async () => {
      mockQuery.offset.mockResolvedValueOnce([]);

      await caller.getAll({
        learningContentId: 'content-1',
        limit: 20,
        offset: 0,
      });

      // Verify that where conditions were applied
      expect(mockQuery.where).toHaveBeenCalled();
    });
  });

  describe('startAttempt', () => {
    it('creates new quiz attempt successfully', async () => {
      const mockQuiz = {
        id: 'quiz-1',
        isPublic: true,
        allowRetakes: true,
        questions: [{ id: 'q1' }, { id: 'q2' }],
        shuffleQuestions: false,
      };

      // Mock quiz query
      mockQuery.limit.mockResolvedValueOnce([mockQuiz]);
      
      // Mock existing attempts query (no previous attempts)
      mockQuery.limit.mockResolvedValueOnce([]);
      
      // Mock insertions
      mockQuery.values.mockResolvedValue(undefined);

      const result = await caller.startAttempt({ quizId: 'quiz-1' });

      expect(result.success).toBe(true);
      expect(result.attemptId).toBe('mock-uuid-123');
      expect(result.progressId).toBe('mock-uuid-123');
    });

    it('prevents retakes when not allowed', async () => {
      const mockQuiz = {
        id: 'quiz-1',
        isPublic: true,
        allowRetakes: false,
        createdBy: 'other-user',
      };

      const mockExistingAttempt = {
        id: 'attempt-1',
        isCompleted: true,
      };

      mockQuery.limit
        .mockResolvedValueOnce([mockQuiz])
        .mockResolvedValueOnce([mockExistingAttempt]);

      await expect(caller.startAttempt({ quizId: 'quiz-1' })).rejects.toThrow(
        expect.objectContaining({
          code: 'FORBIDDEN',
          message: 'Retakes are not allowed for this quiz',
        })
      );
    });
  });

  describe('submitAnswer', () => {
    it('submits answer successfully', async () => {
      const mockAttempt = {
        id: 'attempt-1',
        userId: 'user-123',
        isCompleted: false,
        answers: [],
        totalTimeSpent: 0,
      };

      mockQuery.limit.mockResolvedValueOnce([mockAttempt]);
      mockQuery.set.mockResolvedValueOnce(undefined);

      const result = await caller.submitAnswer({
        attemptId: 'attempt-1',
        questionId: 'q1',
        answer: 1,
        timeSpent: 30,
      });

      expect(result.success).toBe(true);
      expect(mockDb.update).toHaveBeenCalled();
    });

    it('prevents answer submission to completed quiz', async () => {
      const mockAttempt = {
        id: 'attempt-1',
        userId: 'user-123',
        isCompleted: true,
        answers: [],
      };

      mockQuery.limit.mockResolvedValueOnce([mockAttempt]);

      await expect(caller.submitAnswer({
        attemptId: 'attempt-1',
        questionId: 'q1',
        answer: 1,
        timeSpent: 30,
      })).rejects.toThrow(
        expect.objectContaining({
          code: 'BAD_REQUEST',
          message: 'Cannot submit answer to completed quiz',
        })
      );
    });
  });

  describe('complete', () => {
    it('completes quiz and calculates score', async () => {
      const mockAttemptData = {
        attempt: {
          id: 'attempt-1',
          userId: 'user-123',
          isCompleted: false,
          answers: [{ questionId: 'q1', answer: 1 }],
        },
        quiz: {
          questions: [mockGeneratedQuiz.questions[0]],
        },
      };

      const mockEvaluation = {
        score: {
          totalPoints: 10,
          earnedPoints: 10,
          percentage: 100,
          correctAnswers: 1,
          totalQuestions: 1,
        },
        questionResults: [
          {
            questionId: 'q1',
            isCorrect: true,
            pointsEarned: 10,
            feedback: 'Correct!',
          },
        ],
      };

      mockQuery.limit.mockResolvedValueOnce([mockAttemptData]);
      
      const { evaluateQuizAnswers } = await import('@learn-platform/ai');
      (evaluateQuizAnswers as jest.MockedFunction<typeof evaluateQuizAnswers>)
        .mockResolvedValue(mockEvaluation);

      mockQuery.set.mockResolvedValueOnce(undefined);

      const result = await caller.complete({ attemptId: 'attempt-1' });

      expect(result.success).toBe(true);
      expect(result.score).toEqual(mockEvaluation.score);
      expect(evaluateQuizAnswers).toHaveBeenCalled();
    });
  });

  describe('submitFeedback', () => {
    it('submits feedback successfully', async () => {
      mockQuery.values.mockResolvedValueOnce(undefined);

      const result = await caller.submitFeedback({
        quizId: 'quiz-1',
        overallRating: 5,
        comments: 'Great quiz!',
        isAnonymous: false,
      });

      expect(result.success).toBe(true);
      expect(mockDb.insert).toHaveBeenCalled();
    });
  });

  describe('getMyAttempts', () => {
    it('returns user attempts', async () => {
      const mockAttempts = [
        {
          id: 'attempt-1',
          quizId: 'quiz-1',
          startedAt: '2024-01-01T00:00:00Z',
          isCompleted: true,
          score: { percentage: 85 },
          quizTitle: 'Test Quiz',
        },
      ];

      mockQuery.offset.mockResolvedValueOnce(mockAttempts);

      const result = await caller.getMyAttempts({
        limit: 20,
        offset: 0,
      });

      expect(result.attempts).toHaveLength(1);
      expect(result.attempts[0]).toMatchObject({
        id: 'attempt-1',
        quizTitle: 'Test Quiz',
      });
    });
  });
});
