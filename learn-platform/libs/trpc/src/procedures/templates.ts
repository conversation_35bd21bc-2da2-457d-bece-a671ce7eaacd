/**
 * tRPC procedures for explainer template management
 */

import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { router, publicProcedure, protectedProcedure } from '../router';
import { createDatabaseConnectionFromEnv } from '@learn-platform/db/connection';
import { explainerTemplates, eq, and } from '@learn-platform/db/schema';

// Validation schemas
const stepSchema = z.object({
  id: z.string(),
  title: z.string(),
  icon: z.string(),
  blocks: z.array(z.object({
    id: z.string(),
    type: z.string(),
    data: z.any(),
    isEditing: z.boolean().optional(),
  })),
});

const createTemplateSchema = z.object({
  name: z.string().min(1, 'Template name is required').max(200, 'Template name too long'),
  description: z.string().max(500, 'Description too long').optional(),
  steps: z.array(stepSchema).min(1, 'At least one step is required'),
});

const updateTemplateSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Template name is required').max(200, 'Template name too long').optional(),
  description: z.string().max(500, 'Description too long').optional(),
  steps: z.array(stepSchema).optional(),
});

/**
 * Templates router with CRUD operations
 */
export const templatesRouter = router({
  /**
   * Get all templates for the authenticated user
   */
  getAll: protectedProcedure
    .query(async ({ ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        const templates = await db
          .select({
            id: explainerTemplates.id,
            name: explainerTemplates.name,
            description: explainerTemplates.description,
            createdAt: explainerTemplates.createdAt,
            updatedAt: explainerTemplates.updatedAt,
            stepCount: explainerTemplates.steps, // We'll process this to get count
          })
          .from(explainerTemplates)
          .where(eq(explainerTemplates.userId, ctx.userId!))
          .orderBy(explainerTemplates.updatedAt);

        // Process templates to add step count
        const processedTemplates = templates.map(template => ({
          ...template,
          stepCount: Array.isArray(template.stepCount) ? template.stepCount.length : 0,
          steps: undefined, // Remove steps from listing for performance
        }));

        return {
          success: true,
          templates: processedTemplates,
        };
      } catch (error) {
        console.error('Error fetching templates:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch templates',
        });
      }
    }),

  /**
   * Get a specific template by ID
   */
  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        const template = await db
          .select()
          .from(explainerTemplates)
          .where(
            and(
              eq(explainerTemplates.id, input.id),
              eq(explainerTemplates.userId, ctx.userId!)
            )
          )
          .limit(1);

        if (!template.length) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Template not found',
          });
        }

        return {
          success: true,
          template: template[0],
        };
      } catch (error) {
        if (error instanceof TRPCError) throw error;

        console.error('Error fetching template:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to fetch template',
        });
      }
    }),

  /**
   * Create a new template
   */
  create: protectedProcedure
    .input(createTemplateSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        const templateId = crypto.randomUUID();
        const now = new Date();

        const newTemplate = {
          id: templateId,
          name: input.name,
          description: input.description || '',
          steps: input.steps,
          userId: ctx.userId!,
          createdAt: now,
          updatedAt: now,
        };

        await db.insert(explainerTemplates).values(newTemplate);

        return {
          success: true,
          template: newTemplate,
          message: 'Template created successfully',
        };
      } catch (error) {
        console.error('Error creating template:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to create template',
        });
      }
    }),

  /**
   * Update an existing template
   */
  update: protectedProcedure
    .input(updateTemplateSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        // First verify the template exists and belongs to the user
        const existingTemplate = await db
          .select()
          .from(explainerTemplates)
          .where(
            and(
              eq(explainerTemplates.id, input.id),
              eq(explainerTemplates.userId, ctx.userId!)
            )
          )
          .limit(1);

        if (!existingTemplate.length) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Template not found',
          });
        }

        // Prepare update data
        const updateData: any = {
          updatedAt: new Date(),
        };

        if (input.name !== undefined) updateData.name = input.name;
        if (input.description !== undefined) updateData.description = input.description;
        if (input.steps !== undefined) updateData.steps = input.steps;

        // Update the template
        await db
          .update(explainerTemplates)
          .set(updateData)
          .where(eq(explainerTemplates.id, input.id));

        return {
          success: true,
          message: 'Template updated successfully',
        };
      } catch (error) {
        if (error instanceof TRPCError) throw error;

        console.error('Error updating template:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to update template',
        });
      }
    }),

  /**
   * Delete a template
   */
  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      try {
        const { db } = createDatabaseConnectionFromEnv(ctx.env);

        // First verify the template exists and belongs to the user
        const existingTemplate = await db
          .select()
          .from(explainerTemplates)
          .where(
            and(
              eq(explainerTemplates.id, input.id),
              eq(explainerTemplates.userId, ctx.userId!)
            )
          )
          .limit(1);

        if (!existingTemplate.length) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Template not found',
          });
        }

        // Delete the template
        await db
          .delete(explainerTemplates)
          .where(eq(explainerTemplates.id, input.id));

        return {
          success: true,
          message: 'Template deleted successfully',
        };
      } catch (error) {
        if (error instanceof TRPCError) throw error;

        console.error('Error deleting template:', error);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to delete template',
        });
      }
    }),
});
