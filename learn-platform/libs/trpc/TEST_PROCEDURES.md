# Testing the Enhanced tRPC Library

## Quick Test Commands

### 1. Build Tests
```bash
# Test tRPC library build
npx nx build trpc

# Test API app build
npx nx build api

# Test all builds
npx nx run-many --target=build --all
```

### 2. Start the API Server
```bash
cd apps/api
bun run dev
```

### 3. Test API Endpoints

#### Health Check (Public)
```bash
curl http://localhost:8787/trpc/health
```

Expected response:
```json
{
  "result": {
    "data": {
      "status": "ok",
      "timestamp": "2024-01-01T00:00:00.000Z",
      "message": "tRPC server is running on Cloudflare Workers",
      "requestId": "...",
      "country": "unknown",
      "version": "1.0.0"
    }
  }
}
```

#### Greeting (Public with Input)
```bash
curl -X POST http://localhost:8787/trpc/greeting \
  -H "Content-Type: application/json" \
  -d '{"name": "World"}'
```

#### Get User (Protected - Should Fail Without Auth)
```bash
curl -X POST http://localhost:8787/trpc/getUser \
  -H "Content-Type: application/json" \
  -d '{"userId": "user-123"}'
```

Expected: 401 Unauthorized

#### Get User (Protected - With Mock Auth)
```bash
curl -X POST http://localhost:8787/trpc/getUser \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer mock-token-123" \
  -d '{"userId": "user-123"}'
```

#### Get Current User (Protected)
```bash
curl -X POST http://localhost:8787/trpc/me \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer mock-token-123"
```

#### Admin Stats (Admin Only)
```bash
curl -X POST http://localhost:8787/trpc/adminStats \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer admin-token-123"
```

## Integration Test Script

Create a simple test script to verify all endpoints:

```typescript
// test-api.ts
const BASE_URL = 'http://localhost:8787/trpc';

async function testEndpoint(name: string, url: string, options: RequestInit = {}) {
  try {
    const response = await fetch(`${BASE_URL}/${url}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });
    
    const data = await response.json();
    console.log(`✅ ${name}:`, response.status, data);
  } catch (error) {
    console.log(`❌ ${name}:`, error.message);
  }
}

async function runTests() {
  console.log('🧪 Testing tRPC API endpoints...\n');
  
  // Public endpoints
  await testEndpoint('Health Check', 'health');
  await testEndpoint('Greeting', 'greeting', {
    body: JSON.stringify({ name: 'Test User' }),
  });
  
  // Protected endpoints (should fail)
  await testEndpoint('Get User (No Auth)', 'getUser', {
    body: JSON.stringify({ userId: 'user-123' }),
  });
  
  // Protected endpoints (with auth)
  const authHeaders = { 'Authorization': 'Bearer mock-token-123' };
  
  await testEndpoint('Get User (With Auth)', 'getUser', {
    headers: authHeaders,
    body: JSON.stringify({ userId: 'user-123' }),
  });
  
  await testEndpoint('Get Me', 'me', {
    headers: authHeaders,
  });
  
  await testEndpoint('Update User', 'updateUser', {
    headers: authHeaders,
    body: JSON.stringify({ name: 'Updated Name' }),
  });
  
  console.log('\n🎉 Tests completed!');
}

runTests();
```

Run with: `bun run test-api.ts`

## Expected Behavior

### ✅ Working Features:
1. **Public procedures** work without authentication
2. **Protected procedures** require Bearer token
3. **Context enhancement** provides rich request metadata
4. **Error handling** returns proper HTTP status codes
5. **Type safety** maintained throughout
6. **Cloudflare Workers** compatibility verified

### 🔧 Mock Authentication:
- Any Bearer token will create a mock authenticated user
- User ID is derived from the token (last 8 characters)
- Default role is 'user'
- Admin procedures require role 'admin' (currently not implemented in mock)

### 🚀 Next Steps:
1. **Implement real JWT authentication** by updating `parseUserSession` function
2. **Add database integration** with Drizzle ORM or your preferred ORM
3. **Create domain-specific sub-routers** for different API areas
4. **Add comprehensive tests** with Jest or your testing framework
5. **Set up CI/CD** to automatically test the API

## Troubleshooting

### Common Issues:
1. **Port conflicts**: Make sure port 8787 is available
2. **CORS errors**: Check CORS configuration in `apps/api/src/index.ts`
3. **Build errors**: Run `npx nx build trpc` to check for TypeScript issues
4. **Import errors**: Verify workspace dependencies in package.json files

### Debug Mode:
The error formatter includes stack traces in development mode. To enable:
```typescript
// In your Cloudflare Worker environment
globalThis.ENVIRONMENT = 'development';
```
