# tRPC Library Usage Examples

This document provides practical examples of how to use and extend the `@learn-platform/trpc` library.

## Client-Side Usage

### React Query Integration

```typescript
// utils/trpc.ts
import { createTRPCReact } from '@trpc/react-query';
import type { AppRouter } from '@learn-platform/trpc';

export const trpc = createTRPCReact<AppRouter>();

// pages/_app.tsx
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { httpBatchLink } from '@trpc/client';
import { trpc } from '../utils/trpc';

const queryClient = new QueryClient();

const trpcClient = trpc.createClient({
  links: [
    httpBatchLink({
      url: 'http://localhost:8787/trpc',
      headers() {
        const token = localStorage.getItem('auth-token');
        return token ? { authorization: `Bearer ${token}` } : {};
      },
    }),
  ],
});

function MyApp({ Component, pageProps }) {
  return (
    <trpc.Provider client={trpcClient} queryClient={queryClient}>
      <QueryClientProvider client={queryClient}>
        <Component {...pageProps} />
      </QueryClientProvider>
    </trpc.Provider>
  );
}
```

### Using Procedures in Components

```typescript
// components/UserProfile.tsx
import { trpc } from '../utils/trpc';

export function UserProfile({ userId }: { userId: string }) {
  const { data: user, isLoading, error } = trpc.getUser.useQuery({ userId });
  const updateUserMutation = trpc.updateUser.useMutation();

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  const handleUpdateUser = async (data: { name?: string; email?: string }) => {
    try {
      await updateUserMutation.mutateAsync(data);
      // Handle success
    } catch (error) {
      // Handle error
    }
  };

  return (
    <div>
      <h1>{user?.name}</h1>
      <p>{user?.email}</p>
      <button onClick={() => handleUpdateUser({ name: 'New Name' })}>
        Update Name
      </button>
    </div>
  );
}
```

## Extending the Router

### Creating Domain-Specific Sub-Routers

```typescript
// libs/trpc/src/routers/posts.ts
import { z } from 'zod';
import { router, publicProcedure, protectedProcedure } from '../router';

export const postsRouter = router({
  getAll: publicProcedure
    .input(z.object({
      limit: z.number().min(1).max(100).default(10),
      cursor: z.string().optional(),
    }))
    .query(({ input, ctx }) => {
      // Fetch posts from database
      return {
        posts: [],
        nextCursor: null,
      };
    }),

  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(({ input, ctx }) => {
      // Fetch single post
      return {
        id: input.id,
        title: 'Sample Post',
        content: 'Post content...',
        authorId: 'user-123',
      };
    }),

  create: protectedProcedure
    .input(z.object({
      title: z.string().min(1).max(200),
      content: z.string().min(1),
    }))
    .mutation(({ input, ctx }) => {
      // Create new post
      return {
        id: 'post-123',
        title: input.title,
        content: input.content,
        authorId: ctx.userSession.userId,
        createdAt: new Date().toISOString(),
      };
    }),
});

// Update main router to include posts
// libs/trpc/src/router.ts
export const appRouter = router({
  // ... existing procedures
  posts: postsRouter,
});
```

### Custom Middleware

```typescript
// libs/trpc/src/middleware/logging.ts
import { middleware } from '../router';

export const loggingMiddleware = middleware(({ ctx, next, path, type }) => {
  const start = Date.now();
  
  console.log(`[tRPC] ${type} ${path} - Start`);
  
  return next().then((result) => {
    const duration = Date.now() - start;
    console.log(`[tRPC] ${type} ${path} - Success (${duration}ms)`);
    return result;
  }).catch((error) => {
    const duration = Date.now() - start;
    console.log(`[tRPC] ${type} ${path} - Error (${duration}ms):`, error.message);
    throw error;
  });
});

// Usage in procedures
export const loggedProcedure = publicProcedure.use(loggingMiddleware);
```

### Rate Limiting Middleware

```typescript
// libs/trpc/src/middleware/rateLimit.ts
import { TRPCError } from '@trpc/server';
import { middleware } from '../router';

const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

export const rateLimitMiddleware = (maxRequests = 100, windowMs = 60000) => {
  return middleware(({ ctx, next }) => {
    const key = ctx.ip;
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Clean up old entries
    for (const [k, v] of rateLimitStore.entries()) {
      if (v.resetTime < windowStart) {
        rateLimitStore.delete(k);
      }
    }
    
    const current = rateLimitStore.get(key) || { count: 0, resetTime: now + windowMs };
    
    if (current.count >= maxRequests && current.resetTime > now) {
      throw new TRPCError({
        code: 'TOO_MANY_REQUESTS',
        message: 'Rate limit exceeded',
      });
    }
    
    current.count++;
    rateLimitStore.set(key, current);
    
    return next();
  });
};
```

## Database Integration Examples

### With Drizzle ORM

```typescript
// libs/trpc/src/db/schema.ts
import { sqliteTable, text, integer } from 'drizzle-orm/sqlite-core';

export const users = sqliteTable('users', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  email: text('email').notNull().unique(),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
});

// libs/trpc/src/context.ts (updated)
import { drizzle } from 'drizzle-orm/d1';
import * as schema from './db/schema';

export function createContext(opts: CreateContextOptions) {
  const { request, env } = opts;
  
  // Initialize database
  const db = drizzle(env.DB, { schema });
  
  return {
    // ... other context properties
    db,
  };
}

// Usage in procedures
export const getUserProcedure = publicProcedure
  .input(z.object({ userId: z.string() }))
  .query(async ({ input, ctx }) => {
    const user = await ctx.db
      .select()
      .from(schema.users)
      .where(eq(schema.users.id, input.userId))
      .get();
    
    if (!user) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'User not found',
      });
    }
    
    return user;
  });
```

## Testing Examples

### Unit Testing Procedures

```typescript
// libs/trpc/src/__tests__/router.test.ts
import { createContext } from '../context';
import { appRouter } from '../router';

describe('tRPC Router', () => {
  const mockContext = createContext({
    request: new Request('http://localhost/test'),
    env: {},
  });

  it('should return health status', async () => {
    const caller = appRouter.createCaller(mockContext);
    const result = await caller.health();
    
    expect(result.status).toBe('ok');
    expect(result.message).toContain('tRPC server');
  });

  it('should require authentication for protected procedures', async () => {
    const caller = appRouter.createCaller(mockContext);
    
    await expect(caller.me()).rejects.toThrow('UNAUTHORIZED');
  });
});
```

### Integration Testing

```typescript
// apps/api/src/__tests__/integration.test.ts
import { testClient } from 'hono/testing';
import app from '../index';

describe('API Integration', () => {
  it('should handle tRPC requests', async () => {
    const res = await testClient(app).trpc.health.$get();
    const data = await res.json();
    
    expect(data.result.data.status).toBe('ok');
  });
});
```

## Deployment Considerations

### Environment Variables

```toml
# wrangler.toml
[env.production.vars]
JWT_SECRET = "your-production-jwt-secret"
DATABASE_URL = "your-database-url"

[env.development.vars]
JWT_SECRET = "dev-jwt-secret"
DATABASE_URL = "dev-database-url"
```

### Performance Optimization

```typescript
// Enable response caching for static data
export const getCachedData = publicProcedure
  .query(({ ctx }) => {
    // Set cache headers
    ctx.request.headers.set('Cache-Control', 'public, max-age=3600');
    
    return {
      data: 'cached-data',
      timestamp: new Date().toISOString(),
    };
  });
```

This covers the most common usage patterns and extension points for the tRPC library. Refer to the main README.md for basic setup and configuration details.
