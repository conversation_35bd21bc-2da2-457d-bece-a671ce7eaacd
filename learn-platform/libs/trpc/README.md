# @learn-platform/trpc

A centralized tRPC library for the learning platform monorepo, providing type-safe API endpoints with enhanced context and authentication support for Cloudflare Workers.

## Features

- **Type-safe API endpoints** using tRPC v11+ with full TypeScript inference
- **Enhanced context creation** with user session, request metadata, and Cloudflare Workers support
- **Authentication middleware** with protected and admin procedures
- **Comprehensive error handling** with proper error formatting
- **Cloudflare Workers optimized** with support for CF-specific headers and environment
- **Database connection ready** with placeholder for ORM integration (Drizzle, etc.)

## Installation

This library is already configured as a workspace dependency. Import it in your apps:

```typescript
import { appRouter, createContext, type AppRouter } from '@learn-platform/trpc';
```

## Usage

### Basic Setup (Hono + Cloudflare Workers)

```typescript
import { Hono } from 'hono';
import { trpcServer } from '@hono/trpc-server';
import { appRouter, createContext } from '@learn-platform/trpc';

const app = new Hono();

app.use('/trpc/*', trpcServer({
  router: appRouter,
  createContext: (opts, c) => {
    return createContext({
      request: opts.req,
      env: c.env, // Cloudflare Worker environment bindings
    });
  },
}));

export default app;
```

### Available Procedures

#### Public Procedures

- `health` - Health check endpoint with system information
- `greeting` - Example greeting with input validation
- `echo` - Echo endpoint for testing

#### Protected Procedures (Requires Authentication)

- `getUser` - Get user information by ID
- `me` - Get current authenticated user
- `updateUser` - Update user profile

#### Admin Procedures (Requires Admin Role)

- `adminStats` - Get admin statistics

### Context Features

The enhanced context provides:

```typescript
interface Context {
  // Core request information
  request: Request;
  env: any; // Cloudflare environment bindings
  url: URL;
  
  // Enhanced metadata
  ip: string; // Client IP from CF-Connecting-IP
  country: string; // Country from CF-IPCountry
  userAgent: string;
  timestamp: string;
  requestId: string; // CF-Ray or generated UUID
  
  // User session and authentication
  userSession: UserSession;
  isAuthenticated: boolean;
  
  // Utility functions
  getUserId(): string | null;
  getUserRoles(): string[];
  hasRole(role: string): boolean;
  
  // Performance monitoring
  startTime: number;
}
```

### Authentication

The library includes a mock authentication system. To implement real authentication:

1. **Update the `parseUserSession` function** in `context.ts` to decode actual JWT tokens
2. **Add your JWT secret** to Cloudflare Worker environment variables
3. **Implement proper token validation**

Example with real JWT:

```typescript
import jwt from 'jsonwebtoken';

function parseUserSession(token?: string, env?: any): UserSession {
  if (!token) {
    return { isAuthenticated: false, userId: null, roles: [] };
  }
  
  try {
    const decoded = jwt.verify(token, env.JWT_SECRET) as any;
    return {
      isAuthenticated: true,
      userId: decoded.sub,
      roles: decoded.roles || ['user'],
      email: decoded.email,
      name: decoded.name,
    };
  } catch (error) {
    return { isAuthenticated: false, userId: null, roles: [] };
  }
}
```

### Database Integration

To add database support (e.g., with Drizzle ORM):

1. **Install your ORM dependencies**:
```bash
bun add drizzle-orm @cloudflare/workers-types
```

2. **Update the context creation**:
```typescript
import { drizzle } from 'drizzle-orm/d1';
import * as schema from './schema';

export function createContext(opts: CreateContextOptions) {
  const { request, env } = opts;
  
  // Initialize database connection
  const db = drizzle(env.DB, { schema });
  
  return {
    // ... other context properties
    db,
  };
}
```

### Error Handling

The library includes comprehensive error handling:

- **TRPCError** for API-specific errors
- **Enhanced error formatting** with stack traces in development
- **Proper HTTP status codes** for different error types

Example error usage:

```typescript
import { TRPCError } from '@trpc/server';

// In your procedure
if (!user) {
  throw new TRPCError({
    code: 'NOT_FOUND',
    message: 'User not found',
  });
}
```

## Development

### Building

```bash
npx nx build trpc
```

### Testing

```bash
npx nx test trpc
```

### Type Checking

```bash
npx nx type-check trpc
```

## Architecture

```
libs/trpc/
├── src/
│   ├── index.ts          # Main exports
│   ├── router.ts         # tRPC router and procedures
│   ├── context.ts        # Context creation and types
│   └── ...
├── package.json          # Dependencies
└── README.md            # This file
```

## Contributing

When adding new procedures:

1. **Define input/output schemas** using Zod
2. **Use appropriate procedure types** (public, protected, admin)
3. **Add proper error handling** with TRPCError
4. **Update TypeScript types** as needed
5. **Add tests** for new functionality

## License

MIT
