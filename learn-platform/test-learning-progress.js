/**
 * Test script to verify that the learningProgress.updateProgress endpoint is working
 */

async function testLearningProgressEndpoint() {
  const baseUrl = 'http://localhost:8787';
  
  try {
    // First, test the health endpoint to make sure the server is running
    console.log('🔍 Testing health endpoint...');
    const healthResponse = await fetch(`${baseUrl}/trpc/health`);
    const healthData = await healthResponse.json();
    console.log('✅ Health check:', healthData);
    
    // Test the tRPC endpoint structure by making a batch request
    console.log('\n🔍 Testing tRPC batch endpoint structure...');
    const batchUrl = `${baseUrl}/trpc/learningProgress.updateProgress?batch=1`;
    
    // Create a test request (this will fail due to auth, but should show the endpoint exists)
    const testData = {
      "0": {
        "contentId": "test-content-id",
        "currentStepIndex": 1,
        "timeSpent": 30,
        "completedSteps": [0]
      }
    };
    
    const response = await fetch(batchUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData)
    });
    
    const responseText = await response.text();
    console.log('📊 Response status:', response.status);
    console.log('📊 Response headers:', Object.fromEntries(response.headers.entries()));
    console.log('📊 Response body:', responseText);
    
    if (response.status === 401) {
      console.log('✅ Endpoint exists! Got expected 401 Unauthorized (auth required)');
    } else if (response.status === 404) {
      console.log('❌ Endpoint not found! The learningProgress.updateProgress procedure is missing');
    } else {
      console.log('🤔 Unexpected response status:', response.status);
    }
    
  } catch (error) {
    console.error('❌ Error testing endpoint:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 Make sure the API server is running on port 8787');
      console.log('   Run: cd apps/api && bun run dev');
    }
  }
}

// Run the test
testLearningProgressEndpoint();
