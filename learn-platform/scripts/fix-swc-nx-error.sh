#!/bin/bash

# SWC/Nx Error Fix Script
# This script systematically fixes SWC parser panics and Nx dependency processing errors

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to run command with error handling
run_command() {
    local description=$1
    local command=$2
    
    print_status $CYAN "   Running: $description"
    if eval "$command"; then
        print_status $GREEN "   ✅ Success: $description"
        return 0
    else
        print_status $RED "   ❌ Failed: $description"
        return 1
    fi
}

# Function to backup important files
backup_files() {
    print_status $BLUE "📋 Creating backup of important files..."
    
    local backup_dir="backup-$(date +%Y%m%d-%H%M%S)"
    mkdir -p "$backup_dir"
    
    # Backup lockfiles and configs
    [ -f "bun.lockb" ] && cp "bun.lockb" "$backup_dir/"
    [ -f "package.json" ] && cp "package.json" "$backup_dir/"
    [ -f "nx.json" ] && cp "nx.json" "$backup_dir/"
    
    print_status $GREEN "✅ Backup created in: $backup_dir"
}

# Main fix function
main() {
    print_status $PURPLE "🔧 SWC/Nx Error Fix Script"
    print_status $PURPLE "=========================="
    
    # Check if we're in the right directory
    if [ ! -f "nx.json" ] || [ ! -f "package.json" ]; then
        print_status $RED "❌ Please run this script from the monorepo root directory"
        exit 1
    fi
    
    backup_files
    
    print_status $BLUE "\n📋 Step 1: Clear All Caches"
    
    # Clear Nx cache
    run_command "Clear Nx cache" "bunx nx reset"
    
    # Clear Next.js caches
    run_command "Clear Next.js cache (web)" "rm -rf apps/web/.next"
    run_command "Clear Next.js cache (admin)" "rm -rf apps/admin/.next"
    
    # Clear dist and tmp directories
    run_command "Clear dist directory" "rm -rf dist"
    run_command "Clear tmp directory" "rm -rf tmp"
    
    # Clear coverage directory
    run_command "Clear coverage directory" "rm -rf coverage"
    
    print_status $BLUE "\n📋 Step 2: Clean Node Modules"
    
    # Remove individual app node_modules (these can cause conflicts)
    run_command "Remove web app node_modules" "rm -rf apps/web/node_modules"
    run_command "Remove admin app node_modules" "rm -rf apps/admin/node_modules"
    run_command "Remove api app node_modules" "rm -rf apps/api/node_modules"
    
    # Remove root node_modules
    run_command "Remove root node_modules" "rm -rf node_modules"
    
    # Remove lockfiles
    run_command "Remove bun lockfile" "rm -f bun.lockb"
    run_command "Remove individual package-lock files" "find . -name 'package-lock.json' -delete"
    
    print_status $BLUE "\n📋 Step 3: Reinstall Dependencies"
    
    # Reinstall dependencies
    run_command "Install dependencies with bun" "bun install"
    
    print_status $BLUE "\n📋 Step 4: Check for Syntax Errors"
    
    # Check TypeScript compilation
    print_status $CYAN "   Checking TypeScript compilation..."
    if bunx tsc --noEmit --skipLibCheck; then
        print_status $GREEN "   ✅ TypeScript compilation successful"
    else
        print_status $YELLOW "   ⚠️  TypeScript compilation issues found"
        print_status $CYAN "   This might be the cause of the SWC parser panic"
    fi
    
    print_status $BLUE "\n📋 Step 5: Test Nx Commands"
    
    # Test basic Nx commands
    run_command "Test Nx graph generation" "NX_DAEMON=false bunx nx graph --dry-run"
    
    if [ $? -eq 0 ]; then
        print_status $GREEN "✅ Nx graph generation successful"
        
        # Try to run the web app
        print_status $BLUE "\n📋 Step 6: Test Web App"
        print_status $CYAN "   Attempting to start web app..."
        
        # Test in background for 10 seconds
        timeout 10s bunx nx run web:dev --verbose 2>&1 | head -20
        
        if [ $? -eq 0 ] || [ $? -eq 124 ]; then  # 124 is timeout exit code
            print_status $GREEN "✅ Web app appears to be starting successfully"
        else
            print_status $RED "❌ Web app still has issues"
            print_status $CYAN "   Proceeding to advanced troubleshooting..."
        fi
    else
        print_status $RED "❌ Nx graph generation still failing"
        print_status $CYAN "   Proceeding to advanced troubleshooting..."
    fi
    
    print_status $BLUE "\n📋 Step 7: Advanced Troubleshooting"
    
    # Check for problematic files
    print_status $CYAN "   Checking for potential problematic files..."
    
    # Look for files with unusual characters or syntax
    find apps/web/src -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" | while read file; do
        if ! node -c "$file" 2>/dev/null; then
            print_status $YELLOW "   ⚠️  Potential syntax issue in: $file"
        fi
    done
    
    # Check for circular dependencies
    print_status $CYAN "   Checking for circular dependencies..."
    if command -v madge >/dev/null 2>&1; then
        madge --circular apps/web/src || print_status $YELLOW "   ⚠️  Circular dependencies detected"
    else
        print_status $YELLOW "   ⚠️  madge not installed, skipping circular dependency check"
    fi
    
    print_status $BLUE "\n📋 Step 8: Alternative Approaches"
    
    # Try with different Nx settings
    print_status $CYAN "   Trying alternative Nx configurations..."
    
    # Disable parallel processing
    run_command "Test with parallel disabled" "NX_DAEMON=false NX_PARALLEL=1 bunx nx run web:dev --dry-run"
    
    # Try with verbose logging
    run_command "Test with verbose logging" "NX_DAEMON=false bunx nx run web:dev --verbose --dry-run"
    
    print_status $BLUE "\n📋 Summary and Next Steps"
    
    print_status $GREEN "✅ Cleanup and reinstallation completed"
    
    print_status $CYAN "\n🔧 Manual Testing Steps:"
    print_status $CYAN "   1. Try: NX_DAEMON=false bunx nx run web:dev"
    print_status $CYAN "   2. If still failing, try: RUST_BACKTRACE=1 NX_DAEMON=false bunx nx run web:dev"
    print_status $CYAN "   3. Check the detailed error output for specific file issues"
    
    print_status $CYAN "\n🔍 If Issues Persist:"
    print_status $CYAN "   1. Check for syntax errors in recently modified files"
    print_status $CYAN "   2. Try running individual TypeScript compilation: bunx tsc --noEmit"
    print_status $CYAN "   3. Consider updating Nx and SWC versions"
    print_status $CYAN "   4. Check for conflicting dependencies"
    
    print_status $CYAN "\n📖 Additional Resources:"
    print_status $CYAN "   • Nx troubleshooting: https://nx.dev/troubleshooting"
    print_status $CYAN "   • SWC issues: https://github.com/swc-project/swc/issues"
    print_status $CYAN "   • Run: ./scripts/verify-auth-setup.sh (after fixing)"
    
    echo ""
}

# Run the fix
main "$@"
