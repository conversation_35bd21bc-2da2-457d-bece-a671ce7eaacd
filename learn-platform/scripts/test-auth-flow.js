#!/usr/bin/env node

/**
 * Authentication Flow Test Script
 * 
 * This script tests the centralized authentication flow by making HTTP requests
 * to verify that both web and admin apps use the same API service.
 */

const https = require('http');
const { URL } = require('url');

// Test configuration
const config = {
  apiService: 'http://localhost:8787',
  webApp: 'http://localhost:3000',
  adminApp: 'http://localhost:3001',
  testEmail: process.env.TEST_USER_EMAIL || '<EMAIL>',
  testPassword: process.env.TEST_USER_PASSWORD || 'testpassword123'
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Simple HTTP request helper
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Auth-Test-Script/1.0',
        ...options.headers
      }
    };

    const req = https.request(requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => data += chunk);
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          data: data
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (options.body) {
      req.write(JSON.stringify(options.body));
    }

    req.end();
  });
}

// Test functions
async function testServiceAvailability() {
  log('blue', '\n📋 Testing Service Availability');
  
  const services = [
    { name: 'API Service', url: config.apiService + '/api/auth/csrf' },
    { name: 'Web App', url: config.webApp },
    { name: 'Admin App', url: config.adminApp }
  ];

  const results = [];
  
  for (const service of services) {
    try {
      const response = await makeRequest(service.url);
      if (response.status < 500) {
        log('green', `✅ ${service.name} - Available (HTTP ${response.status})`);
        results.push({ name: service.name, available: true, status: response.status });
      } else {
        log('red', `❌ ${service.name} - Server Error (HTTP ${response.status})`);
        results.push({ name: service.name, available: false, status: response.status });
      }
    } catch (error) {
      log('red', `❌ ${service.name} - Not responding (${error.message})`);
      results.push({ name: service.name, available: false, error: error.message });
    }
  }
  
  return results;
}

async function testAuthEndpoints() {
  log('blue', '\n📋 Testing Authentication Endpoints');
  
  const endpoints = [
    { name: 'CSRF Token', url: config.apiService + '/api/auth/csrf' },
    { name: 'Session Check', url: config.apiService + '/api/auth/session' }
  ];

  for (const endpoint of endpoints) {
    try {
      const response = await makeRequest(endpoint.url);
      if (response.status === 200 || response.status === 401) {
        log('green', `✅ ${endpoint.name} - Responding (HTTP ${response.status})`);
      } else {
        log('yellow', `⚠️  ${endpoint.name} - Unexpected response (HTTP ${response.status})`);
      }
    } catch (error) {
      log('red', `❌ ${endpoint.name} - Error: ${error.message}`);
    }
  }
}

async function testAuthClientConfiguration() {
  log('blue', '\n📋 Testing Auth Client Configuration');
  
  // This is a simplified test - in a real scenario, you'd check the actual client behavior
  log('cyan', '   Checking if apps point to centralized API...');
  
  // Test if web app login page loads (indicates app is working)
  try {
    const webResponse = await makeRequest(config.webApp + '/login');
    if (webResponse.status === 200) {
      log('green', '✅ Web app login page accessible');
    } else {
      log('yellow', `⚠️  Web app login page returned HTTP ${webResponse.status}`);
    }
  } catch (error) {
    log('red', `❌ Web app login page error: ${error.message}`);
  }

  // Test if admin app login page loads
  try {
    const adminResponse = await makeRequest(config.adminApp + '/login');
    if (adminResponse.status === 200) {
      log('green', '✅ Admin app login page accessible');
    } else {
      log('yellow', `⚠️  Admin app login page returned HTTP ${adminResponse.status}`);
    }
  } catch (error) {
    log('red', `❌ Admin app login page error: ${error.message}`);
  }
}

async function testCentralizedAuth() {
  log('blue', '\n📋 Testing Centralized Authentication');
  
  log('cyan', '   Note: This test verifies that the API service handles auth requests');
  log('cyan', '   For full authentication testing, use the browser-based tests');
  
  // Test that the API service auth endpoints are working
  try {
    // Get CSRF token
    const csrfResponse = await makeRequest(config.apiService + '/api/auth/csrf');
    if (csrfResponse.status === 200) {
      log('green', '✅ CSRF endpoint working on API service');
    } else {
      log('red', `❌ CSRF endpoint failed (HTTP ${csrfResponse.status})`);
      return;
    }

    // Test session endpoint (should return 401 when not authenticated)
    const sessionResponse = await makeRequest(config.apiService + '/api/auth/session');
    if (sessionResponse.status === 401) {
      log('green', '✅ Session endpoint correctly returns 401 when not authenticated');
    } else {
      log('yellow', `⚠️  Session endpoint returned HTTP ${sessionResponse.status} (expected 401)`);
    }

  } catch (error) {
    log('red', `❌ Centralized auth test failed: ${error.message}`);
  }
}

async function runTests() {
  log('cyan', '🔐 Authentication Architecture Test Suite');
  log('cyan', '==========================================');
  
  try {
    // Test 1: Service availability
    const serviceResults = await testServiceAvailability();
    
    // Test 2: Auth endpoints
    await testAuthEndpoints();
    
    // Test 3: Auth client configuration
    await testAuthClientConfiguration();
    
    // Test 4: Centralized authentication
    await testCentralizedAuth();
    
    // Summary
    log('blue', '\n📋 Test Summary');
    const availableServices = serviceResults.filter(s => s.available).length;
    const totalServices = serviceResults.length;
    
    if (availableServices === totalServices) {
      log('green', `🎉 All ${totalServices} services are available and responding`);
      log('cyan', '\n   Next steps:');
      log('cyan', '   1. Open http://localhost:3000/login in your browser');
      log('cyan', '   2. Create a test account or login');
      log('cyan', '   3. Open http://localhost:3001/login in the same browser');
      log('cyan', '   4. Verify you\'re automatically logged in (session sharing)');
      log('cyan', '   5. Check browser network tab to confirm auth requests go to localhost:8787');
    } else {
      log('yellow', `⚠️  ${availableServices}/${totalServices} services available`);
      log('cyan', '\n   To start missing services:');
      log('cyan', '   • API Service: bunx nx run api:dev');
      log('cyan', '   • Web App: bunx nx run web:dev');
      log('cyan', '   • Admin App: bunx nx run admin:dev');
      log('cyan', '   • All services: ./start-dev-servers.sh all');
    }
    
    log('blue', '\n📖 For detailed verification steps, run:');
    log('cyan', '   ./scripts/verify-auth-setup.sh');
    
  } catch (error) {
    log('red', `❌ Test suite failed: ${error.message}`);
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  runTests().catch(error => {
    log('red', `Fatal error: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { runTests, testServiceAvailability, testAuthEndpoints };
