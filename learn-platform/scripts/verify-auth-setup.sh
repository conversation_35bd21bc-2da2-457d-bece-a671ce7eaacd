#!/bin/bash

# Authentication Setup Verification Script
# This script verifies that the centralized authentication architecture is working correctly

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to check if a port is in use
check_port() {
    local port=$1
    local service_name=$2
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_status $GREEN "✅ $service_name (port $port) is running"
        return 0
    else
        print_status $RED "❌ $service_name (port $port) is not running"
        return 1
    fi
}

# Function to test HTTP endpoint
test_endpoint() {
    local url=$1
    local description=$2
    local expected_status=${3:-200}
    
    print_status $CYAN "   Testing: $description"
    
    if command -v curl >/dev/null 2>&1; then
        local response=$(curl -s -o /dev/null -w "%{http_code}" "$url" 2>/dev/null)
        if [ "$response" = "$expected_status" ] || [ "$response" = "404" ] || [ "$response" = "405" ]; then
            print_status $GREEN "   ✅ $description - Responding (HTTP $response)"
            return 0
        else
            print_status $RED "   ❌ $description - Not responding (HTTP $response)"
            return 1
        fi
    else
        print_status $YELLOW "   ⚠️  curl not available, skipping HTTP test"
        return 0
    fi
}

# Function to check environment file
check_env_file() {
    local file_path=$1
    local app_name=$2
    
    print_status $CYAN "   Checking $app_name environment configuration..."
    
    if [ ! -f "$file_path" ]; then
        print_status $RED "   ❌ $file_path not found"
        return 1
    fi
    
    # Check required variables
    local required_vars=("BETTER_AUTH_SECRET" "DATABASE_URL" "NEXT_PUBLIC_API_URL")
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if ! grep -q "^$var=" "$file_path"; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -eq 0 ]; then
        print_status $GREEN "   ✅ All required environment variables present"
        
        # Check if API URL points to centralized service
        local api_url=$(grep "^NEXT_PUBLIC_API_URL=" "$file_path" | cut -d'=' -f2)
        if [[ "$api_url" == *"8787"* ]]; then
            print_status $GREEN "   ✅ API URL correctly points to centralized service ($api_url)"
        else
            print_status $YELLOW "   ⚠️  API URL might not point to centralized service ($api_url)"
        fi
        
        return 0
    else
        print_status $RED "   ❌ Missing environment variables: ${missing_vars[*]}"
        return 1
    fi
}

# Main verification function
main() {
    print_status $PURPLE "🔐 Authentication Setup Verification"
    print_status $PURPLE "====================================="
    
    # Check if we're in the right directory
    if [ ! -f "nx.json" ] || [ ! -f "package.json" ]; then
        print_status $RED "❌ Please run this script from the monorepo root directory"
        exit 1
    fi
    
    print_status $BLUE "\n📋 Step 1: Checking Development Services"
    
    local services_running=0
    
    # Check API service (most important)
    if check_port 8787 "API Service (Cloudflare Workers)"; then
        ((services_running++))
    else
        print_status $YELLOW "   💡 Start with: bunx nx run api:dev"
    fi
    
    # Check web app
    if check_port 3000 "Web App"; then
        ((services_running++))
    else
        print_status $YELLOW "   💡 Start with: bunx nx run web:dev"
    fi
    
    # Check admin app
    if check_port 3001 "Admin App"; then
        ((services_running++))
    else
        print_status $YELLOW "   💡 Start with: bunx nx run admin:dev"
    fi
    
    print_status $BLUE "\n📋 Step 2: Testing Authentication Endpoints"
    
    if [ $services_running -gt 0 ]; then
        # Test API service endpoints
        if check_port 8787 "API Service" >/dev/null 2>&1; then
            test_endpoint "http://localhost:8787/api/auth/csrf" "API Auth CSRF Endpoint"
            test_endpoint "http://localhost:8787/trpc" "API tRPC Endpoint" 404
        fi
        
        # Test web app
        if check_port 3000 "Web App" >/dev/null 2>&1; then
            test_endpoint "http://localhost:3000" "Web App Homepage"
            test_endpoint "http://localhost:3000/login" "Web App Login Page"
        fi
        
        # Test admin app
        if check_port 3001 "Admin App" >/dev/null 2>&1; then
            test_endpoint "http://localhost:3001" "Admin App Homepage"
            test_endpoint "http://localhost:3001/login" "Admin App Login Page"
        fi
    else
        print_status $YELLOW "   ⚠️  No services running, skipping endpoint tests"
    fi
    
    print_status $BLUE "\n📋 Step 3: Verifying Environment Configuration"
    
    # Check environment files
    check_env_file "apps/web/.env.local" "Web App"
    check_env_file "apps/admin/.env.local" "Admin App"
    
    # Check if API service has environment
    if [ -f "apps/api/.env.local" ]; then
        check_env_file "apps/api/.env.local" "API Service"
    else
        print_status $YELLOW "   ⚠️  apps/api/.env.local not found (may use wrangler.toml instead)"
    fi
    
    print_status $BLUE "\n📋 Step 4: Architecture Verification"
    
    # Check auth client configurations
    print_status $CYAN "   Checking auth client configurations..."
    
    # Check web app auth client
    if grep -q "NEXT_PUBLIC_API_URL" "apps/web/src/lib/auth-client.ts"; then
        print_status $GREEN "   ✅ Web app auth client uses centralized API"
    else
        print_status $RED "   ❌ Web app auth client configuration issue"
    fi
    
    # Check admin app auth client
    if grep -q "NEXT_PUBLIC_API_URL" "apps/admin/src/lib/auth-client.ts"; then
        print_status $GREEN "   ✅ Admin app auth client uses centralized API"
    else
        print_status $RED "   ❌ Admin app auth client configuration issue"
    fi
    
    # Check if admin local routes are disabled
    if grep -q "// export async function GET" "apps/admin/src/app/api/auth/[...all]/route.ts"; then
        print_status $GREEN "   ✅ Admin local auth routes are properly disabled"
    else
        print_status $YELLOW "   ⚠️  Admin local auth routes might still be active"
    fi
    
    print_status $BLUE "\n📋 Step 5: Summary and Recommendations"
    
    if [ $services_running -eq 3 ]; then
        print_status $GREEN "🎉 All services are running!"
        print_status $CYAN "   You can now test authentication:"
        print_status $CYAN "   • Web App: http://localhost:3000/login"
        print_status $CYAN "   • Admin App: http://localhost:3001/login"
        print_status $CYAN "   • API Service: http://localhost:8787/api/auth/csrf"
    elif [ $services_running -gt 0 ]; then
        print_status $YELLOW "⚠️  Some services are running, but not all"
        print_status $CYAN "   To start all services: ./start-dev-servers.sh all"
    else
        print_status $RED "❌ No services are running"
        print_status $CYAN "   To start all services: ./start-dev-servers.sh all"
        print_status $CYAN "   Or start individually:"
        print_status $CYAN "   • bunx nx run api:dev"
        print_status $CYAN "   • bunx nx run web:dev"
        print_status $CYAN "   • bunx nx run admin:dev"
    fi
    
    print_status $BLUE "\n📖 For detailed documentation, see:"
    print_status $CYAN "   • AUTHENTICATION_ARCHITECTURE.md"
    print_status $CYAN "   • AUTHENTICATION_IMPLEMENTATION_SUMMARY.md"
    
    print_status $BLUE "\n🔧 If you encounter issues:"
    print_status $CYAN "   1. Check environment variables in .env.local files"
    print_status $CYAN "   2. Ensure database is accessible"
    print_status $CYAN "   3. Verify all services are running"
    print_status $CYAN "   4. Check browser console for errors"
    
    echo ""
}

# Run the verification
main "$@"
