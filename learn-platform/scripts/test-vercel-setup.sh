#!/bin/bash

# Test script to verify Vercel CLI setup for Nx monorepo
# Run this from the learn-platform directory

set -e

echo "🔍 Testing Vercel CLI setup for Nx monorepo..."
echo "================================================"

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -d "apps/web" ]; then
    echo "❌ Error: Please run this script from the learn-platform directory"
    exit 1
fi

echo "✅ Current directory: $(pwd)"

# Check if Vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    echo "⚠️  Vercel CLI not found. Installing..."
    npm install -g vercel@latest
fi

echo "✅ Vercel CLI version: $(vercel --version)"

# Check if bun is available
if ! command -v bun &> /dev/null; then
    echo "❌ Error: bun is not installed"
    exit 1
fi

echo "✅ Bun version: $(bun --version)"

# Check if dependencies are installed
if [ ! -d "node_modules" ]; then
    echo "⚠️  Dependencies not found. Installing..."
    bun install --frozen-lockfile
fi

echo "✅ Dependencies installed"

# Check web app structure
echo "📁 Checking web app structure..."
if [ ! -f "apps/web/package.json" ]; then
    echo "❌ Error: apps/web/package.json not found"
    exit 1
fi

if [ ! -f "apps/web/vercel.json" ]; then
    echo "❌ Error: apps/web/vercel.json not found"
    exit 1
fi

if [ ! -f "apps/web/next.config.js" ]; then
    echo "❌ Error: apps/web/next.config.js not found"
    exit 1
fi

echo "✅ Web app structure is correct"

# Test Vercel CLI in web app directory
echo "🧪 Testing Vercel CLI in web app directory..."
cd apps/web

# Check if package.json has required scripts
if ! grep -q "vercel-build" package.json; then
    echo "❌ Error: vercel-build script not found in package.json"
    exit 1
fi

echo "✅ vercel-build script found in package.json"

# Test if Vercel can read the configuration
echo "📋 Vercel configuration test..."
if [ -n "$VERCEL_TOKEN" ] && [ -n "$VERCEL_ORG_ID" ] && [ -n "$VERCEL_PROJECT_ID" ]; then
    echo "🔑 Environment variables found, testing Vercel CLI..."
    
    # Test vercel pull (this will fail if project isn't linked, but that's expected)
    if vercel pull --yes --environment=preview --token="$VERCEL_TOKEN" 2>/dev/null; then
        echo "✅ Vercel CLI can connect to project"
    else
        echo "⚠️  Vercel CLI connection test failed (expected if project isn't linked locally)"
    fi
else
    echo "⚠️  Vercel environment variables not set (VERCEL_TOKEN, VERCEL_ORG_ID, VERCEL_PROJECT_ID)"
    echo "   This is normal for local testing - they should be set in GitHub Actions"
fi

# Test Next.js build
echo "🏗️  Testing Next.js build..."
cd ../..

# Build the web app using Nx
if bunx nx build web; then
    echo "✅ Next.js build successful"
else
    echo "❌ Next.js build failed"
    exit 1
fi

echo ""
echo "🎉 All tests passed! Vercel CLI setup is ready for deployment."
echo ""
echo "📝 Next steps:"
echo "   1. Ensure GitHub secrets are set: VERCEL_TOKEN, VERCEL_ORG_ID, VERCEL_PROJECT_ID_WEB"
echo "   2. Push to dev branch to test preview deployment"
echo "   3. Push to main branch to test production deployment"
echo "   4. Monitor GitHub Actions logs for successful deployment"
