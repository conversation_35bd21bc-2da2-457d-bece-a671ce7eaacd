#!/bin/bash

# <PERSON>rip<PERSON> to demonstrate and test Nx affected behavior
# This script helps you understand how Nx determines affected projects

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 Nx Affected Testing Behavior Demonstration${NC}"
echo "=============================================="

# Function to print section headers
print_section() {
    echo -e "\n${PURPLE}📋 $1${NC}"
    echo "----------------------------------------"
}

# Function to run command and show output
run_and_show() {
    echo -e "${YELLOW}Command:${NC} $1"
    echo -e "${BLUE}Output:${NC}"
    eval $1
    echo ""
}

print_section "1. Current Affected Status"
echo "This shows what tests would run based on current changes:"
run_and_show "bunx nx affected --target=test --dry-run"

print_section "2. All Projects with Test Targets"
echo "These are all projects that have test capabilities:"
run_and_show "bunx nx show projects --with-target=test"

print_section "3. Project Dependencies Visualization"
echo "Opening dependency graph (this will open in your browser)..."
echo -e "${YELLOW}Command:${NC} bunx nx graph"
echo -e "${BLUE}Note:${NC} This will open a browser window showing project dependencies"
# Uncomment the next line if you want to automatically open the graph
# bunx nx graph &

print_section "4. Testing Affected Behavior with Changes"
echo "Let's make a small change to see how affected detection works..."

# Create a backup of the current git status
echo -e "${YELLOW}Current git status:${NC}"
git status --porcelain

echo -e "\n${YELLOW}Making a small change to libs/auth/src/auth.ts...${NC}"

# Add a comment to trigger affected detection
echo "// Test comment for affected detection - $(date)" >> libs/auth/src/auth.ts

echo -e "${GREEN}✅ Change made!${NC}"

print_section "5. Affected Projects After Change"
echo "Now let's see what projects are affected by the auth library change:"
run_and_show "bunx nx affected --target=test --dry-run"

print_section "6. Detailed Affected Analysis"
echo "Let's see more details about what's affected:"
run_and_show "bunx nx show projects --affected --base=main"

print_section "7. Different Base Comparisons"
echo "Comparing against different baselines:"

echo -e "${YELLOW}Against previous commit (HEAD~1):${NC}"
run_and_show "bunx nx affected --target=test --base=HEAD~1 --dry-run"

echo -e "${YELLOW}Against main branch (explicit):${NC}"
run_and_show "bunx nx affected --target=test --base=main --head=HEAD --dry-run"

print_section "8. Running Affected Tests"
echo "Now let's actually run the affected tests:"
echo -e "${YELLOW}Command:${NC} bunx nx affected --target=test --parallel"
echo -e "${BLUE}Note:${NC} This will run the actual tests for affected projects"

read -p "Do you want to run the affected tests? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    bunx nx affected --target=test --parallel
else
    echo -e "${YELLOW}Skipped running tests${NC}"
fi

print_section "9. Cleanup"
echo "Removing the test change we made..."
git checkout libs/auth/src/auth.ts
echo -e "${GREEN}✅ Cleanup complete!${NC}"

print_section "10. Verification After Cleanup"
echo "After cleanup, affected status should return to original state:"
run_and_show "bunx nx affected --target=test --dry-run"

print_section "📚 Summary & Next Steps"
echo -e "${GREEN}What we learned:${NC}"
echo "1. 'No tasks were run' means no projects are affected (this is good!)"
echo "2. Changes to a library affect all projects that depend on it"
echo "3. Nx automatically traces dependencies to determine affected projects"
echo "4. Different base comparisons show different affected sets"
echo ""
echo -e "${BLUE}Useful commands for daily development:${NC}"
echo "• Check affected: bunx nx affected --target=test --dry-run"
echo "• Run affected tests: bunx nx affected --target=test --parallel"
echo "• Force all tests: bunx nx run-many --target=test --all"
echo "• View dependencies: bunx nx graph"
echo "• Test specific project: bunx nx test auth"
echo ""
echo -e "${YELLOW}For more details, see:${NC}"
echo "• NX_AFFECTED_TESTING_GUIDE.md"
echo "• TESTING_STRATEGY.md"
echo ""
echo -e "${GREEN}🎉 Demonstration complete!${NC}"
