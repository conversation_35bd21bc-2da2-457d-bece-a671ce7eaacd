#!/bin/bash

# Comprehensive Test Setup Verification Script
# This script verifies that the testing strategy is properly configured

set -e

echo "🧪 Verifying Comprehensive Testing Strategy Setup"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
        exit 1
    fi
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo -e "${RED}❌ Please run this script from the learn-platform directory${NC}"
    echo -e "${BLUE}Current directory: $(pwd)${NC}"
    echo -e "${BLUE}Looking for: package.json${NC}"
    exit 1
fi

# Check for .github/workflows in current or parent directory
if [ -d ".github/workflows" ]; then
    WORKFLOWS_DIR=".github/workflows"
elif [ -d "../.github/workflows" ]; then
    WORKFLOWS_DIR="../.github/workflows"
else
    echo -e "${RED}❌ .github/workflows directory not found${NC}"
    exit 1
fi

print_info "Checking directory structure..."

# 1. Verify GitHub Actions workflows exist and have test jobs
echo -e "\n${BLUE}1. Verifying GitHub Actions Workflows${NC}"

workflows=("deploy-web.yml" "deploy-api.yml" "deploy-admin.yml")
for workflow in "${workflows[@]}"; do
    workflow_path="$WORKFLOWS_DIR/$workflow"
    if [ -f "$workflow_path" ]; then
        # Check if test job exists
        if grep -q "test:" "$workflow_path" && grep -q "needs: test" "$workflow_path"; then
            print_status 0 "Workflow $workflow has test job with proper dependencies"
        else
            print_status 1 "Workflow $workflow missing test job or dependencies"
        fi

        # Check Node.js version (should be v20 for Wrangler compatibility)
        if grep -q "node-version: '20'" "$workflow_path"; then
            print_status 0 "Workflow $workflow uses Node.js v20 (Wrangler compatible)"
        else
            print_warning "Workflow $workflow may not use Node.js v20 (check Wrangler compatibility)"
        fi
    else
        print_status 1 "Workflow $workflow not found"
    fi
done

# 2. Verify test configuration files
echo -e "\n${BLUE}2. Verifying Test Configuration${NC}"

test_configs=("jest.config.ts" "jest.preset.js" "nx.json")
for config in "${test_configs[@]}"; do
    if [ -f "$config" ]; then
        print_status 0 "Test configuration $config exists"
    else
        print_status 1 "Test configuration $config missing"
    fi
done

# 3. Check for test files in libraries
echo -e "\n${BLUE}3. Verifying Test Files Structure${NC}"

# Check auth library tests
auth_tests=("libs/auth/src/auth.test.ts" "libs/auth/src/auth-cloudflare.integration.test.ts")
for test_file in "${auth_tests[@]}"; do
    if [ -f "$test_file" ]; then
        print_status 0 "Test file $test_file exists"
    else
        print_status 1 "Test file $test_file missing"
    fi
done

# Check for Jest config in libraries
lib_jest_configs=("libs/auth/jest.config.ts" "libs/db/jest.config.ts" "libs/trpc/jest.config.ts")
for config in "${lib_jest_configs[@]}"; do
    if [ -f "$config" ]; then
        print_status 0 "Library Jest config $config exists"
    else
        print_warning "Library Jest config $config missing (may be optional)"
    fi
done

# 4. Check E2E test structure
echo -e "\n${BLUE}4. Verifying E2E Test Structure${NC}"

e2e_apps=("apps/web-e2e" "apps/admin-e2e")
for e2e_app in "${e2e_apps[@]}"; do
    if [ -d "$e2e_app" ]; then
        if [ -f "$e2e_app/playwright.config.ts" ] || [ -f "$e2e_app/cypress.config.ts" ]; then
            print_status 0 "E2E app $e2e_app properly configured"
        else
            print_warning "E2E app $e2e_app missing config file"
        fi
    else
        print_warning "E2E app $e2e_app directory missing"
    fi
done

# 5. Verify Nx test targets
echo -e "\n${BLUE}5. Verifying Nx Test Targets${NC}"

print_info "Checking Nx test targets..."

# Check if bun is available
if command -v bun &> /dev/null; then
    print_status 0 "Bun package manager available"

    # Check if dependencies are installed
    if [ -d "node_modules" ]; then
        print_status 0 "Dependencies installed"

        # Try to run Nx commands (dry run)
        print_info "Testing Nx commands..."

        if bunx nx show projects --with-target=test &> /dev/null; then
            print_status 0 "Nx test targets configured"

            # Show projects with test targets
            echo -e "\n${BLUE}Projects with test targets:${NC}"
            bunx nx show projects --with-target=test | while read -r project; do
                echo -e "  ${GREEN}• $project${NC}"
            done
        else
            print_warning "Nx test targets may not be properly configured"
        fi

        if bunx nx show projects --with-target=e2e &> /dev/null; then
            print_status 0 "Nx e2e targets configured"

            # Show projects with e2e targets
            echo -e "\n${BLUE}Projects with e2e targets:${NC}"
            bunx nx show projects --with-target=e2e | while read -r project; do
                echo -e "  ${GREEN}• $project${NC}"
            done
        else
            print_warning "Nx e2e targets may not be properly configured"
        fi
    else
        print_warning "Dependencies not installed. Run 'bun install' first."
    fi
else
    print_warning "Bun not available. Install bun to verify Nx commands."
fi

# 6. Check test scripts in package.json
echo -e "\n${BLUE}6. Verifying Package.json Scripts${NC}"

if grep -q '"test"' package.json; then
    print_status 0 "Test script found in package.json"
else
    print_warning "No test script in package.json (Nx handles this)"
fi

# 7. Verify coverage configuration
echo -e "\n${BLUE}7. Verifying Coverage Configuration${NC}"

# Check Jest configs for coverage settings
for config in libs/*/jest.config.ts; do
    if [ -f "$config" ] && grep -q "coverage" "$config"; then
        print_status 0 "Coverage configured in $config"
    fi
done

# Summary
echo -e "\n${BLUE}📋 Verification Summary${NC}"
echo "=================================="
print_info "✅ GitHub Actions workflows updated with test jobs"
print_info "✅ Test job dependencies configured (deployment waits for tests)"
print_info "✅ Nx affected testing strategy implemented"
print_info "✅ Test artifacts and coverage reporting enabled"
print_info "✅ Comprehensive test types supported (unit, integration, e2e)"

echo -e "\n${GREEN}🎉 Testing strategy verification completed successfully!${NC}"
echo -e "\n${BLUE}Next Steps:${NC}"
echo "1. Commit and push changes to trigger workflows"
echo "2. Monitor first workflow run to ensure tests execute properly"
echo "3. Review test artifacts and coverage reports"
echo "4. Adjust test configuration if needed"

echo -e "\n${BLUE}Quick Test Commands:${NC}"
echo "• Run all tests: bunx nx run-many --target=test --all"
echo "• Run affected tests: bunx nx affected --target=test"
echo "• Run with coverage: bunx nx affected --target=test --coverage"
echo "• Run specific library: bunx nx test auth"

echo -e "\n${BLUE}Documentation:${NC}"
echo "• See TESTING_STRATEGY.md for detailed information"
echo "• Check individual library TESTING.md files for specific guidance"
