#!/usr/bin/env bun
/**
 * Debug script for Better Auth configuration
 * 
 * This script helps diagnose trusted origins configuration issues
 * by simulating different environment scenarios and showing detailed
 * configuration information.
 */

import { debugTrustedOrigins } from '@learn-platform/auth-debug';

function printSeparator(title: string) {
  console.log('\n' + '='.repeat(60));
  console.log(`  ${title}`);
  console.log('='.repeat(60));
}

function printDebugInfo(title: string, env?: any) {
  printSeparator(title);
  
  console.log('Environment variables:');
  console.log('  BETTER_AUTH_SECRET:', env?.BETTER_AUTH_SECRET ? '✅ Set' : '❌ Not set');
  console.log('  BETTER_AUTH_URL:', env?.BETTER_AUTH_URL || '(not set)');
  console.log('  BETTER_AUTH_TRUSTED_ORIGINS:', env?.BETTER_AUTH_TRUSTED_ORIGINS || '(not set)');
  
  const debugInfo = debugTrustedOrigins(env);
  
  console.log('\nConfiguration analysis:');
  console.log('  Base URL:', debugInfo.config.baseURL);
  console.log('  Custom trusted origins:', JSON.stringify(debugInfo.config.trustedOrigins));
  
  console.log('\nOrigins processing:');
  console.log('  Raw origins count:', debugInfo.rawOrigins.length);
  console.log('  Unique origins count:', debugInfo.uniqueOrigins.length);
  console.log('  Duplicates found:', debugInfo.duplicates.length > 0 ? debugInfo.duplicates.join(', ') : 'None');
  console.log('  Production URL included:', debugInfo.hasProductionUrl ? '✅ Yes' : '❌ No');
  
  console.log('\nFinal trusted origins:');
  debugInfo.uniqueOrigins.forEach((origin, index) => {
    console.log(`  ${index + 1}. ${origin}`);
  });
  
  if (debugInfo.duplicates.length > 0) {
    console.log('\n⚠️  WARNING: Duplicates detected in raw origins!');
    console.log('   This suggests the environment variable BETTER_AUTH_TRUSTED_ORIGINS');
    console.log('   contains duplicate values or conflicts with hardcoded origins.');
  }
  
  if (!debugInfo.hasProductionUrl) {
    console.log('\n❌ ERROR: Production URL missing!');
    console.log('   The production URL https://kwaci-learning.bmbn.dev should always be included.');
  }
}

// Test scenarios
console.log('🔍 Better Auth Configuration Debug Tool');
console.log('This tool helps diagnose trusted origins configuration issues.');

// Scenario 1: Current process.env (development)
printDebugInfo('Current Environment (process.env)', undefined);

// Scenario 2: Simulated production environment (correct)
printDebugInfo('Simulated Production Environment (Correct)', {
  BETTER_AUTH_SECRET: 'production-secret-32-chars-minimum',
  BETTER_AUTH_URL: 'https://kwaci-learning.bmbn.dev',
  BETTER_AUTH_TRUSTED_ORIGINS: 'https://kwaci-learning.bmbn.dev',
});

// Scenario 3: Simulated production environment with duplicates (problematic)
printDebugInfo('Simulated Production Environment (With Duplicates)', {
  BETTER_AUTH_SECRET: 'production-secret-32-chars-minimum',
  BETTER_AUTH_URL: 'https://kwaci-learning.bmbn.dev',
  BETTER_AUTH_TRUSTED_ORIGINS: 'http://localhost:3000,http://localhost:8787,http://localhost:3000,http://localhost:3000',
});

// Scenario 4: Simulated production environment (missing production URL)
printDebugInfo('Simulated Production Environment (Missing Production URL)', {
  BETTER_AUTH_SECRET: 'production-secret-32-chars-minimum',
  BETTER_AUTH_URL: 'http://localhost:3000',
  BETTER_AUTH_TRUSTED_ORIGINS: 'http://localhost:3000,http://localhost:8787',
});

// Scenario 5: Empty environment (fallback behavior)
printDebugInfo('Empty Environment (Fallback Behavior)', {});

printSeparator('Recommendations');
console.log('Based on the analysis above:');
console.log('');
console.log('1. ✅ Ensure BETTER_AUTH_URL is set to: https://kwaci-learning.bmbn.dev');
console.log('2. ✅ Ensure BETTER_AUTH_TRUSTED_ORIGINS includes: https://kwaci-learning.bmbn.dev');
console.log('3. ⚠️  Avoid duplicate origins in BETTER_AUTH_TRUSTED_ORIGINS');
console.log('4. 🔧 The production URL is hardcoded and should always appear');
console.log('');
console.log('Example correct production configuration:');
console.log('  BETTER_AUTH_URL=https://kwaci-learning.bmbn.dev');
console.log('  BETTER_AUTH_TRUSTED_ORIGINS=https://kwaci-learning.bmbn.dev');
console.log('  BETTER_AUTH_SECRET=your-secure-secret-key');
console.log('');
console.log('If you see duplicates in production, check:');
console.log('  - Vercel environment variables dashboard');
console.log('  - GitHub Actions secrets configuration');
console.log('  - Environment variable inheritance in deployment');
