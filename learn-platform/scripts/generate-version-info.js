#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * Generate version information at build time
 * This script extracts git hash, version, and build date and injects them into the version service
 * @param {string} projectPath - Optional path to the project's package.json (relative to workspace root)
 */
function generateVersionInfo(projectPath = null) {
  try {
    // Get version from package.json
    let packageJsonPath;
    if (projectPath) {
      // Use specific project's package.json
      packageJsonPath = path.join(__dirname, '..', projectPath, 'package.json');
    } else {
      // Default to workspace root package.json
      packageJsonPath = path.join(__dirname, '..', 'package.json');
    }
    
    if (!fs.existsSync(packageJsonPath)) {
      throw new Error(`Package.json not found at: ${packageJsonPath}`);
    }
    
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    const version = packageJson.version || '0.0.0';

    // Get git information
    let gitHash = 'unknown';
    let gitHashShort = 'unknown';
    
    try {
      gitHash = execSync('git rev-parse HEAD', { encoding: 'utf8' }).trim();
      gitHashShort = execSync('git rev-parse --short HEAD', { encoding: 'utf8' }).trim();
    } catch (error) {
      console.warn('Warning: Could not get git information:', error.message);
    }

    // Get build date
    const buildDate = new Date().toISOString();

    // Create version info object
    const versionInfo = {
      version,
      gitHash,
      gitHashShort,
      buildDate
    };

    // Generate the version service content with injected values
    const versionServiceContent = `// Browser-compatible version service
// Note: This version provides build-time injected version info
// Generated at build time - DO NOT EDIT MANUALLY

export interface VersionInfo {
  version: string;
  gitHash: string;
  gitHashShort: string;
  buildDate: string;
}

export class VersionService {
  private static instance: VersionService;
  private versionInfo: VersionInfo | null = null;

  // Private constructor to prevent direct instantiation
  private constructor() {
    // Singleton pattern - initialization happens in getInstance()
  }

  public static getInstance(): VersionService {
    if (!VersionService.instance) {
      VersionService.instance = new VersionService();
    }
    return VersionService.instance;
  }

  public getVersionInfo(): VersionInfo {
    if (this.versionInfo) {
      return this.versionInfo;
    }

    // Build-time injected version info
    this.versionInfo = ${JSON.stringify(versionInfo, null, 4)};

    return this.versionInfo;
  }

  public clearCache(): void {
    this.versionInfo = null;
  }
}

// Convenience function for easy import
export function getVersionInfo(): VersionInfo {
  return VersionService.getInstance().getVersionInfo();
}
`;

    // Write the generated version service
    const versionServicePath = path.join(__dirname, '..', 'libs', 'version', 'src', 'version.service.ts');
    fs.writeFileSync(versionServicePath, versionServiceContent, 'utf8');

    console.log('✅ Version information generated successfully:');
    console.log(`   Project: ${projectPath || 'workspace root'}`);
    console.log(`   Package.json: ${packageJsonPath}`);
    console.log(`   Version: ${version}`);
    console.log(`   Git Hash: ${gitHashShort} (${gitHash})`);
    console.log(`   Build Date: ${buildDate}`);
    console.log(`   Output: ${versionServicePath}`);

    return versionInfo;
  } catch (error) {
    console.error('❌ Error generating version information:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  // Get project path from command line arguments
  const projectPath = process.argv[2] || null;
  generateVersionInfo(projectPath);
}

module.exports = { generateVersionInfo };