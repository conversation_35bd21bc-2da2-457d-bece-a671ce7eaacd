#!/bin/bash

# Database Setup Script for Learn Platform
# This script helps set up the database environment and run migrations

set -e  # Exit on any error

echo "🚀 Learn Platform Database Setup"
echo "================================"

# Check if .env.local file exists (preferred) or .env file
if [ ! -f ".env.local" ] && [ ! -f ".env" ]; then
    echo "📝 Creating .env.local file from template..."
    if [ -f ".env.example" ]; then
        cp .env.example .env.local
        echo "✅ Created .env.local file from .env.example"
        echo ""
        echo "⚠️  IMPORTANT: Please edit the .env.local file and update:"
        echo "   - DATABASE_URL with your PostgreSQL connection string"
        echo "   - BETTER_AUTH_SECRET with a secure secret key (32+ characters)"
        echo ""
        echo "💡 For Supabase users:"
        echo "   1. Create a project at https://supabase.com"
        echo "   2. Go to Settings → Database"
        echo "   3. Copy the connection string (URI format)"
        echo ""
        read -p "Press Enter after updating the .env.local file..."
    else
        echo "❌ .env.example file not found!"
        exit 1
    fi
elif [ -f ".env.local" ]; then
    echo "✅ .env.local file already exists"
elif [ -f ".env" ]; then
    echo "✅ .env file already exists"
fi

# Check if DATABASE_URL is set
echo ""
echo "🔍 Checking environment variables..."

# Check environment variables in .env.local first, then .env
ENV_FILE=""
if [ -f ".env.local" ]; then
    ENV_FILE=".env.local"
elif [ -f ".env" ]; then
    ENV_FILE=".env"
fi

if [ -n "$ENV_FILE" ]; then
    if grep -q "^DATABASE_URL=postgresql://" "$ENV_FILE"; then
        echo "✅ DATABASE_URL appears to be configured in $ENV_FILE"
    else
        echo "⚠️  DATABASE_URL may not be properly configured"
        echo "   Please ensure it starts with 'postgresql://' in your $ENV_FILE file"
    fi

    if grep -q "^BETTER_AUTH_SECRET=.*" "$ENV_FILE"; then
        echo "✅ BETTER_AUTH_SECRET appears to be configured in $ENV_FILE"
    else
        echo "⚠️  BETTER_AUTH_SECRET may not be properly configured"
        echo "   Please ensure it's set to a secure value (32+ characters) in your $ENV_FILE file"
    fi
else
    echo "⚠️  No environment file found (.env.local or .env)"
fi

# Generate database schema
echo ""
echo "📊 Generating database schema..."
if bun run db:generate; then
    echo "✅ Database schema generated successfully"
else
    echo "❌ Failed to generate database schema"
    exit 1
fi

# Run database migrations
echo ""
echo "🔄 Running database migrations..."
if bun run db:migrate; then
    echo "✅ Database migrations completed successfully"
    echo ""
    echo "🎉 Database setup complete!"
    echo ""
    echo "📋 What's been set up:"
    echo "   ✅ User authentication tables"
    echo "   ✅ Session management"
    echo "   ✅ OAuth provider support"
    echo "   ✅ Email verification system"
    echo ""
    echo "🚀 Your better-auth database is ready!"
    echo "   You can now test user registration and login in your applications."
else
    echo "❌ Database migration failed"
    echo ""
    echo "🔧 Troubleshooting steps:"
    echo "   1. Verify DATABASE_URL is correct in .env file"
    echo "   2. Ensure your database is running and accessible"
    echo "   3. Check network connectivity to your database"
    echo "   4. For Supabase: verify project is active and password is correct"
    echo ""
    echo "📖 See DATABASE_SETUP.md for detailed troubleshooting"
    exit 1
fi
