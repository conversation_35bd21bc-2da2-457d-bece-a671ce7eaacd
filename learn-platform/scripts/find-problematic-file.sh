#!/bin/bash

# Find Problematic File Script
# This script identifies which file is causing the SWC parser panic

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to test a single file with Node.js syntax check
test_file_syntax() {
    local file=$1
    if [[ "$file" == *.ts || "$file" == *.tsx || "$file" == *.js || "$file" == *.jsx ]]; then
        if ! node -c "$file" 2>/dev/null; then
            return 1
        fi
    fi
    return 0
}

# Function to test TypeScript compilation of a file
test_ts_file() {
    local file=$1
    if [[ "$file" == *.ts || "$file" == *.tsx ]]; then
        if ! bunx tsc --noEmit --skipLibCheck "$file" 2>/dev/null; then
            return 1
        fi
    fi
    return 0
}

main() {
    print_status $BLUE "🔍 Finding Problematic File"
    print_status $BLUE "=========================="
    
    print_status $CYAN "\n📋 Step 1: Check for basic syntax errors"
    
    local problematic_files=()
    
    # Check all TypeScript/JavaScript files in the project
    while IFS= read -r -d '' file; do
        if ! test_file_syntax "$file"; then
            print_status $RED "❌ Syntax error in: $file"
            problematic_files+=("$file")
        fi
    done < <(find apps libs -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" -print0 2>/dev/null)
    
    if [ ${#problematic_files[@]} -gt 0 ]; then
        print_status $RED "\n🚨 Found ${#problematic_files[@]} files with syntax errors:"
        for file in "${problematic_files[@]}"; do
            print_status $RED "   • $file"
        done
        print_status $CYAN "\nFix these files and try again."
        return 1
    fi
    
    print_status $GREEN "✅ No basic syntax errors found"
    
    print_status $CYAN "\n📋 Step 2: Check for TypeScript compilation errors"
    
    local ts_problematic_files=()
    
    while IFS= read -r -d '' file; do
        if ! test_ts_file "$file"; then
            print_status $YELLOW "⚠️  TypeScript issue in: $file"
            ts_problematic_files+=("$file")
        fi
    done < <(find apps libs -name "*.ts" -o -name "*.tsx" -print0 2>/dev/null)
    
    if [ ${#ts_problematic_files[@]} -gt 0 ]; then
        print_status $YELLOW "\n⚠️  Found ${#ts_problematic_files[@]} files with TypeScript issues:"
        for file in "${ts_problematic_files[@]}"; do
            print_status $YELLOW "   • $file"
        done
    else
        print_status $GREEN "✅ No TypeScript compilation errors found"
    fi
    
    print_status $CYAN "\n📋 Step 3: Check for files with unusual characters"
    
    local unusual_files=()
    
    while IFS= read -r -d '' file; do
        # Check for non-ASCII characters that might confuse the parser
        if grep -P '[^\x00-\x7F]' "$file" >/dev/null 2>&1; then
            print_status $YELLOW "⚠️  Non-ASCII characters in: $file"
            unusual_files+=("$file")
        fi
    done < <(find apps libs -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" -print0 2>/dev/null)
    
    if [ ${#unusual_files[@]} -gt 0 ]; then
        print_status $YELLOW "\n⚠️  Found ${#unusual_files[@]} files with non-ASCII characters:"
        for file in "${unusual_files[@]}"; do
            print_status $YELLOW "   • $file"
        done
    fi
    
    print_status $CYAN "\n📋 Step 4: Check for empty or corrupted files"
    
    local empty_files=()
    
    while IFS= read -r -d '' file; do
        if [ ! -s "$file" ]; then
            print_status $YELLOW "⚠️  Empty file: $file"
            empty_files+=("$file")
        fi
    done < <(find apps libs -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" -print0 2>/dev/null)
    
    print_status $CYAN "\n📋 Step 5: Test individual project compilation"
    
    # Test each project individually
    local projects=("web" "admin" "api")
    
    for project in "${projects[@]}"; do
        print_status $CYAN "   Testing project: $project"
        
        if [ -d "apps/$project" ]; then
            # Try to compile just this project's TypeScript
            if bunx tsc --noEmit --skipLibCheck --project "apps/$project/tsconfig.json" 2>/dev/null; then
                print_status $GREEN "   ✅ $project: TypeScript compilation OK"
            else
                print_status $RED "   ❌ $project: TypeScript compilation failed"
                print_status $CYAN "   Running detailed check for $project..."
                bunx tsc --noEmit --skipLibCheck --project "apps/$project/tsconfig.json" 2>&1 | head -10
            fi
        fi
    done
    
    print_status $CYAN "\n📋 Step 6: Alternative Nx approaches"
    
    print_status $CYAN "   Trying to bypass the problematic plugin..."
    
    # Try to run Nx with minimal plugins
    print_status $CYAN "   Testing with reduced plugin set..."
    
    # Create a temporary nx.json with minimal plugins
    cp nx.json nx.json.backup
    
    cat > nx.json.temp << 'EOF'
{
  "$schema": "./node_modules/nx/schemas/nx-schema.json",
  "namedInputs": {
    "default": ["{projectRoot}/**/*"],
    "production": ["default"]
  },
  "plugins": [],
  "workspaceLayout": {
    "libsDir": "libs",
    "appsDir": "apps"
  }
}
EOF
    
    mv nx.json.temp nx.json
    
    print_status $CYAN "   Testing with minimal nx.json..."
    if timeout 10s bunx nx run web:dev --dry-run 2>/dev/null; then
        print_status $GREEN "   ✅ Works with minimal plugins - plugin conflict detected"
        print_status $CYAN "   The issue is likely with one of the Nx plugins"
    else
        print_status $RED "   ❌ Still fails with minimal plugins - deeper issue"
    fi
    
    # Restore original nx.json
    mv nx.json.backup nx.json
    
    print_status $BLUE "\n📋 Summary and Recommendations"
    
    if [ ${#problematic_files[@]} -gt 0 ]; then
        print_status $RED "🚨 CRITICAL: Fix syntax errors in these files first:"
        for file in "${problematic_files[@]}"; do
            print_status $RED "   • $file"
        done
        print_status $CYAN "\nAfter fixing syntax errors, run this script again."
    elif [ ${#ts_problematic_files[@]} -gt 0 ]; then
        print_status $YELLOW "⚠️  TypeScript issues found. Consider fixing these:"
        for file in "${ts_problematic_files[@]}"; do
            print_status $YELLOW "   • $file"
        done
    else
        print_status $CYAN "🔧 Try these solutions:"
        print_status $CYAN "   1. Update Nx and SWC: bun update @nx/js @swc/core"
        print_status $CYAN "   2. Try without daemon: NX_DAEMON=false bunx nx run web:dev"
        print_status $CYAN "   3. Use Next.js directly: cd apps/web && bunx next dev"
        print_status $CYAN "   4. Check for circular dependencies"
        print_status $CYAN "   5. Consider downgrading SWC version if recently updated"
    fi
    
    echo ""
}

main "$@"
