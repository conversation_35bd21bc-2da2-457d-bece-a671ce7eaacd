#!/bin/bash

# Admin Authentication Flow Test Script
# This script provides step-by-step instructions to test the complete admin authentication flow

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# Function to check if a port is in use
check_port() {
    local port=$1
    local service_name=$2
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_status $GREEN "✅ $service_name (port $port) is running"
        return 0
    else
        print_status $RED "❌ $service_name (port $port) is not running"
        return 1
    fi
}

# Function to wait for user input
wait_for_user() {
    local message=$1
    print_status $YELLOW "⏸️  $message"
    read -p "Press Enter to continue..."
}

# Main test function
main() {
    print_status $PURPLE "🔐 Admin Authentication Flow Test"
    print_status $PURPLE "=================================="
    
    print_status $BLUE "\n📋 Step 1: Verify Services Are Running"
    
    local services_running=0
    
    # Check API service (most important)
    if check_port 8787 "API Service (Cloudflare Workers)"; then
        ((services_running++))
    else
        print_status $YELLOW "   💡 Start with: bunx nx run api:dev"
    fi
    
    # Check admin app
    if check_port 3001 "Admin App"; then
        ((services_running++))
    else
        print_status $YELLOW "   💡 Start with: bunx nx run admin:dev"
    fi
    
    if [ $services_running -lt 2 ]; then
        print_status $RED "\n❌ Required services are not running!"
        print_status $CYAN "   Please start the services first:"
        print_status $CYAN "   1. bunx nx run api:dev (in terminal 1)"
        print_status $CYAN "   2. bunx nx run admin:dev (in terminal 2)"
        print_status $CYAN "   Or use: ./start-dev-servers.sh all"
        exit 1
    fi
    
    print_status $GREEN "\n✅ All required services are running!"
    
    print_status $BLUE "\n📋 Step 2: Test Unauthenticated Access"
    print_status $CYAN "   We'll test that unauthenticated users are redirected to login"
    
    wait_for_user "Open a new incognito/private browser window"
    
    print_status $CYAN "   1. Navigate to: http://localhost:3001"
    print_status $CYAN "   2. You should be automatically redirected to: http://localhost:3001/login"
    print_status $CYAN "   3. Verify you see the 'Admin Login' page"
    
    wait_for_user "Confirm you see the login page, then continue"
    
    print_status $BLUE "\n📋 Step 3: Test Authentication Flow"
    print_status $CYAN "   Now we'll test the complete login flow"
    
    print_status $CYAN "   1. On the login page, enter your test credentials:"
    print_status $CYAN "      • Email: <EMAIL>"
    print_status $CYAN "      • Password: your-test-password"
    print_status $CYAN "   2. Click 'Sign in'"
    print_status $CYAN "   3. You should be redirected to: http://localhost:3001/dashboard"
    
    wait_for_user "Complete the login process, then continue"
    
    print_status $BLUE "\n📋 Step 4: Verify Dashboard Access"
    print_status $CYAN "   Check that you can access the protected dashboard"
    
    print_status $CYAN "   ✅ Verify you see:"
    print_status $CYAN "      • 'Admin Dashboard' header"
    print_status $CYAN "      • Your user information displayed"
    print_status $CYAN "      • 'Authentication Successful' card"
    print_status $CYAN "      • Dashboard cards (User Management, Content, etc.)"
    print_status $CYAN "      • 'Sign Out' button in the header"
    
    wait_for_user "Confirm you see the dashboard, then continue"
    
    print_status $BLUE "\n📋 Step 5: Test Centralized Authentication"
    print_status $CYAN "   Verify that authentication is using the centralized API"
    
    print_status $CYAN "   1. Open browser Developer Tools (F12)"
    print_status $CYAN "   2. Go to Network tab"
    print_status $CYAN "   3. Refresh the page (F5)"
    print_status $CYAN "   4. Look for requests to 'localhost:8787' in the network tab"
    print_status $CYAN "   5. You should see auth-related requests going to the API service"
    
    wait_for_user "Check the network tab, then continue"
    
    print_status $BLUE "\n📋 Step 6: Test Session Sharing (Optional)"
    print_status $CYAN "   Test that sessions are shared between web and admin apps"
    
    print_status $CYAN "   1. Keep the admin app open and logged in"
    print_status $CYAN "   2. Open a new tab and navigate to: http://localhost:3000"
    print_status $CYAN "   3. If the web app is running, you should be automatically logged in"
    print_status $CYAN "   4. Both apps should show the same user information"
    
    wait_for_user "Test session sharing (if web app is running), then continue"
    
    print_status $BLUE "\n📋 Step 7: Test Sign Out"
    print_status $CYAN "   Test the sign out functionality"
    
    print_status $CYAN "   1. Click the 'Sign Out' button in the admin dashboard header"
    print_status $CYAN "   2. You should be redirected to: http://localhost:3001/login"
    print_status $CYAN "   3. Try to access the dashboard directly: http://localhost:3001/dashboard"
    print_status $CYAN "   4. You should be redirected back to login (protected route working)"
    
    wait_for_user "Test sign out functionality, then continue"
    
    print_status $BLUE "\n📋 Step 8: Test Direct Dashboard Access"
    print_status $CYAN "   Test that unauthenticated users can't access protected routes"
    
    print_status $CYAN "   1. Ensure you're signed out"
    print_status $CYAN "   2. Try to navigate directly to: http://localhost:3001/dashboard"
    print_status $CYAN "   3. You should be redirected to: http://localhost:3001/login"
    print_status $CYAN "   4. This confirms the protected route is working correctly"
    
    wait_for_user "Test direct dashboard access, then continue"
    
    print_status $GREEN "\n🎉 Authentication Flow Test Complete!"
    
    print_status $BLUE "\n📋 Test Results Summary:"
    print_status $CYAN "   ✅ Unauthenticated users redirected to login"
    print_status $CYAN "   ✅ Login form works with centralized API"
    print_status $CYAN "   ✅ Successful login redirects to dashboard"
    print_status $CYAN "   ✅ Dashboard shows user information"
    print_status $CYAN "   ✅ Protected routes work correctly"
    print_status $CYAN "   ✅ Sign out functionality works"
    print_status $CYAN "   ✅ Centralized authentication confirmed"
    
    print_status $BLUE "\n🔧 Technical Verification:"
    print_status $CYAN "   • Auth requests go to localhost:8787 (API service)"
    print_status $CYAN "   • Sessions are managed centrally"
    print_status $CYAN "   • Protected routes redirect properly"
    print_status $CYAN "   • User state is maintained correctly"
    
    print_status $BLUE "\n📖 Additional Tests:"
    print_status $CYAN "   • Run: ./scripts/verify-auth-setup.sh (comprehensive setup check)"
    print_status $CYAN "   • Run: node scripts/test-auth-flow.js (HTTP endpoint tests)"
    print_status $CYAN "   • Check: AUTHENTICATION_ARCHITECTURE.md (detailed docs)"
    
    print_status $YELLOW "\n🎯 Next Steps:"
    print_status $CYAN "   1. Test with different user accounts"
    print_status $CYAN "   2. Test session persistence across browser restarts"
    print_status $CYAN "   3. Test error handling (wrong credentials, network issues)"
    print_status $CYAN "   4. Implement additional admin features"
    
    echo ""
}

# Run the test
main "$@"
