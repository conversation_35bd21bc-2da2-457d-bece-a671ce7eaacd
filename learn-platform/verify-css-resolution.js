#!/usr/bin/env node

/**
 * Verification script for CSS module resolution fix
 * This script checks that all CSS imports and configurations are working correctly
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying CSS Module Resolution Fix...\n');

// Check if required files exist
const requiredFiles = [
  'libs/shared/styles/src/globals.css',
  'libs/shared/styles/package.json',
  'apps/web/next.config.js',
  'apps/admin/next.config.js',
  'apps/web/src/app/global.css',
  'apps/admin/src/app/global.css',
];

let allFilesExist = true;

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} exists`);
  } else {
    console.log(`❌ ${file} missing`);
    allFilesExist = false;
  }
});

// Check webpack configuration
const webNextConfig = fs.readFileSync('apps/web/next.config.js', 'utf8');
const adminNextConfig = fs.readFileSync('apps/admin/next.config.js', 'utf8');

const hasWebpackAlias = webNextConfig.includes('@learn-platform/shared-styles') && 
                       adminNextConfig.includes('@learn-platform/shared-styles');

if (hasWebpackAlias) {
  console.log('✅ Webpack alias configuration found in both apps');
} else {
  console.log('❌ Webpack alias configuration missing');
  allFilesExist = false;
}

// Check CSS imports
const webGlobalCSS = fs.readFileSync('apps/web/src/app/global.css', 'utf8');
const adminGlobalCSS = fs.readFileSync('apps/admin/src/app/global.css', 'utf8');

const hasCSSImports = webGlobalCSS.includes('@learn-platform/shared-styles/globals.css') &&
                     adminGlobalCSS.includes('@learn-platform/shared-styles/globals.css');

if (hasCSSImports) {
  console.log('✅ CSS imports using path mapping found in both apps');
} else {
  console.log('❌ CSS imports using path mapping missing');
  allFilesExist = false;
}

// Check shared styles content
const sharedCSS = fs.readFileSync('libs/shared/styles/src/globals.css', 'utf8');
const hasCSVVariables = sharedCSS.includes('--background') && 
                       sharedCSS.includes('--primary') &&
                       sharedCSS.includes('--radius');

if (hasCSVVariables) {
  console.log('✅ CSS variables for shadcn/ui found in shared styles');
} else {
  console.log('❌ CSS variables for shadcn/ui missing');
  allFilesExist = false;
}

// Check package.json exports
const stylesPackage = JSON.parse(fs.readFileSync('libs/shared/styles/package.json', 'utf8'));
const hasExports = stylesPackage.exports && stylesPackage.exports['./globals.css'];

if (hasExports) {
  console.log('✅ Package.json exports configuration found');
} else {
  console.log('❌ Package.json exports configuration missing');
  allFilesExist = false;
}

console.log('\n' + '='.repeat(50));

if (allFilesExist) {
  console.log('🎉 All CSS module resolution fixes are in place!');
  console.log('✅ Ready to run: bunx nx dev web');
  console.log('✅ Ready to run: bunx nx dev admin');
  process.exit(0);
} else {
  console.log('❌ Some issues found. Please check the missing items above.');
  process.exit(1);
}
