/**
 * Security Testing Suite
 * Tests for authentication, authorization, input validation, and common vulnerabilities
 */

import { test, expect, Page } from '@playwright/test';

// Security test configuration
const SECURITY_TEST_CONFIG = {
  baseURL: process.env.BASE_URL || 'http://localhost:3000',
  apiURL: process.env.API_URL || 'http://localhost:8787',
  testUser: {
    email: process.env.TEST_USER_EMAIL || '<EMAIL>',
    password: process.env.TEST_USER_PASSWORD || 'SecurePassword123!',
  },
  maliciousUser: {
    email: '<EMAIL>',
    password: 'MaliciousPassword123!',
  },
};

// Common malicious payloads for testing
const MALICIOUS_PAYLOADS = {
  xss: [
    '<script>alert("XSS")</script>',
    '"><script>alert("XSS")</script>',
    'javascript:alert("XSS")',
    '<img src=x onerror=alert("XSS")>',
    '<svg onload=alert("XSS")>',
  ],
  sqlInjection: [
    "'; DROP TABLE users; --",
    "' OR '1'='1",
    "' UNION SELECT * FROM users --",
    "admin'--",
    "' OR 1=1#",
  ],
  pathTraversal: [
    '../../../etc/passwd',
    '..\\..\\..\\windows\\system32\\config\\sam',
    '%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd',
    '....//....//....//etc/passwd',
  ],
  commandInjection: [
    '; cat /etc/passwd',
    '| whoami',
    '&& ls -la',
    '`cat /etc/passwd`',
    '$(cat /etc/passwd)',
  ],
  ldapInjection: [
    '*)(uid=*',
    '*)(|(uid=*',
    '*)(&(uid=*',
    '*))%00',
  ],
};

// Helper functions
async function signInAsTestUser(page: Page) {
  await page.goto(`${SECURITY_TEST_CONFIG.baseURL}/auth/signin`);
  await page.fill('[data-testid="email-input"]', SECURITY_TEST_CONFIG.testUser.email);
  await page.fill('[data-testid="password-input"]', SECURITY_TEST_CONFIG.testUser.password);
  await page.click('[data-testid="signin-button"]');
  await page.waitForURL('**/dashboard');
}

async function testInputSanitization(page: Page, inputSelector: string, payloads: string[]) {
  const results = [];
  
  for (const payload of payloads) {
    try {
      await page.fill(inputSelector, payload);
      await page.waitForTimeout(500);
      
      // Check if payload was executed (should not be)
      const pageContent = await page.content();
      const isVulnerable = pageContent.includes('XSS') || 
                          pageContent.includes('alert') ||
                          payload.includes('<script>') && pageContent.includes(payload);
      
      results.push({
        payload,
        vulnerable: isVulnerable,
        sanitized: !isVulnerable,
      });
    } catch (error) {
      results.push({
        payload,
        vulnerable: false,
        sanitized: true,
        error: error.message,
      });
    }
  }
  
  return results;
}

test.describe('Security Testing Suite', () => {
  test.beforeEach(async ({ page }) => {
    // Set up security headers check
    page.on('response', response => {
      const headers = response.headers();
      
      // Check for security headers
      if (response.url().includes(SECURITY_TEST_CONFIG.baseURL)) {
        expect(headers['x-frame-options'] || headers['x-frame-options']).toBeDefined();
        expect(headers['x-content-type-options']).toBe('nosniff');
        expect(headers['x-xss-protection']).toBeDefined();
      }
    });
  });

  test('authentication security', async ({ page }) => {
    // Test 1: Unauthenticated access to protected routes
    const protectedRoutes = [
      '/dashboard',
      '/dashboard/content',
      '/learn/create',
      '/dashboard/settings',
    ];

    for (const route of protectedRoutes) {
      await page.goto(`${SECURITY_TEST_CONFIG.baseURL}${route}`);
      
      // Should redirect to sign in
      await expect(page).toHaveURL(/.*auth\/signin.*/);
    }

    // Test 2: Session timeout
    await signInAsTestUser(page);
    
    // Simulate expired session by clearing cookies
    await page.context().clearCookies();
    await page.goto(`${SECURITY_TEST_CONFIG.baseURL}/dashboard`);
    
    // Should redirect to sign in
    await expect(page).toHaveURL(/.*auth\/signin.*/);

    // Test 3: Password strength validation
    await page.goto(`${SECURITY_TEST_CONFIG.baseURL}/auth/signup`);
    
    const weakPasswords = ['123', 'password', 'abc123', '12345678'];
    
    for (const weakPassword of weakPasswords) {
      await page.fill('[data-testid="password-input"]', weakPassword);
      await page.blur('[data-testid="password-input"]');
      
      // Should show password strength error
      await expect(page.locator('[data-testid="password-error"]')).toBeVisible();
    }
  });

  test('authorization and access control', async ({ page }) => {
    await signInAsTestUser(page);
    
    // Test 1: User can only access their own content
    // This would require creating content with different user IDs
    // and testing access restrictions
    
    // Test 2: Admin-only routes (if they exist)
    const adminRoutes = [
      '/admin',
      '/admin/users',
      '/admin/content',
    ];

    for (const route of adminRoutes) {
      const response = await page.goto(`${SECURITY_TEST_CONFIG.baseURL}${route}`, {
        waitUntil: 'networkidle',
      });
      
      // Should return 403 or redirect for non-admin users
      expect([403, 404]).toContain(response?.status() || 404);
    }

    // Test 3: API endpoint authorization
    const apiEndpoints = [
      '/api/trpc/learningContent.delete',
      '/api/trpc/learningContent.update',
      '/api/trpc/auth.deleteAccount',
    ];

    for (const endpoint of apiEndpoints) {
      const response = await page.request.post(`${SECURITY_TEST_CONFIG.apiURL}${endpoint}`, {
        data: { id: 'non-existent-id' },
      });
      
      // Should require authentication
      expect([401, 403]).toContain(response.status());
    }
  });

  test('input validation and sanitization', async ({ page }) => {
    await signInAsTestUser(page);
    await page.goto(`${SECURITY_TEST_CONFIG.baseURL}/learn/create`);

    // Test XSS protection in topic input
    const xssResults = await testInputSanitization(
      page,
      '[data-testid="topic-input"]',
      MALICIOUS_PAYLOADS.xss
    );

    for (const result of xssResults) {
      expect(result.sanitized).toBe(true);
    }

    // Test XSS protection in focus areas
    const focusAreaResults = await testInputSanitization(
      page,
      '[data-testid="focus-areas-input"]',
      MALICIOUS_PAYLOADS.xss
    );

    for (const result of focusAreaResults) {
      expect(result.sanitized).toBe(true);
    }

    // Test SQL injection protection (if applicable)
    // This would be more relevant for direct database queries
    
    // Test command injection in form inputs
    for (const payload of MALICIOUS_PAYLOADS.commandInjection) {
      await page.fill('[data-testid="topic-input"]', payload);
      await page.click('[data-testid="generate-content-button"]');
      
      // Should not execute commands
      const pageContent = await page.content();
      expect(pageContent).not.toContain('/etc/passwd');
      expect(pageContent).not.toContain('root:');
    }
  });

  test('rate limiting and DoS protection', async ({ page }) => {
    await signInAsTestUser(page);
    await page.goto(`${SECURITY_TEST_CONFIG.baseURL}/learn/create`);

    // Test rate limiting on content generation
    const rapidRequests = [];
    
    for (let i = 0; i < 10; i++) {
      rapidRequests.push(
        page.evaluate(async () => {
          const response = await fetch('/api/trpc/learningContent.generateWithAI', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              topic: `Test topic ${Date.now()}`,
              learningLevel: 'beginner',
              preferredContentTypes: ['paragraph'],
            }),
          });
          return response.status;
        })
      );
    }

    const responses = await Promise.all(rapidRequests);
    
    // Should have some rate limited responses (429)
    const rateLimitedCount = responses.filter(status => status === 429).length;
    expect(rateLimitedCount).toBeGreaterThan(0);
  });

  test('CSRF protection', async ({ page }) => {
    await signInAsTestUser(page);

    // Test CSRF protection on state-changing operations
    const csrfTestEndpoints = [
      { endpoint: '/api/trpc/learningContent.create', method: 'POST' },
      { endpoint: '/api/trpc/learningContent.delete', method: 'POST' },
      { endpoint: '/api/trpc/auth.updateProfile', method: 'POST' },
    ];

    for (const { endpoint, method } of csrfTestEndpoints) {
      // Attempt request without proper CSRF token
      const response = await page.request.fetch(`${SECURITY_TEST_CONFIG.apiURL}${endpoint}`, {
        method,
        headers: {
          'Content-Type': 'application/json',
          // Deliberately omit CSRF token
        },
        data: { test: 'data' },
      });

      // Should be rejected due to missing CSRF protection
      expect([403, 400]).toContain(response.status());
    }
  });

  test('data exposure and information leakage', async ({ page }) => {
    // Test 1: Error messages don't expose sensitive information
    await page.goto(`${SECURITY_TEST_CONFIG.baseURL}/auth/signin`);
    
    await page.fill('[data-testid="email-input"]', '<EMAIL>');
    await page.fill('[data-testid="password-input"]', 'wrongpassword');
    await page.click('[data-testid="signin-button"]');

    const errorMessage = await page.locator('[data-testid="error-message"]').textContent();
    
    // Should not reveal whether email exists
    expect(errorMessage).not.toContain('user not found');
    expect(errorMessage).not.toContain('email does not exist');

    // Test 2: API responses don't expose sensitive data
    const response = await page.request.get(`${SECURITY_TEST_CONFIG.apiURL}/api/trpc/auth.getSession`);
    const responseBody = await response.text();
    
    // Should not expose password hashes, internal IDs, etc.
    expect(responseBody).not.toMatch(/\$2[aby]\$\d+\$/); // bcrypt hashes
    expect(responseBody).not.toContain('password');
    expect(responseBody).not.toContain('hash');

    // Test 3: Source code not exposed
    const sourceCodePaths = [
      '/.env',
      '/.env.local',
      '/package.json',
      '/tsconfig.json',
      '/src/',
    ];

    for (const path of sourceCodePaths) {
      const response = await page.request.get(`${SECURITY_TEST_CONFIG.baseURL}${path}`);
      expect(response.status()).toBe(404);
    }
  });

  test('secure headers and configuration', async ({ page }) => {
    const response = await page.goto(SECURITY_TEST_CONFIG.baseURL);
    const headers = response?.headers() || {};

    // Test security headers
    expect(headers['strict-transport-security']).toBeDefined();
    expect(headers['x-frame-options']).toBeDefined();
    expect(headers['x-content-type-options']).toBe('nosniff');
    expect(headers['referrer-policy']).toBeDefined();
    
    // Test Content Security Policy
    expect(headers['content-security-policy']).toBeDefined();
    
    // Test that sensitive headers are not exposed
    expect(headers['server']).toBeUndefined();
    expect(headers['x-powered-by']).toBeUndefined();
  });

  test('file upload security (if applicable)', async ({ page }) => {
    // This test would be relevant if the application supports file uploads
    // Test for:
    // - File type validation
    // - File size limits
    // - Malicious file detection
    // - Path traversal in filenames
    
    // Placeholder for file upload security tests
    console.log('File upload security tests would go here');
  });

  test('session management security', async ({ page }) => {
    await signInAsTestUser(page);

    // Test 1: Session cookies are secure
    const cookies = await page.context().cookies();
    const sessionCookie = cookies.find(cookie => 
      cookie.name.includes('session') || cookie.name.includes('auth')
    );

    if (sessionCookie) {
      expect(sessionCookie.secure).toBe(true);
      expect(sessionCookie.httpOnly).toBe(true);
      expect(sessionCookie.sameSite).toBe('Strict');
    }

    // Test 2: Session invalidation on logout
    await page.click('[data-testid="user-menu"]');
    await page.click('[data-testid="signout-button"]');

    // Try to access protected route with old session
    await page.goto(`${SECURITY_TEST_CONFIG.baseURL}/dashboard`);
    await expect(page).toHaveURL(/.*auth\/signin.*/);

    // Test 3: Concurrent session handling
    // This would require testing multiple browser contexts
  });
});
