#!/usr/bin/env node

/**
 * Test script to verify Tailwind CSS build process
 * This script tests if Tailwind can properly process our configurations
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🧪 Testing Tailwind CSS Build Process...\n');

// Test 1: Verify Tailwind can process web app config
function testTailwindConfig(appName) {
  console.log(`Testing ${appName} Tailwind configuration...`);
  
  const configPath = path.join(__dirname, `apps/${appName}/tailwind.config.js`);
  
  if (!fs.existsSync(configPath)) {
    console.log(`   ❌ Config file not found: ${configPath}`);
    return false;
  }
  
  try {
    // Try to require the config to check for syntax errors
    delete require.cache[require.resolve(configPath)];
    const config = require(configPath);
    
    // Check if config has required properties
    if (!config.content || !Array.isArray(config.content)) {
      console.log(`   ❌ Invalid content configuration`);
      return false;
    }
    
    // Check if shared UI path is included
    const hasSharedUIPath = config.content.some(path => 
      path.includes('libs/shared/ui/src')
    );
    
    if (!hasSharedUIPath) {
      console.log(`   ⚠️  Shared UI path not found in content`);
      return false;
    }
    
    console.log(`   ✅ ${appName} config is valid`);
    return true;
  } catch (error) {
    console.log(`   ❌ Config error: ${error.message}`);
    return false;
  }
}

// Test 2: Create a simple CSS test file and process it
function testTailwindProcessing(appName) {
  console.log(`\nTesting Tailwind CSS processing for ${appName}...`);
  
  const testCSS = `
@tailwind base;
@tailwind components;
@tailwind utilities;

.test-component {
  @apply bg-primary text-primary-foreground p-4 rounded-lg;
}

.test-button {
  @apply btn btn-primary;
}
`;
  
  const tempDir = path.join(__dirname, 'tmp');
  const testCSSPath = path.join(tempDir, `test-${appName}.css`);
  const outputCSSPath = path.join(tempDir, `output-${appName}.css`);
  const configPath = path.join(__dirname, `apps/${appName}/tailwind.config.js`);
  
  try {
    // Ensure temp directory exists
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
    }
    
    // Write test CSS
    fs.writeFileSync(testCSSPath, testCSS);
    
    // Try to process with Tailwind
    const command = `npx tailwindcss -i ${testCSSPath} -o ${outputCSSPath} --config ${configPath}`;
    
    console.log(`   Running: ${command}`);
    execSync(command, { stdio: 'pipe' });
    
    // Check if output was generated
    if (fs.existsSync(outputCSSPath)) {
      const outputContent = fs.readFileSync(outputCSSPath, 'utf8');
      
      // Check for expected CSS variables
      const hasVariables = outputContent.includes('--primary') || outputContent.includes('--background');
      const hasUtilities = outputContent.includes('.bg-primary') || outputContent.includes('.p-4');
      
      if (hasVariables && hasUtilities) {
        console.log(`   ✅ Tailwind processing successful`);
        
        // Clean up
        fs.unlinkSync(testCSSPath);
        fs.unlinkSync(outputCSSPath);
        
        return true;
      } else {
        console.log(`   ⚠️  Output generated but missing expected content`);
        return false;
      }
    } else {
      console.log(`   ❌ No output file generated`);
      return false;
    }
    
  } catch (error) {
    console.log(`   ❌ Processing failed: ${error.message}`);
    
    // Clean up on error
    try {
      if (fs.existsSync(testCSSPath)) fs.unlinkSync(testCSSPath);
      if (fs.existsSync(outputCSSPath)) fs.unlinkSync(outputCSSPath);
    } catch (cleanupError) {
      // Ignore cleanup errors
    }
    
    return false;
  }
}

// Test 3: Verify shared UI components can be imported
function testSharedUIImports() {
  console.log('\nTesting shared UI component imports...');
  
  const testCode = `
const React = require('react');

// Test if we can resolve the shared UI module
try {
  const path = require('path');
  const uiIndexPath = path.join(__dirname, 'libs/shared/ui/src/index.ts');
  
  if (!require('fs').existsSync(uiIndexPath)) {
    throw new Error('UI index file not found');
  }
  
  // Read the index file to check exports
  const indexContent = require('fs').readFileSync(uiIndexPath, 'utf8');
  
  const hasButtonExport = indexContent.includes('Button');
  const hasCardExport = indexContent.includes('Card');
  const hasInputExport = indexContent.includes('Input');
  
  if (hasButtonExport && hasCardExport && hasInputExport) {
    console.log('   ✅ All core components exported correctly');
    return true;
  } else {
    console.log('   ⚠️  Some components missing from exports');
    return false;
  }
  
} catch (error) {
  console.log('   ❌ Import test failed:', error.message);
  return false;
}
`;
  
  try {
    eval(testCode);
    return true;
  } catch (error) {
    console.log(`   ❌ Import test failed: ${error.message}`);
    return false;
  }
}

// Run all tests
async function runAllTests() {
  const results = {
    webConfig: testTailwindConfig('web'),
    adminConfig: testTailwindConfig('admin'),
    sharedUIImports: testSharedUIImports(),
    webProcessing: false,
    adminProcessing: false
  };
  
  // Only test processing if configs are valid
  if (results.webConfig) {
    results.webProcessing = testTailwindProcessing('web');
  }
  
  if (results.adminConfig) {
    results.adminProcessing = testTailwindProcessing('admin');
  }
  
  // Summary
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  console.log(`Web app config:        ${results.webConfig ? '✅' : '❌'}`);
  console.log(`Admin app config:      ${results.adminConfig ? '✅' : '❌'}`);
  console.log(`Shared UI imports:     ${results.sharedUIImports ? '✅' : '❌'}`);
  console.log(`Web app processing:    ${results.webProcessing ? '✅' : '❌'}`);
  console.log(`Admin app processing:  ${results.adminProcessing ? '✅' : '❌'}`);
  
  const allPassed = Object.values(results).every(result => result === true);
  
  console.log(`\n🎯 Overall Status: ${allPassed ? '✅ ALL TESTS PASSED' : '⚠️  SOME TESTS FAILED'}`);
  
  if (allPassed) {
    console.log('\n🎉 Your TailwindCSS and shared UI setup is working correctly!');
    console.log('   You can now start the development servers and test in the browser.');
  } else {
    console.log('\n🔧 Some issues were found. Please check the configuration and try again.');
  }
  
  return allPassed;
}

// Execute tests
runAllTests().catch(error => {
  console.error('❌ Test execution failed:', error);
  process.exit(1);
});
