# tRPC Router Architecture

This document explains the dual router architecture used in the learning platform and why it exists.

## Important Note
**Dont do this:**
❌ Missing Quiz Router Integration: The quiz router was defined in the centralized tRPC library but not integrated into the Cloudflare Workers router
❌ Missing Database Schema: Quiz tables were not included in the workers database schema
❌ Environment Variable Access Issue: The AI library was trying to access environment variables using process.env instead of the Cloudflare Workers ctx.env object
❌ Content Extraction Bug: The quiz generator's content extraction function wasn't handling the correct data structure for different block types

**Solution**
1. Added Quiz Tables to Workers Schema
Added  quiz and  quizAttempt tables to apps/api/src/utils/schema.ts
Included proper indexes and relationships
Added TypeScript type exports
2. Created Worker-Specific Quiz Procedures
Created apps/api/src/procedures/quiz.ts with all CRUD operations:
generate: Generate quiz from learning content using AI
getAll: Get all quizzes for authenticated user
getById: Get specific quiz by ID
startAttempt: Start a quiz attempt
complete: Complete quiz and calculate score
delete: Delete a quiz
3. Integrated Quiz Router into Workers Router
Added quiz router import to apps/api/src/router/workers-router.ts
Created workersQuizRouter with all endpoints
Added it to the main workers app router
4. Fixed Environment Variable Access
Modified libs/ai/src/config/ai-config.ts to accept environment variables from Cloudflare Workers context
Updated createAIProvider() function to use env?.['OPENROUTER_API_KEY']
Added env property to QuizGenerationOptions interface
Updated quiz procedures to pass ctx.env to AI generation functions
5. Fixed Content Extraction
Enhanced extractTextFromBlock() function in libs/ai/src/services/quiz-generator.ts
Added support for both direct data formats and nested data structures
Fixed handling of paragraph, infoBox, bulletList, and other block types

## 🏗️ Architecture Overview

The learning platform uses two separate tRPC routers:

1. **Centralized Router** (`@learn-platform/trpc`) - Type definitions and shared logic
2. **Workers Router** (`apps/api`) - Cloudflare Workers-specific implementation

```mermaid
graph TB
    subgraph "Centralized Router (@learn-platform/trpc)"
        CR[appRouter]
        CR --> AUTH[auth procedures]
        CR --> LC[learningContent procedures]
        CR --> LP[learningProgress procedures]
        CR --> TEMP[templates procedures]
        CR --> FB[learningFeedback procedures]
    end
    
    subgraph "Workers Router (apps/api)"
        WR[workersAppRouter]
        WR --> WAUTH[auth procedures - duplicated]
        WR --> WLC[workersLearningContent procedures]
        WR --> WLP[workersLearningProgress procedures]
        WR --> WTEMP[workersTemplates procedures]
    end
    
    subgraph "Frontend Apps"
        WEB[Web App]
        ADMIN[Admin App]
    end
    
    subgraph "API Deployment"
        CF[Cloudflare Workers API]
    end
    
    WEB --> CF
    ADMIN --> CF
    CF --> WR
```

## 🎯 Why Two Routers?

### **1. Centralized Router (`@learn-platform/trpc`)**
**Purpose**: Shared type definitions and common procedures

```typescript
// libs/trpc/src/router.ts
export const appRouter = router({
  auth: router({ /* auth procedures */ }),
  learningContent: learningContentRouter,
  learningProgress: learningProgressRouter, // ← This exists here
  templates: templatesRouter,
});

export type AppRouter = typeof appRouter; // ← Frontend apps use this type
```

**Benefits**:
- ✅ **Type Safety**: Frontend apps get perfect TypeScript types
- ✅ **Single Source of Truth**: All procedure definitions in one place
- ✅ **Consistency**: Same API shape across all apps

### **2. Workers Router (`apps/api`)**
**Purpose**: Cloudflare Workers-specific implementation

```typescript
// apps/api/src/router/workers-router.ts
export const workersAppRouter = router({
  auth: router({ /* workers-specific auth */ }),
  learningContent: workersLearningContentRouter,
  learningProgress: workersLearningProgressRouter,
  templates: workersTemplatesRouter,
});
```

## 🤔 Why Not Just Use One Router?

### **The Problem: Monorepo Import Issues**

The centralized router has import resolution problems in Cloudflare Workers:

```typescript
// ❌ This doesn't work well in Cloudflare Workers
import { createDatabaseConnectionFromEnv } from '@learn-platform/db/connection';
import { learningProgress, learningContent, eq, and, desc } from '@learn-platform/db/schema';
```

**Issues**:
- 🚫 **Import Resolution**: Cloudflare Workers can't resolve monorepo packages properly
- 🚫 **Bundle Size**: Pulls in unnecessary dependencies
- 🚫 **Build Complexity**: Complex webpack/bundling configuration needed

### **The Solution: Workers-Specific Implementation**

```typescript
// ✅ This works perfectly in Cloudflare Workers
import { createWorkersDatabaseFromEnv } from '../utils/database';
import { learningProgress, learningContent, eq, and, desc } from '../utils/schema';
```

**Benefits**:
- ✅ **No Import Issues**: Local imports work reliably
- ✅ **Smaller Bundle**: Only includes what's needed
- ✅ **Faster Builds**: No complex resolution needed

## 📊 Comparison Table

| Aspect | Centralized Router | Workers Router |
|--------|-------------------|----------------|
| **Purpose** | Type definitions & shared logic | Cloudflare Workers implementation |
| **Used By** | Frontend apps (for types) | API server (actual runtime) |
| **Database** | `@learn-platform/db` | Local `utils/database.ts` |
| **Schema** | `@learn-platform/db/schema` | Local `utils/schema.ts` |
| **Import Style** | Monorepo packages | Relative imports |
| **Bundle Size** | Large (includes all deps) | Small (only needed code) |
| **Maintenance** | Single source of truth | Must sync manually |

## 🔄 The Synchronization Challenge

The main challenge with this architecture is keeping both routers in sync. When adding new procedures:

```typescript
// ✅ Step 1: Add to centralized router (libs/trpc/src/router.ts)
export const appRouter = router({
  learningProgress: learningProgressRouter, // ← Add here first
});

// ✅ Step 2: Add to workers router (apps/api/src/router/workers-router.ts)
export const workersAppRouter = router({
  learningProgress: workersLearningProgressRouter, // ← Then add here
});
```

**Common Issues**:
- ❌ Procedure exists in centralized router but missing in workers router
- ❌ Different input/output schemas between routers
- ❌ Inconsistent procedure names or paths

## 🛠️ Adding New Procedures

When adding new tRPC procedures, follow this checklist:

### 1. **Centralized Router** (for types)
- [ ] Add procedure to `libs/trpc/src/procedures/[feature].ts`
- [ ] Export router from `libs/trpc/src/router.ts`
- [ ] Update `AppRouter` type

### 2. **Workers Router** (for runtime)
- [ ] Create workers-specific procedure in `apps/api/src/procedures/[feature].ts`
- [ ] Add router to `apps/api/src/router/workers-router.ts`
- [ ] Import and register in `workersAppRouter`

### 3. **Schema Synchronization**
- [ ] Add database schema to `libs/db/src/schema.ts`
- [ ] Copy schema to `apps/api/src/utils/schema.ts`
- [ ] Ensure both schemas are identical

## 🎯 Best Practices

### **1. Keep Routers in Sync**
```typescript
// Always ensure both routers have the same structure
const centralizedRoutes = Object.keys(appRouter._def.procedures);
const workersRoutes = Object.keys(workersAppRouter._def.procedures);
// These should match!
```

### **2. Use Consistent Naming**
```typescript
// ✅ Good: Consistent naming
learningContent: learningContentRouter,        // Centralized
learningContent: workersLearningContentRouter, // Workers

// ❌ Bad: Inconsistent naming
learningContent: learningContentRouter,        // Centralized
content: workersContentRouter,                 // Workers
```

### **3. Document Dependencies**
```typescript
// Always document which procedures depend on each other
export const workersLearningProgressRouter = router({
  // Requires: learningContent procedures for validation
  updateProgress: protectedProcedure.mutation(/* ... */),
});
```

## 🔍 Debugging Router Issues

### **Common Error: "No procedure found on path"**
```bash
Error: No procedure found on path 'learningProgress.updateProgress'
```

**Solution Checklist**:
1. ✅ Check if procedure exists in centralized router
2. ✅ Check if procedure exists in workers router
3. ✅ Verify procedure names match exactly
4. ✅ Ensure workers router is properly imported in API

### **Testing Router Parity**
```javascript
// Test script to verify both routers have same procedures
const centralizedProcedures = Object.keys(appRouter._def.procedures);
const workersProcedures = Object.keys(workersAppRouter._def.procedures);

const missing = centralizedProcedures.filter(p => !workersProcedures.includes(p));
if (missing.length > 0) {
  console.error('Missing procedures in workers router:', missing);
}
```

## 💡 Future Improvements

### **Option 1: Code Generation**
Generate workers router from centralized router automatically:
```bash
npm run generate:workers-router
```

### **Option 2: Unified Router**
Solve import issues and use single router everywhere:
```typescript
// Future: Single router with conditional imports
export const appRouter = router({
  learningProgress: isWorkers ? workersLearningProgress : learningProgress,
});
```

### **Option 3: Router Validation**
Add automated tests to ensure router parity:
```typescript
describe('Router Parity', () => {
  it('should have matching procedures', () => {
    expect(getRouterProcedures(appRouter))
      .toEqual(getRouterProcedures(workersAppRouter));
  });
});
```

## 📝 Key Takeaways

1. **Frontend apps** use the centralized router for **types only**
2. **API server** uses the workers router for **actual execution**
3. **Manual synchronization** is required between them
4. **Always add procedures to both routers** when extending the API
5. **Test both routers** to ensure they stay in sync

This dual router architecture exists to solve Cloudflare Workers' import resolution limitations while maintaining type safety across the monorepo.
