/**
 * Test cookie handling in authentication
 */

const API_BASE = 'http://localhost:8787';

async function testCookies() {
  console.log('🔍 Testing cookie handling...\n');

  const testUser = {
    email: '<EMAIL>',
    password: 'testpassword123'
  };

  // Test sign-in and check response headers
  console.log('1. Testing sign-in and checking cookies...');
  try {
    const response = await fetch(`${API_BASE}/api/auth/sign-in/email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify(testUser)
    });

    console.log('   Response status:', response.status);
    console.log('   Response headers:');
    
    // Check all headers
    for (const [key, value] of response.headers.entries()) {
      console.log(`     ${key}: ${value}`);
    }

    // Specifically check Set-Cookie headers
    const setCookieHeaders = response.headers.get('set-cookie');
    console.log('   Set-Cookie headers:', setCookieHeaders);

    const data = await response.json();
    console.log('   Response data:', data);

  } catch (error) {
    console.log('❌ Sign-in test failed:', error.message);
  }

  // Test session endpoint with manual cookie
  console.log('\n2. Testing session endpoint...');
  try {
    const response = await fetch(`${API_BASE}/api/auth/get-session`, {
      credentials: 'include',
      headers: {
        'Origin': 'http://localhost:3001'
      }
    });

    console.log('   Session response status:', response.status);
    console.log('   Session response headers:');
    
    for (const [key, value] of response.headers.entries()) {
      console.log(`     ${key}: ${value}`);
    }

    const data = await response.text();
    console.log('   Session data:', data);

  } catch (error) {
    console.log('❌ Session test failed:', error.message);
  }

  console.log('\n🔍 Cookie test complete!');
}

testCookies().catch(console.error);
