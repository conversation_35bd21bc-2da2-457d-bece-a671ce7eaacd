/**
 * Debug script to test authentication flow
 */

const API_BASE = 'http://localhost:8787';

async function testAuthFlow() {
  console.log('🔍 Testing authentication flow...\n');

  // Test 1: Check API health
  console.log('1. Testing API health...');
  try {
    const response = await fetch(`${API_BASE}/`);
    const data = await response.json();
    console.log('✅ API is healthy:', data.message);
  } catch (error) {
    console.log('❌ API health check failed:', error.message);
    return;
  }

  // Test 2: Check session endpoint (no auth)
  console.log('\n2. Testing session endpoint (no auth)...');
  try {
    const response = await fetch(`${API_BASE}/api/auth/get-session`, {
      credentials: 'include'
    });
    const data = await response.text();
    console.log('✅ Session endpoint response:', data);
  } catch (error) {
    console.log('❌ Session endpoint failed:', error.message);
  }

  // Test 3: Test sign-in with invalid credentials
  console.log('\n3. Testing sign-in with invalid credentials...');
  try {
    const response = await fetch(`${API_BASE}/api/auth/sign-in/email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'wrongpassword'
      })
    });
    const data = await response.json();
    console.log('✅ Invalid login response:', data);
  } catch (error) {
    console.log('❌ Sign-in test failed:', error.message);
  }

  // Test 4: Test CORS headers
  console.log('\n4. Testing CORS headers...');
  try {
    const response = await fetch(`${API_BASE}/api/auth/get-session`, {
      method: 'OPTIONS',
      headers: {
        'Origin': 'http://localhost:3001',
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'Content-Type'
      }
    });
    console.log('✅ CORS preflight status:', response.status);
    console.log('   Access-Control-Allow-Origin:', response.headers.get('Access-Control-Allow-Origin'));
    console.log('   Access-Control-Allow-Credentials:', response.headers.get('Access-Control-Allow-Credentials'));
  } catch (error) {
    console.log('❌ CORS test failed:', error.message);
  }

  // Test 5: Test better-auth client endpoints
  console.log('\n5. Testing better-auth client endpoints...');
  const endpoints = [
    '/api/auth/session',
    '/api/auth/get-session',
    '/api/auth/me',
    '/api/auth/user'
  ];

  for (const endpoint of endpoints) {
    try {
      const response = await fetch(`${API_BASE}${endpoint}`, {
        credentials: 'include'
      });
      console.log(`   ${endpoint}: ${response.status} - ${response.statusText}`);
    } catch (error) {
      console.log(`   ${endpoint}: Error - ${error.message}`);
    }
  }

  console.log('\n🔍 Debug complete!');
}

// Run the test
testAuthFlow().catch(console.error);
