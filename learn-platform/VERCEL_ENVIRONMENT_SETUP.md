# Vercel Environment Variables Setup Guide

## 🎯 Problem

The INVALID_ORIGIN error is occurring in the Vercel deployment because:

1. `BETTER_AUTH_SECRET` is not set in the Vercel environment
2. The Next.js app uses the auth library directly via `/api/auth/[...all]/route.ts`
3. Environment variables need to be configured in the Vercel dashboard

## 🔧 Required Environment Variables for Vercel

### **Step 1: Access Vercel Dashboard**
1. Go to [Vercel Dashboard](https://vercel.com/dashboard)
2. Navigate to your project (likely named `learn-platform-web` or similar)
3. Go to **Settings** → **Environment Variables**

### **Step 2: Set Required Variables**

Add these environment variables in your Vercel project:

#### **Production Environment Variables**
```bash
# Authentication Configuration
BETTER_AUTH_SECRET=your-production-secret-key-min-32-chars
BETTER_AUTH_URL=https://kwaci-learning.bmbn.dev
BETTER_AUTH_TRUSTED_ORIGINS=https://kwaci-learning.bmbn.dev

# Database Configuration
DATABASE_URL=your-production-database-url

# API Configuration
NEXT_PUBLIC_API_URL=https://your-cloudflare-workers-api-url.workers.dev
```

#### **Preview Environment Variables**
```bash
# Authentication Configuration
BETTER_AUTH_SECRET=your-preview-secret-key-min-32-chars
BETTER_AUTH_URL=https://your-preview-domain.vercel.app
BETTER_AUTH_TRUSTED_ORIGINS=https://your-preview-domain.vercel.app

# Database Configuration
DATABASE_URL=your-preview-database-url

# API Configuration
NEXT_PUBLIC_API_URL=https://your-cloudflare-workers-dev-api-url.workers.dev
```

### **Step 3: Environment Variable Settings**

For each variable, set the **Environment** to:
- ✅ **Production** (for main branch deployments)
- ✅ **Preview** (for dev branch and PR deployments)
- ✅ **Development** (for local development, though this uses .env files)

## 🔐 Security Best Practices

### **Generate Secure Secrets**
```bash
# Generate a secure BETTER_AUTH_SECRET (32+ characters)
openssl rand -base64 32
```

### **Environment-Specific URLs**
- **Production**: Use your actual domain `https://kwaci-learning.bmbn.dev`
- **Preview**: Use Vercel's preview URLs or a staging domain
- **Development**: Use `http://localhost:3000`

## 🚀 Deployment Steps

### **Step 1: Set Environment Variables**
1. Add all required variables in Vercel dashboard
2. Ensure they're set for both Production and Preview environments

### **Step 2: Redeploy**
1. Go to **Deployments** tab in Vercel dashboard
2. Click **Redeploy** on the latest deployment
3. Or push a new commit to trigger a fresh deployment

### **Step 3: Verify Configuration**
1. Check the deployment logs for the debug messages:
   ```
   [Better Auth Config] Environment: Node.js, NODE_ENV: production
   [Better Auth Config] Base URL: https://kwaci-learning.bmbn.dev
   [Better Auth Config] Secret set: true
   [Better Auth] Configured trusted origins: http://localhost:3000, http://localhost:3001, http://localhost:8787, http://127.0.0.1:8787, https://kwaci-learning.bmbn.dev
   ```

2. The INVALID_ORIGIN error should be resolved

## 🔍 Troubleshooting

### **Issue: BETTER_AUTH_SECRET not set**
**Solution**: 
- Verify the variable is set in Vercel dashboard
- Check it's enabled for the correct environment (Production/Preview)
- Redeploy after adding the variable

### **Issue: Still getting INVALID_ORIGIN**
**Solution**:
- Check the debug logs to see what trusted origins are configured
- Verify `BETTER_AUTH_URL` matches your domain exactly
- Ensure `BETTER_AUTH_TRUSTED_ORIGINS` includes your domain

### **Issue: Environment variables not loading**
**Solution**:
- Variables must be set before deployment
- Redeploy after adding new variables
- Check variable names are exactly correct (case-sensitive)

## 📋 Verification Checklist

- [ ] `BETTER_AUTH_SECRET` set in Vercel dashboard
- [ ] `BETTER_AUTH_URL` set to production domain
- [ ] `BETTER_AUTH_TRUSTED_ORIGINS` includes production domain
- [ ] `DATABASE_URL` set to production database
- [ ] `NEXT_PUBLIC_API_URL` set to Cloudflare Workers API
- [ ] Variables enabled for Production environment
- [ ] Variables enabled for Preview environment
- [ ] Application redeployed after setting variables
- [ ] Debug logs show correct configuration
- [ ] INVALID_ORIGIN error resolved

## 🎯 Expected Result

After setting up the environment variables correctly, you should see:

1. **No BETTER_AUTH_SECRET warning**
2. **Trusted origins include your production domain**
3. **No INVALID_ORIGIN errors**
4. **Successful authentication flow**

The debug logs should show:
```
[Better Auth Config] Environment: Node.js, NODE_ENV: production
[Better Auth Config] Base URL: https://kwaci-learning.bmbn.dev
[Better Auth Config] Secret set: true
[Better Auth] Configured trusted origins: ..., https://kwaci-learning.bmbn.dev
```
