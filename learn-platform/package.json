{"name": "@learn-platform/source", "version": "0.0.0", "license": "MIT", "scripts": {"db:generate": "drizzle-kit generate --config=libs/db/drizzle.config.ts", "db:migrate": "drizzle-kit migrate --config=libs/db/drizzle.config.ts", "db:setup": "bash scripts/setup-database.sh"}, "private": true, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hono/zod-validator": "^0.7.0", "@hookform/resolvers": "^5.1.1", "@opennextjs/cloudflare": "^1.3.0", "@openrouter/ai-sdk-provider": "^0.7.2", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-query": "^5.80.7", "@trpc/client": "^11.4.0", "@trpc/react-query": "^11.4.0", "ai": "^4.3.16", "better-auth": "^1.2.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "drizzle-orm": "^0.44.2", "lucide-react": "^0.516.0", "next": "~15.2.4", "postgres": "^3.4.7", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.58.1", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "zustand": "^5.0.5"}, "devDependencies": {"@cloudflare/workers-types": "^4.20241127.0", "@eslint/compat": "^1.1.1", "@eslint/eslintrc": "^2.1.1", "@eslint/js": "^9.8.0", "@hono/trpc-server": "^0.3.2", "@next/eslint-plugin-next": "^15.2.4", "@nx/cypress": "21.1.3", "@nx/devkit": "21.1.3", "@nx/eslint": "21.1.3", "@nx/eslint-plugin": "21.1.3", "@nx/jest": "21.1.3", "@nx/js": "21.1.3", "@nx/next": "21.1.3", "@nx/playwright": "21.1.3", "@nx/workspace": "21.1.3", "@playwright/test": "^1.36.0", "@swc-node/register": "~1.9.1", "@swc/cli": "~0.6.0", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@testing-library/dom": "10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "16.1.0", "@trpc/server": "^11.0.0", "@types/jest": "^29.5.12", "@types/node": "18.16.9", "@types/react": "19.0.0", "@types/react-dom": "19.0.0", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "babel-jest": "^29.7.0", "cypress": "^14.2.1", "drizzle-kit": "^0.31.1", "eslint": "^9.8.0", "eslint-config-next": "^15.2.4", "eslint-config-prettier": "^10.0.0", "eslint-plugin-cypress": "^3.5.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-playwright": "^1.6.2", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "5.0.0", "hono": "^4.6.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "nx": "^21.2.0", "postcss": "^8.5.5", "prettier": "^2.6.2", "tailwindcss": "^3.4.0", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "tslib": "^2.8.1", "typescript": "~5.7.2", "typescript-eslint": "^8.19.0", "wrangler": "4.20.0", "zod": "^3.23.8"}}