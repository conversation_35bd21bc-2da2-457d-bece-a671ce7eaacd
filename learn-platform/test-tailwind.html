<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tailwind CSS Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        brand: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                            950: '#082f49',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        /* Component classes */
        .btn {
            @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
        }
        .btn-primary {
            @apply btn bg-brand-600 text-white hover:bg-brand-700 focus:ring-brand-500;
        }
        .card {
            @apply bg-white rounded-lg shadow-lg border border-gray-200;
        }
        .card-header {
            @apply px-6 py-4 border-b border-gray-200;
        }
        .card-body {
            @apply px-6 py-4;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-brand-50 to-brand-100 min-h-screen">
    <div class="max-w-4xl mx-auto px-4 py-16">
        <div class="text-center mb-16">
            <h1 class="text-6xl font-bold text-gray-900 mb-4">
                <span class="bg-gradient-to-r from-brand-500 to-brand-600 bg-clip-text text-transparent">
                    Tailwind CSS
                </span>
                Test Page 🎉
            </h1>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                This page demonstrates that the Tailwind CSS setup is working correctly with custom design tokens and component classes.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-gray-900">Brand Colors</h3>
                </div>
                <div class="card-body">
                    <div class="flex space-x-2">
                        <div class="w-8 h-8 bg-brand-300 rounded-full"></div>
                        <div class="w-8 h-8 bg-brand-500 rounded-full"></div>
                        <div class="w-8 h-8 bg-brand-700 rounded-full"></div>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-gray-900">Components</h3>
                </div>
                <div class="card-body">
                    <button class="btn-primary">Primary Button</button>
                </div>
            </div>

            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-gray-900">Utilities</h3>
                </div>
                <div class="card-body">
                    <div class="text-sm text-gray-600">
                        Custom spacing, shadows, and animations work!
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center">
            <p class="text-gray-600">
                ✅ Tailwind CSS is properly configured and working!
            </p>
        </div>
    </div>
</body>
</html>
