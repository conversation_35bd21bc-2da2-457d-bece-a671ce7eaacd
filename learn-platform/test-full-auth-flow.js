/**
 * Test the complete authentication flow
 */

import { createAuthClient } from 'better-auth/client';

const authClient = createAuthClient({
  baseURL: 'http://localhost:8787',
});

async function testFullAuthFlow() {
  console.log('🔍 Testing complete authentication flow...\n');

  const testUser = {
    email: '<EMAIL>',
    password: 'testpassword123'
  };

  // Step 1: Check initial session (should be null)
  console.log('1. Checking initial session...');
  try {
    const initialSession = await authClient.getSession();
    console.log('✅ Initial session:', initialSession);
  } catch (error) {
    console.log('❌ Initial session check failed:', error.message);
  }

  // Step 2: Sign in
  console.log('\n2. Signing in...');
  try {
    const signInResult = await authClient.signIn.email(testUser);
    console.log('✅ Sign in result:', signInResult);
    
    if (signInResult.error) {
      console.log('❌ Sign in failed:', signInResult.error);
      return;
    }
  } catch (error) {
    console.log('❌ Sign in failed:', error.message);
    return;
  }

  // Step 3: Check session after sign in
  console.log('\n3. Checking session after sign in...');
  try {
    const sessionAfterSignIn = await authClient.getSession();
    console.log('✅ Session after sign in:', sessionAfterSignIn);
    
    if (sessionAfterSignIn.data?.user) {
      console.log('🎉 Authentication successful!');
      console.log('   User ID:', sessionAfterSignIn.data.user.id);
      console.log('   User Email:', sessionAfterSignIn.data.user.email);
      console.log('   User Name:', sessionAfterSignIn.data.user.name);
    } else {
      console.log('❌ No user data in session after sign in');
    }
  } catch (error) {
    console.log('❌ Session check after sign in failed:', error.message);
  }

  // Step 4: Sign out
  console.log('\n4. Signing out...');
  try {
    const signOutResult = await authClient.signOut();
    console.log('✅ Sign out result:', signOutResult);
  } catch (error) {
    console.log('❌ Sign out failed:', error.message);
  }

  // Step 5: Check session after sign out
  console.log('\n5. Checking session after sign out...');
  try {
    const sessionAfterSignOut = await authClient.getSession();
    console.log('✅ Session after sign out:', sessionAfterSignOut);
  } catch (error) {
    console.log('❌ Session check after sign out failed:', error.message);
  }

  console.log('\n🔍 Complete authentication flow test finished!');
}

testFullAuthFlow().catch(console.error);
