/* eslint-disable @nx/enforce-module-boundaries */
/**
 * Test script to verify the database connection fix
 * This script tests that the database connection isolation works correctly
 */

// Mock environment for testing
const mockEnv = {
  DATABASE_URL: '********************************/demo'
};

// Test the database connection function
async function testDatabaseConnection() {
  try {
    console.log('🧪 Testing database connection fix...');
    
    // Import the fixed database utility
    const { createWorkersDatabaseFromEnv } = await import('./apps/api/src/utils/database.js');
    
    console.log('✅ Successfully imported database utility');
    
    // Test creating multiple connections (simulating different requests)
    console.log('🔄 Testing request isolation...');
    
    const connection1 = createWorkersDatabaseFromEnv(mockEnv);
    const connection2 = createWorkersDatabaseFromEnv(mockEnv);
    const connection3 = createWorkersDatabaseFromEnv(mockEnv);
    
    console.log('✅ Created 3 isolated database connections');
    
    // Verify each connection has the required properties
    const connections = [connection1, connection2, connection3];
    connections.forEach((conn, index) => {
      if (!conn.db || !conn.client) {
        throw new Error(`Connection ${index + 1} missing required properties`);
      }
      
      if (typeof conn.db.select !== 'function') {
        throw new Error(`Connection ${index + 1} db missing select method`);
      }
      
      if (typeof conn.client.end !== 'function') {
        throw new Error(`Connection ${index + 1} client missing end method`);
      }
    });
    
    console.log('✅ All connections have required properties');
    
    // Verify connections are isolated (different instances)
    if (connection1 === connection2 || connection2 === connection3 || connection1 === connection3) {
      throw new Error('Connections are not isolated - same instance returned');
    }
    
    if (connection1.db === connection2.db || connection2.db === connection3.db || connection1.db === connection3.db) {
      throw new Error('Database instances are not isolated - same instance returned');
    }
    
    console.log('✅ Connections are properly isolated');
    
    // Test demo connection handling
    console.log('🔄 Testing demo connection handling...');
    const demoConnection = createWorkersDatabaseFromEnv(mockEnv);
    
    if (!demoConnection.db.transaction) {
      throw new Error('Demo connection missing transaction support');
    }
    
    console.log('✅ Demo connection properly configured');
    
    console.log('🎉 All database connection tests passed!');
    console.log('');
    console.log('✨ The Cloudflare Workers database connection fix is working correctly:');
    console.log('   • Each request gets its own isolated database connection');
    console.log('   • No sharing of I/O objects across request contexts');
    console.log('   • Demo environment properly supported');
    console.log('   • Connection cleanup utilities available');
    console.log('');
    console.log('🚀 The "Cannot perform I/O on behalf of a different request" error should be resolved!');
    
  } catch (error) {
    console.error('❌ Database connection test failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Test the learning progress procedures
async function testLearningProgressProcedures() {
  try {
    console.log('🧪 Testing learning progress procedures...');
    
    // Import the procedures
    const { workersLearningProgressProcedures } = await import('./apps/api/src/procedures/learning-progress.js');
    
    console.log('✅ Successfully imported learning progress procedures');
    
    // Verify all required procedures exist
    const requiredProcedures = [
      'getProgress',
      'updateProgress',
      'addBookmark',
      'addNote',
      'removeBookmark',
      'updateNote',
      'deleteNote'
    ];
    
    requiredProcedures.forEach(procName => {
      if (typeof workersLearningProgressProcedures[procName] !== 'function') {
        throw new Error(`Missing or invalid procedure: ${procName}`);
      }
    });
    
    console.log('✅ All required procedures are available');
    console.log('🎉 Learning progress procedures test passed!');
    
  } catch (error) {
    console.error('❌ Learning progress procedures test failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Run tests
async function runTests() {
  console.log('🔧 Cloudflare Workers Database Connection Fix - Test Suite');
  console.log('='.repeat(60));
  console.log('');
  
  await testDatabaseConnection();
  console.log('');
  await testLearningProgressProcedures();
  
  console.log('');
  console.log('🎊 All tests passed! The database connection fix is working correctly.');
}

// Handle module loading for both CommonJS and ES modules
if (import.meta.url === `file://${process.argv[1]}`) {
  runTests().catch(error => {
    console.error('❌ Test suite failed:', error);
    process.exit(1);
  });
}

export { testDatabaseConnection, testLearningProgressProcedures };
