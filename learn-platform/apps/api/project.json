{"name": "api", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api/src", "projectType": "application", "tags": ["scope:api", "type:worker"], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/apps/api", "main": "apps/api/src/index.ts", "tsConfig": "apps/api/tsconfig.json", "assets": ["apps/api/wrangler.jsonc"]}}, "dev": {"executor": "nx:run-commands", "options": {"command": "wrangler dev --config wrangler.jsonc", "cwd": "apps/api"}}, "deploy": {"executor": "nx:run-commands", "options": {"command": "wrangler deploy --config wrangler.jsonc", "cwd": "apps/api", "env": {"CLOUDFLARE_API_TOKEN": "{env.CLOUDFLARE_API_TOKEN}"}}}, "deploy:dev": {"executor": "nx:run-commands", "options": {"command": "wrangler deploy --config wrangler.jsonc --env dev", "cwd": "apps/api", "env": {"CLOUDFLARE_API_TOKEN": "{env.CLOUDFLARE_API_TOKEN}"}}}, "deploy:prod": {"executor": "nx:run-commands", "options": {"command": "wrangler deploy --config wrangler.jsonc --env prod", "cwd": "apps/api", "env": {"CLOUDFLARE_API_TOKEN": "{env.CLOUDFLARE_API_TOKEN}"}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/api/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/apps/api"], "options": {"jestConfig": "apps/api/jest.config.ts", "passWithNoTests": true}}}, "implicitDependencies": ["trpc"]}