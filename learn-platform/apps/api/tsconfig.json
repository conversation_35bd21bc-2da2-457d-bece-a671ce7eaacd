{"extends": "../../tsconfig.base.json", "compilerOptions": {"module": "esnext", "target": "es2022", "lib": ["es2022"], "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "types": ["@cloudflare/workers-types"], "noEmit": true}, "include": ["src/**/*.ts"], "exclude": ["node_modules", "dist", "src/**/*.js", "src/**/*.js.map"]}