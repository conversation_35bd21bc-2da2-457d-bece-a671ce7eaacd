/**
 * Integration tests for API authentication flow
 *
 * Tests the complete authentication flow including:
 * - Better Auth endpoints (sign up, sign in, sign out)
 * - Session cookie handling
 * - Protected route access
 * - Error scenarios
 */

/// <reference types="@cloudflare/workers-types" />

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';

// Mock Better Auth and database dependencies
jest.mock('@learn-platform/auth', () => ({
  createAuth: jest.fn(() => ({
    handler: jest.fn().mockImplementation((request: Request) => {
      const url = new URL(request.url);

      // Mock different auth endpoints
      if (url.pathname === '/api/auth/session') {
        // Check if request has session cookie
        const cookieHeader = request.headers.get('Cookie');
        if (cookieHeader && cookieHeader.includes('session=mock-session-token')) {
          return Promise.resolve(new Response(JSON.stringify({
            user: {
              id: '1',
              email: '<EMAIL>',
              name: 'Test User',
              emailVerified: true
            }
          }), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
          }));
        }
        return Promise.resolve(new Response(JSON.stringify({ user: null }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        }));
      }

      if (url.pathname === '/api/auth/sign-up' && request.method === 'POST') {
        return request.text().then(body => {
          try {
            const userData = JSON.parse(body);

            // Validate required fields
            if (!userData.email || !userData.password || !userData.name) {
              return new Response(JSON.stringify({
                error: 'Bad Request',
                message: 'Missing required fields: email, password, name'
              }), {
                status: 400,
                headers: { 'Content-Type': 'application/json' }
              });
            }

            // Validate email format
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(userData.email)) {
              return new Response(JSON.stringify({
                error: 'Bad Request',
                message: 'Invalid email format'
              }), {
                status: 400,
                headers: { 'Content-Type': 'application/json' }
              });
            }

            return new Response(JSON.stringify({
              user: {
                id: '1',
                email: userData.email,
                name: userData.name,
                emailVerified: false
              }
            }), {
              status: 201,
              headers: {
                'Content-Type': 'application/json',
                'Set-Cookie': 'session=mock-session-token; HttpOnly; Path=/'
              }
            });
          } catch (error) {
            return new Response(JSON.stringify({
              error: 'Bad Request',
              message: 'Invalid JSON in request body'
            }), {
              status: 400,
              headers: { 'Content-Type': 'application/json' }
            });
          }
        });
      }

      if (url.pathname === '/api/auth/sign-in/email' && request.method === 'POST') {
        return request.text().then(body => {
          try {
            const credentials = JSON.parse(body);
            if (credentials.password === 'wrongpassword') {
              return new Response(JSON.stringify({
                error: 'Invalid credentials'
              }), {
                status: 400,
                headers: { 'Content-Type': 'application/json' }
              });
            }
            return new Response(JSON.stringify({
              user: {
                id: '1',
                email: credentials.email,
                name: 'Test User',
                emailVerified: true
              }
            }), {
              status: 200,
              headers: {
                'Content-Type': 'application/json',
                'Set-Cookie': 'session=mock-session-token; HttpOnly; Path=/'
              }
            });
          } catch (error) {
            return new Response(JSON.stringify({
              error: 'Bad Request',
              message: 'Invalid JSON in request body'
            }), {
              status: 400,
              headers: { 'Content-Type': 'application/json' }
            });
          }
        });
      }

      return Promise.resolve(new Response('Not Found', { status: 404 }));
    }),
  })),
  createAuthContextWithEnv: jest.fn().mockImplementation((request: Request) => {
    const cookieHeader = request.headers.get('Cookie');
    if (cookieHeader && cookieHeader.includes('session=mock-session-token')) {
      return Promise.resolve({
        isAuthenticated: true,
        user: {
          id: '1',
          email: '<EMAIL>',
          name: 'Test User',
          emailVerified: true
        },
        session: {
          id: 'mock-session-id',
          userId: '1',
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        userId: '1',
      });
    }
    return Promise.resolve({
      isAuthenticated: false,
      user: null,
      session: null,
      userId: null,
    });
  }),
  isSessionValid: jest.fn().mockReturnValue(true),
}));

jest.mock('@learn-platform/trpc', () => ({
  appRouter: {
    createCaller: jest.fn(),
  },
  createContext: jest.fn().mockResolvedValue({
    request: {},
    env: {},
    isAuthenticated: false,
    userId: null,
    user: null,
  }),
}));

// Mock the protected routes
jest.mock('./routes/protected', () => {
  const { Hono } = require('hono');
  const protectedRoutes = new Hono();

  // Mock authentication middleware
  const mockAuthMiddleware = async (c: any, next: any) => {
    const cookieHeader = c.req.header('Cookie');
    if (!cookieHeader || !cookieHeader.includes('session=mock-session-token')) {
      return c.json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Authentication required. Please log in to access this resource.',
        },
        timestamp: new Date().toISOString(),
      }, 401);
    }

    // Set mock auth context
    c.set('auth', {
      user: { id: '1', email: '<EMAIL>', name: 'Test User' },
      session: { id: 'mock-session-id', userId: '1' },
      isAuthenticated: true,
      userId: '1',
    });

    await next();
  };

  protectedRoutes.use('*', mockAuthMiddleware);

  // Mock protected endpoints
  protectedRoutes.get('/user-profile', (c: any) => {
    return c.json({
      success: true,
      data: {
        id: '1',
        email: '<EMAIL>',
        name: 'Test User',
        avatar: null,
        emailVerified: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        preferences: {
          theme: 'light',
          language: 'en',
          timezone: 'UTC',
          notifications: { email: true, push: true, marketing: false },
          privacy: { profileVisibility: 'public', showEmail: false, showLastSeen: true },
        },
        metadata: {
          totalLogins: 42,
          accountStatus: 'active',
          roles: [],
          permissions: [],
        },
      },
    });
  });

  protectedRoutes.get('/dashboard', (c: any) => {
    return c.json({
      success: true,
      data: {
        user: { id: '1', email: '<EMAIL>', name: 'Test User' },
        stats: {
          coursesEnrolled: 5,
          coursesCompleted: 2,
          totalLearningTime: 120,
          currentStreak: 7,
          achievements: [],
        },
        recentActivity: [],
        recommendations: [],
      },
    });
  });

  protectedRoutes.put('/preferences', async (c: any) => {
    try {
      const body = await c.req.json();

      // Basic validation
      if (body.theme && !['light', 'dark', 'system'].includes(body.theme)) {
        return c.json({
          success: false,
          error: { code: 'VALIDATION_ERROR', message: 'Invalid theme value' },
        }, 400);
      }

      return c.json({
        success: true,
        data: {
          theme: body.theme || 'light',
          notifications: body.notifications || { email: true, push: true, marketing: false },
        },
      });
    } catch (error) {
      return c.json({
        success: false,
        error: { code: 'VALIDATION_ERROR', message: 'Invalid request data' },
      }, 400);
    }
  });

  protectedRoutes.get('/session-info', (c: any) => {
    return c.json({
      success: true,
      data: {
        session: {
          id: 'mock-session-id',
          userId: '1',
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        },
        user: {
          id: '1',
          email: '<EMAIL>',
          name: 'Test User',
        },
        request: {
          ip: '127.0.0.1',
          userAgent: 'Test Agent',
        },
      },
    });
  });

  return { protectedRoutes };
});

import app from './index';

// Mock environment for Cloudflare Workers
const mockEnv = {
  BETTER_AUTH_SECRET: 'test-secret-key-for-testing-only',
  BETTER_AUTH_URL: 'http://localhost:8787',
  DATABASE_URL: 'postgresql://test:test@localhost:5432/test',
};

// Test user data
const TEST_USER = {
  email: '<EMAIL>',
  password: 'SecurePassword123!',
  name: 'Test User',
};

// Helper to create mock Request objects
function createMockRequest(
  url: string,
  options: {
    method?: string;
    headers?: Record<string, string>;
    body?: string;
    cookies?: string;
  } = {}
): Request {
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  if (options.cookies) {
    headers['Cookie'] = options.cookies;
  }

  return new Request(url, {
    method: options.method || 'GET',
    headers,
    body: options.body,
  });
}

// Helper to extract cookies from response
function extractCookies(response: Response): string {
  const setCookieHeader = response.headers.get('Set-Cookie');
  return setCookieHeader || '';
}

// Helper to make authenticated requests
async function makeAuthenticatedRequest(
  endpoint: string,
  sessionCookieOrOptions?: string | {
    method?: string;
    body?: string;
    headers?: Record<string, string>;
  },
  options: {
    method?: string;
    body?: string;
    headers?: Record<string, string>;
  } = {}
): Promise<Response> {
  let sessionCookie = '';
  let requestOptions = options;

  // Handle overloaded parameters
  if (typeof sessionCookieOrOptions === 'string') {
    sessionCookie = sessionCookieOrOptions;
  } else if (sessionCookieOrOptions) {
    requestOptions = sessionCookieOrOptions;
  }

  const request = createMockRequest(`http://localhost:8787${endpoint}`, {
    method: requestOptions.method || 'GET',
    headers: requestOptions.headers,
    body: requestOptions.body,
    cookies: sessionCookie,
  });

  return app.request(request, mockEnv as any);
}

describe('API Authentication Integration', () => {
  let sessionCookie = '';

  beforeEach(() => {
    // Reset session cookie before each test
    sessionCookie = '';
  });

  afterEach(() => {
    // Clear mocks if jest is available (for Jest compatibility)
    if (typeof jest !== 'undefined' && jest.clearAllMocks) {
      jest.clearAllMocks();
    }
  });

  describe('Health Check', () => {
    it('should return healthy status', async () => {
      const request = createMockRequest('http://localhost:8787/');
      const response = await app.request(request, mockEnv as any);

      expect(response.status).toBe(200);

      const data = await response.json() as any;
      expect(data).toMatchObject({
        message: expect.stringContaining('Learning Platform API'),
        status: 'healthy',
        timestamp: expect.any(String),
      });
    });
  });

  describe('Authentication Flow', () => {
    it('should sign up a new user', async () => {
      const request = createMockRequest('http://localhost:8787/api/auth/sign-up', {
        method: 'POST',
        body: JSON.stringify(TEST_USER),
      });

      const response = await app.request(request, mockEnv as any);

      // Note: This might return different status codes depending on Better Auth configuration
      // Common responses: 200 (success), 201 (created), or 400 (user exists)
      expect([200, 201, 400]).toContain(response.status);

      if (response.status === 200 || response.status === 201) {
        const data = await response.json() as any;
        expect(data).toHaveProperty('user');

        // Extract session cookie if provided
        const cookies = extractCookies(response);
        if (cookies) {
          sessionCookie = cookies;
        }
      }
    });

    it('should sign in with valid credentials', async () => {
      const request = createMockRequest('http://localhost:8787/api/auth/sign-in/email', {
        method: 'POST',
        body: JSON.stringify({
          email: TEST_USER.email,
          password: TEST_USER.password,
        }),
      });

      const response = await app.request(request, mockEnv as any);

      // Should succeed or return user already exists
      expect([200, 201]).toContain(response.status);

      const data = await response.json() as any;
      expect(data).toHaveProperty('user');
      expect(data.user).toMatchObject({
        email: TEST_USER.email,
        name: TEST_USER.name,
      });

      // Extract session cookie
      const cookies = extractCookies(response);
      expect(cookies).toBeTruthy();
      sessionCookie = cookies;
    });

    it('should reject sign in with invalid credentials', async () => {
      const request = createMockRequest('http://localhost:8787/api/auth/sign-in/email', {
        method: 'POST',
        body: JSON.stringify({
          email: TEST_USER.email,
          password: 'wrongpassword',
        }),
      });

      const response = await app.request(request, mockEnv as any);

      expect(response.status).toBe(400);

      const data = await response.json() as any;
      expect(data).toHaveProperty('error');
    });
  });

  describe('Session Management', () => {
    beforeEach(async () => {
      // Sign in to get a valid session
      const signInRequest = createMockRequest('http://localhost:8787/api/auth/sign-in/email', {
        method: 'POST',
        body: JSON.stringify({
          email: TEST_USER.email,
          password: TEST_USER.password,
        }),
      });

      const signInResponse = await app.request(signInRequest, mockEnv as any);
      if (signInResponse.status === 200 || signInResponse.status === 201) {
        sessionCookie = extractCookies(signInResponse);
      }
    });

    it('should get current session when authenticated', async () => {
      if (!sessionCookie) {
        // Skip if no session cookie (sign in failed)
        return;
      }

      const response = await makeAuthenticatedRequest('/api/auth/session', sessionCookie);

      expect(response.status).toBe(200);

      const data = await response.json() as any;
      expect(data).toHaveProperty('user');
      expect(data.user).not.toBeNull();
      expect(data.user.email).toBe(TEST_USER.email);
    });

    it('should return null session when not authenticated', async () => {
      const request = createMockRequest('http://localhost:8787/api/auth/session');
      const response = await app.request(request, mockEnv as any);

      expect(response.status).toBe(200);

      const data = await response.json() as any;
      expect(data.user).toBeNull();
    });
  });

  describe('Protected Routes', () => {
    beforeEach(async () => {
      // Ensure we have a valid session for protected route tests
      const signInRequest = createMockRequest('http://localhost:8787/api/auth/sign-in/email', {
        method: 'POST',
        body: JSON.stringify({
          email: TEST_USER.email,
          password: TEST_USER.password,
        }),
      });

      const signInResponse = await app.request(signInRequest, mockEnv as any);
      if (signInResponse.status === 200 || signInResponse.status === 201) {
        sessionCookie = extractCookies(signInResponse);
      }
    });

    it('should block access to protected routes without authentication', async () => {
      const request = createMockRequest('http://localhost:8787/api/protected/user-profile');
      const response = await app.request(request, mockEnv as any);

      expect(response.status).toBe(401);

      const data = await response.json();
      expect(data).toMatchObject({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: expect.stringContaining('Authentication required'),
        },
      });
    });

    it('should allow access to protected routes with valid session', async () => {
      if (!sessionCookie) {
        // Skip if no session cookie
        return;
      }

      const response = await makeAuthenticatedRequest('/api/protected/user-profile', sessionCookie);

      expect(response.status).toBe(200);

      const data = await response.json();
      expect(data).toMatchObject({
        success: true,
        data: {
          id: expect.any(String),
          email: TEST_USER.email,
          name: TEST_USER.name,
          preferences: expect.any(Object),
          metadata: expect.any(Object),
        },
      });
    });

    it('should return dashboard data for authenticated users', async () => {
      if (!sessionCookie) {
        return;
      }

      const response = await makeAuthenticatedRequest('/api/protected/dashboard', sessionCookie);

      expect(response.status).toBe(200);

      const data = await response.json();
      expect(data).toMatchObject({
        success: true,
        data: {
          user: expect.any(Object),
          stats: {
            coursesEnrolled: expect.any(Number),
            coursesCompleted: expect.any(Number),
            totalLearningTime: expect.any(Number),
            currentStreak: expect.any(Number),
            achievements: expect.arrayContaining([]),
          },
          recentActivity: expect.arrayContaining([]),
          recommendations: expect.arrayContaining([]),
        },
      });
    });

    it('should update user preferences with valid data', async () => {
      if (!sessionCookie) {
        return;
      }

      const updateData = {
        theme: 'dark' as const,
        notifications: {
          email: false,
          marketing: false,
        },
      };

      const response = await makeAuthenticatedRequest('/api/protected/preferences', sessionCookie, {
        method: 'PUT',
        body: JSON.stringify(updateData),
        headers: { 'Content-Type': 'application/json' },
      });

      expect(response.status).toBe(200);

      const data = await response.json();
      expect(data).toMatchObject({
        success: true,
        data: {
          theme: 'dark',
          notifications: {
            email: false,
            marketing: false,
          },
        },
      });
    });

    it('should validate preferences update data', async () => {
      if (!sessionCookie) {
        return;
      }

      const invalidData = {
        theme: 'invalid-theme',
        notifications: 'not-an-object',
      };

      const response = await makeAuthenticatedRequest('/api/protected/preferences', sessionCookie, {
        method: 'PUT',
        body: JSON.stringify(invalidData),
        headers: { 'Content-Type': 'application/json' },
      });

      expect(response.status).toBe(400);
    });

    it('should return session info for authenticated users', async () => {
      if (!sessionCookie) {
        return;
      }

      const response = await makeAuthenticatedRequest('/api/protected/session-info', sessionCookie);

      expect(response.status).toBe(200);

      const data = await response.json();
      expect(data).toMatchObject({
        success: true,
        data: {
          session: {
            id: expect.any(String),
            userId: expect.any(String),
            expiresAt: expect.any(String),
          },
          user: {
            id: expect.any(String),
            email: TEST_USER.email,
            name: TEST_USER.name,
          },
          request: expect.any(Object),
        },
      });
    });
  });

  describe('Error Scenarios', () => {
    it('should handle malformed JSON in auth requests', async () => {
      const request = createMockRequest('http://localhost:8787/api/auth/sign-in/email', {
        method: 'POST',
        body: 'invalid-json',
        headers: { 'Content-Type': 'application/json' },
      });

      const response = await app.request(request, mockEnv as any);

      expect(response.status).toBe(400);
    });

    it('should handle missing required fields in sign up', async () => {
      const request = createMockRequest('http://localhost:8787/api/auth/sign-up', {
        method: 'POST',
        body: JSON.stringify({
          email: '<EMAIL>',
          // Missing password and name
        }),
      });

      const response = await app.request(request, mockEnv as any);

      expect(response.status).toBe(400);
    });

    it('should handle invalid email format', async () => {
      const request = createMockRequest('http://localhost:8787/api/auth/sign-up', {
        method: 'POST',
        body: JSON.stringify({
          email: 'invalid-email',
          password: 'password123',
          name: 'Test User',
        }),
      });

      const response = await app.request(request, mockEnv as any);

      expect(response.status).toBe(400);
    });

    it('should return 404 for non-existent endpoints', async () => {
      const request = createMockRequest('http://localhost:8787/api/non-existent');
      const response = await app.request(request, mockEnv as any);

      expect(response.status).toBe(404);

      const data = await response.json() as any;
      expect(data).toMatchObject({
        error: 'Not Found',
        message: 'The requested endpoint does not exist',
      });
    });
  });

  describe('CORS Configuration', () => {
    it('should include CORS headers in responses', async () => {
      const request = createMockRequest('http://localhost:8787/', {
        headers: {
          'Origin': 'http://localhost:3000',
        },
      });

      const response = await app.request(request, mockEnv as any);

      expect(response.headers.get('Access-Control-Allow-Origin')).toBeTruthy();
      expect(response.headers.get('Access-Control-Allow-Credentials')).toBe('true');
    });

    it('should handle preflight OPTIONS requests', async () => {
      const request = createMockRequest('http://localhost:8787/api/protected/user-profile', {
        method: 'OPTIONS',
        headers: {
          'Origin': 'http://localhost:3000',
          'Access-Control-Request-Method': 'GET',
          'Access-Control-Request-Headers': 'Content-Type',
        },
      });

      const response = await app.request(request, mockEnv as any);

      expect([200, 204]).toContain(response.status);
      expect(response.headers.get('Access-Control-Allow-Methods')).toBeTruthy();
      expect(response.headers.get('Access-Control-Allow-Headers')).toBeTruthy();
    });
  });
});
