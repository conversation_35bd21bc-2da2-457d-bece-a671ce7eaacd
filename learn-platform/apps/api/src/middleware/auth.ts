/**
 * Authentication middleware for Hono routes
 *
 * This middleware provides authentication checking for direct Hono routes
 * (non-tRPC endpoints) using the Better Auth implementation.
 *
 * Features:
 * - Session validation and user authentication
 * - Comprehensive error handling for auth failures
 * - Request metadata extraction (IP, User-Agent, etc.)
 * - Type-safe context enhancement
 * - Cloudflare Workers compatibility
 */

import type { Context, Next } from 'hono';
import { HTTPException } from 'hono/http-exception';
import type {
  ApiResponse,
  AuthenticatedContext,
  ProtectedRouteContext
} from '../types/api';

// Cloudflare Workers crypto polyfill
const getCrypto = () => {
  if (typeof globalThis !== 'undefined' && (globalThis as any).crypto) {
    return (globalThis as any).crypto;
  }
  // Fallback for environments without crypto
  return {
    randomUUID: () => Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
  };
};

/**
 * Authentication middleware for protected Hono routes
 *
 * This middleware:
 * 1. Extracts the session from request headers/cookies
 * 2. Validates the session and checks expiration
 * 3. Enhances the context with authenticated user information
 * 4. Provides comprehensive error handling for auth failures
 *
 * Usage:
 * ```typescript
 * app.use('/api/protected/*', authMiddleware);
 * app.get('/api/protected/profile', (c) => {
 *   // c.get('auth') contains authenticated user info
 *   const { user, session } = c.get('auth');
 *   return c.json({ user });
 * });
 * ```
 */
export async function authMiddleware(c: Context, next: Next) {
  try {
    // Extract request metadata for logging and security
    const requestMetadata = extractRequestMetadata(c);

    // Dynamically import auth functions
    const { createAuthContextWithEnv, isSessionValid } = await import('@learn-platform/auth');

    // Get authentication context using Better Auth
    const authContext = await createAuthContextWithEnv(c.req.raw, c.env);

    // Check if user is authenticated
    if (!authContext.isAuthenticated || !authContext.session) {
      throw new HTTPException(401, {
        message: createErrorResponse('UNAUTHORIZED', 'Authentication required. Please log in to access this resource.', requestMetadata.requestId)
      });
    }

    // Validate session expiration
    if (!isSessionValid(authContext.session)) {
      throw new HTTPException(401, {
        message: createErrorResponse('SESSION_EXPIRED', 'Your session has expired. Please log in again.', requestMetadata.requestId)
      });
    }

    // Create authenticated context
    const authenticatedContext: AuthenticatedContext = {
      user: authContext.user as AuthenticatedContext['user'],
      session: authContext.session as AuthenticatedContext['session'],
      isAuthenticated: true,
      userId: authContext.userId as NonNullable<typeof authContext.userId>,
    };

    // Create protected route context
    const protectedContext: ProtectedRouteContext = {
      auth: authenticatedContext,
      ...requestMetadata,
    };

    // Enhance Hono context with authentication information
    c.set('auth', authenticatedContext);
    c.set('protectedContext', protectedContext);
    c.set('user', authContext.user);
    c.set('session', authContext.session);
    c.set('userId', authContext.userId);
    c.set('requestId', requestMetadata.requestId);

    // Log successful authentication (in production, consider using structured logging)
    console.log(`Authenticated request: ${authContext.userId} - ${requestMetadata.requestId}`);

    await next();
  } catch (error) {
    // Handle authentication errors
    if (error instanceof HTTPException) {
      // Re-throw HTTP exceptions (already properly formatted)
      throw error;
    }

    // Handle unexpected errors
    console.error('Authentication middleware error:', error);

    const requestId = c.get('requestId') || getCrypto().randomUUID();
    throw new HTTPException(500, {
      message: createErrorResponse(
        'INTERNAL_ERROR',
        'An unexpected error occurred during authentication.',
        requestId
      )
    });
  }
}

/**
 * Optional authentication middleware
 *
 * Similar to authMiddleware but doesn't require authentication.
 * Adds user information to context if available, but allows unauthenticated requests.
 *
 * Useful for endpoints that have different behavior for authenticated vs unauthenticated users.
 */
export async function optionalAuthMiddleware(c: Context, next: Next) {
  try {
    const requestMetadata = extractRequestMetadata(c);

    // Dynamically import auth functions
    const { createAuthContextWithEnv, isSessionValid } = await import('@learn-platform/auth');

    const authContext = await createAuthContextWithEnv(c.req.raw, c.env);

    // Set context regardless of authentication status
    c.set('isAuthenticated', authContext.isAuthenticated);
    c.set('user', authContext.user);
    c.set('session', authContext.session);
    c.set('userId', authContext.userId);
    c.set('requestId', requestMetadata.requestId);

    if (authContext.isAuthenticated && authContext.session) {
      // Validate session if present
      if (isSessionValid(authContext.session)) {
        const authenticatedContext: AuthenticatedContext = {
          user: authContext.user as AuthenticatedContext['user'],
          session: authContext.session as AuthenticatedContext['session'],
          isAuthenticated: true,
          userId: authContext.userId as NonNullable<typeof authContext.userId>,
        };

        c.set('auth', authenticatedContext);
      } else {
        // Session expired, clear auth context
        c.set('isAuthenticated', false);
        c.set('user', null);
        c.set('session', null);
        c.set('userId', null);
      }
    }

    await next();
  } catch (error) {
    console.error('Optional auth middleware error:', error);
    // Don't throw errors in optional auth - just proceed without auth
    c.set('isAuthenticated', false);
    c.set('user', null);
    c.set('session', null);
    c.set('userId', null);
    c.set('requestId', getCrypto().randomUUID());

    await next();
  }
}

/**
 * Role-based authorization middleware
 *
 * Requires authentication and checks if user has required roles.
 * Must be used after authMiddleware.
 *
 * @param requiredRoles - Array of roles that user must have
 */
export function requireRoles(requiredRoles: string[]) {
  return async (c: Context, next: Next) => {
    const auth = c.get('auth') as AuthenticatedContext | undefined;

    if (!auth) {
      throw new HTTPException(401, {
        message: createErrorResponse('UNAUTHORIZED', 'Authentication required for role-based access.')
      });
    }

    // TODO: Implement role checking when role system is added
    // For now, we'll just check if user is authenticated
    // In a real implementation, you would check user roles from database

    const userRoles: string[] = []; // TODO: Get from user.roles or database
    const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role));

    if (!hasRequiredRole) {
      throw new HTTPException(403, {
        message: createErrorResponse(
          'FORBIDDEN',
          `Access denied. Required roles: ${requiredRoles.join(', ')}`
        )
      });
    }

    await next();
  };
}

/**
 * Extract request metadata for logging and security
 */
function extractRequestMetadata(c: Context) {
  const request = c.req.raw;

  return {
    requestId: request.headers.get('CF-Ray') ||
               request.headers.get('X-Request-ID') ||
               getCrypto().randomUUID(),
    timestamp: new Date().toISOString(),
    ip: request.headers.get('CF-Connecting-IP') ||
        request.headers.get('X-Forwarded-For') ||
        request.headers.get('X-Real-IP') ||
        'unknown',
    userAgent: request.headers.get('user-agent') || '',
    country: request.headers.get('CF-IPCountry') || undefined,
  };
}

/**
 * Create standardized error response
 */
function createErrorResponse(
  code: string,
  message: string,
  requestId?: string
): string {
  const errorResponse: ApiResponse = {
    success: false,
    error: {
      code,
      message,
    },
    timestamp: new Date().toISOString(),
    requestId,
  };

  return JSON.stringify(errorResponse);
}

/**
 * Utility function to get authenticated context from Hono context
 * Provides type safety when accessing auth context in route handlers
 */
export function getAuthContext(c: Context): AuthenticatedContext {
  const auth = c.get('auth') as AuthenticatedContext | undefined;

  if (!auth) {
    throw new HTTPException(401, {
      message: createErrorResponse('UNAUTHORIZED', 'Authentication context not found.')
    });
  }

  return auth;
}

/**
 * Utility function to create success response
 */
export function createSuccessResponse<T>(
  data: T,
  requestId?: string
): ApiResponse<T> {
  return {
    success: true,
    data,
    timestamp: new Date().toISOString(),
    requestId,
  };
}
