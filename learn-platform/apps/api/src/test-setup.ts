/// <reference types="jest" />

/**
 * Test setup for API tests
 * Configures Jest environment for Node.js testing
 */

// TypeScript interfaces for proper typing
interface MockRequestInit {
  method?: string;
  headers?: HeadersLike | [string, string][] | Record<string, string>;
  body?: unknown;
}

interface MockResponseInit {
  status?: number;
  headers?: Record<string, string>;
}

interface HeadersLike {
  forEach: (callback: (value: string, key: string) => void) => void;
}

interface GlobalWithMocks {
  fetch: jest.MockedFunction<(...args: unknown[]) => Promise<unknown>>;
  Request?: unknown;
  Response?: unknown;
  crypto?: unknown;
}

// Mock console methods to reduce noise in tests
beforeAll(() => {
  jest.spyOn(console, 'warn').mockImplementation(jest.fn());
  jest.spyOn(console, 'error').mockImplementation(jest.fn());
  jest.spyOn(console, 'log').mockImplementation(jest.fn());
});

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks();
});

// Global cleanup
afterAll(() => {
  jest.restoreAllMocks();
});

// Mock global fetch for API tests
(global as unknown as GlobalWithMocks).fetch = jest.fn();

// Mock Request for Node.js environments (compatible with Cloudflare Workers)
if (typeof (global as unknown as GlobalWithMocks).Request === 'undefined') {
  (global as unknown as GlobalWithMocks).Request = class MockRequest {
    url: string;
    method: string;
    headers: Map<string, string>;
    body?: unknown;

    constructor(url: string, init: MockRequestInit = {}) {
      this.url = url;
      this.method = init.method || 'GET';
      this.headers = new Map();
      this.body = init.body;

      if (init.headers) {
        if (typeof (init.headers as HeadersLike).forEach === 'function') {
          // Handle Headers-like object
          (init.headers as HeadersLike).forEach((value: string, key: string) => {
            this.headers.set(key.toLowerCase(), value);
          });
        } else if (Array.isArray(init.headers)) {
          // Handle array of tuples
          init.headers.forEach(([key, value]: [string, string]) => {
            this.headers.set(key.toLowerCase(), value);
          });
        } else {
          // Handle plain object
          Object.entries(init.headers as Record<string, string>).forEach(([key, value]) => {
            this.headers.set(key.toLowerCase(), value);
          });
        }
      }
    }
  };
}

// Mock Response for Node.js environments (compatible with Cloudflare Workers)
if (typeof (global as unknown as GlobalWithMocks).Response === 'undefined') {
  (global as unknown as GlobalWithMocks).Response = class MockResponse {
    body: unknown;
    status: number;
    ok: boolean;
    headers: Map<string, string>;

    constructor(body?: unknown, init: MockResponseInit = {}) {
      this.body = body;
      this.status = init.status || 200;
      this.ok = this.status >= 200 && this.status < 300;
      this.headers = new Map();

      if (init.headers) {
        Object.entries(init.headers).forEach(([key, value]) => {
          this.headers.set(key.toLowerCase(), value);
        });
      }
    }
  };
}

// Mock crypto for Node.js environment
if (typeof (global as unknown as GlobalWithMocks).crypto === 'undefined') {
  const { webcrypto } = require('crypto');
  (global as unknown as GlobalWithMocks).crypto = webcrypto;
}
