/**
 * TypeScript type definitions for API requests and responses
 *
 * This file contains all the type definitions used across the protected API routes,
 * ensuring type safety for both server-side implementations and client-side usage.
 */

import type { User as AuthUser, AuthSession } from '@learn-platform/auth';

/**
 * API-compatible User type that matches better-auth types
 * This ensures compatibility with the actual better-auth user structure
 */
export type User = AuthUser;

/**
 * Standard API response wrapper
 * All API responses should follow this structure for consistency
 */
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: ApiError;
  timestamp: string;
  requestId?: string;
}

/**
 * API error structure
 * Provides detailed error information for client-side handling
 */
export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
  stack?: string; // Only included in development
}

/**
 * Authentication error codes
 * Standardized error codes for authentication-related failures
 */
export enum AuthErrorCode {
  UNAUTHORIZED = 'UNAUTHORIZED',
  SESSION_EXPIRED = 'SESSION_EXPIRED',
  INVALID_TOKEN = 'INVALID_TOKEN',
  FORBIDDEN = 'FORBIDDEN',
  AUTHENTICATION_REQUIRED = 'AUTHENTICATION_REQUIRED',
}

/**
 * User profile data structure
 * Extended user information returned by profile endpoints
 */
export interface UserProfile {
  id: string;
  email: string;
  name: string;
  avatar?: string | null;
  emailVerified: boolean;
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
  preferences: UserPreferences;
  metadata: UserMetadata;
}

/**
 * User preferences structure
 * User-configurable settings and preferences
 */
export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  timezone: string;
  notifications: {
    email: boolean;
    push: boolean;
    marketing: boolean;
  };
  privacy: {
    profileVisibility: 'public' | 'private' | 'friends';
    showEmail: boolean;
    showLastSeen: boolean;
  };
}

/**
 * User metadata structure
 * System-generated user information
 */
export interface UserMetadata {
  totalLogins: number;
  lastLoginIp?: string;
  lastLoginCountry?: string;
  lastLoginUserAgent?: string;
  accountStatus: 'active' | 'suspended' | 'pending_verification';
  roles: string[];
  permissions: string[];
}

/**
 * Dashboard data structure
 * Aggregated data for user dashboard
 */
export interface DashboardData {
  user: UserProfile;
  stats: {
    coursesEnrolled: number;
    coursesCompleted: number;
    totalLearningTime: number; // in minutes
    currentStreak: number; // days
    achievements: Achievement[];
  };
  recentActivity: ActivityItem[];
  recommendations: CourseRecommendation[];
}

/**
 * Achievement structure
 * User achievements and badges
 */
export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  unlockedAt: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

/**
 * Activity item structure
 * Recent user activity entries
 */
export interface ActivityItem {
  id: string;
  type: 'course_started' | 'course_completed' | 'lesson_completed' | 'achievement_unlocked';
  title: string;
  description: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

/**
 * Course recommendation structure
 * Personalized course recommendations
 */
export interface CourseRecommendation {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedDuration: number; // in minutes
  rating: number;
  enrollmentCount: number;
  tags: string[];
  reason: string; // Why this course is recommended
}

/**
 * Request types for protected endpoints
 */

/**
 * Update user preferences request
 */
export interface UpdatePreferencesRequest {
  preferences: Partial<UserPreferences>;
}

/**
 * Update user profile request
 */
export interface UpdateProfileRequest {
  name?: string;
  avatar?: string;
}

/**
 * Response types for protected endpoints
 */

/**
 * User profile response
 */
export type UserProfileResponse = ApiResponse<UserProfile>;

/**
 * Dashboard data response
 */
export type DashboardResponse = ApiResponse<DashboardData>;

/**
 * Update preferences response
 */
export type UpdatePreferencesResponse = ApiResponse<UserPreferences>;

/**
 * Update profile response
 */
export type UpdateProfileResponse = ApiResponse<UserProfile>;

/**
 * Authentication context for middleware
 * Extended context with user information for protected routes
 */
export interface AuthenticatedContext {
  user: AuthUser;
  session: AuthSession;
  isAuthenticated: true;
  userId: string;
}

/**
 * Request context for protected routes
 * Combines Hono context with authentication information
 */
export interface ProtectedRouteContext {
  auth: AuthenticatedContext;
  requestId: string;
  timestamp: string;
  ip: string;
  userAgent: string;
  country?: string;
}
