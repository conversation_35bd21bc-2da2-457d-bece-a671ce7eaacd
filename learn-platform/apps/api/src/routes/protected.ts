/**
 * Protected API routes using direct Hono handlers
 *
 * This file demonstrates how to create protected API endpoints using Hono
 * directly (without tRPC) while maintaining proper authentication and
 * type safety.
 *
 * These routes complement the tRPC endpoints and show an alternative
 * approach for REST-style APIs that require authentication.
 */

import { Hono } from 'hono';
import { HTTPException } from 'hono/http-exception';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import {
  authMiddleware,
  getAuthContext,
  createSuccessResponse
} from '../middleware/auth';
import type {
  UserProfile,
  DashboardData,
  UserPreferences
} from '../types/api';

// Create a new Hono app for protected routes
const protectedRoutes = new Hono();

// Apply authentication middleware to all protected routes
protectedRoutes.use('*', authMiddleware);

/**
 * GET /api/protected/user-profile
 *
 * Retrieves the complete user profile for the authenticated user.
 *
 * Authentication: Required
 * Returns: UserProfile with preferences and metadata
 *
 * Example response:
 * {
 *   "success": true,
 *   "data": {
 *     "id": "user_123",
 *     "email": "<EMAIL>",
 *     "name": "<PERSON>",
 *     "avatar": "https://...",
 *     "emailVerified": true,
 *     "createdAt": "2024-01-01T00:00:00Z",
 *     "preferences": { ... },
 *     "metadata": { ... }
 *   }
 * }
 */
protectedRoutes.get('/user-profile', async (c) => {
  try {
    const auth = getAuthContext(c);
    const requestId = c.get('requestId' as never) as string;

    // In a real application, you would fetch additional user data from the database
    // For this example, we'll create a mock profile based on the authenticated user
    const userProfile: UserProfile = {
      id: auth.user.id,
      email: auth.user.email,
      name: auth.user.name,
      avatar: auth.user.avatar,
      emailVerified: auth.user.emailVerified,
      createdAt: auth.user.createdAt.toISOString(),
      updatedAt: auth.user.updatedAt.toISOString(),
      lastLoginAt: new Date().toISOString(), // Would come from session tracking
      preferences: {
        theme: 'system',
        language: 'en',
        timezone: 'UTC',
        notifications: {
          email: true,
          push: true,
          marketing: false,
        },
        privacy: {
          profileVisibility: 'public',
          showEmail: false,
          showLastSeen: true,
        },
      },
      metadata: {
        totalLogins: 42, // Would come from database
        lastLoginIp: (c.get('protectedContext' as never) as Record<string, unknown>)?.['ip'] as string,
        lastLoginCountry: (c.get('protectedContext' as never) as Record<string, unknown>)?.['country'] as string,
        lastLoginUserAgent: (c.get('protectedContext' as never) as Record<string, unknown>)?.['userAgent'] as string,
        accountStatus: 'active',
        roles: [], // TODO: Implement role system
        permissions: [], // TODO: Implement permission system
      },
    };

    return c.json(createSuccessResponse(userProfile, requestId as string));
  } catch (error) {
    console.error('Error fetching user profile:', error);
    throw new HTTPException(500, {
      message: JSON.stringify({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to fetch user profile',
        },
        timestamp: new Date().toISOString(),
      })
    });
  }
});

/**
 * GET /api/protected/dashboard
 *
 * Retrieves dashboard data for the authenticated user including
 * stats, recent activity, and personalized recommendations.
 *
 * Authentication: Required
 * Returns: DashboardData with user stats and activity
 */
protectedRoutes.get('/dashboard', async (c) => {
  try {
    const auth = getAuthContext(c);
    const requestId = c.get('requestId' as never) as string;

    // Mock dashboard data - in a real app, this would come from database queries
    const dashboardData: DashboardData = {
      user: {
        id: auth.user.id,
        email: auth.user.email,
        name: auth.user.name,
        avatar: auth.user.avatar,
        emailVerified: auth.user.emailVerified,
        createdAt: auth.user.createdAt.toISOString(),
        updatedAt: auth.user.updatedAt.toISOString(),
        preferences: {
          theme: 'system',
          language: 'en',
          timezone: 'UTC',
          notifications: { email: true, push: true, marketing: false },
          privacy: { profileVisibility: 'public', showEmail: false, showLastSeen: true },
        },
        metadata: {
          totalLogins: 42,
          accountStatus: 'active',
          roles: [],
          permissions: [],
        },
      },
      stats: {
        coursesEnrolled: 5,
        coursesCompleted: 2,
        totalLearningTime: 1440, // 24 hours in minutes
        currentStreak: 7, // 7 days
        achievements: [
          {
            id: 'first_course',
            title: 'First Steps',
            description: 'Completed your first course',
            icon: '🎯',
            unlockedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
            rarity: 'common',
          },
          {
            id: 'week_streak',
            title: 'Week Warrior',
            description: 'Maintained a 7-day learning streak',
            icon: '🔥',
            unlockedAt: new Date().toISOString(),
            rarity: 'rare',
          },
        ],
      },
      recentActivity: [
        {
          id: 'activity_1',
          type: 'achievement_unlocked',
          title: 'Achievement Unlocked: Week Warrior',
          description: 'You maintained a 7-day learning streak!',
          timestamp: new Date().toISOString(),
          metadata: { achievementId: 'week_streak' },
        },
        {
          id: 'activity_2',
          type: 'lesson_completed',
          title: 'Completed: Advanced TypeScript Patterns',
          description: 'Lesson 3 of TypeScript Mastery course',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          metadata: { courseId: 'typescript_mastery', lessonId: 'lesson_3' },
        },
      ],
      recommendations: [
        {
          id: 'course_react_advanced',
          title: 'Advanced React Patterns',
          description: 'Master advanced React concepts and patterns',
          thumbnail: 'https://example.com/react-course.jpg',
          difficulty: 'advanced',
          estimatedDuration: 480, // 8 hours
          rating: 4.8,
          enrollmentCount: 1250,
          tags: ['React', 'JavaScript', 'Frontend'],
          reason: 'Based on your TypeScript progress',
        },
      ],
    };

    return c.json(createSuccessResponse(dashboardData, requestId as string));
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    throw new HTTPException(500, {
      message: JSON.stringify({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to fetch dashboard data',
        },
        timestamp: new Date().toISOString(),
      })
    });
  }
});

/**
 * PUT /api/protected/preferences
 *
 * Updates user preferences for the authenticated user.
 *
 * Authentication: Required
 * Body: Partial<UserPreferences>
 * Returns: Updated UserPreferences
 */
const updatePreferencesSchema = z.object({
  theme: z.enum(['light', 'dark', 'system']).optional(),
  language: z.string().optional(),
  timezone: z.string().optional(),
  notifications: z.object({
    email: z.boolean().optional(),
    push: z.boolean().optional(),
    marketing: z.boolean().optional(),
  }).optional(),
  privacy: z.object({
    profileVisibility: z.enum(['public', 'private', 'friends']).optional(),
    showEmail: z.boolean().optional(),
    showLastSeen: z.boolean().optional(),
  }).optional(),
});

protectedRoutes.put(
  '/preferences',
  zValidator('json', updatePreferencesSchema),
  async (c) => {
    try {
      const auth = getAuthContext(c);
      const requestId = c.get('requestId' as never) as string;
      const updates = c.req.valid('json');

      // In a real application, you would update the preferences in the database
      // For this example, we'll return the updated preferences
      const updatedPreferences: UserPreferences = {
        theme: updates.theme || 'system',
        language: updates.language || 'en',
        timezone: updates.timezone || 'UTC',
        notifications: {
          email: updates.notifications?.email ?? true,
          push: updates.notifications?.push ?? true,
          marketing: updates.notifications?.marketing ?? false,
        },
        privacy: {
          profileVisibility: updates.privacy?.profileVisibility || 'public',
          showEmail: updates.privacy?.showEmail ?? false,
          showLastSeen: updates.privacy?.showLastSeen ?? true,
        },
      };

      console.log(`Updated preferences for user ${auth.user.id}:`, updates);

      return c.json(createSuccessResponse(updatedPreferences, requestId as string));
    } catch (error) {
      console.error('Error updating preferences:', error);
      throw new HTTPException(500, {
        message: JSON.stringify({
          success: false,
          error: {
            code: 'INTERNAL_ERROR',
            message: 'Failed to update preferences',
          },
          timestamp: new Date().toISOString(),
        })
      });
    }
  }
);

/**
 * GET /api/protected/session-info
 *
 * Returns detailed information about the current session.
 * Useful for debugging and session management.
 *
 * Authentication: Required
 * Returns: Session and user information
 */
protectedRoutes.get('/session-info', async (c) => {
  try {
    const auth = getAuthContext(c);
    const requestId = c.get('requestId' as never) as string;
    const protectedContext = c.get('protectedContext' as never) as Record<string, unknown>;

    const sessionInfo = {
      session: {
        id: auth.session.session.id,
        userId: auth.session.session.userId,
        expiresAt: auth.session.session.expiresAt.toISOString(),
        createdAt: auth.session.session.createdAt.toISOString(),
        updatedAt: auth.session.session.updatedAt.toISOString(),
      },
      user: {
        id: auth.user.id,
        email: auth.user.email,
        name: auth.user.name,
        emailVerified: auth.user.emailVerified,
      },
      request: {
        ip: protectedContext?.['ip'],
        country: protectedContext?.['country'],
        userAgent: protectedContext?.['userAgent'],
        timestamp: protectedContext?.['timestamp'],
      },
    };

    return c.json(createSuccessResponse(sessionInfo, requestId as string));
  } catch (error) {
    console.error('Error fetching session info:', error);
    throw new HTTPException(500, {
      message: JSON.stringify({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to fetch session information',
        },
        timestamp: new Date().toISOString(),
      })
    });
  }
});

export { protectedRoutes };
