/**
 * Test file for learning content filter functionality
 */

import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import { workersLearningContentProcedures } from './learning-content';
import { createTestUser, createTestLearningContent, cleanupTestData } from '../test-utils';

describe('Learning Content Filters', () => {
  let testUserId: string;
  let testContentIds: string[] = [];

  beforeAll(async () => {
    // Create test user
    testUserId = await createTestUser();

    // Create test learning content with different properties for filtering
    const testContents = [
      {
        title: 'JavaScript Basics',
        description: 'Learn JavaScript fundamentals',
        learningLevel: 'beginner' as const,
        estimatedReadingTime: 15,
        tags: ['javascript', 'programming'],
        steps: [{
          id: 'step1',
          title: 'Introduction',
          icon: '📚',
          blocks: [{ id: 'block1', type: 'paragraph', data: { text: 'Hello' } }]
        }]
      },
      {
        title: 'Advanced React',
        description: 'Master React concepts',
        learningLevel: 'advanced' as const,
        estimatedReadingTime: 45,
        tags: ['react', 'frontend'],
        steps: [{
          id: 'step1',
          title: 'Hooks',
          icon: '⚛️',
          blocks: [{ id: 'block1', type: 'bulletList', data: { items: ['useState', 'useEffect'] } }]
        }]
      },
      {
        title: 'Python Data Science',
        description: 'Data analysis with Python',
        learningLevel: 'intermediate' as const,
        estimatedReadingTime: 30,
        tags: ['python', 'data-science'],
        steps: [{
          id: 'step1',
          title: 'Pandas',
          icon: '🐼',
          blocks: [{ id: 'block1', type: 'table', data: { headers: ['Name', 'Age'], rows: [['John', '25']] } }]
        }]
      }
    ];

    for (const content of testContents) {
      const contentId = await createTestLearningContent(testUserId, content);
      testContentIds.push(contentId);
    }
  });

  afterAll(async () => {
    await cleanupTestData(testUserId, testContentIds);
  });

  it('should filter by learning level', async () => {
    const mockCtx = {
      userId: testUserId,
      env: process.env
    };

    const result = await workersLearningContentProcedures.getMy({
      input: {
        limit: 20,
        offset: 0,
        learningLevel: 'beginner'
      },
      ctx: mockCtx
    });

    expect(result.success).toBe(true);
    expect(result.content).toHaveLength(1);
    expect(result.content[0].title).toBe('JavaScript Basics');
  });

  it('should filter by search term', async () => {
    const mockCtx = {
      userId: testUserId,
      env: process.env
    };

    const result = await workersLearningContentProcedures.getMy({
      input: {
        limit: 20,
        offset: 0,
        search: 'React'
      },
      ctx: mockCtx
    });

    expect(result.success).toBe(true);
    expect(result.content).toHaveLength(1);
    expect(result.content[0].title).toBe('Advanced React');
  });

  it('should filter by reading time range', async () => {
    const mockCtx = {
      userId: testUserId,
      env: process.env
    };

    const result = await workersLearningContentProcedures.getMy({
      input: {
        limit: 20,
        offset: 0,
        readingTimeRange: { min: 20, max: 40 }
      },
      ctx: mockCtx
    });

    expect(result.success).toBe(true);
    expect(result.content).toHaveLength(1);
    expect(result.content[0].title).toBe('Python Data Science');
  });

  it('should filter by tags', async () => {
    const mockCtx = {
      userId: testUserId,
      env: process.env
    };

    const result = await workersLearningContentProcedures.getMy({
      input: {
        limit: 20,
        offset: 0,
        tags: ['python']
      },
      ctx: mockCtx
    });

    expect(result.success).toBe(true);
    expect(result.content).toHaveLength(1);
    expect(result.content[0].title).toBe('Python Data Science');
  });

  it('should filter by content types', async () => {
    const mockCtx = {
      userId: testUserId,
      env: process.env
    };

    const result = await workersLearningContentProcedures.getMy({
      input: {
        limit: 20,
        offset: 0,
        contentTypes: ['table']
      },
      ctx: mockCtx
    });

    expect(result.success).toBe(true);
    expect(result.content).toHaveLength(1);
    expect(result.content[0].title).toBe('Python Data Science');
  });

  it('should combine multiple filters with AND logic', async () => {
    const mockCtx = {
      userId: testUserId,
      env: process.env
    };

    const result = await workersLearningContentProcedures.getMy({
      input: {
        limit: 20,
        offset: 0,
        learningLevel: 'advanced',
        tags: ['react'],
        contentTypes: ['bulletList']
      },
      ctx: mockCtx
    });

    expect(result.success).toBe(true);
    expect(result.content).toHaveLength(1);
    expect(result.content[0].title).toBe('Advanced React');
  });

  it('should return empty results when filters do not match', async () => {
    const mockCtx = {
      userId: testUserId,
      env: process.env
    };

    const result = await workersLearningContentProcedures.getMy({
      input: {
        limit: 20,
        offset: 0,
        learningLevel: 'beginner',
        tags: ['react'] // No beginner React content
      },
      ctx: mockCtx
    });

    expect(result.success).toBe(true);
    expect(result.content).toHaveLength(0);
  });
});
