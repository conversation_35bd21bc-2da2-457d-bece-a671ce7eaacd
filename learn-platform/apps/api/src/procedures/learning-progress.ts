/**
 * tRPC procedures for learning progress management (Cloudflare Workers version)
 *
 * This is a Worker-specific version of the learning progress procedures that uses
 * local database utilities to avoid monorepo import resolution issues.
 */

import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { createWorkersDatabaseFromEnv } from '../utils/database';
import { learningProgress, learningContent, eq, and, desc } from '../utils/schema';

// Input schemas
const updateProgressSchema = z.object({
  contentId: z.string(),
  currentStepIndex: z.number().min(0),
  timeSpent: z.number().min(0).optional(), // Time spent in this session (seconds)
  completedSteps: z.array(z.number()).optional(),
});

const addBookmarkSchema = z.object({
  contentId: z.string(),
  stepIndex: z.number().min(0),
  note: z.string().optional(),
});

const addNoteSchema = z.object({
  contentId: z.string(),
  stepIndex: z.number().min(0),
  content: z.string().min(1),
});

const removeBookmarkSchema = z.object({
  contentId: z.string(),
  stepIndex: z.number().min(0),
});

const updateNoteSchema = z.object({
  contentId: z.string(),
  stepIndex: z.number().min(0),
  noteIndex: z.number().min(0),
  content: z.string().min(1),
});

const deleteNoteSchema = z.object({
  contentId: z.string(),
  stepIndex: z.number().min(0),
  noteIndex: z.number().min(0),
});

/**
 * Note: Request deduplication has been removed to prevent cross-request I/O issues
 * in Cloudflare Workers. Database transactions provide sufficient consistency guarantees.
 *
 * The previous global deduplication mechanism shared promises across request contexts,
 * which violated Cloudflare Workers' request isolation model and caused
 * "Cannot perform I/O on behalf of a different request" errors.
 */

/**
 * Learning progress procedures for Cloudflare Workers
 */
export const workersLearningProgressProcedures = {
  /**
   * Get progress for a specific learning content
   * Works for both authenticated and anonymous users
   */
  getProgress: async ({ input, ctx }: { input: { contentId: string }, ctx: any }) => {
    try {
      // For anonymous users, return null progress (they can still use the learning content)
      if (!ctx.isAuthenticated || !ctx.userId) {
        return {
          success: true,
          progress: null,
          message: 'Progress tracking requires authentication. Sign in to save your progress.',
        };
      }

      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      const progress = await db
        .select()
        .from(learningProgress)
        .where(
          and(
            eq(learningProgress.contentId, input.contentId),
            eq(learningProgress.userId, ctx.userId)
          )
        )
        .limit(1);

      return {
        success: true,
        progress: progress[0] || null,
      };
    } catch (error) {
      console.error('Error fetching learning progress:', error);
      console.error('Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        name: error instanceof Error ? error.name : undefined,
        code: (error as any)?.code,
      });

      // Re-throw tRPC errors as-is (like UNAUTHORIZED)
      if (error instanceof TRPCError) {
        throw error;
      }

      // Add authentication-specific error handling
      if (error instanceof Error && (
        error.message.includes('authentication') ||
        error.message.includes('unauthorized') ||
        error.message.includes('logged in')
      )) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: 'Authentication required. Please log in to view progress.',
        });
      }

      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: `Failed to fetch learning progress: ${error instanceof Error ? error.message : 'Unknown error'}`,
      });
    }
  },

  /**
   * Update learning progress
   * Works for both authenticated and anonymous users
   */
  updateProgress: async ({ input, ctx }: { input: z.infer<typeof updateProgressSchema>, ctx: any }) => {
    // For anonymous users, return a success response without saving to database
    if (!ctx.isAuthenticated || !ctx.userId) {
      return {
        success: true,
        progress: {
          id: 'anonymous',
          contentId: input.contentId,
          userId: 'anonymous',
          currentStepIndex: input.currentStepIndex,
          completedSteps: input.completedSteps || [],
          totalTimeSpent: input.timeSpent || 0,
          completionPercentage: 0,
          isCompleted: false,
          bookmarks: [],
          notes: [],
          lastAccessedAt: new Date(),
          sessionCount: 1,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        message: 'Progress not saved. Sign in to save your learning progress.',
      };
    }

    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      // Use a transaction to ensure consistency and reduce connection time
      const result = await db.transaction(async (tx: any) => {
        // First, check if progress record exists
        const existingProgress = await tx
          .select()
          .from(learningProgress)
          .where(
            and(
              eq(learningProgress.contentId, input.contentId),
              eq(learningProgress.userId, ctx.userId!)
            )
          )
          .limit(1);

        // Get content info to calculate total steps
        const content = await tx
          .select()
          .from(learningContent)
          .where(eq(learningContent.id, input.contentId))
          .limit(1);

        if (!content.length) {
          throw new TRPCError({
            code: 'NOT_FOUND',
            message: 'Learning content not found',
          });
        }

        const totalSteps = (content[0].steps as any[]).length;
        const completedSteps = input.completedSteps || [];
        const completionPercentage = Math.round((completedSteps.length / totalSteps) * 100);
        const isCompleted = completedSteps.length >= totalSteps;
        const now = new Date();

        if (existingProgress.length > 0) {
          // Update existing progress
          const currentProgress = existingProgress[0];
          const newTotalTimeSpent = (currentProgress.totalTimeSpent || 0) + (input.timeSpent || 0);
          const newSessionCount = (currentProgress.sessionCount || 1) + 1;

          await tx
            .update(learningProgress)
            .set({
              currentStepIndex: input.currentStepIndex,
              completedSteps: completedSteps as any,
              totalTimeSpent: newTotalTimeSpent,
              completionPercentage,
              isCompleted,
              lastAccessedAt: now,
              sessionCount: newSessionCount,
              updatedAt: now,
            })
            .where(eq(learningProgress.id, currentProgress.id));

          return {
            success: true,
            progress: {
              ...currentProgress,
              currentStepIndex: input.currentStepIndex,
              completedSteps,
              totalTimeSpent: newTotalTimeSpent,
              completionPercentage,
              isCompleted,
              sessionCount: newSessionCount,
            },
          };
        } else {
          // Create new progress record
          const newProgress = {
            id: crypto.randomUUID(),
            contentId: input.contentId,
            userId: ctx.userId!,
            currentStepIndex: input.currentStepIndex,
            completedSteps: completedSteps as any,
            totalTimeSpent: input.timeSpent || 0,
            completionPercentage,
            isCompleted,
            bookmarks: [],
            notes: [],
            lastAccessedAt: now,
            sessionCount: 1,
            createdAt: now,
            updatedAt: now,
          };

          await tx.insert(learningProgress).values(newProgress);

          return {
            success: true,
            progress: newProgress,
          };
        }
      });

      return result;
    } catch (error) {
      console.error('Error updating learning progress:', error);
      console.error('Error details:', {
        message: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
        name: error instanceof Error ? error.name : undefined,
        code: (error as any)?.code,
        cause: (error as any)?.cause,
      });

      // Re-throw tRPC errors as-is (like UNAUTHORIZED)
      if (error instanceof TRPCError) {
        throw error;
      }

      // Add more specific error handling for connection issues
      if (error instanceof Error && error.message.includes('Max client connections reached')) {
        throw new TRPCError({
          code: 'TOO_MANY_REQUESTS',
          message: 'Database is temporarily busy. Please try again in a moment.',
        });
      }

      // Add authentication-specific error handling
      if (error instanceof Error && (
        error.message.includes('authentication') ||
        error.message.includes('unauthorized') ||
        error.message.includes('logged in')
      )) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: 'Authentication required. Please log in to update progress.',
        });
      }

      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: `Failed to update learning progress: ${error instanceof Error ? error.message : 'Unknown error'}`,
      });
    }
  },

  /**
   * Add bookmark to learning content
   */
  addBookmark: async ({ input, ctx }: { input: z.infer<typeof addBookmarkSchema>, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      // Use a transaction to ensure consistency
      const result = await db.transaction(async (tx: any) => {
        // Get existing progress
        const existingProgress = await tx
          .select()
          .from(learningProgress)
          .where(
            and(
              eq(learningProgress.contentId, input.contentId),
              eq(learningProgress.userId, ctx.userId!)
            )
          )
          .limit(1);

        let progress;

        if (!existingProgress.length) {
          // Create new progress record if it doesn't exist
          const content = await tx
            .select()
            .from(learningContent)
            .where(eq(learningContent.id, input.contentId))
            .limit(1);

          if (!content.length) {
            throw new TRPCError({
              code: 'NOT_FOUND',
              message: 'Learning content not found',
            });
          }

          const now = new Date();
          const newProgress = {
            id: crypto.randomUUID(),
            contentId: input.contentId,
            userId: ctx.userId!,
            currentStepIndex: 0,
            completedSteps: [],
            totalTimeSpent: 0,
            completionPercentage: 0,
            isCompleted: false,
            bookmarks: [],
            notes: [],
            lastAccessedAt: now,
            sessionCount: 1,
            createdAt: now,
            updatedAt: now,
          };

          await tx.insert(learningProgress).values(newProgress);
          progress = newProgress;
        } else {
          progress = existingProgress[0];
        }

        const currentBookmarks = progress.bookmarks as any[] || [];

        // Check if bookmark already exists for this step
        const existingBookmarkIndex = currentBookmarks.findIndex(
          (bookmark: any) => bookmark.stepIndex === input.stepIndex
        );

        let updatedBookmarks;
        if (existingBookmarkIndex >= 0) {
          // Update existing bookmark
          updatedBookmarks = [...currentBookmarks];
          updatedBookmarks[existingBookmarkIndex] = {
            stepIndex: input.stepIndex,
            note: input.note,
            createdAt: new Date().toISOString(),
          };
        } else {
          // Add new bookmark
          updatedBookmarks = [
            ...currentBookmarks,
            {
              stepIndex: input.stepIndex,
              note: input.note,
              createdAt: new Date().toISOString(),
            },
          ];
        }

        await tx
          .update(learningProgress)
          .set({
            bookmarks: updatedBookmarks as any,
            updatedAt: new Date(),
          })
          .where(eq(learningProgress.id, progress.id));

        return {
          success: true,
          bookmarks: updatedBookmarks,
        };
      });

      return result;
    } catch (error) {
      console.error('Error adding bookmark:', error);

      // Re-throw tRPC errors as-is
      if (error instanceof TRPCError) {
        throw error;
      }

      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to add bookmark',
      });
    }
  },

  /**
   * Add note to learning content
   */
  addNote: async ({ input, ctx }: { input: z.infer<typeof addNoteSchema>, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      // Use a transaction to ensure consistency
      const result = await db.transaction(async (tx: any) => {
        // Get existing progress
        const existingProgress = await tx
          .select()
          .from(learningProgress)
          .where(
            and(
              eq(learningProgress.contentId, input.contentId),
              eq(learningProgress.userId, ctx.userId!)
            )
          )
          .limit(1);

        let progress;

        if (!existingProgress.length) {
          // Create new progress record if it doesn't exist
          const content = await tx
            .select()
            .from(learningContent)
            .where(eq(learningContent.id, input.contentId))
            .limit(1);

          if (!content.length) {
            throw new TRPCError({
              code: 'NOT_FOUND',
              message: 'Learning content not found',
            });
          }

          const now = new Date();
          const newProgress = {
            id: crypto.randomUUID(),
            contentId: input.contentId,
            userId: ctx.userId!,
            currentStepIndex: 0,
            completedSteps: [],
            totalTimeSpent: 0,
            completionPercentage: 0,
            isCompleted: false,
            bookmarks: [],
            notes: [],
            lastAccessedAt: now,
            sessionCount: 1,
            createdAt: now,
            updatedAt: now,
          };

          await tx.insert(learningProgress).values(newProgress);
          progress = newProgress;
        } else {
          progress = existingProgress[0];
        }

        const currentNotes = progress.notes as any[] || [];

        // Add new note
        const updatedNotes = [
          ...currentNotes,
          {
            stepIndex: input.stepIndex,
            content: input.content,
            createdAt: new Date().toISOString(),
          },
        ];

        await tx
          .update(learningProgress)
          .set({
            notes: updatedNotes as any,
            updatedAt: new Date(),
          })
          .where(eq(learningProgress.id, progress.id));

        return {
          success: true,
          notes: updatedNotes,
        };
      });

      return result;
    } catch (error) {
      console.error('Error adding note:', error);

      // Re-throw tRPC errors as-is
      if (error instanceof TRPCError) {
        throw error;
      }

      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to add note',
      });
    }
  },

  /**
   * Remove bookmark from learning content
   */
  removeBookmark: async ({ input, ctx }: { input: z.infer<typeof removeBookmarkSchema>, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      // Get existing progress
      const existingProgress = await db
        .select()
        .from(learningProgress)
        .where(
          and(
            eq(learningProgress.contentId, input.contentId),
            eq(learningProgress.userId, ctx.userId!)
          )
        )
        .limit(1);

      if (!existingProgress.length) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Learning progress not found',
        });
      }

      const progress = existingProgress[0];
      const currentBookmarks = progress.bookmarks as any[] || [];

      // Remove bookmark for this step
      const updatedBookmarks = currentBookmarks.filter(
        (bookmark: any) => bookmark.stepIndex !== input.stepIndex
      );

      await db
        .update(learningProgress)
        .set({
          bookmarks: updatedBookmarks as any,
          updatedAt: new Date(),
        })
        .where(eq(learningProgress.id, progress.id));

      return {
        success: true,
        bookmarks: updatedBookmarks,
      };
    } catch (error) {
      console.error('Error removing bookmark:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to remove bookmark',
      });
    }
  },

  /**
   * Update an existing note
   */
  updateNote: async ({ input, ctx }: { input: z.infer<typeof updateNoteSchema>, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      // Get existing progress
      const existingProgress = await db
        .select()
        .from(learningProgress)
        .where(
          and(
            eq(learningProgress.contentId, input.contentId),
            eq(learningProgress.userId, ctx.userId!)
          )
        )
        .limit(1);

      if (!existingProgress.length) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Learning progress not found',
        });
      }

      const progress = existingProgress[0];
      const currentNotes = progress.notes as any[] || [];

      // Find notes for this step
      const stepNotes = currentNotes.filter((note: any) => note.stepIndex === input.stepIndex);

      if (input.noteIndex >= stepNotes.length) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Note not found',
        });
      }

      // Update the specific note
      const updatedNotes = currentNotes.map((note: any) => {
        if (note.stepIndex === input.stepIndex) {
          const stepNoteIndex = stepNotes.findIndex((sn: any) => sn === note);
          if (stepNoteIndex === input.noteIndex) {
            return {
              ...note,
              content: input.content,
              updatedAt: new Date().toISOString(),
            };
          }
        }
        return note;
      });

      await db
        .update(learningProgress)
        .set({
          notes: updatedNotes as any,
          updatedAt: new Date(),
        })
        .where(eq(learningProgress.id, progress.id));

      return {
        success: true,
        notes: updatedNotes,
      };
    } catch (error) {
      console.error('Error updating note:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to update note',
      });
    }
  },

  /**
   * Delete a note
   */
  deleteNote: async ({ input, ctx }: { input: z.infer<typeof deleteNoteSchema>, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      // Get existing progress
      const existingProgress = await db
        .select()
        .from(learningProgress)
        .where(
          and(
            eq(learningProgress.contentId, input.contentId),
            eq(learningProgress.userId, ctx.userId!)
          )
        )
        .limit(1);

      if (!existingProgress.length) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Learning progress not found',
        });
      }

      const progress = existingProgress[0];
      const currentNotes = progress.notes as any[] || [];

      // Find notes for this step
      const stepNotes = currentNotes.filter((note: any) => note.stepIndex === input.stepIndex);

      if (input.noteIndex >= stepNotes.length) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Note not found',
        });
      }

      // Remove the specific note
      const noteToRemove = stepNotes[input.noteIndex];
      const updatedNotes = currentNotes.filter((note: any) => note !== noteToRemove);

      await db
        .update(learningProgress)
        .set({
          notes: updatedNotes as any,
          updatedAt: new Date(),
        })
        .where(eq(learningProgress.id, progress.id));

      return {
        success: true,
        notes: updatedNotes,
      };
    } catch (error) {
      console.error('Error deleting note:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to delete note',
      });
    }
  },

  /**
   * Get user's overall learning statistics
   */
  getStats: async ({ ctx }: { ctx: any }) => {
    try {
      // For anonymous users, return empty stats
      if (!ctx.isAuthenticated || !ctx.userId) {
        return {
          success: true,
          stats: {
            totalContent: 0,
            completedContent: 0,
            inProgressContent: 0,
            totalTimeSpent: 0,
            averageCompletion: 0,
            recentActivity: [],
          },
          message: 'Sign in to view your learning statistics.',
        };
      }

      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      const allProgress = await db
        .select()
        .from(learningProgress)
        .where(eq(learningProgress.userId, ctx.userId))
        .orderBy(desc(learningProgress.updatedAt));

      const totalContent = allProgress.length;
      const completedContent = allProgress.filter((p: any) => p.isCompleted).length;
      const inProgressContent = allProgress.filter((p: any) => !p.isCompleted && (p.currentStepIndex ?? 0) > 0).length;
      const totalTimeSpent = allProgress.reduce((sum: number, p: any) => sum + (p.totalTimeSpent || 0), 0);
      const averageCompletion = totalContent > 0
        ? Math.round(allProgress.reduce((sum: number, p: any) => sum + (p.completionPercentage || 0), 0) / totalContent)
        : 0;

      return {
        success: true,
        stats: {
          totalContent,
          completedContent,
          inProgressContent,
          totalTimeSpent, // in seconds
          averageCompletion,
          recentActivity: allProgress.slice(0, 5), // Last 5 activities
        },
      };
    } catch (error) {
      console.error('Error fetching learning stats:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch learning statistics',
      });
    }
  },

  /**
   * Get the latest in-progress learning content for the user
   */
  getLatestInProgress: async ({ ctx }: { ctx: any }) => {
    try {
      // For anonymous users, return null
      if (!ctx.isAuthenticated || !ctx.userId) {
        return {
          success: true,
          activity: null,
        };
      }

      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      // Get the most recent in-progress learning activity
      const inProgressActivity = await db
        .select({
          id: learningProgress.id,
          contentId: learningProgress.contentId,
          currentStepIndex: learningProgress.currentStepIndex,
          completionPercentage: learningProgress.completionPercentage,
          isCompleted: learningProgress.isCompleted,
          updatedAt: learningProgress.updatedAt,
          totalTimeSpent: learningProgress.totalTimeSpent,
        })
        .from(learningProgress)
        .where(
          and(
            eq(learningProgress.userId, ctx.userId),
            eq(learningProgress.isCompleted, false)
          )
        )
        .orderBy(desc(learningProgress.updatedAt))
        .limit(5); // Get a few to filter client-side

      // Filter for activities where currentStepIndex > 0 (actually in progress)
      const filteredActivity = inProgressActivity.filter(activity =>
        (activity.currentStepIndex ?? 0) > 0
      );

      if (filteredActivity.length === 0) {
        return {
          success: true,
          activity: null,
        };
      }

      const activity = filteredActivity[0];

      // Get the associated learning content details
      const content = await db
        .select({
          id: learningContent.id,
          title: learningContent.title,
          description: learningContent.description,
          learningLevel: learningContent.learningLevel,
          estimatedReadingTime: learningContent.estimatedReadingTime,
        })
        .from(learningContent)
        .where(eq(learningContent.id, activity.contentId))
        .limit(1);

      if (content.length === 0) {
        return {
          success: true,
          activity: null,
        };
      }

      return {
        success: true,
        activity: {
          ...activity,
          content: content[0],
        },
      };
    } catch (error) {
      console.error('Error fetching latest in-progress learning:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch latest in-progress learning',
      });
    }
  },
};

// Export validation schemas for reuse
export {
  updateProgressSchema,
  addBookmarkSchema,
  addNoteSchema,
  removeBookmarkSchema,
  updateNoteSchema,
  deleteNoteSchema,
};
