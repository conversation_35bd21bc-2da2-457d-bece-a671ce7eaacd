/**
 * tRPC procedures for release management (Cloudflare Workers version)
 *
 * This is a Worker-specific version of the releases procedures that uses
 * local database utilities to avoid monorepo import resolution issues.
 */

import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { createWorkersDatabaseFromEnv } from '../utils/database';
import { releases, userReleaseNotifications, user, eq, and, desc, sql } from '../utils/schema';

// Utility functions (copied from @learn-platform/releases to avoid import issues)
function generateReleaseId(): string {
  return `release_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

function generateNotificationId(): string {
  return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

function validateVersion(version: string): boolean {
  const semverRegex = /^v?\d+\.\d+\.\d+(-[a-zA-Z0-9.-]+)?$/;
  return semverRegex.test(version);
}

function normalizeVersion(version: string): string {
  return version.startsWith('v') ? version : `v${version}`;
}

// Validation schemas
const createReleaseSchema = z.object({
  version: z.string()
    .min(1, 'Version is required')
    .max(50, 'Version too long')
    .refine(validateVersion, 'Invalid version format. Use semantic versioning (e.g., v1.0.0)'),
  description: z.string()
    .min(1, 'Description is required')
    .max(5000, 'Description too long'),
  releaseDate: z.coerce.date(),
});

const updateReleaseSchema = z.object({
  id: z.string(),
  version: z.string()
    .min(1, 'Version is required')
    .max(50, 'Version too long')
    .refine(validateVersion, 'Invalid version format')
    .optional(),
  description: z.string()
    .min(1, 'Description is required')
    .max(5000, 'Description too long')
    .optional(),
  releaseDate: z.coerce.date().optional(),
});

const publishReleaseSchema = z.object({
  id: z.string(),
  isPublished: z.boolean(),
});

const markAsReadSchema = z.object({
  releaseId: z.string(),
});

/**
 * Releases procedures for Cloudflare Workers
 */
export const workersReleasesProcedures = {
  /**
   * Get all releases (admin only) - includes drafts and published
   */
  getAllAdmin: async ({ ctx }: { ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      const allReleases = await db
        .select({
          id: releases.id,
          version: releases.version,
          description: releases.description,
          isPublished: releases.isPublished,
          publishedAt: releases.publishedAt,
          releaseDate: releases.releaseDate,
          createdBy: releases.createdBy,
          createdAt: releases.createdAt,
          updatedAt: releases.updatedAt,
        })
        .from(releases)
        .orderBy(desc(releases.createdAt));

      return {
        success: true,
        releases: allReleases,
      };
    } catch (error) {
      console.error('Error fetching all releases:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch releases',
      });
    }
  },

  /**
   * Get published releases for users
   */
  getPublished: async ({ ctx }: { ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      // Get published releases with user's read status
      const publishedReleases = await db
        .select({
          id: releases.id,
          version: releases.version,
          description: releases.description,
          publishedAt: releases.publishedAt,
          releaseDate: releases.releaseDate,
          isRead: userReleaseNotifications.isRead,
          readAt: userReleaseNotifications.readAt,
        })
        .from(releases)
        .leftJoin(
          userReleaseNotifications,
          and(
            eq(userReleaseNotifications.releaseId, releases.id),
            eq(userReleaseNotifications.userId, ctx.userId!)
          )
        )
        .where(eq(releases.isPublished, true))
        .orderBy(desc(releases.publishedAt));

      return {
        success: true,
        releases: publishedReleases.map((release: any) => ({
          ...release,
          isReadByUser: release.isRead || false,
        })),
      };
    } catch (error) {
      console.error('Error fetching published releases:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch releases',
      });
    }
  },

  /**
   * Get a specific release by ID
   */
  getById: async ({ input, ctx }: { input: { id: string }, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      const release = await db
        .select()
        .from(releases)
        .where(eq(releases.id, input.id))
        .limit(1);

      if (!release.length) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Release not found',
        });
      }

      return {
        success: true,
        release: release[0],
      };
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error('Error fetching release:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch release',
      });
    }
  },

  /**
   * Create a new release (admin only)
   */
  create: async ({ input, ctx }: { input: z.infer<typeof createReleaseSchema>, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      const releaseId = generateReleaseId();
      const normalizedVersion = normalizeVersion(input.version);
      const now = new Date();

      // Check if version already exists
      const existingRelease = await db
        .select()
        .from(releases)
        .where(eq(releases.version, normalizedVersion))
        .limit(1);

      if (existingRelease.length) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: 'A release with this version already exists',
        });
      }

      const newRelease = {
        id: releaseId,
        version: normalizedVersion,
        description: input.description,
        releaseDate: input.releaseDate,
        isPublished: false,
        publishedAt: null,
        createdBy: ctx.userId!,
        createdAt: now,
        updatedAt: now,
      };

      await db.insert(releases).values(newRelease);

      return {
        success: true,
        release: newRelease,
        message: 'Release created successfully',
      };
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error('Error creating release:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to create release',
      });
    }
  },

  /**
   * Update an existing release (admin only)
   */
  update: async ({ input, ctx }: { input: z.infer<typeof updateReleaseSchema>, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      // First verify the release exists
      const existingRelease = await db
        .select()
        .from(releases)
        .where(eq(releases.id, input.id))
        .limit(1);

      if (!existingRelease.length) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Release not found',
        });
      }

      // Check version uniqueness if version is being updated
      if (input.version) {
        const normalizedVersion = normalizeVersion(input.version);
        const versionExists = await db
          .select()
          .from(releases)
          .where(
            and(
              eq(releases.version, normalizedVersion),
              sql`${releases.id} != ${input.id}`
            )
          )
          .limit(1);

        if (versionExists.length) {
          throw new TRPCError({
            code: 'CONFLICT',
            message: 'A release with this version already exists',
          });
        }
      }

      // Prepare update data
      const updateData: any = {
        updatedAt: new Date(),
      };

      if (input.version !== undefined) updateData.version = normalizeVersion(input.version);
      if (input.description !== undefined) updateData.description = input.description;
      if (input.releaseDate !== undefined) updateData.releaseDate = input.releaseDate;

      // Update the release
      await db
        .update(releases)
        .set(updateData)
        .where(eq(releases.id, input.id));

      return {
        success: true,
        message: 'Release updated successfully',
      };
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error('Error updating release:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to update release',
      });
    }
  },

  /**
   * Publish or unpublish a release (admin only)
   */
  publish: async ({ input, ctx }: { input: z.infer<typeof publishReleaseSchema>, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      // First verify the release exists
      const existingRelease = await db
        .select()
        .from(releases)
        .where(eq(releases.id, input.id))
        .limit(1);

      if (!existingRelease.length) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Release not found',
        });
      }

      const now = new Date();
      const updateData = {
        isPublished: input.isPublished,
        publishedAt: input.isPublished ? now : null,
        updatedAt: now,
      };

      // Update the release
      await db
        .update(releases)
        .set(updateData)
        .where(eq(releases.id, input.id));

      return {
        success: true,
        message: input.isPublished ? 'Release published successfully' : 'Release unpublished successfully',
      };
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error('Error publishing/unpublishing release:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to update release publication status',
      });
    }
  },

  /**
   * Delete a release (admin only)
   */
  delete: async ({ input, ctx }: { input: { id: string }, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      // First verify the release exists
      const existingRelease = await db
        .select()
        .from(releases)
        .where(eq(releases.id, input.id))
        .limit(1);

      if (!existingRelease.length) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Release not found',
        });
      }

      // Delete the release (cascade will handle user notifications)
      await db
        .delete(releases)
        .where(eq(releases.id, input.id));

      return {
        success: true,
        message: 'Release deleted successfully',
      };
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error('Error deleting release:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to delete release',
      });
    }
  },

  /**
   * Mark a release as read for the current user
   */
  markAsRead: async ({ input, ctx }: { input: z.infer<typeof markAsReadSchema>, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      // Check if the release exists and is published
      const release = await db
        .select()
        .from(releases)
        .where(
          and(
            eq(releases.id, input.releaseId),
            eq(releases.isPublished, true)
          )
        )
        .limit(1);

      if (!release.length) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Published release not found',
        });
      }

      // Check if notification already exists
      const existingNotification = await db
        .select()
        .from(userReleaseNotifications)
        .where(
          and(
            eq(userReleaseNotifications.userId, ctx.userId!),
            eq(userReleaseNotifications.releaseId, input.releaseId)
          )
        )
        .limit(1);

      const now = new Date();

      if (existingNotification.length) {
        // Update existing notification
        await db
          .update(userReleaseNotifications)
          .set({
            isRead: true,
            readAt: now,
            updatedAt: now,
          })
          .where(eq(userReleaseNotifications.id, existingNotification[0].id));
      } else {
        // Create new notification record
        const notificationId = generateNotificationId();
        await db.insert(userReleaseNotifications).values({
          id: notificationId,
          userId: ctx.userId!,
          releaseId: input.releaseId,
          isRead: true,
          readAt: now,
          createdAt: now,
          updatedAt: now,
        });
      }

      return {
        success: true,
        message: 'Release marked as read',
      };
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error('Error marking release as read:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to mark release as read',
      });
    }
  },

  /**
   * Get release statistics (admin only)
   */
  getStats: async ({ ctx }: { ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      // Get release counts
      const [releaseStats] = await db
        .select({
          totalReleases: sql<number>`count(*)`,
          publishedReleases: sql<number>`count(*) filter (where ${releases.isPublished} = true)`,
          draftReleases: sql<number>`count(*) filter (where ${releases.isPublished} = false)`,
        })
        .from(releases);

      // Get notification counts
      const [notificationStats] = await db
        .select({
          totalNotifications: sql<number>`count(*)`,
          readNotifications: sql<number>`count(*) filter (where ${userReleaseNotifications.isRead} = true)`,
          unreadNotifications: sql<number>`count(*) filter (where ${userReleaseNotifications.isRead} = false)`,
        })
        .from(userReleaseNotifications);

      return {
        success: true,
        stats: {
          totalReleases: releaseStats.totalReleases || 0,
          publishedReleases: releaseStats.publishedReleases || 0,
          draftReleases: releaseStats.draftReleases || 0,
          totalNotifications: notificationStats.totalNotifications || 0,
          readNotifications: notificationStats.readNotifications || 0,
          unreadNotifications: notificationStats.unreadNotifications || 0,
        },
      };
    } catch (error) {
      console.error('Error fetching release statistics:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch release statistics',
      });
    }
  },
};

// Export validation schemas for reuse
export { createReleaseSchema, updateReleaseSchema, publishReleaseSchema, markAsReadSchema };
