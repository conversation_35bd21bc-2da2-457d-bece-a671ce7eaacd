/**
 * tRPC procedures for explainer template management (Cloudflare Workers version)
 *
 * This is a Worker-specific version of the templates procedures that uses
 * local database utilities to avoid monorepo import resolution issues.
 */

import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { createWorkersDatabaseFromEnv } from '../utils/database';
import { explainerTemplates, eq, and } from '../utils/schema';

// Validation schemas
const stepSchema = z.object({
  id: z.string(),
  title: z.string(),
  icon: z.string(),
  blocks: z.array(z.object({
    id: z.string(),
    type: z.string(),
    data: z.any(),
    isEditing: z.boolean().optional(),
  })),
});

const createTemplateSchema = z.object({
  name: z.string().min(1, 'Template name is required').max(200, 'Template name too long'),
  description: z.string().max(500, 'Description too long').optional(),
  steps: z.array(stepSchema).min(1, 'At least one step is required'),
});

const updateTemplateSchema = z.object({
  id: z.string(),
  name: z.string().min(1, 'Template name is required').max(200, 'Template name too long').optional(),
  description: z.string().max(500, 'Description too long').optional(),
  steps: z.array(stepSchema).optional(),
});

/**
 * Templates procedures for Cloudflare Workers
 */
export const workersTemplatesProcedures = {
  /**
   * Get all templates for the authenticated user
   */
  getAll: async ({ ctx }: { ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      const templates = await db
        .select({
          id: explainerTemplates.id,
          name: explainerTemplates.name,
          description: explainerTemplates.description,
          createdAt: explainerTemplates.createdAt,
          updatedAt: explainerTemplates.updatedAt,
          stepCount: explainerTemplates.steps, // We'll process this to get count
        })
        .from(explainerTemplates)
        .where(eq(explainerTemplates.userId, ctx.userId!))
        .orderBy(explainerTemplates.updatedAt);

      // Process templates to add step count
      const processedTemplates = templates.map((template: any) => ({
        ...template,
        stepCount: Array.isArray(template.stepCount) ? template.stepCount.length : 0,
        steps: undefined, // Remove steps from listing for performance
      }));

      return {
        success: true,
        templates: processedTemplates,
      };
    } catch (error) {
      console.error('Error fetching templates:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch templates',
      });
    }
  },

  /**
   * Get a specific template by ID
   */
  getById: async ({ input, ctx }: { input: { id: string }, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      const template = await db
        .select()
        .from(explainerTemplates)
        .where(
          and(
            eq(explainerTemplates.id, input.id),
            eq(explainerTemplates.userId, ctx.userId!)
          )
        )
        .limit(1);

      if (!template.length) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Template not found',
        });
      }

      return {
        success: true,
        template: template[0],
      };
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error('Error fetching template:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch template',
      });
    }
  },

  /**
   * Create a new template
   */
  create: async ({ input, ctx }: { input: z.infer<typeof createTemplateSchema>, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      const templateId = crypto.randomUUID();
      const now = new Date();

      const newTemplate = {
        id: templateId,
        name: input.name,
        description: input.description || '',
        steps: input.steps,
        userId: ctx.userId!,
        createdAt: now,
        updatedAt: now,
      };

      await db.insert(explainerTemplates).values(newTemplate);

      return {
        success: true,
        template: newTemplate,
        message: 'Template created successfully',
      };
    } catch (error) {
      console.error('Error creating template:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to create template',
      });
    }
  },

  /**
   * Update an existing template
   */
  update: async ({ input, ctx }: { input: z.infer<typeof updateTemplateSchema>, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      // First verify the template exists and belongs to the user
      const existingTemplate = await db
        .select()
        .from(explainerTemplates)
        .where(
          and(
            eq(explainerTemplates.id, input.id),
            eq(explainerTemplates.userId, ctx.userId!)
          )
        )
        .limit(1);

      if (!existingTemplate.length) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Template not found',
        });
      }

      // Prepare update data
      const updateData: any = {
        updatedAt: new Date(),
      };

      if (input.name !== undefined) updateData.name = input.name;
      if (input.description !== undefined) updateData.description = input.description;
      if (input.steps !== undefined) updateData.steps = input.steps;

      // Update the template
      await db
        .update(explainerTemplates)
        .set(updateData)
        .where(eq(explainerTemplates.id, input.id));

      return {
        success: true,
        message: 'Template updated successfully',
      };
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error('Error updating template:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to update template',
      });
    }
  },

  /**
   * Delete a template
   */
  delete: async ({ input, ctx }: { input: { id: string }, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      // First verify the template exists and belongs to the user
      const existingTemplate = await db
        .select()
        .from(explainerTemplates)
        .where(
          and(
            eq(explainerTemplates.id, input.id),
            eq(explainerTemplates.userId, ctx.userId!)
          )
        )
        .limit(1);

      if (!existingTemplate.length) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Template not found',
        });
      }

      // Delete the template
      await db
        .delete(explainerTemplates)
        .where(eq(explainerTemplates.id, input.id));

      return {
        success: true,
        message: 'Template deleted successfully',
      };
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error('Error deleting template:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to delete template',
      });
    }
  },
};

// Export validation schemas for reuse
export { createTemplateSchema, updateTemplateSchema, stepSchema };
