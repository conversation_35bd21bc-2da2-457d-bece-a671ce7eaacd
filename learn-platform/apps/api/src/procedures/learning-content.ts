/**
 * tRPC procedures for learning content management (Cloudflare Workers version)
 *
 * This is a Worker-specific version of the learning content procedures that uses
 * local database utilities to avoid monorepo import resolution issues.
 */

import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { createWorkersDatabaseFromEnv } from '../utils/database';
import { learningContent, learningProgress, eq, and, or, desc, gte, lte, ilike } from '../utils/schema';

// Validation schemas
const stepSchema = z.object({
  id: z.string(),
  title: z.string(),
  icon: z.string(),
  blocks: z.array(z.object({
    id: z.string(),
    type: z.string(),
    data: z.any(),
    isEditing: z.boolean().optional(),
  })),
});

const createLearningContentSchema = z.object({
  title: z.string().min(1).max(200),
  description: z.string().max(1000).optional(),
  steps: z.array(stepSchema).min(1).max(15),
  learningLevel: z.enum(['beginner', 'intermediate', 'advanced']),
  estimatedReadingTime: z.number().min(1).max(300),
  isPublic: z.boolean().default(false),
  tags: z.array(z.string()).default([]),
  aiMetadata: z.object({
    aiModel: z.string().optional(),
    generatedAt: z.string().optional(),
    contentTypes: z.array(z.string()).optional(),
    originalPrompt: z.string().optional(),
  }).optional(),
});

const updateLearningContentSchema = z.object({
  id: z.string(),
  title: z.string().min(1).max(200).optional(),
  description: z.string().max(1000).optional(),
  steps: z.array(stepSchema).optional(),
  learningLevel: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
  estimatedReadingTime: z.number().min(1).max(300).optional(),
  isPublic: z.boolean().optional(),
  tags: z.array(z.string()).optional(),
});

const getLearningContentSchema = z.object({
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0),
  learningLevel: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
  isPublic: z.boolean().optional(),
  tags: z.array(z.string()).optional(),
  search: z.string().optional(),
  dateRange: z
    .object({
      from: z.string().optional(),
      to: z.string().optional(),
    })
    .optional(),
  contentTypes: z.array(z.string()).optional(),
  readingTimeRange: z
    .object({
      min: z.number().optional(),
      max: z.number().optional(),
    })
    .optional(),
  completionStatus: z.enum(['completed', 'in-progress']).optional(),
});

// AI generation input schema
const aiGenerationInputSchema = z.object({
  topic: z.string().min(3).max(200),
  learningLevel: z.enum(['beginner', 'intermediate', 'advanced']),
  preferredContentTypes: z.array(z.string()).min(1),
  focusAreas: z.string().max(500).optional(),
});

// Transform AI-generated content to database format
function transformAIContentToDBFormat(aiContent: any): any[] {
  return aiContent.steps.map((step: any) => ({
    id: crypto.randomUUID(),
    title: step.title,
    icon: step.icon,
    blocks: [{
      id: crypto.randomUUID(),
      type: step.type,
      data: step.data,
      isEditing: false,
    }],
  }));
}

/**
 * Learning content procedures for Cloudflare Workers
 */
export const workersLearningContentProcedures = {
  /**
   * Get learning content with filtering and pagination
   * Public content visible to all, private content only to owner
   */
  getAll: async ({ input, ctx }: { input: z.infer<typeof getLearningContentSchema>, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      // Build where conditions
      const whereConditions = [];

      // If user is authenticated, show their private content + all public content
      // If not authenticated, only show public content
      if (ctx.isAuthenticated) {
        whereConditions.push(
          or(
            eq(learningContent.isPublic, true),
            eq(learningContent.userId, ctx.userId!)
          )
        );
      } else {
        whereConditions.push(eq(learningContent.isPublic, true));
      }

      // Add filters
      if (input.learningLevel) {
        whereConditions.push(eq(learningContent.learningLevel, input.learningLevel));
      }

      if (input.isPublic !== undefined) {
        whereConditions.push(eq(learningContent.isPublic, input.isPublic));
      }

      const content = await db
        .select({
          id: learningContent.id,
          title: learningContent.title,
          description: learningContent.description,
          learningLevel: learningContent.learningLevel,
          estimatedReadingTime: learningContent.estimatedReadingTime,
          isPublic: learningContent.isPublic,
          tags: learningContent.tags,
          userId: learningContent.userId,
          createdAt: learningContent.createdAt,
          updatedAt: learningContent.updatedAt,
        })
        .from(learningContent)
        .where(and(...whereConditions))
        .orderBy(desc(learningContent.updatedAt))
        .limit(input.limit)
        .offset(input.offset);

      return {
        success: true,
        content,
        pagination: {
          limit: input.limit,
          offset: input.offset,
          hasMore: content.length === input.limit,
        },
      };
    } catch (error) {
      console.error('Error fetching learning content:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch learning content',
      });
    }
  },

  /**
   * Get specific learning content by ID
   */
  getById: async ({ input, ctx }: { input: { id: string }, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      const content = await db
        .select()
        .from(learningContent)
        .where(eq(learningContent.id, input.id))
        .limit(1);

      if (!content.length) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Learning content not found',
        });
      }

      const contentItem = content[0];

      // Check if user can access this content
      if (!contentItem.isPublic && contentItem.userId !== ctx.userId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied to private content',
        });
      }

      return {
        success: true,
        content: contentItem,
      };
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }
      console.error('Error fetching learning content:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch learning content',
      });
    }
  },

  /**
   * Create new learning content
   */
  create: async ({ input, ctx }: { input: z.infer<typeof createLearningContentSchema>, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      const contentId = crypto.randomUUID();
      const now = new Date();

      const newContent = {
        id: contentId,
        title: input.title,
        description: input.description || '',
        steps: input.steps,
        learningLevel: input.learningLevel,
        estimatedReadingTime: input.estimatedReadingTime,
        isPublic: input.isPublic,
        tags: input.tags,
        aiMetadata: input.aiMetadata || null,
        userId: ctx.userId!,
        createdAt: now,
        updatedAt: now,
      };

      await db.insert(learningContent).values(newContent);

      return {
        success: true,
        content: newContent,
        message: 'Learning content created successfully',
      };
    } catch (error) {
      console.error('Error creating learning content:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to create learning content',
      });
    }
  },

  /**
   * Update existing learning content
   */
  update: async ({ input, ctx }: { input: z.infer<typeof updateLearningContentSchema>, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      // First verify the content exists and belongs to the user
      const existingContent = await db
        .select()
        .from(learningContent)
        .where(
          and(
            eq(learningContent.id, input.id),
            eq(learningContent.userId, ctx.userId!)
          )
        )
        .limit(1);

      if (!existingContent.length) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Learning content not found',
        });
      }

      // Prepare update data
      const updateData: any = {
        updatedAt: new Date(),
      };

      if (input.title !== undefined) updateData.title = input.title;
      if (input.description !== undefined) updateData.description = input.description;
      if (input.steps !== undefined) updateData.steps = input.steps;
      if (input.learningLevel !== undefined) updateData.learningLevel = input.learningLevel;
      if (input.estimatedReadingTime !== undefined) updateData.estimatedReadingTime = input.estimatedReadingTime;
      if (input.isPublic !== undefined) updateData.isPublic = input.isPublic;
      if (input.tags !== undefined) updateData.tags = input.tags;

      await db
        .update(learningContent)
        .set(updateData)
        .where(eq(learningContent.id, input.id));

      return {
        success: true,
        message: 'Learning content updated successfully',
      };
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }
      console.error('Error updating learning content:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to update learning content',
      });
    }
  },

  /**
   * Delete learning content
   */
  delete: async ({ input, ctx }: { input: { id: string }, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      // First verify the content exists and belongs to the user
      const existingContent = await db
        .select()
        .from(learningContent)
        .where(
          and(
            eq(learningContent.id, input.id),
            eq(learningContent.userId, ctx.userId!)
          )
        )
        .limit(1);

      if (!existingContent.length) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Learning content not found',
        });
      }

      await db
        .delete(learningContent)
        .where(eq(learningContent.id, input.id));

      return {
        success: true,
        message: 'Learning content deleted successfully',
      };
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }
      console.error('Error deleting learning content:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to delete learning content',
      });
    }
  },

  /**
   * Get user's own learning content with progress data
   */
  getMy: async ({ input, ctx }: { input: z.infer<typeof getLearningContentSchema>, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      const whereConditions = [eq(learningContent.userId, ctx.userId!)];

      // Learning level filter
      if (input.learningLevel) {
        whereConditions.push(eq(learningContent.learningLevel, input.learningLevel));
      }

      // Search filter (title and description)
      if (input.search) {
        const searchCondition = or(
          ilike(learningContent.title, `%${input.search}%`),
          ilike(learningContent.description, `%${input.search}%`)
        );
        if (searchCondition) {
          whereConditions.push(searchCondition);
        }
      }

      // Date range filter
      if (input.dateRange?.from) {
        whereConditions.push(
          gte(learningContent.createdAt, new Date(input.dateRange.from))
        );
      }
      if (input.dateRange?.to) {
        const toDate = new Date(input.dateRange.to);
        toDate.setHours(23, 59, 59, 999); // End of day
        whereConditions.push(lte(learningContent.createdAt, toDate));
      }

      // Reading time range filter
      if (input.readingTimeRange?.min !== undefined) {
        whereConditions.push(
          gte(learningContent.estimatedReadingTime, input.readingTimeRange.min)
        );
      }
      if (input.readingTimeRange?.max !== undefined) {
        whereConditions.push(
          lte(learningContent.estimatedReadingTime, input.readingTimeRange.max)
        );
      }

      // Visibility filter (isPublic)
      if (input.isPublic !== undefined) {
        whereConditions.push(eq(learningContent.isPublic, input.isPublic));
      }

      // Join learning content with progress data
      // Note: Using regular select() instead of selectDistinct() because JSON columns
      // don't have equality operators in PostgreSQL. The unique constraint on
      // learningProgress (contentId, userId) ensures no duplicates.
      const contentWithProgress = await db
        .select({
          // Learning content fields
          id: learningContent.id,
          title: learningContent.title,
          description: learningContent.description,
          steps: learningContent.steps,
          learningLevel: learningContent.learningLevel,
          estimatedReadingTime: learningContent.estimatedReadingTime,
          isPublic: learningContent.isPublic,
          tags: learningContent.tags,
          aiMetadata: learningContent.aiMetadata,
          userId: learningContent.userId,
          createdAt: learningContent.createdAt,
          updatedAt: learningContent.updatedAt,
          // Progress fields (nullable)
          progressId: learningProgress.id,
          currentStepIndex: learningProgress.currentStepIndex,
          completedSteps: learningProgress.completedSteps,
          totalTimeSpent: learningProgress.totalTimeSpent,
          completionPercentage: learningProgress.completionPercentage,
          isCompleted: learningProgress.isCompleted,
          lastAccessedAt: learningProgress.lastAccessedAt,
          // Additional progress fields that were missing
          bookmarks: learningProgress.bookmarks,
          notes: learningProgress.notes,
          sessionCount: learningProgress.sessionCount,
        })
        .from(learningContent)
        .leftJoin(
          learningProgress,
          and(
            eq(learningProgress.contentId, learningContent.id),
            eq(learningProgress.userId, ctx.userId!)
          )
        )
        .where(and(...whereConditions))
        .orderBy(desc(learningContent.updatedAt))
        .limit(input.limit)
        .offset(input.offset);

      // Transform the result to include progress as a nested object
      const content = contentWithProgress.map((item: any) => ({
        id: item.id,
        title: item.title,
        description: item.description,
        steps: item.steps,
        learningLevel: item.learningLevel,
        estimatedReadingTime: item.estimatedReadingTime,
        isPublic: item.isPublic,
        tags: item.tags,
        aiMetadata: item.aiMetadata,
        userId: item.userId,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
        // Include progress data if it exists
        progress: item.progressId ? {
          id: item.progressId,
          currentStepIndex: item.currentStepIndex || 0,
          completedSteps: item.completedSteps || [],
          totalTimeSpent: item.totalTimeSpent || 0,
          completionPercentage: item.completionPercentage || 0,
          isCompleted: item.isCompleted || false,
          lastAccessedAt: item.lastAccessedAt,
          bookmarks: item.bookmarks || [],
          notes: item.notes || [],
          sessionCount: item.sessionCount || 1,
        } : null,
      }));

      // Post-process for content type and tags filtering (since they're stored in JSON)
      let filteredContent = content;

      // Filter by content types
      if (input.contentTypes && input.contentTypes.length > 0) {
        filteredContent = filteredContent.filter((item: any) => {
          if (!item.steps || !Array.isArray(item.steps)) return false;

          // Check if any step contains the requested content types
          return item.steps.some((step: any) => {
            if (!step.blocks || !Array.isArray(step.blocks)) return false;
            return step.blocks.some((block: any) =>
              input.contentTypes!.includes(block.type)
            );
          });
        });
      }

      // Filter by tags
      if (input.tags && input.tags.length > 0) {
        filteredContent = filteredContent.filter((item: any) => {
          if (!item.tags || !Array.isArray(item.tags)) return false;
          // Check if content has at least one matching tag
          return input.tags!.some((tag) =>
            item.tags!.some((contentTag: string) =>
              contentTag.toLowerCase().includes(tag.toLowerCase())
            )
          );
        });
      }

      // Filter by completion status
      if (input.completionStatus) {
        filteredContent = filteredContent.filter((item: any) => {
          if (input.completionStatus === 'completed') {
            // Show only completed content
            return item.progress && item.progress.isCompleted === true;
          } else if (input.completionStatus === 'in-progress') {
            // Show content that has progress but is not completed, or has no progress at all
            return !item.progress || (item.progress && item.progress.isCompleted === false);
          }
          return true;
        });
      }

      return {
        success: true,
        content: filteredContent,
        pagination: {
          limit: input.limit,
          offset: input.offset,
          hasMore: content.length === input.limit,
        },
      };
    } catch (error) {
      console.error('Error fetching user learning content:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch user learning content',
      });
    }
  },

  /**
   * Duplicate learning content
   */
  duplicate: async ({ input, ctx }: { input: { id: string, newTitle?: string }, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      // Get the original content
      const originalContent = await db
        .select()
        .from(learningContent)
        .where(eq(learningContent.id, input.id))
        .limit(1);

      if (!originalContent.length) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Learning content not found',
        });
      }

      const original = originalContent[0];

      // Check access permissions
      if (!original.isPublic && original.userId !== ctx.userId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied to private content',
        });
      }

      // Create a duplicate
      const contentId = crypto.randomUUID();
      const now = new Date();

      const duplicateData = {
        id: contentId,
        title: input.newTitle || `${original.title} (Copy)`,
        description: original.description,
        steps: original.steps,
        learningLevel: original.learningLevel,
        estimatedReadingTime: original.estimatedReadingTime,
        isPublic: false, // Duplicates are private by default
        tags: original.tags,
        aiMetadata: original.aiMetadata,
        userId: ctx.userId!,
        createdAt: now,
        updatedAt: now,
      };

      await db.insert(learningContent).values(duplicateData);

      return {
        success: true,
        content: duplicateData,
        message: 'Learning content duplicated successfully',
      };
    } catch (error) {
      if (error instanceof TRPCError) {
        throw error;
      }
      console.error('Error duplicating learning content:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to duplicate learning content',
      });
    }
  },

  /**
   * Get learning content statistics for the authenticated user
   */
  getStats: async ({ ctx }: { ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      const userContent = await db
        .select()
        .from(learningContent)
        .where(eq(learningContent.userId, ctx.userId!));

      const stats = {
        totalContent: userContent.length,
        publicContent: userContent.filter((c: any) => c.isPublic).length,
        privateContent: userContent.filter((c: any) => !c.isPublic).length,
        contentByLevel: {
          beginner: userContent.filter((c: any) => c.learningLevel === 'beginner').length,
          intermediate: userContent.filter((c: any) => c.learningLevel === 'intermediate').length,
          advanced: userContent.filter((c: any) => c.learningLevel === 'advanced').length,
        },
      };

      return {
        success: true,
        stats,
      };
    } catch (error) {
      console.error('Error fetching learning content stats:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch learning content statistics',
      });
    }
  },

  /**
   * Generate AI-powered learning content and save to database
   */
  generateWithAI: async ({ input, ctx }: { input: z.infer<typeof aiGenerationInputSchema>, ctx: any }) => {
    try {
      // Validate that required environment variables are available
      if (!ctx.env?.OPENROUTER_API_KEY) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'OPENROUTER_API_KEY is not configured in the Workers environment',
        });
      }

      // Import AI generation service
      const { generateLearningContent } = await import('@learn-platform/ai');

      // Generate content using AI with Cloudflare Workers environment
      const aiGeneratedContent = await generateLearningContent({
        topic: input.topic,
        learningLevel: input.learningLevel,
        preferredContentTypes: input.preferredContentTypes as any,
        focusAreas: input.focusAreas,
      }, {
        env: ctx.env, // Pass Cloudflare Workers environment variables
      });

      // Transform AI content to database format
      const dbSteps = transformAIContentToDBFormat(aiGeneratedContent);

      // Save to database
      const { db } = createWorkersDatabaseFromEnv(ctx.env);
      const contentId = crypto.randomUUID();
      const now = new Date();

      const newContent = {
        id: contentId,
        title: aiGeneratedContent.title,
        description: aiGeneratedContent.description,
        steps: dbSteps,
        learningLevel: input.learningLevel,
        estimatedReadingTime: aiGeneratedContent.estimatedReadingTime,
        isPublic: false, // Default to private
        tags: [], // TODO: Extract tags from content
        aiMetadata: {
          aiModel: aiGeneratedContent.metadata.aiModel,
          generatedAt: aiGeneratedContent.metadata.generatedAt,
          contentTypes: aiGeneratedContent.metadata.contentTypes,
          originalPrompt: input.topic,
        },
        userId: ctx.userId!,
        createdAt: now,
        updatedAt: now,
      };

      await db.insert(learningContent).values(newContent);

      return {
        success: true,
        contentId,
        content: newContent,
        message: 'Learning content generated and saved successfully',
      };
    } catch (error) {
      console.error('Error generating AI content:', error);

      // Handle specific AI errors
      if (error instanceof Error) {
        if (error.message.includes('Configuration error')) {
          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: 'AI service is not properly configured. Please try again later.',
          });
        }
        if (error.message.includes('rate limit')) {
          throw new TRPCError({
            code: 'TOO_MANY_REQUESTS',
            message: 'AI service rate limit exceeded. Please try again in a few minutes.',
          });
        }
        if (error.message.includes('OPENROUTER_API_KEY')) {
          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: 'AI service configuration error: API key not found.',
          });
        }
      }

      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to generate learning content. Please try again.',
      });
    }
  },
};

// Export validation schemas for reuse
export {
  createLearningContentSchema,
  updateLearningContentSchema,
  getLearningContentSchema,
  stepSchema,
  aiGenerationInputSchema
};
