/**
 * tRPC procedures for quiz management (Cloudflare Workers version)
 *
 * This is a Worker-specific version of the quiz procedures that uses
 * local database utilities to avoid monorepo import resolution issues.
 */

import { z } from 'zod';
import { TRPCError } from '@trpc/server';
import { createWorkersDatabaseFromEnv } from '../utils/database';
import { quiz, quizAttempt, learningContent, eq, and, or, desc, ilike } from '../utils/schema';

// Validation schemas
const generateQuizInputSchema = z.object({
  learningContentId: z.string().min(1, 'Learning content ID is required'),
  quizTypes: z.array(z.enum(['flashcard', 'multipleChoice', 'trueFalse', 'fillInBlank', 'matching', 'freeText', 'ordering'])).min(1, 'At least one quiz type is required'),
  difficulty: z.enum(['easy', 'medium', 'hard']).default('medium'),
  questionsPerType: z.number().min(1).max(10).default(3),
  includeHints: z.boolean().default(true),
  includeExplanations: z.boolean().default(true),
});

const startQuizInputSchema = z.object({
  quizId: z.string().min(1, 'Quiz ID is required'),
});

const completeQuizInputSchema = z.object({
  attemptId: z.string().min(1, 'Attempt ID is required'),
});

const submitAnswerInputSchema = z.object({
  attemptId: z.string().min(1, 'Attempt ID is required'),
  questionId: z.string().min(1, 'Question ID is required'),
  answer: z.any(), // Union type based on question type
  timeSpent: z.number().min(0), // seconds
  isTemporary: z.boolean().default(false), // For draft answers
});

const getMyAttemptsInputSchema = z.object({
  quizId: z.string().optional(),
  limit: z.number().min(1).max(50).default(20),
  offset: z.number().min(0).default(0),
});

const getCurrentAttemptInputSchema = z.object({
  quizId: z.string().min(1, 'Quiz ID is required'),
});

const getQuizHistoryInputSchema = z.object({
  learningContentId: z.string().min(1, 'Learning content ID is required'),
  limit: z.number().min(1).max(50).default(20),
  offset: z.number().min(0).default(0),
});

const getQuizzesInputSchema = z.object({
  learningContentId: z.string().optional(),
  difficulty: z.enum(['easy', 'medium', 'hard']).optional(),
  search: z.string().optional(),
  limit: z.number().min(1).max(50).default(20),
  offset: z.number().min(0).default(0),
  includePublic: z.boolean().default(true),
});

/**
 * Quiz procedures for Cloudflare Workers
 */
export const workersQuizProcedures = {
  /**
   * Generate a new quiz from learning content
   */
  generate: async ({ input, ctx }: { input: z.infer<typeof generateQuizInputSchema>, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      // Verify learning content exists and user has access
      const content = await db
        .select()
        .from(learningContent)
        .where(
          and(
            eq(learningContent.id, input.learningContentId),
            or(
              eq(learningContent.isPublic, true),
              eq(learningContent.userId, ctx.userId!)
            )
          )
        )
        .limit(1);

      if (content.length === 0) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Learning content not found or access denied',
        });
      }

      // Import quiz generation service
      const { generateQuizFromContent } = await import('@learn-platform/ai');

      // Generate quiz using AI
      console.info('Starting quiz generation with AI (Cloudflare Workers)...');
      const generatedQuiz = await generateQuizFromContent(
        content[0],
        {
          learningContentId: input.learningContentId,
          quizTypes: input.quizTypes,
          difficulty: input.difficulty,
          questionsPerType: input.questionsPerType,
          includeHints: input.includeHints,
          includeExplanations: input.includeExplanations,
        },
        {
          validateContent: true,
          retryOnFailure: true,
          env: ctx.env, // Pass Cloudflare Workers environment variables
        }
      );

      console.info('Quiz generation completed successfully (Cloudflare Workers):', {
        questionsGenerated: generatedQuiz.questions.length,
        totalPoints: generatedQuiz.totalPoints,
        estimatedDuration: generatedQuiz.estimatedDuration
      });

      // Save quiz to database
      const quizId = crypto.randomUUID();
      const now = new Date();

      const newQuiz = {
        id: quizId,
        title: generatedQuiz.title,
        description: generatedQuiz.description,
        learningContentId: input.learningContentId,
        difficulty: input.difficulty,
        estimatedDuration: generatedQuiz.estimatedDuration,
        totalPoints: generatedQuiz.totalPoints,
        questions: generatedQuiz.questions,
        metadata: generatedQuiz.metadata,
        isPublic: false,
        allowRetakes: true,
        showCorrectAnswers: true,
        shuffleQuestions: false,
        timeLimit: null,
        createdBy: ctx.userId!,
        createdAt: now,
        updatedAt: now,
      };

      console.info('Attempting to save quiz to database (Cloudflare Workers)...', {
        quizId,
        questionsCount: newQuiz.questions.length,
        userId: ctx.userId
      });

      try {
        await db.insert(quiz).values(newQuiz);
        console.info('Quiz saved to database successfully (Cloudflare Workers):', { quizId });
      } catch (dbError) {
        console.error('Database insertion failed (Cloudflare Workers):', dbError);
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: `Failed to save quiz to database: ${dbError instanceof Error ? dbError.message : 'Unknown database error'}`,
        });
      }

      console.info('Returning quiz generation response (Cloudflare Workers)...', { quizId });
      return {
        success: true,
        quiz: newQuiz,
        message: 'Quiz generated successfully',
      };
    } catch (error) {
      console.error('Error generating quiz:', error);

      if (error instanceof TRPCError) throw error;

      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to generate quiz',
      });
    }
  },

  /**
   * Get all quizzes with filtering support
   */
  getAll: async ({ input, ctx }: { input: z.infer<typeof getQuizzesInputSchema>, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      // Build where conditions
      const whereConditions = [];

      if (input.learningContentId) {
        whereConditions.push(eq(quiz.learningContentId, input.learningContentId));
      }

      if (input.difficulty) {
        whereConditions.push(eq(quiz.difficulty, input.difficulty));
      }

      // Search filter (title and description)
      if (input.search) {
        const searchCondition = or(
          ilike(quiz.title, `%${input.search}%`),
          ilike(quiz.description, `%${input.search}%`)
        );
        if (searchCondition) {
          whereConditions.push(searchCondition);
        }
      }

      // Access control
      if (input.includePublic) {
        whereConditions.push(
          or(
            eq(quiz.isPublic, true),
            eq(quiz.createdBy, ctx.userId!)
          )
        );
      } else {
        whereConditions.push(eq(quiz.createdBy, ctx.userId!));
      }

      const quizzes = await db
        .select({
          id: quiz.id,
          title: quiz.title,
          description: quiz.description,
          difficulty: quiz.difficulty,
          estimatedDuration: quiz.estimatedDuration,
          totalPoints: quiz.totalPoints,
          isPublic: quiz.isPublic,
          createdAt: quiz.createdAt,
          questionCount: quiz.questions, // We'll process this to get count
        })
        .from(quiz)
        .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
        .orderBy(desc(quiz.createdAt))
        .limit(input.limit)
        .offset(input.offset);

      // Process quizzes to add question count
      const processedQuizzes = quizzes.map((quizItem: any) => ({
        ...quizItem,
        questionCount: Array.isArray(quizItem.questionCount) ? quizItem.questionCount.length : 0,
        questions: undefined, // Remove questions from listing for performance
      }));

      return {
        success: true,
        quizzes: processedQuizzes,
        hasMore: quizzes.length === input.limit,
      };
    } catch (error) {
      console.error('Error fetching quizzes:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch quizzes',
      });
    }
  },

  /**
   * Get a specific quiz by ID
   */
  getById: async ({ input, ctx }: { input: { id: string }, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      const quizData = await db
        .select()
        .from(quiz)
        .where(eq(quiz.id, input.id))
        .limit(1);

      if (!quizData.length) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Quiz not found',
        });
      }

      const quizItem = quizData[0];

      // Check access permissions
      if (!quizItem.isPublic && quizItem.createdBy !== ctx.userId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied to this quiz',
        });
      }

      return {
        success: true,
        quiz: quizItem,
      };
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error('Error fetching quiz:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch quiz',
      });
    }
  },

  /**
   * Start a quiz attempt
   */
  startAttempt: async ({ input, ctx }: { input: z.infer<typeof startQuizInputSchema>, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      // Get quiz details
      const quizData = await db
        .select()
        .from(quiz)
        .where(eq(quiz.id, input.quizId))
        .limit(1);

      if (quizData.length === 0) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Quiz not found',
        });
      }

      const quizItem = quizData[0];

      // Check access permissions
      if (!quizItem.isPublic && quizItem.createdBy !== ctx.userId) {
        throw new TRPCError({
          code: 'FORBIDDEN',
          message: 'Access denied to this quiz',
        });
      }

      // Create quiz attempt
      const attemptId = crypto.randomUUID();
      const now = new Date();

      const newAttempt = {
        id: attemptId,
        quizId: input.quizId,
        userId: ctx.userId!,
        startedAt: now,
        completedAt: null,
        isCompleted: false,
        answers: [],
        totalTimeSpent: 0,
        score: null,
        questionResults: null,
        metadata: {
          userAgent: ctx.userAgent,
          ipAddress: ctx.ip,
          questionOrder: quizItem.shuffleQuestions
            ? shuffleArray(quizItem.questions.map((_, i) => i))
            : quizItem.questions.map((_, i) => i),
        },
      };

      await db.insert(quizAttempt).values(newAttempt);

      return {
        success: true,
        attempt: newAttempt,
        quiz: quizItem,
        message: 'Quiz attempt started successfully',
      };
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error('Error starting quiz attempt:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to start quiz attempt',
      });
    }
  },

  /**
   * Submit an answer for a question
   */
  submitAnswer: async ({ input, ctx }: { input: z.infer<typeof submitAnswerInputSchema>, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      // Verify attempt belongs to user
      const attempt = await db
        .select()
        .from(quizAttempt)
        .where(
          and(
            eq(quizAttempt.id, input.attemptId),
            eq(quizAttempt.userId, ctx.userId!)
          )
        )
        .limit(1);

      if (attempt.length === 0) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Quiz attempt not found',
        });
      }

      if (attempt[0].isCompleted) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Cannot submit answer to completed quiz',
        });
      }

      // Update answers and progress
      const currentAnswers = attempt[0].answers || [];
      const existingAnswerIndex = currentAnswers.findIndex((a: any) => a.questionId === input.questionId);

      if (existingAnswerIndex >= 0) {
        // Update existing answer
        currentAnswers[existingAnswerIndex] = {
          questionId: input.questionId,
          questionType: currentAnswers[existingAnswerIndex].questionType,
          answer: input.answer,
          timeSpent: input.timeSpent,
        };
      } else {
        // Add new answer
        currentAnswers.push({
          questionId: input.questionId,
          questionType: 'unknown', // Will be determined during scoring
          answer: input.answer,
          timeSpent: input.timeSpent,
        });
      }

      await db
        .update(quizAttempt)
        .set({
          answers: currentAnswers,
          totalTimeSpent: (attempt[0].totalTimeSpent || 0) + input.timeSpent,
        })
        .where(eq(quizAttempt.id, input.attemptId));

      return {
        success: true,
        message: 'Answer submitted successfully',
      };
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error('Error submitting answer:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to submit answer',
      });
    }
  },

  /**
   * Complete quiz and calculate score
   */
  complete: async ({ input, ctx }: { input: z.infer<typeof completeQuizInputSchema>, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      // Get attempt and quiz data
      const attemptData = await db
        .select({
          attempt: quizAttempt,
          quiz: quiz,
        })
        .from(quizAttempt)
        .innerJoin(quiz, eq(quizAttempt.quizId, quiz.id))
        .where(
          and(
            eq(quizAttempt.id, input.attemptId),
            eq(quizAttempt.userId, ctx.userId!)
          )
        )
        .limit(1);

      if (attemptData.length === 0) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Quiz attempt not found',
        });
      }

      const { attempt, quiz: quizData } = attemptData[0];

      if (attempt.isCompleted) {
        throw new TRPCError({
          code: 'BAD_REQUEST',
          message: 'Quiz is already completed',
        });
      }

      // Calculate score using AI evaluation service (same as libs/trpc implementation)
      const { evaluateQuizAnswers } = await import('@learn-platform/ai');

      const evaluation = await evaluateQuizAnswers(
        quizData.questions,
        attempt.answers || [],
        {
          env: ctx.env // Pass Cloudflare Workers environment for API keys
        }
      );

      const score = evaluation.score;
      const questionResults = evaluation.questionResults;

      // Update attempt with completion data
      const now = new Date();
      await db
        .update(quizAttempt)
        .set({
          completedAt: now,
          isCompleted: true,
          score,
          questionResults,
        })
        .where(eq(quizAttempt.id, input.attemptId));

      return {
        success: true,
        score,
        questionResults,
        message: 'Quiz completed successfully',
      };
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error('Error completing quiz:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to complete quiz',
      });
    }
  },

  /**
   * Delete a quiz
   */
  delete: async ({ input, ctx }: { input: { id: string }, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      // First verify the quiz exists and belongs to the user
      const existingQuiz = await db
        .select()
        .from(quiz)
        .where(
          and(
            eq(quiz.id, input.id),
            eq(quiz.createdBy, ctx.userId!)
          )
        )
        .limit(1);

      if (!existingQuiz.length) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Quiz not found',
        });
      }

      // Delete the quiz (cascade will handle attempts)
      await db
        .delete(quiz)
        .where(eq(quiz.id, input.id));

      return {
        success: true,
        message: 'Quiz deleted successfully',
      };
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      console.error('Error deleting quiz:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to delete quiz',
      });
    }
  },

  /**
   * Get user's quiz attempts
   */
  getMyAttempts: async ({ input, ctx }: { input: { quizId?: string, limit?: number, offset?: number }, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      const whereConditions = [eq(quizAttempt.userId, ctx.userId!)];

      if (input.quizId) {
        whereConditions.push(eq(quizAttempt.quizId, input.quizId));
      }

      const attempts = await db
        .select({
          id: quizAttempt.id,
          quizId: quizAttempt.quizId,
          startedAt: quizAttempt.startedAt,
          completedAt: quizAttempt.completedAt,
          isCompleted: quizAttempt.isCompleted,
          score: quizAttempt.score,
          totalTimeSpent: quizAttempt.totalTimeSpent,
          answers: quizAttempt.answers,
          quizTitle: quiz.title,
          quizDifficulty: quiz.difficulty,
          quizQuestions: quiz.questions,
        })
        .from(quizAttempt)
        .innerJoin(quiz, eq(quizAttempt.quizId, quiz.id))
        .where(and(...whereConditions))
        .orderBy(desc(quizAttempt.startedAt))
        .limit(input.limit || 20)
        .offset(input.offset || 0);

      return {
        attempts,
        hasMore: attempts.length === (input.limit || 20),
      };
    } catch (error) {
      console.error('Failed to fetch attempts:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch attempts',
      });
    }
  },

  /**
   * Get current incomplete attempt for a quiz
   */
  getCurrentAttempt: async ({ input, ctx }: { input: z.infer<typeof getCurrentAttemptInputSchema>, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      // Get the most recent incomplete attempt for this quiz and user
      const attempt = await db
        .select()
        .from(quizAttempt)
        .where(
          and(
            eq(quizAttempt.quizId, input.quizId),
            eq(quizAttempt.userId, ctx.userId!),
            eq(quizAttempt.isCompleted, false)
          )
        )
        .orderBy(desc(quizAttempt.startedAt))
        .limit(1);

      if (attempt.length === 0) {
        return {
          success: true,
          attempt: null,
          progress: null,
        };
      }

      // Fetch quiz data to get the total questions count
      const quizData = await db
        .select()
        .from(quiz)
        .where(eq(quiz.id, input.quizId))
        .limit(1);

      if (quizData.length === 0) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'Quiz not found',
        });
      }

      const totalQuestions = quizData[0].questions?.length || 0;
      const questionsAnswered = attempt[0].answers?.length || 0;

      // Calculate current question index based on answers length
      // This assumes questions are answered sequentially
      const currentQuestionIndex = Math.min(questionsAnswered, totalQuestions - 1);

      const progress = {
        currentQuestionIndex,
        questionsAnswered,
        totalQuestions,
        timeSpentSoFar: attempt[0].totalTimeSpent || 0,
        lastActiveAt: new Date(),
        // Add missing properties that QuizContainer expects
        bookmarkedQuestions: [], // Empty array for now
        currentAnswers: attempt[0].answers || [], // Use existing answers
      };

      return {
        success: true,
        attempt: attempt[0],
        progress,
      };
    } catch (error) {
      console.error('Failed to fetch current attempt:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch current attempt',
      });
    }
  },

  /**
   * Get quiz history for a specific learning content
   */
  getHistoryByLearningContent: async ({ input, ctx }: { input: z.infer<typeof getQuizHistoryInputSchema>, ctx: any }) => {
    try {
      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      // Get quiz attempts with quiz details for the specific learning content
      const attempts = await db
        .select({
          id: quizAttempt.id,
          quizId: quizAttempt.quizId,
          quizTitle: quiz.title,
          startedAt: quizAttempt.startedAt,
          completedAt: quizAttempt.completedAt,
          isCompleted: quizAttempt.isCompleted,
          score: quizAttempt.score,
          totalTimeSpent: quizAttempt.totalTimeSpent,
          difficulty: quiz.difficulty,
        })
        .from(quizAttempt)
        .innerJoin(quiz, eq(quizAttempt.quizId, quiz.id))
        .where(
          and(
            eq(quiz.learningContentId, input.learningContentId),
            eq(quizAttempt.userId, ctx.userId!)
          )
        )
        .orderBy(desc(quizAttempt.startedAt))
        .limit(input.limit)
        .offset(input.offset);

      // Calculate statistics for all attempts for this learning content
      const statsQuery = await db
        .select({
          totalAttempts: quizAttempt.id,
          completedAttempts: quizAttempt.isCompleted,
          score: quizAttempt.score,
          totalTimeSpent: quizAttempt.totalTimeSpent,
          startedAt: quizAttempt.startedAt,
        })
        .from(quizAttempt)
        .innerJoin(quiz, eq(quizAttempt.quizId, quiz.id))
        .where(
          and(
            eq(quiz.learningContentId, input.learningContentId),
            eq(quizAttempt.userId, ctx.userId!)
          )
        );

      // Calculate stats from the raw data
      const totalAttempts = statsQuery.length;
      const completedAttempts = statsQuery.filter((a: any) => a.completedAttempts).length;
      const completedAttemptsWithScores = statsQuery.filter((a: any) => a.completedAttempts && a.score);

      const averageScore = completedAttemptsWithScores.length > 0
        ? completedAttemptsWithScores.reduce((sum: number, a: any) => sum + (a.score?.percentage || 0), 0) / completedAttemptsWithScores.length
        : 0;

      const averageTimeSpent = completedAttempts > 0
        ? statsQuery.filter((a: any) => a.completedAttempts).reduce((sum: number, a: any) => sum + (a.totalTimeSpent || 0), 0) / completedAttempts
        : 0;

      const bestScore = completedAttemptsWithScores.length > 0
        ? Math.max(...completedAttemptsWithScores.map((a: any) => a.score?.percentage || 0))
        : 0;

      const lastAttemptDate = totalAttempts > 0
        ? Math.max(...statsQuery.map((a: any) => new Date(a.startedAt).getTime()))
        : undefined;

      const stats = {
        totalAttempts,
        completedAttempts,
        averageScore,
        averageTimeSpent,
        bestScore,
        lastAttemptDate: lastAttemptDate ? new Date(lastAttemptDate).toISOString() : undefined,
      };

      // Transform attempts to match expected interface
      const transformedAttempts = attempts.map((attempt: any) => ({
        id: attempt.id,
        quizId: attempt.quizId,
        quizTitle: attempt.quizTitle,
        startedAt: attempt.startedAt.toISOString(),
        completedAt: attempt.completedAt?.toISOString(),
        isCompleted: attempt.isCompleted,
        score: attempt.score || undefined,
        totalTimeSpent: attempt.totalTimeSpent || 0,
        difficulty: attempt.difficulty as 'easy' | 'medium' | 'hard',
      }));

      return {
        attempts: transformedAttempts,
        stats,
        hasMore: attempts.length === input.limit,
      };
    } catch (error) {
      console.error('Failed to fetch quiz history:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch quiz history',
      });
    }
  },

  /**
   * Get the latest in-progress quiz attempt for the user
   */
  getLatestInProgress: async ({ ctx }: { ctx: any }) => {
    try {
      // For anonymous users, return null
      if (!ctx.isAuthenticated || !ctx.userId) {
        return {
          success: true,
          attempt: null,
        };
      }

      const { db } = createWorkersDatabaseFromEnv(ctx.env);

      // Get the most recent incomplete quiz attempt
      const inProgressAttempt = await db
        .select({
          id: quizAttempt.id,
          quizId: quizAttempt.quizId,
          startedAt: quizAttempt.startedAt,
          isCompleted: quizAttempt.isCompleted,
          totalTimeSpent: quizAttempt.totalTimeSpent,
          quizTitle: quiz.title,
          quizDifficulty: quiz.difficulty,
        })
        .from(quizAttempt)
        .innerJoin(quiz, eq(quizAttempt.quizId, quiz.id))
        .where(
          and(
            eq(quizAttempt.userId, ctx.userId),
            eq(quizAttempt.isCompleted, false)
          )
        )
        .orderBy(desc(quizAttempt.startedAt))
        .limit(1);

      if (inProgressAttempt.length === 0) {
        return {
          success: true,
          attempt: null,
        };
      }

      return {
        success: true,
        attempt: inProgressAttempt[0],
      };
    } catch (error) {
      console.error('Failed to fetch latest in-progress quiz:', error);
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to fetch latest in-progress quiz',
      });
    }
  },
};

// Utility function to shuffle array
function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

// Export validation schemas for reuse
export { generateQuizInputSchema, startQuizInputSchema, submitAnswerInputSchema, completeQuizInputSchema, getMyAttemptsInputSchema, getCurrentAttemptInputSchema, getQuizHistoryInputSchema, getQuizzesInputSchema };
