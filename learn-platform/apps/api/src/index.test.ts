/**
 * Tests for the main API worker application
 * Tests basic functionality and structure
 */

// Mock the auth dependencies
jest.mock('@learn-platform/auth', () => ({
  createAuth: jest.fn(() => ({
    handler: jest.fn().mockResolvedValue({
      status: 200,
      json: () => Promise.resolve({ user: null }),
    }),
  })),
  createAuthContextWithEnv: jest.fn().mockResolvedValue({
    isAuthenticated: false,
    user: null,
    session: null,
    userId: null,
  }),
  isSessionValid: jest.fn().mockReturnValue(true),
}));

// Mock the tRPC dependencies
jest.mock('@learn-platform/trpc', () => ({
  appRouter: {
    createCaller: jest.fn(),
  },
  createContext: jest.fn().mockResolvedValue({
    request: {},
    env: {},
    isAuthenticated: false,
    userId: null,
    user: null,
  }),
}));

// Mock the protected routes
jest.mock('./routes/protected', () => ({
  protectedRoutes: {
    routes: [],
  },
}));

import app from './index';

describe('API Worker Application', () => {
  describe('Application Structure', () => {
    it('should export a Hono app instance', () => {
      expect(app).toBeDefined();
      expect(typeof app.request).toBe('function');
      expect(typeof app.use).toBe('function');
      expect(typeof app.get).toBe('function');
      expect(typeof app.post).toBe('function');
    });

    it('should have proper middleware configuration', () => {
      // Test that the app has been configured with middleware
      expect(app).toBeDefined();
      // The app should have routes configured
      expect(app.routes).toBeDefined();
    });
  });

});
