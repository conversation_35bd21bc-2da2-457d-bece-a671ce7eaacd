/**
 * Tests for Cloudflare Workers database connection utility
 */

import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { createWorkersDatabaseFromEnv, closeConnection } from './database';

// Mock postgres to avoid actual database connections in tests
jest.mock('postgres', () => {
  const mockPostgres = jest.fn(() => ({
    end: jest.fn().mockResolvedValue(undefined),
  }));
  return mockPostgres;
});

// Mock drizzle
jest.mock('drizzle-orm/postgres-js', () => ({
  drizzle: jest.fn(() => ({
    select: jest.fn(),
    insert: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    transaction: jest.fn(),
  })),
}));

describe('Database Connection Utility', () => {
  const mockEnv = {
    DATABASE_URL: 'postgresql://user:password@localhost:5432/testdb',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createWorkersDatabaseFromEnv', () => {
    it('should create a database connection with valid environment', () => {
      const { db, client } = createWorkersDatabaseFromEnv(mockEnv);

      expect(db).toBeDefined();
      expect(client).toBeDefined();
      expect(typeof db.select).toBe('function');
      expect(typeof client.end).toBe('function');
    });

    it('should throw error when DATABASE_URL is missing', () => {
      expect(() => {
        createWorkersDatabaseFromEnv({});
      }).toThrow('DATABASE_URL environment variable is required');
    });

    it('should create mock connection for demo environment', () => {
      const demoEnv = {
        DATABASE_URL: '********************************/demo',
      };

      const { db, client } = createWorkersDatabaseFromEnv(demoEnv);

      expect(db).toBeDefined();
      expect(client).toBeDefined();
      expect(typeof db.transaction).toBe('function');
    });

    it('should create isolated connections for each call', () => {
      const postgres = require('postgres');
      postgres.mockClear();

      const connection1 = createWorkersDatabaseFromEnv(mockEnv);
      const connection2 = createWorkersDatabaseFromEnv(mockEnv);

      // Each call should create a new connection instance
      expect(connection1).not.toBe(connection2);
      expect(connection1.db).not.toBe(connection2.db);

      // Verify postgres constructor was called twice (once for each connection)
      expect(postgres).toHaveBeenCalledTimes(2);
    });
  });

  describe('closeConnection', () => {
    it('should close connection without throwing', async () => {
      const { client } = createWorkersDatabaseFromEnv(mockEnv);

      await expect(closeConnection(client)).resolves.not.toThrow();
      expect(client.end).toHaveBeenCalledTimes(1);
    });

    it('should handle connection close errors gracefully', async () => {
      const { client } = createWorkersDatabaseFromEnv(mockEnv);
      (client.end as jest.Mock).mockRejectedValueOnce(new Error('Connection already closed'));

      // Should not throw even if client.end() fails
      await expect(closeConnection(client)).resolves.not.toThrow();
    });
  });

  describe('Request Isolation', () => {
    it('should not share connections between simulated requests', () => {
      const postgres = require('postgres');
      postgres.mockClear();

      // Simulate multiple concurrent requests
      const requests = Array.from({ length: 5 }, () =>
        createWorkersDatabaseFromEnv(mockEnv)
      );

      // Verify postgres constructor was called 5 times (once for each request)
      expect(postgres).toHaveBeenCalledTimes(5);

      // Each request should have its own connection object
      expect(requests[0]).not.toBe(requests[1]);
      expect(requests[0].db).not.toBe(requests[1].db);
    });
  });
});
