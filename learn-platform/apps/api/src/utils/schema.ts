/**
 * Database schema definitions for Cloudflare Workers
 *
 * This module contains the necessary schema definitions for the API Worker,
 * copied from the main database library to avoid import resolution issues.
 */

import { pgTable, text, timestamp, json, boolean, integer, decimal, index } from "drizzle-orm/pg-core";

/**
 * User table (minimal definition for foreign key reference)
 */
export const user = pgTable("user", {
  id: text('id').primaryKey(),
  email: text('email').notNull().unique(),
  name: text('name').notNull(),
  emailVerified: timestamp('emailVerified'),
  image: text('image'),
  createdAt: timestamp('createdAt').$defaultFn(() => new Date()).notNull(),
  updatedAt: timestamp('updatedAt').$defaultFn(() => new Date()).notNull(),
});

/**
 * Explainer templates table
 * Stores user-created explainer templates with their content and metadata
 */
export const explainerTemplates = pgTable("explainer_templates", {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  description: text('description').default(''),

  // Store the complete template data as JSON
  // This includes steps array with all content blocks
  steps: json('steps').notNull().$type<Array<{
    id: string;
    title: string;
    icon: string;
    blocks: Array<{
      id: string;
      type: string;
      data: any;
      isEditing?: boolean;
    }>;
  }>>(),

  // User ownership
  userId: text('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),

  // Timestamps
  createdAt: timestamp('created_at').$defaultFn(() => new Date()).notNull(),
  updatedAt: timestamp('updated_at').$defaultFn(() => new Date()).notNull(),
});

/**
 * Learning content table
 * Stores user-generated learning content for public consumption
 * This is separate from explainer templates which are admin-created
 */
export const learningContent = pgTable("learning_content", {
  id: text('id').primaryKey(),
  title: text('title').notNull(),
  description: text('description').default(''),

  // Store the complete learning content as JSON
  // This includes steps array with all content blocks
  steps: json('steps').notNull().$type<Array<{
    id: string;
    title: string;
    icon: string;
    blocks: Array<{
      id: string;
      type: string;
      data: any;
      isEditing?: boolean;
    }>;
  }>>(),

  // Learning metadata
  learningLevel: text('learning_level').notNull().$type<'beginner' | 'intermediate' | 'advanced'>(),
  estimatedReadingTime: integer('estimated_reading_time').notNull(), // in minutes

  // Content visibility and categorization
  isPublic: boolean('is_public').default(false).notNull(),
  tags: json('tags').$type<string[]>().default([]),

  // AI generation metadata (optional)
  aiMetadata: json('ai_metadata').$type<{
    aiModel?: string;
    generatedAt?: string;
    contentTypes?: string[];
    originalPrompt?: string;
  }>(),

  // User ownership
  userId: text('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),

  // Timestamps
  createdAt: timestamp('created_at').$defaultFn(() => new Date()).notNull(),
  updatedAt: timestamp('updated_at').$defaultFn(() => new Date()).notNull(),
});

/**
 * Learning progress table
 * Tracks user progress through learning content
 */
export const learningProgress = pgTable("learning_progress", {
  id: text('id').primaryKey(),
  contentId: text('content_id').notNull().references(() => learningContent.id, { onDelete: 'cascade' }),
  userId: text('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),

  // Progress tracking
  currentStepIndex: integer('current_step_index').default(0).notNull(),
  completedSteps: json('completed_steps').$type<number[]>().default([]),

  // Time tracking
  totalTimeSpent: integer('total_time_spent').default(0), // in seconds
  sessionCount: integer('session_count').default(1),

  // Completion tracking
  completionPercentage: integer('completion_percentage').default(0),
  isCompleted: boolean('is_completed').default(false),

  // User annotations
  bookmarks: json('bookmarks').$type<Array<{
    stepIndex: number;
    note?: string;
    createdAt: string;
  }>>().default([]),
  notes: json('notes').$type<Array<{
    stepIndex: number;
    content: string;
    createdAt: string;
  }>>().default([]),

  // Timestamps
  lastAccessedAt: timestamp('last_accessed_at').$defaultFn(() => new Date()).notNull(),
  createdAt: timestamp('created_at').$defaultFn(() => new Date()).notNull(),
  updatedAt: timestamp('updated_at').$defaultFn(() => new Date()).notNull(),
});

/**
 * Releases table
 * Stores system releases with version information and publication status
 */
export const releases = pgTable("releases", {
  id: text('id').primaryKey(),
  version: text('version').notNull().unique(), // e.g., "v1.2.0", "v2.0.0-beta.1"
  description: text('description').notNull(), // Rich text description of the release

  // Publication management
  isPublished: boolean('is_published').$defaultFn(() => false).notNull(),
  publishedAt: timestamp('published_at'), // When the release was published (null if unpublished)
  releaseDate: timestamp('release_date').notNull(), // Intended release date

  // User ownership - admin who created the release
  createdBy: text('created_by').notNull().references(() => user.id, { onDelete: 'cascade' }),

  // Timestamps
  createdAt: timestamp('created_at').$defaultFn(() => new Date()).notNull(),
  updatedAt: timestamp('updated_at').$defaultFn(() => new Date()).notNull(),
});

/**
 * User release notifications table
 * Tracks which users have seen which releases
 */
export const userReleaseNotifications = pgTable("user_release_notifications", {
  id: text('id').primaryKey(),

  // Foreign keys
  userId: text('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),
  releaseId: text('release_id').notNull().references(() => releases.id, { onDelete: 'cascade' }),

  // Tracking information
  isRead: boolean('is_read').$defaultFn(() => false).notNull(),
  readAt: timestamp('read_at'), // When the user marked it as read (null if unread)

  // Timestamps
  createdAt: timestamp('created_at').$defaultFn(() => new Date()).notNull(),
  updatedAt: timestamp('updated_at').$defaultFn(() => new Date()).notNull(),
});

/**
 * Quiz table
 * Stores generated quizzes based on learning content
 */
export const quiz = pgTable("quiz", {
  id: text('id').primaryKey(),
  title: text('title').notNull(),
  description: text('description').default(''),

  // Reference to the learning content this quiz was generated from
  learningContentId: text('learning_content_id').notNull().references(() => learningContent.id, { onDelete: 'cascade' }),

  // Quiz configuration
  difficulty: text('difficulty').notNull().$type<'easy' | 'medium' | 'hard'>(),
  estimatedDuration: integer('estimated_duration').notNull(), // in minutes
  totalPoints: integer('total_points').notNull(),

  // Quiz questions stored as JSON array
  questions: json('questions').notNull().$type<Array<{
    id: string;
    type: 'flashcard' | 'multipleChoice' | 'trueFalse' | 'fillInBlank' | 'matching' | 'freeText' | 'ordering';
    difficulty: 'easy' | 'medium' | 'hard';
    sourceStepId: string;
    sourceContent: string;
    points: number;
    // Type-specific question data
    [key: string]: any;
  }>>(),

  // Generation metadata
  metadata: json('metadata').$type<{
    generatedAt: string;
    aiModel: string;
    sourceStepsUsed: string[];
    difficultyDistribution: Record<string, number>;
    typeDistribution: Record<string, number>;
    validationPassed?: boolean;
    generationOptions?: Record<string, any>;
  }>(),

  // Quiz settings
  isPublic: boolean('is_public').default(false).notNull(),
  allowRetakes: boolean('allow_retakes').default(true).notNull(),
  showCorrectAnswers: boolean('show_correct_answers').default(true).notNull(),
  shuffleQuestions: boolean('shuffle_questions').default(false).notNull(),
  timeLimit: integer('time_limit'), // in minutes, null for no limit

  // Ownership and timestamps
  createdBy: text('created_by').notNull().references(() => user.id, { onDelete: 'cascade' }),
  createdAt: timestamp('created_at').$defaultFn(() => new Date()).notNull(),
  updatedAt: timestamp('updated_at').$defaultFn(() => new Date()).notNull(),
}, (table) => ({
  learningContentIdx: index('quiz_learning_content_idx').on(table.learningContentId),
  createdByIdx: index('quiz_created_by_idx').on(table.createdBy),
  difficultyIdx: index('quiz_difficulty_idx').on(table.difficulty),
  createdAtIdx: index('quiz_created_at_idx').on(table.createdAt),
}));

/**
 * Quiz attempts table
 * Tracks user attempts at quizzes
 */
export const quizAttempt = pgTable("quiz_attempt", {
  id: text('id').primaryKey(),

  // References
  quizId: text('quiz_id').notNull().references(() => quiz.id, { onDelete: 'cascade' }),
  userId: text('user_id').notNull().references(() => user.id, { onDelete: 'cascade' }),

  // Attempt details
  startedAt: timestamp('started_at').$defaultFn(() => new Date()).notNull(),
  completedAt: timestamp('completed_at'),
  isCompleted: boolean('is_completed').default(false).notNull(),

  // User answers stored as JSON
  answers: json('answers').$type<Array<{
    questionId: string;
    questionType: string;
    answer: any; // Union type based on question type
    timeSpent: number; // seconds spent on this question
    isCorrect?: boolean; // Calculated after submission
    pointsEarned?: number;
  }>>().default([]),

  // Scoring
  totalTimeSpent: integer('total_time_spent').default(0), // in seconds
  score: json('score').$type<{
    totalPoints: number;
    earnedPoints: number;
    percentage: number;
    correctAnswers: number;
    totalQuestions: number;
  }>(),

  // Question-level results
  questionResults: json('question_results').$type<Array<{
    questionId: string;
    isCorrect: boolean;
    pointsEarned: number;
    feedback?: string;
    userAnswer?: any;
    correctAnswer?: any;
  }>>(),

  // Metadata
  metadata: json('metadata').$type<{
    userAgent?: string;
    ipAddress?: string;
    deviceType?: string;
    browserInfo?: string;
    questionOrder?: number[]; // Order questions were presented (array indices)
    pausedDurations?: number[]; // Times when quiz was paused
  }>(),

}, (table) => ({
  quizUserIdx: index('quiz_attempt_quiz_user_idx').on(table.quizId, table.userId),
  userIdx: index('quiz_attempt_user_idx').on(table.userId),
  completedAtIdx: index('quiz_attempt_completed_at_idx').on(table.completedAt),
  isCompletedIdx: index('quiz_attempt_is_completed_idx').on(table.isCompleted),
}));

// Export type for TypeScript inference
export type ExplainerTemplate = typeof explainerTemplates.$inferSelect;
export type NewExplainerTemplate = typeof explainerTemplates.$inferInsert;
export type LearningContent = typeof learningContent.$inferSelect;
export type NewLearningContent = typeof learningContent.$inferInsert;
export type LearningProgress = typeof learningProgress.$inferSelect;
export type NewLearningProgress = typeof learningProgress.$inferInsert;
export type Release = typeof releases.$inferSelect;
export type NewRelease = typeof releases.$inferInsert;
export type UserReleaseNotification = typeof userReleaseNotifications.$inferSelect;
export type NewUserReleaseNotification = typeof userReleaseNotifications.$inferInsert;
export type Quiz = typeof quiz.$inferSelect;
export type NewQuiz = typeof quiz.$inferInsert;
export type QuizAttempt = typeof quizAttempt.$inferSelect;
export type NewQuizAttempt = typeof quizAttempt.$inferInsert;

// Re-export commonly used Drizzle operators
export { eq, and, or, desc, asc, sql, gte, lte, ilike } from 'drizzle-orm';
