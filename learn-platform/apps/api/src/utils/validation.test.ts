/**
 * Tests for utility functions and validation helpers
 * These tests cover common utility functions that might be used across the API
 */

import { describe, it, expect } from '@jest/globals';

// Utility functions for testing (these would typically be in separate files)
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  // Additional check for consecutive dots
  if (email.includes('..')) return false;
  return emailRegex.test(email);
};

export const sanitizeInput = (input: string): string => {
  return input.trim().replace(/[<>]/g, '');
};

export const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

export const isValidPassword = (password: string): boolean => {
  return password.length >= 8 && /[A-Za-z]/.test(password) && /[0-9]/.test(password);
};

export const formatTimestamp = (date: Date): string => {
  return date.toISOString();
};

describe('Utility Functions', () => {
  describe('validateEmail', () => {
    it('should validate correct email addresses', () => {
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('<EMAIL>')).toBe(true);
    });

    it('should reject invalid email addresses', () => {
      expect(validateEmail('invalid-email')).toBe(false);
      expect(validateEmail('test@')).toBe(false);
      expect(validateEmail('@example.com')).toBe(false);
      expect(validateEmail('<EMAIL>')).toBe(false);
      expect(validateEmail('')).toBe(false);
    });
  });

  describe('sanitizeInput', () => {
    it('should remove dangerous characters', () => {
      expect(sanitizeInput('<script>alert("xss")</script>')).toBe('scriptalert("xss")/script');
      expect(sanitizeInput('Hello <world>')).toBe('Hello world');
    });

    it('should trim whitespace', () => {
      expect(sanitizeInput('  hello world  ')).toBe('hello world');
      expect(sanitizeInput('\n\ttest\n\t')).toBe('test');
    });

    it('should handle empty strings', () => {
      expect(sanitizeInput('')).toBe('');
      expect(sanitizeInput('   ')).toBe('');
    });
  });

  describe('generateRequestId', () => {
    it('should generate unique request IDs', () => {
      const id1 = generateRequestId();
      const id2 = generateRequestId();

      expect(id1).not.toBe(id2);
      expect(id1).toMatch(/^req_\d+_[a-z0-9]+$/);
      expect(id2).toMatch(/^req_\d+_[a-z0-9]+$/);
    });

    it('should have consistent format', () => {
      const id = generateRequestId();
      expect(id).toMatch(/^req_\d+_[a-z0-9]{9}$/);
    });
  });

  describe('isValidPassword', () => {
    it('should validate strong passwords', () => {
      expect(isValidPassword('password123')).toBe(true);
      expect(isValidPassword('MySecure1Pass')).toBe(true);
      expect(isValidPassword('test1234')).toBe(true);
    });

    it('should reject weak passwords', () => {
      expect(isValidPassword('short')).toBe(false);
      expect(isValidPassword('onlyletters')).toBe(false);
      expect(isValidPassword('12345678')).toBe(false);
      expect(isValidPassword('')).toBe(false);
    });

    it('should require minimum length', () => {
      expect(isValidPassword('abc123')).toBe(false); // 6 chars
      expect(isValidPassword('abcd1234')).toBe(true); // 8 chars
    });
  });

  describe('formatTimestamp', () => {
    it('should format dates as ISO strings', () => {
      const date = new Date('2024-01-01T12:00:00.000Z');
      expect(formatTimestamp(date)).toBe('2024-01-01T12:00:00.000Z');
    });

    it('should handle current date', () => {
      const now = new Date();
      const formatted = formatTimestamp(now);

      expect(formatted).toBe(now.toISOString());
      expect(() => new Date(formatted)).not.toThrow();
    });
  });
});
