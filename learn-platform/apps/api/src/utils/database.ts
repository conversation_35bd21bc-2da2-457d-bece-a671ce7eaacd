/**
 * Database connection utility for Cloudflare Workers
 *
 * This module provides a Workers-optimized database connection using the postgres library
 * directly, following Cloudflare Workers best practices for PostgreSQL connections.
 *
 * Features:
 * - Request-isolated database connections (no cross-request sharing)
 * - Proper connection cleanup and error handling
 * - Transaction support for multi-query operations
 * - Optimized settings for Cloudflare Workers environment
 *
 * IMPORTANT: This implementation creates fresh database connections for each request
 * to prevent "Cannot perform I/O on behalf of a different request" errors that occur
 * when sharing database connections across Cloudflare Workers request contexts.
 */

import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema';

/**
 * Database connection configuration optimized for Cloudflare Workers
 */
export interface WorkersDatabaseConfig {
  connectionString: string;
  max?: number;
  fetch_types?: boolean;
}

/**
 * Connection configuration optimized for Cloudflare Workers
 * Each request gets its own isolated connection to prevent cross-request I/O issues
 */
const WORKERS_CONNECTION_CONFIG = {
  max: 1, // Single connection per request to prevent sub-pooling
  idle_timeout: 30, // 30 seconds idle timeout
  connect_timeout: 10, // 10 seconds connection timeout
  fetch_types: false, // Disable type fetching for better performance
};

/**
 * Create a request-isolated database connection for Cloudflare Workers
 * Each request gets its own connection to prevent cross-request I/O issues
 */
function createIsolatedConnection(config: WorkersDatabaseConfig) {
  const { connectionString } = config;

  // Create postgres client with Workers-optimized settings
  const client = postgres(connectionString, {
    ...WORKERS_CONNECTION_CONFIG,
    fetch_types: config.fetch_types ?? WORKERS_CONNECTION_CONFIG.fetch_types,
  });

  // Create drizzle instance with schema
  const db = drizzle(client, { schema });

  return { db, client };
}

/**
 * Safely close a database connection with error handling
 */
export async function closeConnection(client: postgres.Sql) {
  try {
    await client.end();
  } catch (error) {
    console.warn('Error closing database connection:', error);
  }
}

/**
 * Create a database connection optimized for Cloudflare Workers
 * Following the official Cloudflare Workers PostgreSQL tutorial recommendations
 *
 * @deprecated Use getOrCreatePooledConnection instead for better connection management
 */
export function createWorkersDatabase(config: WorkersDatabaseConfig) {
  // Create postgres client with Workers-optimized settings
  const client = postgres(config.connectionString, {
    // Workers limit the number of concurrent external connections
    max: config.max ?? 5,

    // If you are using array types in your Postgres schema, it is necessary to fetch
    // type information to correctly de/serialize them. However, if you are not using
    // those, disabling this will save you an extra round-trip every time you connect.
    fetch_types: config.fetch_types ?? false,
  });

  // Create drizzle instance with schema
  const db = drizzle(client, { schema });

  return { db, client };
}

/**
 * Create a database connection using Cloudflare Workers environment variables
 * This function creates a request-isolated connection to prevent cross-request I/O issues
 */
export function createWorkersDatabaseFromEnv(env: Record<string, any>) {
  const connectionString = env?.['DATABASE_URL'];

  if (!connectionString) {
    throw new Error(
      'DATABASE_URL environment variable is required. ' +
      'Please set it as a secret in your Cloudflare Workers environment.'
    );
  }

  // For demo/development with dummy connection string, create a mock connection
  if (connectionString.includes('demo:demo@demo')) {
    console.warn('Using demo database connection - authentication features will be limited');

    // Create a mock postgres client that doesn't actually connect
    const mockClient = {
      end: () => Promise.resolve(),
    } as any;

    // Create a mock drizzle instance
    const mockDb = {
      select: () => ({ from: () => ({ where: () => Promise.resolve([]) }) }),
      insert: () => ({ values: () => Promise.resolve({ insertId: 'mock-id' }) }),
      update: () => ({ set: () => ({ where: () => Promise.resolve() }) }),
      delete: () => ({ where: () => Promise.resolve() }),
      transaction: (fn: any) => fn(mockDb), // Mock transaction support
    } as any;

    return { db: mockDb, client: mockClient };
  }

  // Create isolated connection for this request
  return createIsolatedConnection({ connectionString });
}

/**
 * Utility function to create a database connection with proper cleanup
 * Returns both the database instance and a cleanup function
 */
export function createDatabaseWithCleanup(env: Record<string, any>) {
  const { db, client } = createWorkersDatabaseFromEnv(env);

  const cleanup = async () => {
    await closeConnection(client);
  };

  return { db, client, cleanup };
}
