/**
 * tRPC context creation for Cloudflare Workers (local version)
 *
 * This is a Worker-specific version of the context creation to avoid
 * importing from the main tRPC library which has dependency issues.
 */

import type { Context } from '../router/workers-router';

export interface CreateContextOptions {
  request: Request;
  env?: any; // Cloudflare Worker environment bindings
}

/**
 * Creates the tRPC context from the incoming request
 * This is called for every request to the tRPC server
 */
export async function createWorkersContext(opts: CreateContextOptions): Promise<Context> {
  const { request, env } = opts;

  // Extract URL information
  const url = new URL(request.url);

  // Extract Cloudflare-specific headers and request metadata
  const requestMetadata = {
    ip: request.headers.get('CF-Connecting-IP') ||
        request.headers.get('X-Forwarded-For') ||
        request.headers.get('X-Real-IP') ||
        'unknown',
    country: request.headers.get('CF-IPCountry') || 'unknown',
    userAgent: request.headers.get('user-agent') || '',
    timestamp: new Date().toISOString(),
    requestId: request.headers.get('CF-Ray') ||
               request.headers.get('X-Request-ID') ||
               crypto.randomUUID(),
  };

  // Get authentication context using better-auth
  // Use the Cloudflare Workers environment if available
  const { createAuthContext, createAuthContextWithEnv } = await import('@learn-platform/auth');
  const authContext = env
    ? await createAuthContextWithEnv(request, env)
    : await createAuthContext(request);

  return {
    // Core request information
    request,
    env,
    url,

    // Enhanced metadata
    ...requestMetadata,

    // Authentication context from better-auth
    session: authContext.session,
    isAuthenticated: authContext.isAuthenticated,
    userId: authContext.userId,
    user: authContext.user,

    // Legacy userSession for backward compatibility
    userSession: {
      isAuthenticated: authContext.isAuthenticated,
      userId: authContext.userId,
      roles: [], // TODO: Implement role system
      email: authContext.user?.email,
      name: authContext.user?.name,
    },

    // Utility functions
    getUserId: () => authContext.userId,
    getUserRoles: () => [], // TODO: Implement role system
    hasRole: (_role: string) => false, // TODO: Implement role system

    // Request timing for performance monitoring
    startTime: Date.now(),
  };
}
