/**
 * Cloudflare Workers-specific tRPC router
 *
 * This router extends the base tRPC router with Worker-specific procedures
 * that use local database utilities to avoid monorepo import resolution issues.
 */

import { initTRPC, TRPCError } from '@trpc/server';
import { z } from 'zod';
import { workersTemplatesProcedures, createTemplateSchema, updateTemplateSchema } from '../procedures/templates';
import {
  workersLearningContentProcedures,
  createLearningContentSchema,
  updateLearningContentSchema,
  getLearningContentSchema,
  aiGenerationInputSchema
} from '../procedures/learning-content';
import {
  workersLearningProgressProcedures,
  updateProgressSchema,
  addBookmarkSchema,
  addNoteSchema,
  removeBookmarkSchema,
  updateNoteSchema,
  deleteNoteSchema
} from '../procedures/learning-progress';
import {
  workersReleasesProcedures,
  createReleaseSchema,
  updateReleaseSchema,
  publishReleaseSchema,
  markAsReadSchema
} from '../procedures/releases';
import {
  workersQuizProcedures,
  generateQuizInputSchema,
  startQuizInputSchema,
  submitAnswerInputSchema,
  completeQuizInputSchema,
  getMyAttemptsInputSchema,
  getCurrentAttemptInputSchema,
  getQuizHistoryInputSchema,
  getQuizzesInputSchema
} from '../procedures/quiz';

/**
 * Local Context type definition to avoid importing from main tRPC library
 */
export interface Context extends Record<string, unknown> {
  request: Request;
  env?: any;
  url: URL;
  ip: string;
  country: string;
  userAgent: string;
  timestamp: string;
  requestId: string;
  session: any;
  isAuthenticated: boolean;
  userId: string | null;
  user: any;
  userSession: {
    isAuthenticated: boolean;
    userId: string | null;
    roles: string[];
    email?: string;
    name?: string;
  };
  getUserId: () => string | null;
  getUserRoles: () => string[];
  hasRole: (role: string) => boolean;
  startTime: number;
}

/**
 * Initialize tRPC with context and enhanced error formatting
 */
const t = initTRPC.context<Context>().create({
  errorFormatter({ shape, error }) {
    // Check if we're in development mode (Cloudflare Workers compatible)
    const isDevelopment = typeof globalThis !== 'undefined' &&
                         (globalThis as any).ENVIRONMENT === 'development';

    return {
      ...shape,
      data: {
        ...shape.data,
        code: error.code,
        stack: isDevelopment ? error.stack : undefined,
      },
    };
  },
});

/**
 * Export reusable router and procedure helpers
 */
export const router = t.router;
export const publicProcedure = t.procedure;

/**
 * Protected procedure that requires authentication
 */
export const protectedProcedure = publicProcedure.use(({ ctx, next }) => {
  if (!ctx.isAuthenticated) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'You must be logged in to access this resource',
    });
  }

  return next({
    ctx: {
      ...ctx,
      user: ctx.user,
    },
  });
});

/**
 * Admin procedure that requires admin role
 */
export const adminProcedure = protectedProcedure.use(({ ctx, next }) => {
  // TODO: Implement proper role checking
  // For now, we'll just check if user is authenticated
  // In a real app, you would check user roles from database
  if (!ctx.isAuthenticated) {
    throw new TRPCError({
      code: 'FORBIDDEN',
      message: 'You must have admin privileges to access this resource',
    });
  }

  return next({
    ctx,
  });
});

/**
 * Worker-specific templates router
 */
export const workersTemplatesRouter = router({
  getAll: protectedProcedure
    .query(async ({ ctx }) => {
      return workersTemplatesProcedures.getAll({ ctx });
    }),

  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input, ctx }) => {
      return workersTemplatesProcedures.getById({ input, ctx });
    }),

  create: protectedProcedure
    .input(createTemplateSchema)
    .mutation(async ({ input, ctx }) => {
      return workersTemplatesProcedures.create({ input, ctx });
    }),

  update: protectedProcedure
    .input(updateTemplateSchema)
    .mutation(async ({ input, ctx }) => {
      return workersTemplatesProcedures.update({ input, ctx });
    }),

  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      return workersTemplatesProcedures.delete({ input, ctx });
    }),
});

/**
 * Worker-specific learning content router
 */
export const workersLearningContentRouter = router({
  getAll: publicProcedure
    .input(getLearningContentSchema)
    .query(async ({ input, ctx }) => {
      return workersLearningContentProcedures.getAll({ input, ctx });
    }),

  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input, ctx }) => {
      return workersLearningContentProcedures.getById({ input, ctx });
    }),

  create: protectedProcedure
    .input(createLearningContentSchema)
    .mutation(async ({ input, ctx }) => {
      return workersLearningContentProcedures.create({ input, ctx });
    }),

  update: protectedProcedure
    .input(updateLearningContentSchema)
    .mutation(async ({ input, ctx }) => {
      return workersLearningContentProcedures.update({ input, ctx });
    }),

  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      return workersLearningContentProcedures.delete({ input, ctx });
    }),

  getMy: protectedProcedure
    .input(getLearningContentSchema)
    .query(async ({ input, ctx }) => {
      return workersLearningContentProcedures.getMy({ input, ctx });
    }),

  duplicate: protectedProcedure
    .input(z.object({
      id: z.string(),
      newTitle: z.string().min(1).max(200).optional(),
    }))
    .mutation(async ({ input, ctx }) => {
      return workersLearningContentProcedures.duplicate({ input, ctx });
    }),

  getStats: protectedProcedure
    .query(async ({ ctx }) => {
      return workersLearningContentProcedures.getStats({ ctx });
    }),

  generateWithAI: protectedProcedure
    .input(aiGenerationInputSchema)
    .mutation(async ({ input, ctx }) => {
      return workersLearningContentProcedures.generateWithAI({ input, ctx });
    }),
});

/**
 * Worker-specific learning progress router
 */
export const workersLearningProgressRouter = router({
  getProgress: publicProcedure
    .input(z.object({ contentId: z.string() }))
    .query(async ({ input, ctx }) => {
      return workersLearningProgressProcedures.getProgress({ input, ctx });
    }),

  updateProgress: publicProcedure
    .input(updateProgressSchema)
    .mutation(async ({ input, ctx }) => {
      return workersLearningProgressProcedures.updateProgress({ input, ctx });
    }),

  addBookmark: protectedProcedure
    .input(addBookmarkSchema)
    .mutation(async ({ input, ctx }) => {
      return workersLearningProgressProcedures.addBookmark({ input, ctx });
    }),

  addNote: protectedProcedure
    .input(addNoteSchema)
    .mutation(async ({ input, ctx }) => {
      return workersLearningProgressProcedures.addNote({ input, ctx });
    }),

  removeBookmark: protectedProcedure
    .input(removeBookmarkSchema)
    .mutation(async ({ input, ctx }) => {
      return workersLearningProgressProcedures.removeBookmark({ input, ctx });
    }),

  updateNote: protectedProcedure
    .input(updateNoteSchema)
    .mutation(async ({ input, ctx }) => {
      return workersLearningProgressProcedures.updateNote({ input, ctx });
    }),

  deleteNote: protectedProcedure
    .input(deleteNoteSchema)
    .mutation(async ({ input, ctx }) => {
      return workersLearningProgressProcedures.deleteNote({ input, ctx });
    }),

  getStats: protectedProcedure
    .query(async ({ ctx }) => {
      return workersLearningProgressProcedures.getStats({ ctx });
    }),

  getLatestInProgress: protectedProcedure
    .query(async ({ ctx }) => {
      return workersLearningProgressProcedures.getLatestInProgress({ ctx });
    }),
});

/**
 * Worker-specific releases router
 */
export const workersReleasesRouter = router({
  getAllAdmin: adminProcedure
    .query(async ({ ctx }) => {
      return workersReleasesProcedures.getAllAdmin({ ctx });
    }),

  getPublished: protectedProcedure
    .query(async ({ ctx }) => {
      return workersReleasesProcedures.getPublished({ ctx });
    }),

  getById: adminProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input, ctx }) => {
      return workersReleasesProcedures.getById({ input, ctx });
    }),

  create: adminProcedure
    .input(createReleaseSchema)
    .mutation(async ({ input, ctx }) => {
      return workersReleasesProcedures.create({ input, ctx });
    }),

  update: adminProcedure
    .input(updateReleaseSchema)
    .mutation(async ({ input, ctx }) => {
      return workersReleasesProcedures.update({ input, ctx });
    }),

  publish: adminProcedure
    .input(publishReleaseSchema)
    .mutation(async ({ input, ctx }) => {
      return workersReleasesProcedures.publish({ input, ctx });
    }),

  delete: adminProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      return workersReleasesProcedures.delete({ input, ctx });
    }),

  markAsRead: protectedProcedure
    .input(markAsReadSchema)
    .mutation(async ({ input, ctx }) => {
      return workersReleasesProcedures.markAsRead({ input, ctx });
    }),

  getStats: adminProcedure
    .query(async ({ ctx }) => {
      return workersReleasesProcedures.getStats({ ctx });
    }),
});

/**
 * Worker-specific quiz router
 */
export const workersQuizRouter = router({
  generate: protectedProcedure
    .input(generateQuizInputSchema)
    .mutation(async ({ input, ctx }) => {
      return workersQuizProcedures.generate({ input, ctx });
    }),

  getAll: protectedProcedure
    .input(getQuizzesInputSchema)
    .query(async ({ input, ctx }) => {
      return workersQuizProcedures.getAll({ input, ctx });
    }),

  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input, ctx }) => {
      return workersQuizProcedures.getById({ input, ctx });
    }),

  startAttempt: protectedProcedure
    .input(startQuizInputSchema)
    .mutation(async ({ input, ctx }) => {
      return workersQuizProcedures.startAttempt({ input, ctx });
    }),

  submitAnswer: protectedProcedure
    .input(submitAnswerInputSchema)
    .mutation(async ({ input, ctx }) => {
      return workersQuizProcedures.submitAnswer({ input, ctx });
    }),

  complete: protectedProcedure
    .input(completeQuizInputSchema)
    .mutation(async ({ input, ctx }) => {
      return workersQuizProcedures.complete({ input, ctx });
    }),

  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      return workersQuizProcedures.delete({ input, ctx });
    }),

  getMyAttempts: protectedProcedure
    .input(getMyAttemptsInputSchema)
    .query(async ({ input, ctx }) => {
      return workersQuizProcedures.getMyAttempts({ input, ctx });
    }),

  getCurrentAttempt: protectedProcedure
    .input(getCurrentAttemptInputSchema)
    .query(async ({ input, ctx }) => {
      return workersQuizProcedures.getCurrentAttempt({ input, ctx });
    }),

  getHistoryByLearningContent: protectedProcedure
    .input(getQuizHistoryInputSchema)
    .query(async ({ input, ctx }) => {
      return workersQuizProcedures.getHistoryByLearningContent({ input, ctx });
    }),

  getLatestInProgress: protectedProcedure
    .query(async ({ ctx }) => {
      return workersQuizProcedures.getLatestInProgress({ ctx });
    }),
});

/**
 * Extended app router with Worker-specific procedures
 * This router includes all the base procedures plus Worker-specific ones
 */
export const workersAppRouter = router({
  // Import all base procedures from the main tRPC router
  // We'll need to manually include the ones we need to avoid import issues

  // Health check endpoint
  health: publicProcedure
    .query(({ ctx }) => {
      return {
        status: 'ok',
        timestamp: ctx.timestamp,
        message: 'tRPC server is running on Cloudflare Workers',
        requestId: ctx.requestId,
        country: ctx.country,
        version: '1.0.0',
      };
    }),

  // Authentication procedures
  auth: router({
    signUp: publicProcedure
      .input(z.object({
        email: z.string().email('Invalid email format'),
        password: z.string().min(8, 'Password must be at least 8 characters'),
        name: z.string().min(1, 'Name is required'),
        avatar: z.string().optional(),
      }))
      .mutation(async ({ input, ctx }) => {
        try {
          const { createAuth } = await import('@learn-platform/auth');
          const auth = createAuth(ctx.env);
          const result = await auth.api.signUpEmail({
            body: {
              email: input.email,
              password: input.password,
              name: input.name,
              avatar: input.avatar || '',
            },
            headers: ctx.request.headers,
          });

          return {
            success: true,
            message: 'User created successfully',
            user: result.user,
          };
        } catch (error) {
          throw new TRPCError({
            code: 'BAD_REQUEST',
            message: error instanceof Error ? error.message : 'Failed to create user',
          });
        }
      }),

    signIn: publicProcedure
      .input(z.object({
        email: z.string().email('Invalid email format'),
        password: z.string().min(1, 'Password is required'),
      }))
      .mutation(async ({ input, ctx }) => {
        try {
          const { createAuth } = await import('@learn-platform/auth');
          const auth = createAuth(ctx.env);
          const result = await auth.api.signInEmail({
            body: {
              email: input.email,
              password: input.password,
            },
            headers: ctx.request.headers,
          });

          return {
            success: true,
            message: 'Signed in successfully',
            user: result.user,
          };
        } catch (error) {
          throw new TRPCError({
            code: 'UNAUTHORIZED',
            message: error instanceof Error ? error.message : 'Invalid credentials',
          });
        }
      }),

    signOut: protectedProcedure
      .mutation(async ({ ctx }) => {
        try {
          const { createAuth } = await import('@learn-platform/auth');
          const auth = createAuth(ctx.env);
          await auth.api.signOut({
            headers: ctx.request.headers,
          });

          return {
            success: true,
            message: 'Signed out successfully',
          };
        } catch (error) {
          throw new TRPCError({
            code: 'INTERNAL_SERVER_ERROR',
            message: 'Failed to sign out',
          });
        }
      }),

    getSession: publicProcedure
      .query(({ ctx }) => {
        return {
          session: ctx.session,
          isAuthenticated: ctx.isAuthenticated,
          user: ctx.user,
        };
      }),
  }),

  // Worker-specific templates router
  templates: workersTemplatesRouter,

  // Worker-specific learning content router
  learningContent: workersLearningContentRouter,

  // Worker-specific learning progress router
  learningProgress: workersLearningProgressRouter,

  // Worker-specific releases router
  releases: workersReleasesRouter,

  // Worker-specific quiz router
  quiz: workersQuizRouter,

  // User procedures
  me: protectedProcedure
    .query(({ ctx }) => {
      return {
        id: ctx.userId,
        name: ctx.user?.name || 'Demo User',
        email: ctx.user?.email || '<EMAIL>',
        roles: [],
        isAuthenticated: ctx.isAuthenticated,
      };
    }),
});

/**
 * Export the router type for client-side type safety
 */
export type WorkersAppRouter = typeof workersAppRouter;
