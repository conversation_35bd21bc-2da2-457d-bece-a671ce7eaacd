/**
 * Cloudflare Worker API using Hono + tRPC
 *
 * This worker serves as the API backend for the learning platform,
 * providing type-safe endpoints through tRPC and handling CORS for
 * cross-origin requests from the web and admin applications.
 */

import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { trpcServer } from '@hono/trpc-server';
import { createWorkersContext } from './utils/context';
import { workersAppRouter } from './router/workers-router';
import { protectedRoutes } from './routes/protected';

// Initialize Hono app
const app = new Hono();

// Configure CORS for cross-origin requests from web and admin apps
app.use('*', cors({
  origin: [
    'http://localhost:3000',  // Local web app
    'http://localhost:3001',  // Local admin app
    'https://kwaci-learning.bmbn.dev',  // Production web app (Vercel)
    'https://learn-platform-web-preview.bm.workers.dev',  // Preview web app (Cloudflare Workers)
    'https://learn-platform-web-prod.bm.workers.dev',  // Production web app (Cloudflare Workers)
    'https://learn-platform-admin-preview.bm.workers.dev',  // Preview admin app (Cloudflare Workers)
    'https://learn-platform-admin-prod.bm.workers.dev',  // Production admin app (Cloudflare Workers)
    // Add admin production domain when available
    // 'https://admin.kwaci-learning.bmbn.dev',
  ],
  allowHeaders: ['Content-Type', 'Authorization', 'Cookie'],
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  exposeHeaders: ['Content-Length', 'Set-Cookie'],
  maxAge: 600,
  credentials: true,
}));

// Health check endpoint
app.get('/', (c) => {
  return c.json({
    message: 'Learning Platform API - Fixed Build Loop',
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.1',
  });
});

// Debug endpoint for auth configuration (temporary for debugging)
app.get('/debug/auth-config', async (c) => {
  try {
    const authModule = await import('@learn-platform/auth');
    const env = c.env as Record<string, unknown>; // Better typing for Cloudflare Workers env

    // Check if debugTrustedOrigins exists in the module
    if ('debugTrustedOrigins' in authModule && typeof authModule.debugTrustedOrigins === 'function') {
      const debugInfo = authModule.debugTrustedOrigins(env);

      return c.json({
        message: 'Auth Configuration Debug Info',
        timestamp: new Date().toISOString(),
        environment: {
          isCloudflareWorkers: true,
          envKeys: Object.keys(env || {}),
          betterAuthUrl: env?.['BETTER_AUTH_URL'],
          betterAuthTrustedOrigins: env?.['BETTER_AUTH_TRUSTED_ORIGINS'],
          betterAuthSecretExists: !!env?.['BETTER_AUTH_SECRET'],
        },
        debugInfo,
      });
    } else {
      return c.json({
        message: 'Debug function not available',
        timestamp: new Date().toISOString(),
        environment: {
          isCloudflareWorkers: true,
          envKeys: Object.keys(env || {}),
          betterAuthUrl: env?.['BETTER_AUTH_URL'],
          betterAuthTrustedOrigins: env?.['BETTER_AUTH_TRUSTED_ORIGINS'],
          betterAuthSecretExists: !!env?.['BETTER_AUTH_SECRET'],
        },
      });
    }
  } catch (error) {
    console.error('Debug endpoint error:', error);
    return c.json({
      error: 'Failed to get debug info',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
    }, 500);
  }
});

// Mount Better Auth handler for authentication endpoints
app.on(['POST', 'GET'], '/api/auth/**', async (c) => {
  const { createAuth } = await import('@learn-platform/auth');
  const auth = createAuth(c.env as Record<string, unknown>); // Cloudflare Workers env binding
  return auth.handler(c.req.raw);
});

// Mount tRPC server at /trpc endpoint
app.use(
  '/trpc/*',
  trpcServer({
    router: workersAppRouter,
    createContext: (opts, c) => {
      return createWorkersContext({
        request: opts.req,
        env: c.env, // Cloudflare Worker environment bindings
      });
    },
    onError: ({ error, path }) => {
      console.error(`tRPC Error on path '${path}':`, error);
    },
  })
);

// Mount protected REST API routes at /api/protected
app.route('/api/protected', protectedRoutes);

// Global error handler
app.onError((err, c) => {
  console.error('Unhandled error:', err);
  return c.json(
    {
      error: 'Internal Server Error',
      message: 'An unexpected error occurred',
      timestamp: new Date().toISOString(),
    },
    500
  );
});

// 404 handler
app.notFound((c) => {
  return c.json(
    {
      error: 'Not Found',
      message: 'The requested endpoint does not exist',
      timestamp: new Date().toISOString(),
    },
    404
  );
});

// Export the app for Cloudflare Workers
export default app;
