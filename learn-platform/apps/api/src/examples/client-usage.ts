/**
 * Client-side usage examples for protected API routes
 *
 * This file demonstrates how to call the protected API endpoints from
 * client applications, including proper authentication handling and
 * error management.
 *
 * These examples show both tRPC and direct HTTP approaches.
 */

import React from 'react';
import type { AppRouter } from '@learn-platform/trpc';
import { createTRPCProxyClient, httpBatchLink } from '@trpc/client';
import type {
  UserProfile,
  DashboardData,
  UserPreferences,
  ApiResponse
} from '../types/api';

// =============================================================================
// tRPC Client Examples
// =============================================================================

/**
 * Create tRPC client with authentication
 *
 * The tRPC client automatically handles authentication through cookies
 * when making requests to the same domain. For cross-domain requests,
 * you may need to configure credentials and CORS properly.
 */
const trpcClient = createTRPCProxyClient<AppRouter>({
  links: [
    httpBatchLink({
      url: 'http://localhost:8787/trpc', // Your API URL

      // Include credentials (cookies) for authentication
      fetch(url, options) {
        return fetch(url, {
          ...options,
          credentials: 'include', // Important: Include cookies for auth
        } as any);
      },

      // Optional: Add custom headers
      headers() {
        return {
          // Add any custom headers here
          'X-Client-Version': '1.0.0',
        };
      },
    }),
  ],
});

/**
 * Example: Get user profile using tRPC
 */
export async function getUserProfileTRPC(): Promise<UserProfile> {
  try {
    const profile = await trpcClient.getUserProfile.query();
    console.log('User profile:', profile);

    // Type assertion: Protected procedures guarantee userId is not null
    // but TypeScript doesn't know this, so we assert the type
    if (!profile.id) {
      throw new Error('Invalid profile data: missing user ID');
    }

    return profile as UserProfile;
  } catch (error) {
    console.error('Failed to fetch user profile:', error);

    // Handle specific tRPC errors
    if (error instanceof Error && 'data' in error) {
      const trpcError = error as any;
      if (trpcError.data?.code === 'UNAUTHORIZED') {
        // Redirect to login or show auth modal
        console.log('User not authenticated, redirecting to login...');
        // window.location.href = '/login';
      }
    }

    throw error;
  }
}

/**
 * Example: Get dashboard data using tRPC
 */
export async function getDashboardDataTRPC(): Promise<DashboardData> {
  try {
    const dashboard = await trpcClient.getDashboard.query();
    console.log('Dashboard data:', dashboard);

    // Type assertion: Protected procedures guarantee userId is not null
    // but TypeScript doesn't know this, so we assert the type
    if (!dashboard.user.id) {
      throw new Error('Invalid dashboard data: missing user ID');
    }

    return dashboard as DashboardData;
  } catch (error) {
    console.error('Failed to fetch dashboard data:', error);
    throw error;
  }
}

/**
 * Example: Update user preferences using tRPC
 */
export async function updatePreferencesTRPC(
  preferences: Partial<UserPreferences>
): Promise<UserPreferences> {
  try {
    const updated = await trpcClient.updatePreferences.mutate(preferences);
    console.log('Updated preferences:', updated);
    return updated;
  } catch (error) {
    console.error('Failed to update preferences:', error);
    throw error;
  }
}

/**
 * Example: Sign in using tRPC
 */
export async function signInTRPC(email: string, password: string) {
  try {
    const result = await trpcClient.auth.signIn.mutate({ email, password });
    console.log('Sign in successful:', result);
    return result;
  } catch (error) {
    console.error('Sign in failed:', error);
    throw error;
  }
}

// =============================================================================
// Direct HTTP Client Examples
// =============================================================================

/**
 * Base configuration for HTTP requests
 */
const API_BASE_URL = 'http://localhost:8787/api';

/**
 * Helper function to make authenticated HTTP requests
 */
async function authenticatedFetch<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  const url = `${API_BASE_URL}${endpoint}`;

  const response = await fetch(url, {
    ...options,
    credentials: 'include', // Include cookies for authentication
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
  } as any);

  const data: ApiResponse<T> = await response.json();

  // Handle HTTP errors
  if (!response.ok) {
    console.error(`HTTP ${response.status}:`, data);

    // Handle authentication errors
    if (response.status === 401) {
      console.log('Authentication required, redirecting to login...');
      // Handle authentication failure (redirect to login, show modal, etc.)
      // window.location.href = '/login';
    }

    throw new Error(data.error?.message || `HTTP ${response.status}`);
  }

  // Handle API errors
  if (!data.success) {
    throw new Error(data.error?.message || 'API request failed');
  }

  return data;
}

/**
 * Example: Get user profile using direct HTTP
 */
export async function getUserProfileHTTP(): Promise<UserProfile> {
  try {
    const response = await authenticatedFetch<UserProfile>('/protected/user-profile');
    return response.data!;
  } catch (error) {
    console.error('Failed to fetch user profile:', error);
    throw error;
  }
}

/**
 * Example: Get dashboard data using direct HTTP
 */
export async function getDashboardDataHTTP(): Promise<DashboardData> {
  try {
    const response = await authenticatedFetch<DashboardData>('/protected/dashboard');
    return response.data!;
  } catch (error) {
    console.error('Failed to fetch dashboard data:', error);
    throw error;
  }
}

/**
 * Example: Update user preferences using direct HTTP
 */
export async function updatePreferencesHTTP(
  preferences: Partial<UserPreferences>
): Promise<UserPreferences> {
  try {
    const response = await authenticatedFetch<UserPreferences>('/protected/preferences', {
      method: 'PUT',
      body: JSON.stringify(preferences),
    });
    return response.data!;
  } catch (error) {
    console.error('Failed to update preferences:', error);
    throw error;
  }
}

/**
 * Example: Get session information using direct HTTP
 */
export async function getSessionInfoHTTP() {
  try {
    const response = await authenticatedFetch('/protected/session-info');
    return response.data;
  } catch (error) {
    console.error('Failed to fetch session info:', error);
    throw error;
  }
}

// =============================================================================
// React Hook Examples (for React applications)
// =============================================================================

/**
 * Example React hook for user profile
 * This would typically use a library like React Query or SWR
 */
export function useUserProfile() {
  // This is a simplified example - in a real app, you'd use React Query or SWR
  const [profile, setProfile] = React.useState<UserProfile | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    getUserProfileTRPC()
      .then(setProfile)
      .catch((err) => setError(err.message))
      .finally(() => setLoading(false));
  }, []);

  return { profile, loading, error };
}

/**
 * Example React hook for dashboard data
 */
export function useDashboard() {
  const [dashboard, setDashboard] = React.useState<DashboardData | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<string | null>(null);

  React.useEffect(() => {
    getDashboardDataTRPC()
      .then(setDashboard)
      .catch((err) => setError(err.message))
      .finally(() => setLoading(false));
  }, []);

  const refresh = React.useCallback(() => {
    setLoading(true);
    getDashboardDataTRPC()
      .then(setDashboard)
      .catch((err) => setError(err.message))
      .finally(() => setLoading(false));
  }, []);

  return { dashboard, loading, error, refresh };
}

// =============================================================================
// Error Handling Utilities
// =============================================================================

/**
 * Utility function to handle authentication errors consistently
 */
export function handleAuthError(error: any) {
  if (error?.data?.code === 'UNAUTHORIZED' || error?.message?.includes('401')) {
    // Clear any local auth state (only in browser environment)
    if (typeof globalThis !== 'undefined' && 'localStorage' in globalThis) {
      try {
        (globalThis as any).localStorage.removeItem('user');
        (globalThis as any).sessionStorage.clear();
      } catch (e) {
        // Ignore storage errors in case of restricted environments
        console.warn('Could not clear local storage:', e);
      }
    }

    // Redirect to login
    console.log('Authentication failed, redirecting to login...');
    // if (typeof window !== 'undefined') window.location.href = '/login';

    return true; // Indicates auth error was handled
  }

  return false; // Not an auth error
}

/**
 * Utility function to retry requests with exponential backoff
 */
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries = 3,
  baseDelay = 1000
): Promise<T> {
  let lastError: any;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;

      // Don't retry auth errors
      if (handleAuthError(error)) {
        throw error;
      }

      // Don't retry on last attempt
      if (attempt === maxRetries) {
        break;
      }

      // Wait with exponential backoff
      const delay = baseDelay * Math.pow(2, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
}
