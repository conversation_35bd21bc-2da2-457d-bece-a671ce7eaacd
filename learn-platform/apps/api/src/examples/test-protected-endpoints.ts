/**
 * Test script for protected API endpoints
 *
 * This script demonstrates how to test the protected API endpoints
 * by first authenticating and then calling protected routes.
 *
 * Run this script to verify that authentication is working correctly.
 */

import type { AppRouter } from '@learn-platform/trpc';
import { createTRPCProxyClient, httpBatchLink } from '@trpc/client';
import { db, user, eq } from '@learn-platform/db';

// Configuration
const API_BASE_URL = 'http://localhost:8787';

/**
 * Get environment variable with fallback
 */
function getEnvVar(key: string, fallback: string): string {
  try {
    return (globalThis as any).process?.env?.[key] || fallback;
  } catch {
    return fallback;
  }
}

const TEST_USER = {
  email: getEnvVar('TEST_USER_EMAIL', '<EMAIL>'),
  password: getEnvVar('TEST_USER_PASSWORD', 'testpassword123'),
  name: 'Test User',
};

/**
 * Check if a user exists in the database by email
 */
async function checkUserExists(email: string): Promise<boolean> {
  try {
    const [existingUser] = await db.select().from(user).where(eq(user.email, email));
    return !!existingUser;
  } catch (error) {
    console.log('⚠️  Database check failed:', (error as Error).message);
    console.log('   Falling back to signup attempt...');
    return false; // Fallback to signup attempt if DB check fails
  }
}

// Create tRPC client
const trpcClient = createTRPCProxyClient<AppRouter>({
  links: [
    httpBatchLink({
      url: `${API_BASE_URL}/trpc`,
      fetch(url, options) {
        return fetch(url, {
          ...options,
          credentials: 'include',
        } as any);
      },
    }),
  ],
});

/**
 * Test authentication flow
 */
async function testAuthentication() {
  console.log('🔐 Testing Authentication Flow...\n');

  try {
    // Step 1: Try to access protected endpoint without authentication
    console.log('1. Testing unauthenticated access (should fail)...');
    try {
      await trpcClient.getUserProfile.query();
      console.log('❌ ERROR: Protected endpoint accessible without auth!');
    } catch (error) {
      console.log('✅ Correctly blocked unauthenticated access');
      console.log(`   Error: ${(error as Error).message}\n`);
    }

    // Step 2: Check if test user exists in database
    console.log('2. Checking if test user exists...');
    const userExists = await checkUserExists(TEST_USER.email);

    if (userExists) {
      console.log('ℹ️  Test user already exists, proceeding with sign in...\n');
    } else {
      // Step 2a: Create test user if it doesn't exist
      console.log('2a. Creating test user...');
      try {
        const signUpResult = await trpcClient.auth.signUp.mutate(TEST_USER);
        console.log('✅ User created successfully');
        console.log(`   User ID: ${signUpResult.user?.id}`);
        console.log(`   Email: ${signUpResult.user?.email}\n`);
      } catch (error) {
        console.log('❌ Sign up failed:', (error as Error).message);
        return;
      }
    }

    // Step 3: Sign in
    console.log('3. Signing in...');
    try {
      const signInResult = await trpcClient.auth.signIn.mutate({
        email: TEST_USER.email,
        password: TEST_USER.password,
      });
      console.log('✅ Sign in successful');
      console.log(`   User: ${signInResult.user?.name} (${signInResult.user?.email})\n`);
    } catch (error) {
      console.log('❌ Sign in failed:', (error as Error).message);
      return;
    }

    // Step 4: Test protected tRPC endpoints
    console.log('4. Testing protected tRPC endpoints...');

    // Test user profile
    try {
      const profile = await trpcClient.getUserProfile.query();
      console.log('✅ getUserProfile successful');
      console.log(`   Profile: ${profile.name} (${profile.email})`);
    } catch (error) {
      console.log('❌ getUserProfile failed:', (error as Error).message);
    }

    // Test dashboard
    try {
      const dashboard = await trpcClient.getDashboard.query();
      console.log('✅ getDashboard successful');
      console.log(`   Courses enrolled: ${dashboard.stats.coursesEnrolled}`);
      console.log(`   Learning streak: ${dashboard.stats.currentStreak} days`);
    } catch (error) {
      console.log('❌ getDashboard failed:', (error as Error).message);
    }

    // Test update preferences
    try {
      const updatedPrefs = await trpcClient.updatePreferences.mutate({
        theme: 'dark',
        notifications: { email: false },
      });
      console.log('✅ updatePreferences successful');
      console.log(`   Theme: ${updatedPrefs.theme}`);
      console.log(`   Email notifications: ${updatedPrefs.notifications.email}`);
    } catch (error) {
      console.log('❌ updatePreferences failed:', (error as Error).message);
    }

    console.log('\n');

  } catch (error) {
    console.log('❌ Authentication test failed:', (error as Error).message);
  }
}

/**
 * Test direct HTTP endpoints
 */
async function testHTTPEndpoints() {
  console.log('🌐 Testing Direct HTTP Endpoints...\n');

  // Helper function for authenticated requests
  async function authenticatedFetch(endpoint: string, options: RequestInit = {}) {
    const response = await fetch(`${API_BASE_URL}/api${endpoint}`, {
      ...options,
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    } as any);

    const data = await response.json();

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${(data as any).error?.message || 'Request failed'}`);
    }

    return data;
  }

  try {
    // Test user profile endpoint
    console.log('1. Testing GET /api/protected/user-profile...');
    try {
      const profileResponse = await authenticatedFetch('/protected/user-profile') as any;
      console.log('✅ User profile endpoint successful');
      console.log(`   User: ${profileResponse.data.name} (${profileResponse.data.email})`);
      console.log(`   Account status: ${profileResponse.data.metadata.accountStatus}`);
    } catch (error) {
      console.log('❌ User profile endpoint failed:', (error as Error).message);
    }

    // Test dashboard endpoint
    console.log('\n2. Testing GET /api/protected/dashboard...');
    try {
      const dashboardResponse = await authenticatedFetch('/protected/dashboard') as any;
      console.log('✅ Dashboard endpoint successful');
      console.log(`   Courses: ${dashboardResponse.data.stats.coursesEnrolled} enrolled, ${dashboardResponse.data.stats.coursesCompleted} completed`);
      console.log(`   Achievements: ${dashboardResponse.data.stats.achievements.length}`);
    } catch (error) {
      console.log('❌ Dashboard endpoint failed:', (error as Error).message);
    }

    // Test preferences update endpoint
    console.log('\n3. Testing PUT /api/protected/preferences...');
    try {
      const prefsResponse = await authenticatedFetch('/protected/preferences', {
        method: 'PUT',
        body: JSON.stringify({
          theme: 'light',
          language: 'es',
          notifications: {
            push: false,
            marketing: true,
          },
        }),
      }) as any;
      console.log('✅ Preferences update successful');
      console.log(`   Theme: ${prefsResponse.data.theme}`);
      console.log(`   Language: ${prefsResponse.data.language}`);
      console.log(`   Marketing notifications: ${prefsResponse.data.notifications.marketing}`);
    } catch (error) {
      console.log('❌ Preferences update failed:', (error as Error).message);
    }

    // Test session info endpoint
    console.log('\n4. Testing GET /api/protected/session-info...');
    try {
      const sessionResponse = await authenticatedFetch('/protected/session-info') as any;
      console.log('✅ Session info endpoint successful');
      console.log(`   Session ID: ${sessionResponse.data.session.id}`);
      console.log(`   Expires: ${sessionResponse.data.session.expiresAt}`);
      console.log(`   Request IP: ${sessionResponse.data.request.ip}`);
      console.log(`   Country: ${sessionResponse.data.request.country || 'Unknown'}`);
    } catch (error) {
      console.log('❌ Session info endpoint failed:', (error as Error).message);
    }

    console.log('\n');

  } catch (error) {
    console.log('❌ HTTP endpoints test failed:', (error as Error).message);
  }
}

/**
 * Test error scenarios
 */
async function testErrorScenarios() {
  console.log('⚠️  Testing Error Scenarios...\n');

  try {
    // Test accessing protected endpoint without authentication
    console.log('1. Testing unauthenticated HTTP request...');
    try {
      const response = await fetch(`${API_BASE_URL}/api/protected/user-profile`, {
        credentials: 'omit', // Don't include cookies
      } as any);

      if (response.status === 401) {
        console.log('✅ Correctly returned 401 for unauthenticated request');
        const errorData = await response.json() as any;
        console.log(`   Error code: ${errorData.error?.code}`);
        console.log(`   Error message: ${errorData.error?.message}`);
      } else {
        console.log('❌ Expected 401 but got:', response.status);
      }
    } catch (error) {
      console.log('❌ Unauthenticated request test failed:', (error as Error).message);
    }

    // Test invalid endpoint
    console.log('\n2. Testing invalid endpoint...');
    try {
      const response = await fetch(`${API_BASE_URL}/api/protected/nonexistent`, {
        credentials: 'include',
      } as any);

      if (response.status === 404) {
        console.log('✅ Correctly returned 404 for invalid endpoint');
      } else {
        console.log('❌ Expected 404 but got:', response.status);
      }
    } catch (error) {
      console.log('❌ Invalid endpoint test failed:', (error as Error).message);
    }

    // Test malformed request body
    console.log('\n3. Testing malformed request body...');
    try {
      const response = await fetch(`${API_BASE_URL}/api/protected/preferences`, {
        method: 'PUT',
        credentials: 'include',
        headers: { 'Content-Type': 'application/json' },
        body: 'invalid json',
      } as any);

      if (response.status >= 400) {
        console.log('✅ Correctly handled malformed request body');
        console.log(`   Status: ${response.status}`);
      } else {
        console.log('❌ Expected error but got:', response.status);
      }
    } catch (error) {
      console.log('❌ Malformed request test failed:', (error as Error).message);
    }

    console.log('\n');

  } catch (error) {
    console.log('❌ Error scenarios test failed:', (error as Error).message);
  }
}

/**
 * Main test runner
 */
async function runTests() {
  console.log('🚀 Starting Protected API Endpoints Test Suite\n');
  console.log('=' .repeat(60));
  console.log('\n');

  await testAuthentication();
  await testHTTPEndpoints();
  await testErrorScenarios();

  console.log('✨ Test suite completed!\n');
  console.log('📝 Summary:');
  console.log('   - Authentication flow tested');
  console.log('   - tRPC protected procedures tested');
  console.log('   - Direct HTTP protected routes tested');
  console.log('   - Error scenarios validated');
  console.log('\n');
  console.log('🔗 Available endpoints:');
  console.log('   tRPC: http://localhost:8787/trpc');
  console.log('   Auth: http://localhost:8787/api/auth/*');
  console.log('   Protected: http://localhost:8787/api/protected/*');
}

// Run tests if this file is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

export {
  testAuthentication,
  testHTTPEndpoints,
  testErrorScenarios,
  runTests,
};
