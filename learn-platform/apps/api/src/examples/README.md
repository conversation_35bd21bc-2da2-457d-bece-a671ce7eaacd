# Protected API Routes - Comprehensive Guide

This guide demonstrates how to implement and use protected API routes in the Learn Platform using Better Auth for authentication.

## Overview

The Learn Platform provides two approaches for creating protected API endpoints:

1. **tRPC Procedures** - Type-safe, full-stack TypeScript with automatic serialization
2. **Direct Hono Routes** - Traditional REST API endpoints with manual type checking

Both approaches use the same Better Auth implementation for session management and authentication.

## Authentication Flow

```mermaid
sequenceDiagram
    participant Client
    participant API
    participant BetterAuth
    participant Database

    Client->>API: Request with session cookie
    API->>BetterAuth: Validate session
    BetterAuth->>Database: Check session validity
    Database-->>BetterAuth: Session data
    BetterAuth-->>API: User context
    
    alt Session valid
        API->>API: Process request
        API-->>Client: Protected data
    else Session invalid/expired
        API-->>Client: 401 Unauthorized
    end
```

## tRPC Protected Procedures

### Implementation

tRPC procedures use the `protectedProcedure` middleware that automatically:
- Validates user authentication
- Throws `UNAUTHORIZED` errors for unauthenticated requests
- Provides type-safe user context

```typescript
// In libs/trpc/src/router.ts
export const protectedProcedure = publicProcedure.use(({ ctx, next }) => {
  if (!ctx.isAuthenticated) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'You must be logged in to access this resource',
    });
  }

  return next({
    ctx: {
      ...ctx,
      user: ctx.user, // Guaranteed to be non-null
    },
  });
});
```

### Example Procedures

```typescript
// Get user profile
getUserProfile: protectedProcedure
  .query(({ ctx }) => {
    return {
      id: ctx.userId,
      email: ctx.user?.email,
      name: ctx.user?.name,
      // ... additional profile data
    };
  }),

// Update preferences
updatePreferences: protectedProcedure
  .input(z.object({
    theme: z.enum(['light', 'dark', 'system']).optional(),
    // ... other preference fields
  }))
  .mutation(({ input, ctx }) => {
    // Update user preferences in database
    return updatedPreferences;
  }),
```

### Client Usage

```typescript
import { createTRPCProxyClient, httpBatchLink } from '@trpc/client';

const client = createTRPCProxyClient<AppRouter>({
  links: [
    httpBatchLink({
      url: 'http://localhost:8787/trpc',
      fetch(url, options) {
        return fetch(url, {
          ...options,
          credentials: 'include', // Include auth cookies
        });
      },
    }),
  ],
});

// Call protected procedure
const profile = await client.getUserProfile.query();
```

## Direct Hono Routes

### Authentication Middleware

The `authMiddleware` provides authentication for direct Hono routes:

```typescript
// In apps/api/src/middleware/auth.ts
export async function authMiddleware(c: Context, next: Next) {
  const authContext = await createAuthContextWithEnv(c.req.raw, c.env);
  
  if (!authContext.isAuthenticated || !authContext.session) {
    throw new HTTPException(401, {
      message: createErrorResponse('UNAUTHORIZED', 'Authentication required')
    });
  }
  
  if (!isSessionValid(authContext.session)) {
    throw new HTTPException(401, {
      message: createErrorResponse('SESSION_EXPIRED', 'Session expired')
    });
  }
  
  // Enhance context with auth info
  c.set('auth', authenticatedContext);
  c.set('user', authContext.user);
  
  await next();
}
```

### Protected Route Implementation

```typescript
// In apps/api/src/routes/protected.ts
const protectedRoutes = new Hono();

// Apply auth middleware to all routes
protectedRoutes.use('*', authMiddleware);

protectedRoutes.get('/user-profile', async (c) => {
  const auth = getAuthContext(c);
  const requestId = c.get('requestId');
  
  const userProfile = {
    id: auth.user.id,
    email: auth.user.email,
    // ... build profile data
  };
  
  return c.json(createSuccessResponse(userProfile, requestId));
});
```

### Client Usage

```typescript
async function authenticatedFetch<T>(endpoint: string, options: RequestInit = {}) {
  const response = await fetch(`${API_BASE_URL}${endpoint}`, {
    ...options,
    credentials: 'include', // Include auth cookies
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
  });
  
  if (!response.ok) {
    if (response.status === 401) {
      // Handle authentication failure
      window.location.href = '/login';
    }
    throw new Error(`HTTP ${response.status}`);
  }
  
  return response.json();
}

// Call protected endpoint
const profile = await authenticatedFetch('/protected/user-profile');
```

## Error Handling

### Authentication Errors

Both approaches handle these authentication scenarios:

| Error | Code | Description | Client Action |
|-------|------|-------------|---------------|
| No session | `UNAUTHORIZED` | User not logged in | Redirect to login |
| Expired session | `SESSION_EXPIRED` | Session has expired | Redirect to login |
| Invalid token | `INVALID_TOKEN` | Malformed session | Clear local state, redirect |
| Insufficient permissions | `FORBIDDEN` | User lacks required role | Show access denied |

### tRPC Error Handling

```typescript
try {
  const data = await client.getUserProfile.query();
} catch (error) {
  if (error.data?.code === 'UNAUTHORIZED') {
    // Redirect to login
    window.location.href = '/login';
  } else {
    // Handle other errors
    console.error('API Error:', error.message);
  }
}
```

### HTTP Error Handling

```typescript
try {
  const data = await authenticatedFetch('/protected/profile');
} catch (error) {
  if (error.message.includes('401')) {
    // Handle authentication error
    handleAuthError();
  } else {
    // Handle other errors
    console.error('Request failed:', error.message);
  }
}
```

## Security Best Practices

### 1. Session Management
- Sessions expire after 7 days by default
- Session cookies are HTTP-only and secure in production
- Session validation happens on every request

### 2. CORS Configuration
```typescript
app.use('*', cors({
  origin: ['http://localhost:3000', 'https://yourdomain.com'],
  credentials: true, // Allow cookies
  allowHeaders: ['Content-Type', 'Authorization'],
}));
```

### 3. Request Metadata
All protected routes capture:
- IP address (from Cloudflare headers)
- User agent
- Country (from Cloudflare)
- Request timestamp
- Unique request ID

### 4. Error Information
- Production: Generic error messages
- Development: Detailed error information including stack traces
- All errors include request IDs for debugging

## Available Endpoints

### tRPC Procedures

| Procedure | Type | Description |
|-----------|------|-------------|
| `getUserProfile` | Query | Get complete user profile |
| `getDashboard` | Query | Get dashboard data with stats |
| `updatePreferences` | Mutation | Update user preferences |
| `auth.signIn` | Mutation | Sign in user |
| `auth.signUp` | Mutation | Register new user |

### HTTP Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/protected/user-profile` | GET | Get user profile |
| `/api/protected/dashboard` | GET | Get dashboard data |
| `/api/protected/preferences` | PUT | Update preferences |
| `/api/protected/session-info` | GET | Get session details |

## Testing Authentication

### 1. Sign Up/Sign In
```typescript
// Sign up
await client.auth.signUp.mutate({
  email: '<EMAIL>',
  password: 'securepassword',
  name: 'John Doe'
});

// Sign in
await client.auth.signIn.mutate({
  email: '<EMAIL>',
  password: 'securepassword'
});
```

### 2. Test Protected Endpoints
```typescript
// This will work after authentication
const profile = await client.getUserProfile.query();

// This will throw UNAUTHORIZED if not authenticated
```

### 3. Test Session Expiration
Sessions can be tested by manually expiring them or waiting for the 7-day expiration.

## Integration with Frontend

### React Query Integration
```typescript
import { useQuery } from '@tanstack/react-query';

function useUserProfile() {
  return useQuery({
    queryKey: ['userProfile'],
    queryFn: () => client.getUserProfile.query(),
    retry: (failureCount, error) => {
      // Don't retry auth errors
      if (error.data?.code === 'UNAUTHORIZED') return false;
      return failureCount < 3;
    },
  });
}
```

### SWR Integration
```typescript
import useSWR from 'swr';

function useUserProfile() {
  return useSWR('userProfile', () => client.getUserProfile.query(), {
    onError: (error) => {
      if (error.data?.code === 'UNAUTHORIZED') {
        window.location.href = '/login';
      }
    },
  });
}
```

This comprehensive guide covers all aspects of implementing and using protected API routes with Better Auth in the Learn Platform.
