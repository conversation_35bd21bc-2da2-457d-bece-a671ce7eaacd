/**
 * Manual test script to verify visibility filter functionality
 * Run with: node test-visibility-filter.js
 */

const API_BASE_URL = 'http://localhost:8787';

async function testVisibilityFilter() {
  console.log('🧪 Testing Visibility Filter Functionality\n');

  try {
    // Test 1: Get all content (no filter)
    console.log('📋 Test 1: Get all content (no filter)');
    const allContentResponse = await fetch(`${API_BASE_URL}/trpc/learningContent.getMy?batch=1&input={"0":{"json":{"limit":50,"offset":0}}}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (allContentResponse.ok) {
      const allContentData = await allContentResponse.json();
      const allContent = allContentData[0]?.result?.data?.content || [];
      console.log(`✅ Found ${allContent.length} total content items`);
      
      // Show breakdown by visibility
      const publicCount = allContent.filter(item => item.isPublic).length;
      const privateCount = allContent.filter(item => !item.isPublic).length;
      console.log(`   📊 Public: ${publicCount}, Private: ${privateCount}\n`);
    } else {
      console.log('❌ Failed to fetch all content\n');
    }

    // Test 2: Get only public content
    console.log('📋 Test 2: Get only public content (isPublic: true)');
    const publicContentResponse = await fetch(`${API_BASE_URL}/trpc/learningContent.getMy?batch=1&input={"0":{"json":{"limit":50,"offset":0,"isPublic":true}}}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (publicContentResponse.ok) {
      const publicContentData = await publicContentResponse.json();
      const publicContent = publicContentData[0]?.result?.data?.content || [];
      console.log(`✅ Found ${publicContent.length} public content items`);
      
      // Verify all items are public
      const allPublic = publicContent.every(item => item.isPublic);
      console.log(`   🔍 All items are public: ${allPublic ? '✅' : '❌'}\n`);
    } else {
      console.log('❌ Failed to fetch public content\n');
    }

    // Test 3: Get only private content
    console.log('📋 Test 3: Get only private content (isPublic: false)');
    const privateContentResponse = await fetch(`${API_BASE_URL}/trpc/learningContent.getMy?batch=1&input={"0":{"json":{"limit":50,"offset":0,"isPublic":false}}}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (privateContentResponse.ok) {
      const privateContentData = await privateContentResponse.json();
      const privateContent = privateContentData[0]?.result?.data?.content || [];
      console.log(`✅ Found ${privateContent.length} private content items`);
      
      // Verify all items are private
      const allPrivate = privateContent.every(item => !item.isPublic);
      console.log(`   🔍 All items are private: ${allPrivate ? '✅' : '❌'}\n`);
    } else {
      console.log('❌ Failed to fetch private content\n');
    }

    // Test 4: Combined filter test (visibility + learning level)
    console.log('📋 Test 4: Combined filters (public + beginner level)');
    const combinedResponse = await fetch(`${API_BASE_URL}/trpc/learningContent.getMy?batch=1&input={"0":{"json":{"limit":50,"offset":0,"isPublic":true,"learningLevel":"beginner"}}}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    if (combinedResponse.ok) {
      const combinedData = await combinedResponse.json();
      const combinedContent = combinedData[0]?.result?.data?.content || [];
      console.log(`✅ Found ${combinedContent.length} public beginner content items`);
      
      // Verify all items match both criteria
      const allMatch = combinedContent.every(item => item.isPublic && item.learningLevel === 'beginner');
      console.log(`   🔍 All items are public AND beginner level: ${allMatch ? '✅' : '❌'}\n`);
    } else {
      console.log('❌ Failed to fetch combined filtered content\n');
    }

    console.log('🎉 Visibility filter test completed!');
    console.log('\n💡 To test in the UI:');
    console.log('   1. Go to http://localhost:3000/dashboard/my-learning');
    console.log('   2. Click "Advanced Filters" to expand the filter panel');
    console.log('   3. Change the "Visibility" dropdown from "All Content" to "Public Only" or "Private Only"');
    console.log('   4. Verify that the content list updates to show only the selected visibility type');
    console.log('   5. Check that the active filter indicator shows the visibility filter');

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Run the test
testVisibilityFilter();
