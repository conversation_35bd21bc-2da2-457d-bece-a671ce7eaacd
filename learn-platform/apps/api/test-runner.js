#!/usr/bin/env node

/**
 * Test runner script for API authentication tests
 * 
 * This script provides an easy way to run different types of tests
 * and can be used for CI/CD or local development.
 * 
 * Usage:
 *   node test-runner.js                    # Run all tests
 *   node test-runner.js auth               # Run only auth tests
 *   node test-runner.js --watch            # Run tests in watch mode
 *   node test-runner.js --coverage         # Run tests with coverage
 */

const { spawn } = require('child_process');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logHeader(message) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`  ${message}`, 'bright');
  log('='.repeat(60), 'cyan');
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

// Parse command line arguments
const args = process.argv.slice(2);
const testType = args.find(arg => !arg.startsWith('--'));
const flags = args.filter(arg => arg.startsWith('--'));

// Build Jest command
function buildJestCommand() {
  const jestArgs = ['jest'];
  
  // Add test file pattern based on test type
  if (testType === 'auth') {
    jestArgs.push('auth-integration.test.ts');
  } else if (testType === 'utils') {
    jestArgs.push('utils');
  } else if (testType === 'all' || !testType) {
    // Run all tests
    jestArgs.push('src/**/*.test.ts');
  } else {
    jestArgs.push(testType);
  }
  
  // Add flags
  if (flags.includes('--watch')) {
    jestArgs.push('--watch');
  }
  
  if (flags.includes('--coverage')) {
    jestArgs.push('--coverage');
  }
  
  if (flags.includes('--verbose')) {
    jestArgs.push('--verbose');
  }
  
  // Add default Jest options
  jestArgs.push('--passWithNoTests');
  
  return jestArgs;
}

// Run Jest with the built command
function runTests() {
  const jestCommand = buildJestCommand();
  
  logHeader('API Authentication Tests');
  logInfo(`Running command: ${jestCommand.join(' ')}`);
  logInfo(`Working directory: ${process.cwd()}`);
  
  const jest = spawn('npx', jestCommand, {
    stdio: 'inherit',
    shell: true,
    cwd: process.cwd(),
  });
  
  jest.on('close', (code) => {
    if (code === 0) {
      logSuccess('All tests passed!');
      
      // Show available endpoints for manual testing
      log('\n' + '-'.repeat(40), 'cyan');
      logInfo('Available endpoints for manual testing:');
      log('  Health Check: GET http://localhost:8787/', 'blue');
      log('  Sign Up: POST http://localhost:8787/api/auth/sign-up', 'blue');
      log('  Sign In: POST http://localhost:8787/api/auth/sign-in/email', 'blue');
      log('  Session: GET http://localhost:8787/api/auth/session', 'blue');
      log('  User Profile: GET http://localhost:8787/api/protected/user-profile', 'blue');
      log('  Dashboard: GET http://localhost:8787/api/protected/dashboard', 'blue');
      log('  Preferences: PUT http://localhost:8787/api/protected/preferences', 'blue');
      
      log('\n' + '-'.repeat(40), 'cyan');
      logInfo('To start the development server:');
      log('  cd apps/api && bun run dev', 'yellow');
      
      log('\n' + '-'.repeat(40), 'cyan');
      logInfo('To test with Postman/cURL:');
      log('  1. Start the dev server: bun run dev', 'yellow');
      log('  2. Sign up/in to get session cookie', 'yellow');
      log('  3. Use cookie in subsequent requests', 'yellow');
      
    } else {
      logError(`Tests failed with exit code ${code}`);
      
      log('\n' + '-'.repeat(40), 'cyan');
      logWarning('Troubleshooting tips:');
      log('  • Make sure all dependencies are installed: bun install', 'yellow');
      log('  • Check that TypeScript compiles: bun run build', 'yellow');
      log('  • Verify test setup is correct', 'yellow');
      log('  • Check for any missing environment variables', 'yellow');
    }
    
    process.exit(code);
  });
  
  jest.on('error', (error) => {
    logError(`Failed to start Jest: ${error.message}`);
    process.exit(1);
  });
}

// Show help if requested
if (flags.includes('--help') || flags.includes('-h')) {
  logHeader('API Test Runner Help');
  
  log('Usage:', 'bright');
  log('  node test-runner.js [test-type] [flags]');
  
  log('\nTest Types:', 'bright');
  log('  auth       Run authentication tests only');
  log('  utils      Run utility function tests only');
  log('  all        Run all tests (default)');
  
  log('\nFlags:', 'bright');
  log('  --watch      Run tests in watch mode');
  log('  --coverage   Generate test coverage report');
  log('  --verbose    Show detailed test output');
  log('  --help, -h   Show this help message');
  
  log('\nExamples:', 'bright');
  log('  node test-runner.js                    # Run all tests');
  log('  node test-runner.js auth               # Run auth tests only');
  log('  node test-runner.js --watch            # Watch mode');
  log('  node test-runner.js auth --coverage    # Auth tests with coverage');
  
  process.exit(0);
}

// Run the tests
runTests();
