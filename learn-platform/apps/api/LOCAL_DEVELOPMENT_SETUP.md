# Local Development Setup for Cloudflare Workers API

## Problem Solved

This document explains how we resolved the `BETTER_AUTH_SECRET not set, using default secret for development` error when running the Cloudflare Workers API locally.

## Root Cause

The error occurred because:

1. **better-auth** requires the `BETTER_AUTH_SECRET` environment variable for secure operations
2. In Cloudflare Workers, environment variables are accessed through the `env` binding, not `process.env`
3. The auth library was being initialized at module load time, before the Cloudflare Workers environment was available

## Solution Implemented

### 1. Created `.dev.vars` File

We created a `.dev.vars` file in the API directory with the local development secret:

```bash
# learn-platform/apps/api/.dev.vars
BETTER_AUTH_SECRET=DDqwBfv5/yP2XMLBDAiPlw6gQceiEYTey6e5tnvKZwg=
```

**Important**: This file is automatically ignored by git (`.env.*` is in `.gitignore`).

### 2. Updated Auth Library for Cloudflare Workers

Modified the auth library to support both Node.js and Cloudflare Workers environments:

- Added `createAuth(env)` function that accepts Cloudflare Workers environment bindings
- Made the default auth instance lazy-loaded to prevent initialization at module load time
- Updated session utilities to work with dynamic auth instances

### 3. Updated tRPC Router

Modified the tRPC router to use dynamic auth instances:

- Changed from static `auth` import to `createAuth(ctx.env)` calls
- This ensures the auth instance has access to the Cloudflare Workers environment

## How It Works

1. **Local Development**: Wrangler loads variables from `.dev.vars` and makes them available as `env.BETTER_AUTH_SECRET`
2. **Dynamic Auth Creation**: The tRPC procedures create auth instances with `createAuth(ctx.env)` 
3. **Environment Access**: The auth library can now access the secret from the Cloudflare Workers environment

## Verification

The solution is working correctly when:

1. ✅ No error message appears when starting `bun run dev`
2. ✅ Wrangler shows: `Using vars defined in .dev.vars`
3. ✅ API endpoints respond correctly (tested `/` and `/trpc/health`)

## Security Notes

- **Production**: Use `wrangler secret put BETTER_AUTH_SECRET` for production deployment
- **Local Development**: The `.dev.vars` file is ignored by git and safe for local secrets
- **Secret Generation**: Generated using `openssl rand -base64 32` for cryptographic security

## Files Modified

- `learn-platform/apps/api/.dev.vars` (created)
- `learn-platform/libs/auth/src/auth.ts` (updated for Cloudflare Workers support)
- `learn-platform/libs/auth/src/session.ts` (updated for dynamic auth instances)
- `learn-platform/libs/trpc/src/router.ts` (updated to use dynamic auth)
- `learn-platform/libs/trpc/src/context.ts` (updated to use Cloudflare Workers env)

## Best Practices Followed

1. **Environment-specific configuration**: Different approaches for local vs production
2. **Security**: Secrets are not committed to version control
3. **Compatibility**: Solution works in both Node.js and Cloudflare Workers environments
4. **Lazy loading**: Auth instances are created only when needed
