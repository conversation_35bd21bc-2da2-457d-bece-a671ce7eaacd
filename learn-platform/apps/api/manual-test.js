#!/usr/bin/env node

/**
 * Manual API Testing Script
 *
 * This script tests the API endpoints manually using fetch.
 * Run this while the development server is running.
 *
 * Usage:
 *   1. Start the dev server: bun run dev
 *   2. In another terminal: node manual-test.js
 */

const API_BASE = 'http://localhost:8787';

// ANSI colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logHeader(message) {
  log('\n' + '='.repeat(60), 'cyan');
  log(`  ${message}`, 'bright');
  log('='.repeat(60), 'cyan');
}

/**
 * Get environment variable with fallback
 */
function getEnvVar(key, fallback) {
  return process.env[key] || fallback;
}

// Test data - using environment variables for consistency
const TEST_USER = {
  email: getEnvVar('TEST_USER_EMAIL', '<EMAIL>'),
  password: getEnvVar('TEST_USER_PASSWORD', 'TestPassword123!'),
  name: 'Test User',
};

let sessionCookie = '';

// Helper function to make requests
async function makeRequest(endpoint, options = {}) {
  const url = `${API_BASE}${endpoint}`;
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  if (sessionCookie && !options.skipAuth) {
    headers['Cookie'] = sessionCookie;
  }

  try {
    const response = await fetch(url, {
      method: 'GET',
      ...options,
      headers,
    });

    // Extract cookies from response
    const setCookie = response.headers.get('Set-Cookie');
    if (setCookie && setCookie.includes('session')) {
      sessionCookie = setCookie;
    }

    const contentType = response.headers.get('Content-Type');
    let data;

    if (contentType && contentType.includes('application/json')) {
      data = await response.json();
    } else {
      data = await response.text();
    }

    return {
      status: response.status,
      data,
      headers: Object.fromEntries(response.headers.entries()),
    };
  } catch (error) {
    return {
      status: 0,
      data: { error: error.message },
      headers: {},
    };
  }
}

// Test functions
async function testHealthCheck() {
  logHeader('Health Check Test');

  const result = await makeRequest('/', { skipAuth: true });

  if (result.status === 200) {
    logSuccess('Health check passed');
    logInfo(`Response: ${JSON.stringify(result.data, null, 2)}`);
  } else {
    logError(`Health check failed: ${result.status}`);
    logInfo(`Response: ${JSON.stringify(result.data, null, 2)}`);
  }

  return result.status === 200;
}

async function testCORS() {
  logHeader('CORS Test');

  const result = await makeRequest('/', {
    skipAuth: true,
    headers: {
      'Origin': 'http://localhost:3000',
    },
  });

  const hasCORS = result.headers['access-control-allow-origin'] &&
                  result.headers['access-control-allow-credentials'] === 'true';

  if (hasCORS) {
    logSuccess('CORS headers present');
    logInfo(`Origin: ${result.headers['access-control-allow-origin']}`);
    logInfo(`Credentials: ${result.headers['access-control-allow-credentials']}`);
  } else {
    logError('CORS headers missing');
  }

  return hasCORS;
}

async function testAuthEndpoints() {
  logHeader('Authentication Endpoints Test');

  // Initialize result variables at function scope
  let signUpResult = null;
  let authSuccess = false;

  // Step 1: Try to sign in first (check if user already exists)
  logInfo('Step 1: Attempting sign in (checking if user exists)...');
  const signInResult = await makeRequest('/api/auth/sign-in/email', {
    method: 'POST',
    body: JSON.stringify({
      email: TEST_USER.email,
      password: TEST_USER.password,
    }),
    skipAuth: true,
  });

  if ([200, 201].includes(signInResult.status)) {
    logSuccess('Sign in successful - user already exists');
    logInfo(`User: ${signInResult.data.user?.email}`);
    if (sessionCookie) {
      logSuccess('Session cookie received');
    }
    authSuccess = true;
  } else if ([400, 401, 404].includes(signInResult.status)) {
    // User doesn't exist or wrong credentials, try to create user
    logInfo('Sign in failed - attempting to create user...');

    // Step 2: Try to sign up
    logInfo('Step 2: Creating new user...');
    signUpResult = await makeRequest('/api/auth/sign-up/email', {
      method: 'POST',
      body: JSON.stringify(TEST_USER),
      skipAuth: true,
    });

    if ([200, 201].includes(signUpResult.status)) {
      logSuccess('User created successfully');
      logInfo(`User: ${signUpResult.data.user?.email}`);

      // Step 3: Sign in after successful signup
      logInfo('Step 3: Signing in after registration...');
      const finalSignInResult = await makeRequest('/api/auth/sign-in/email', {
        method: 'POST',
        body: JSON.stringify({
          email: TEST_USER.email,
          password: TEST_USER.password,
        }),
        skipAuth: true,
      });

      if ([200, 201].includes(finalSignInResult.status)) {
        logSuccess('Sign in after registration successful');
        if (sessionCookie) {
          logSuccess('Session cookie received');
        }
        authSuccess = true;
      } else {
        logError(`Sign in after registration failed: ${finalSignInResult.status}`);
      }
    } else if (signUpResult.status === 400 && signUpResult.data?.error?.message?.includes('already exists')) {
      logInfo('User already exists, trying sign in again...');
      // User was created between our first signin attempt and signup attempt
      const retrySignInResult = await makeRequest('/api/auth/sign-in/email', {
        method: 'POST',
        body: JSON.stringify({
          email: TEST_USER.email,
          password: TEST_USER.password,
        }),
        skipAuth: true,
      });

      if ([200, 201].includes(retrySignInResult.status)) {
        logSuccess('Retry sign in successful');
        authSuccess = true;
      } else {
        logError(`Retry sign in failed: ${retrySignInResult.status}`);
      }
    } else {
      logError(`Sign up failed: ${signUpResult.status}`);
      logInfo(`Response: ${JSON.stringify(signUpResult.data, null, 2)}`);
    }
  } else {
    logError(`Unexpected sign in response: ${signInResult.status}`);
    logInfo(`Response: ${JSON.stringify(signInResult.data, null, 2)}`);
  }

  // Test session endpoint
  logInfo('Testing session endpoint...');
  const sessionResult = await makeRequest('/api/auth/session', { skipAuth: true });

  if ([200, 404].includes(sessionResult.status)) {
    logSuccess(`Session endpoint responding: ${sessionResult.status}`);
  } else {
    logError(`Session endpoint failed: ${sessionResult.status}`);
  }

  // Return true if authentication was successful and session endpoint is responding
  return authSuccess && sessionResult.status !== 0;
}

async function testProtectedRoutes() {
  logHeader('Protected Routes Test');

  const endpoints = [
    '/api/protected/user-profile',
    '/api/protected/dashboard',
    '/api/protected/session-info',
  ];

  let allResponding = true;

  for (const endpoint of endpoints) {
    logInfo(`Testing ${endpoint}...`);

    // Test without auth (should be 401 or 500)
    const unauthResult = await makeRequest(endpoint, { skipAuth: true });

    if ([401, 500].includes(unauthResult.status)) {
      logSuccess(`${endpoint} properly protected (${unauthResult.status})`);
    } else if (unauthResult.status === 404) {
      logError(`${endpoint} not found (404)`);
      allResponding = false;
    } else {
      logInfo(`${endpoint} returned ${unauthResult.status}`);
    }

    // Test with session cookie if available
    if (sessionCookie) {
      const authResult = await makeRequest(endpoint);
      logInfo(`With auth: ${authResult.status}`);
    }
  }

  // Test PUT endpoint
  logInfo('Testing preferences PUT endpoint...');
  const putResult = await makeRequest('/api/protected/preferences', {
    method: 'PUT',
    body: JSON.stringify({ theme: 'dark' }),
    skipAuth: true,
  });

  if ([400, 401, 500].includes(putResult.status)) {
    logSuccess(`Preferences endpoint responding: ${putResult.status}`);
  } else if (putResult.status === 404) {
    logError('Preferences endpoint not found (404)');
    allResponding = false;
  }

  return allResponding;
}

async function testTRPC() {
  logHeader('tRPC Integration Test');

  const result = await makeRequest('/trpc/health', { skipAuth: true });

  if (result.status !== 404) {
    logSuccess(`tRPC endpoint responding: ${result.status}`);
    return true;
  } else {
    logError('tRPC endpoint not found (404)');
    return false;
  }
}

async function testErrorHandling() {
  logHeader('Error Handling Test');

  // Test 404
  const notFoundResult = await makeRequest('/non-existent', { skipAuth: true });

  if (notFoundResult.status === 404) {
    logSuccess('404 handling works');
    if (notFoundResult.data && notFoundResult.data.error) {
      logSuccess('404 returns JSON error format');
    }
  } else {
    logError(`Expected 404, got ${notFoundResult.status}`);
  }

  // Test malformed JSON
  const malformedResult = await makeRequest('/api/auth/sign-in/email', {
    method: 'POST',
    body: 'invalid-json',
    skipAuth: true,
  });

  if (malformedResult.status >= 400) {
    logSuccess('Malformed JSON handled gracefully');
  } else {
    logError('Malformed JSON not handled properly');
  }

  return notFoundResult.status === 404;
}

// Main test runner
async function runTests() {
  logHeader('API Manual Testing Suite');
  logInfo(`Testing API at: ${API_BASE}`);
  logInfo(`Test user: ${TEST_USER.email}`);
  logInfo('Using robust authentication flow (signin first, then signup if needed)');

  const results = {
    health: await testHealthCheck(),
    cors: await testCORS(),
    auth: await testAuthEndpoints(),
    protected: await testProtectedRoutes(),
    trpc: await testTRPC(),
    errors: await testErrorHandling(),
  };

  logHeader('Test Results Summary');

  Object.entries(results).forEach(([test, passed]) => {
    if (passed) {
      logSuccess(`${test.toUpperCase()}: PASSED`);
    } else {
      logError(`${test.toUpperCase()}: FAILED`);
    }
  });

  const passedCount = Object.values(results).filter(Boolean).length;
  const totalCount = Object.keys(results).length;

  log(`\nOverall: ${passedCount}/${totalCount} tests passed`,
      passedCount === totalCount ? 'green' : 'yellow');

  if (passedCount === totalCount) {
    logSuccess('🎉 All tests passed! Your API is working correctly.');
  } else {
    logInfo('💡 Some tests failed. Check the output above for details.');
  }

  logHeader('Next Steps');
  logInfo('1. Fix any failing tests');
  logInfo('2. Test with Postman using the endpoints above');
  logInfo('3. Run unit tests: bun test');
  logInfo('4. Deploy when ready: bun run deploy');
}

// Check if server is running
async function checkServer() {
  try {
    await fetch(API_BASE);
    return true;
  } catch (error) {
    logError(`Cannot connect to ${API_BASE}`);
    logInfo('Make sure the development server is running:');
    logInfo('  cd apps/api && bun run dev');
    return false;
  }
}

// Run the tests
async function main() {
  const serverRunning = await checkServer();

  if (serverRunning) {
    await runTests();
  } else {
    process.exit(1);
  }
}

main().catch(console.error);
