{"name": "@learn-platform/api", "version": "0.0.1", "type": "module", "scripts": {"dev": "wrangler dev", "build": "tsc --noEmit", "test": "jest utils/validation.test.ts", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:manual": "node manual-test.js", "test:all": "bun test && node manual-test.js", "deploy": "wrangler deploy", "deploy:dev": "wrangler deploy --env dev", "deploy:prod": "wrangler deploy --env prod", "secrets:check": "./check-secrets.sh", "secrets:list:dev": "wrangler secret list --env dev", "secrets:list:prod": "wrangler secret list --env prod"}, "dependencies": {"@learn-platform/trpc": "workspace:*", "@trpc/server": "^11.0.0", "hono": "^4.6.0", "@hono/trpc-server": "^0.3.2", "@hono/zod-validator": "^0.7.0", "zod": "^3.23.8", "postgres": "^3.4.7", "drizzle-orm": "^0.44.2"}, "devDependencies": {"@cloudflare/workers-types": "^4.20241127.0", "wrangler": "^4.20.0", "typescript": "~5.7.2"}}