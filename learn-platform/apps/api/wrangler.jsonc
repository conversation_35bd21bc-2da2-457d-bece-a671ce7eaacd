{
  "name": "learn-platform-api",
  "main": "src/index.ts",
  "compatibility_date": "2024-12-01",
  // Account ID will be provided via CLOUDFLARE_ACCOUNT_ID environment variable
  "compatibility_flags": ["nodejs_compat"],

  // Development configuration
  "env": {
    "dev": {
      "name": "learn-platform-api-dev",
      "vars": {
        "BETTER_AUTH_URL": "https://kwaci-learning.bmbn.dev",
        "BETTER_AUTH_TRUSTED_ORIGINS": "https://kwaci-learning.bmbn.dev,https://learn-platform-api-dev.bm.workers.dev,https://learn-platform-web-preview.bm.workers.dev,https://learn-platform-admin-preview.bm.workers.dev"
      }
    },
    // Production configuration
    "prod": {
      "name": "learn-platform-api-prod",
      "vars": {
        "BETTER_AUTH_URL": "https://kwaci-learning.bmbn.dev",
        "BETTER_AUTH_TRUSTED_ORIGINS": "https://kwaci-learning.bmbn.dev,https://learn-platform-api-prod.bm.workers.dev,https://learn-platform-web-prod.bm.workers.dev,https://learn-platform-admin-prod.bm.workers.dev"
      }
      // Optional: Configure custom domains for production
      // "routes": [
      //   {
      //     "pattern": "api.yourdomain.com/*"
      //   }
      // ],
      // Note: Sensitive variables like DATABASE_URL and BETTER_AUTH_SECRET
      // should be set via Cloudflare Workers dashboard secrets, not here
      // Optional: KV namespaces (add as needed)
      // "kv_namespaces": [
      //   {
      //     "binding": "CACHE",
      //     "id": "your-kv-namespace-id"
      //   }
      // ]
    }
  },

  // Local development settings
  "dev": {
    "port": 8787,
    "local_protocol": "http"
  }
}
