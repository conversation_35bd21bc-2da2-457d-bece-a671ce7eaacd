-- Database Optimization Recommendations for Learning Content Filters
-- These indexes can be added to improve filter performance

-- 1. GIN Index for Tags Array (JSON)
-- Improves performance of tags filtering
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_content_tags_gin 
ON learning_content USING GIN (tags);

-- Usage: WHERE tags @> '["javascript"]'::jsonb

-- 2. Composite Index for Common Filter Combinations
-- Optimizes queries that filter by user + learning level + reading time
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_content_user_level_time
ON learning_content (user_id, learning_level, estimated_reading_time);

-- 3. Index for Date Range Filtering
-- Improves performance of date-based queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_content_created_at
ON learning_content (created_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_content_updated_at
ON learning_content (updated_at DESC);

-- 4. Full-Text Search Index
-- Enables fast text search across title and description
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_content_search_text
ON learning_content USING GIN (to_tsvector('english', title || ' ' || description));

-- Usage: WHERE to_tsvector('english', title || ' ' || description) @@ plainto_tsquery('search term')

-- 5. GIN Index for Steps JSON (Content Types)
-- Improves performance when filtering by content types within steps
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_content_steps_gin
ON learning_content USING GIN (steps);

-- Usage: WHERE steps::jsonb @@ '$.*.blocks[*] ? (@.type == "paragraph")'::jsonpath

-- 6. Partial Index for Public Content
-- Optimizes queries for public content discovery
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_content_public
ON learning_content (updated_at DESC) 
WHERE is_public = true;

-- 7. Composite Index for User Content with Visibility
-- Optimizes the main getMy query pattern
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_learning_content_user_visibility
ON learning_content (user_id, is_public, updated_at DESC);

-- Performance Analysis Queries
-- Use these to monitor index effectiveness

-- Check index usage
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_scan as index_scans,
    idx_tup_read as tuples_read,
    idx_tup_fetch as tuples_fetched
FROM pg_stat_user_indexes 
WHERE tablename = 'learning_content'
ORDER BY idx_scan DESC;

-- Check table statistics
SELECT 
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes,
    n_live_tup as live_tuples,
    n_dead_tup as dead_tuples,
    last_vacuum,
    last_autovacuum,
    last_analyze,
    last_autoanalyze
FROM pg_stat_user_tables 
WHERE relname = 'learning_content';

-- Analyze query performance
-- Run EXPLAIN ANALYZE on common filter queries to verify index usage

-- Example: Check if tags filter uses the GIN index
EXPLAIN ANALYZE 
SELECT * FROM learning_content 
WHERE user_id = 'test-user' 
AND tags @> '["javascript"]'::jsonb;

-- Example: Check if search uses the full-text index  
EXPLAIN ANALYZE
SELECT * FROM learning_content
WHERE user_id = 'test-user'
AND to_tsvector('english', title || ' ' || description) @@ plainto_tsquery('react');

-- Example: Check composite index usage
EXPLAIN ANALYZE
SELECT * FROM learning_content
WHERE user_id = 'test-user'
AND learning_level = 'beginner'
AND estimated_reading_time BETWEEN 10 AND 30
ORDER BY updated_at DESC;
