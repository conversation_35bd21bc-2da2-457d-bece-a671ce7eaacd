#!/bin/bash

# Script to check Cloudflare Workers secrets
# Run this from learn-platform/apps/api directory

echo "🔍 Checking Cloudflare Workers secrets..."
echo "=========================================="

# Check if wrangler is installed
if ! command -v wrangler &> /dev/null; then
    echo "❌ Wrangler CLI not found. Please install it first:"
    echo "   npm install -g wrangler"
    exit 1
fi

# Check if authenticated
echo "📋 Checking authentication..."
if ! wrangler whoami &> /dev/null; then
    echo "❌ Not authenticated with Cloudflare. Please run:"
    echo "   wrangler login"
    exit 1
fi

echo "✅ Authenticated with Cloudflare"
echo ""

# Function to check secrets for an environment
check_env_secrets() {
    local env=$1
    local worker_name=$2
    
    echo "🔐 Checking secrets for environment: $env"
    echo "   Worker name: $worker_name"
    echo "   ----------------------------------------"
    
    # List all secrets for this environment
    echo "📝 All secrets in $env environment:"
    if wrangler secret list --env $env 2>/dev/null; then
        echo ""
    else
        echo "❌ Failed to list secrets for $env environment"
        echo "   This might mean the worker doesn't exist yet or you don't have permissions"
        echo ""
        return 1
    fi
    
    # Check specific secrets we care about
    echo "🔍 Checking specific secrets:"
    
    # Check BETTER_AUTH_SECRET
    if wrangler secret list --env $env 2>/dev/null | grep -q "BETTER_AUTH_SECRET"; then
        echo "   ✅ BETTER_AUTH_SECRET: EXISTS"
    else
        echo "   ❌ BETTER_AUTH_SECRET: NOT FOUND"
    fi
    
    # Check DATABASE_URL
    if wrangler secret list --env $env 2>/dev/null | grep -q "DATABASE_URL"; then
        echo "   ✅ DATABASE_URL: EXISTS"
    else
        echo "   ❌ DATABASE_URL: NOT FOUND"
    fi
    
    echo ""
}

# Check development environment
check_env_secrets "dev" "learn-platform-api-dev"

# Check production environment  
check_env_secrets "prod" "learn-platform-api-prod"

echo "💡 Tips:"
echo "========="
echo "1. To add a secret:"
echo "   wrangler secret put SECRET_NAME --env dev"
echo ""
echo "2. To delete a secret:"
echo "   wrangler secret delete SECRET_NAME --env dev"
echo ""
echo "3. To view this worker in Cloudflare dashboard:"
echo "   https://dash.cloudflare.com/workers"
echo ""
echo "4. Secrets are encrypted and their values are never shown"
echo "   You can only see if they exist or not"
