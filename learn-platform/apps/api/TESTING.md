# API Testing Guide

This guide covers how to test the authentication and protected routes in the Learn Platform API.

## 🧪 **Testing Tools & Setup**

### **Tools Used**
- **Jest** - Testing framework (already configured)
- **Hono** - Web framework with built-in testing support
- **Better Auth** - Authentication system
- **TypeScript** - Type safety in tests

### **Test Files**
- `src/auth-integration.test.ts` - Complete authentication flow tests
- `src/utils/validation.test.ts` - Utility function tests
- `src/test-utils.ts` - Helper functions and utilities
- `test-runner.js` - Custom test runner script

## 🚀 **Running Tests**

### **Using npm/bun scripts:**
```bash
# Run all tests
bun test

# Run tests in watch mode
bun run test:watch

# Run tests with coverage
bun run test:coverage

# Run only authentication tests
bun run test:auth
```

### **Using the custom test runner:**
```bash
# Run all tests
node test-runner.js

# Run only auth tests
node test-runner.js auth

# Run with watch mode
node test-runner.js --watch

# Run with coverage
node test-runner.js --coverage

# Get help
node test-runner.js --help
```

### **Using Nx:**
```bash
# Run API tests
bunx nx test api

# Run tests with coverage
bunx nx test api --coverage

# Run tests in watch mode
bunx nx test api --watch
```

## 🔧 **Robust Authentication Testing**

### **Environment Variables**
All test scripts now use environment variables for consistent test credentials:

```bash
# Add to your .env.local file
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=TestPassword123!
```

### **Duplicate User Prevention**
The test scripts implement a robust authentication flow:

1. **Try Sign In First**: Attempt to sign in with existing credentials
2. **Sign Up If Needed**: Only create user if sign in fails
3. **Retry Sign In**: Sign in after successful user creation
4. **Handle Race Conditions**: Gracefully handle concurrent user creation

This approach eliminates duplicate user creation issues and makes tests more reliable.

### **Manual Test Script**
The `manual-test.js` script now uses this robust flow:

```bash
# Run the improved manual test
bun run test:manual
```

## 📋 **Test Coverage**

The test suite covers:

### **Authentication Flow**
- ✅ User sign up with valid data
- ✅ User sign in with valid credentials
- ✅ Session cookie handling
- ✅ Invalid credentials rejection
- ✅ Malformed request handling

### **Protected Routes**
- ✅ Access control (authenticated vs unauthenticated)
- ✅ User profile endpoint
- ✅ Dashboard data endpoint
- ✅ Preferences update with validation
- ✅ Session info endpoint

### **Error Scenarios**
- ✅ Invalid JSON handling
- ✅ Missing required fields
- ✅ Invalid email format
- ✅ 404 for non-existent endpoints

### **CORS & Security**
- ✅ CORS headers configuration
- ✅ Preflight OPTIONS requests
- ✅ Credential handling

## 🔧 **Manual Testing**

### **1. Start Development Server**
```bash
cd apps/api
bun run dev
```

### **2. Test with cURL**

#### **Sign Up**
```bash
curl -X POST http://localhost:8787/api/auth/sign-up \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"TestPassword123!","name":"Test User"}' \
  -c cookies.txt
```

#### **Sign In**
```bash
curl -X POST http://localhost:8787/api/auth/sign-in/email \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"TestPassword123!"}' \
  -c cookies.txt
```

#### **Test Protected Endpoint**
```bash
curl -X GET http://localhost:8787/api/protected/user-profile \
  -b cookies.txt
```

#### **Update Preferences**
```bash
curl -X PUT http://localhost:8787/api/protected/preferences \
  -H "Content-Type: application/json" \
  -d '{"theme":"dark","notifications":{"email":false}}' \
  -b cookies.txt
```

### **3. Test with Postman**

1. **Enable Cookie Management**: Settings → General → "Automatically manage cookies"

2. **Sign Up/Sign In**: 
   - POST to `http://localhost:8787/api/auth/sign-up` or `/api/auth/sign-in/email`
   - Cookies will be automatically saved

3. **Test Protected Routes**:
   - GET `http://localhost:8787/api/protected/user-profile`
   - GET `http://localhost:8787/api/protected/dashboard`
   - PUT `http://localhost:8787/api/protected/preferences`

## 🐛 **Debugging Tests**

### **Common Issues**

1. **Tests failing with "Cannot resolve module"**
   ```bash
   # Install dependencies
   bun install
   
   # Check TypeScript compilation
   bun run build
   ```

2. **Authentication tests failing**
   - Check that Better Auth is properly configured
   - Verify environment variables are set
   - Ensure database connection is working

3. **CORS tests failing**
   - Verify CORS configuration in `src/index.ts`
   - Check that `credentials: true` is set

### **Debug Mode**
```bash
# Run tests with verbose output
node test-runner.js --verbose

# Run specific test file
bunx jest auth-integration.test.ts --verbose
```

### **Test Environment Variables**
The tests use these mock environment variables:
```javascript
const mockEnv = {
  BETTER_AUTH_SECRET: 'test-secret-key-for-testing-only',
  BETTER_AUTH_URL: 'http://localhost:8787',
  DATABASE_URL: 'postgresql://test:test@localhost:5432/test',
};
```

## 📊 **Test Results**

### **Expected Test Output**
```
API Authentication Integration
  ✓ Health Check
    ✓ should return healthy status
  ✓ Authentication Flow
    ✓ should sign up a new user
    ✓ should sign in with valid credentials
    ✓ should reject sign in with invalid credentials
  ✓ Session Management
    ✓ should get current session when authenticated
    ✓ should return null session when not authenticated
  ✓ Protected Routes
    ✓ should block access without authentication
    ✓ should allow access with valid session
    ✓ should return dashboard data
    ✓ should update user preferences
    ✓ should validate preferences data
  ✓ Error Scenarios
    ✓ should handle malformed JSON
    ✓ should handle missing fields
    ✓ should return 404 for non-existent endpoints
  ✓ CORS Configuration
    ✓ should include CORS headers
    ✓ should handle preflight requests

Test Suites: 1 passed, 1 total
Tests:       15 passed, 15 total
```

## 🔗 **Available Endpoints**

| Endpoint | Method | Description | Auth Required |
|----------|--------|-------------|---------------|
| `/` | GET | Health check | No |
| `/api/auth/sign-up` | POST | Register user | No |
| `/api/auth/sign-in/email` | POST | Sign in | No |
| `/api/auth/session` | GET | Get session | No |
| `/api/auth/sign-out` | POST | Sign out | Yes |
| `/api/protected/user-profile` | GET | Get user profile | Yes |
| `/api/protected/dashboard` | GET | Get dashboard data | Yes |
| `/api/protected/preferences` | PUT | Update preferences | Yes |
| `/api/protected/session-info` | GET | Get session details | Yes |

## 🎯 **Next Steps**

1. **Add more test scenarios** for edge cases
2. **Integration with database** for real data testing
3. **Performance testing** for high load scenarios
4. **Security testing** for vulnerability assessment
5. **E2E testing** with real browser automation
