example one:
```
import React, { useState } from 'react';
import { ChevronRight, ChevronLeft, Brain, BookOpen, Zap, MessageSquare } from 'lucide-react';

const LLMExplanation = () => {
  const [currentStep, setCurrentStep] = useState(0);

  const steps = [
    {
      title: "What is an LLM?",
      icon: <Brain className="w-8 h-8 text-blue-500" />,
      content: (
        <div className="space-y-4">
          <p className="text-lg">A Large Language Model (LLM) is like a super-smart autocomplete system that has read millions of books, articles, and websites.</p>
          <div className="bg-blue-50 p-4 rounded-lg">
            <p className="font-semibold text-blue-800">Think of it like:</p>
            <p className="text-blue-700">A person who has read everything on the internet and can predict what word comes next in any sentence, but at an incredibly sophisticated level.</p>
          </div>
        </div>
      )
    },
    {
      title: "Training: Learning from Text",
      icon: <BookOpen className="w-8 h-8 text-green-500" />,
      content: (
        <div className="space-y-4">
          <p className="text-lg">LLMs learn by reading massive amounts of text and playing a guessing game:</p>
          <div className="bg-green-50 p-4 rounded-lg space-y-2">
            <p><strong>Step 1:</strong> Show the AI text with some words hidden</p>
            <p><strong>Step 2:</strong> AI tries to guess the missing words</p>
            <p><strong>Step 3:</strong> Check if the guess was right</p>
            <p><strong>Step 4:</strong> Adjust the AI's "brain" to get better</p>
            <p><strong>Repeat:</strong> Billions of times with different texts!</p>
          </div>
          <p className="text-sm text-gray-600">This process teaches the AI patterns in language, facts about the world, and how to communicate.</p>
        </div>
      )
    },
    {
      title: "The Neural Network Brain",
      icon: <Zap className="w-8 h-8 text-purple-500" />,
      content: (
        <div className="space-y-4">
          <p className="text-lg">Inside an LLM is a "neural network" - like a digital brain made of math:</p>
          <div className="bg-purple-50 p-4 rounded-lg">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div className="bg-white p-3 rounded border">
                <div className="font-semibold text-purple-800">Input Layer</div>
                <div className="text-sm">Converts words to numbers</div>
              </div>
              <div className="bg-white p-3 rounded border">
                <div className="font-semibold text-purple-800">Hidden Layers</div>
                <div className="text-sm">Processes patterns (billions of connections!)</div>
              </div>
              <div className="bg-white p-3 rounded border">
                <div className="font-semibold text-purple-800">Output Layer</div>
                <div className="text-sm">Predicts next word</div>
              </div>
            </div>
          </div>
          <p className="text-sm text-gray-600">Each "neuron" is like a tiny decision-maker that says "based on what I see, this word is likely/unlikely to come next."</p>
        </div>
      )
    },
    {
      title: "How It Responds to You",
      icon: <MessageSquare className="w-8 h-8 text-orange-500" />,
      content: (
        <div className="space-y-4">
          <p className="text-lg">When you ask a question, here's what happens:</p>
          <div className="bg-orange-50 p-4 rounded-lg space-y-3">
            <div className="flex items-start space-x-3">
              <div className="bg-orange-200 rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">1</div>
              <p><strong>Tokenization:</strong> Your text gets broken into pieces (words/parts of words)</p>
            </div>
            <div className="flex items-start space-x-3">
              <div className="bg-orange-200 rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">2</div>
              <p><strong>Context Understanding:</strong> The AI looks at all the pieces together to understand meaning</p>
            </div>
            <div className="flex items-start space-x-3">
              <div className="bg-orange-200 rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">3</div>
              <p><strong>Generation:</strong> It predicts the best next word, then the next, then the next...</p>
            </div>
            <div className="flex items-start space-x-3">
              <div className="bg-orange-200 rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold">4</div>
              <p><strong>Output:</strong> All those predicted words become your answer!</p>
            </div>
          </div>
        </div>
      )
    },
    {
      title: "Key Insights",
      icon: <Brain className="w-8 h-8 text-red-500" />,
      content: (
        <div className="space-y-4">
          <p className="text-lg">Important things to understand about LLMs:</p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-red-50 p-4 rounded-lg">
              <h4 className="font-semibold text-red-800 mb-2">What They're Good At:</h4>
              <ul className="text-sm space-y-1 text-red-700">
                <li>• Pattern recognition in text</li>
                <li>• Generating human-like responses</li>
                <li>• Understanding context</li>
                <li>• Creative writing and analysis</li>
              </ul>
            </div>
            <div className="bg-red-50 p-4 rounded-lg">
              <h4 className="font-semibold text-red-800 mb-2">Limitations:</h4>
              <ul className="text-sm space-y-1 text-red-700">
                <li>• No real understanding (just patterns)</li>
                <li>• Can make confident-sounding mistakes</li>
                <li>• Knowledge has a cutoff date</li>
                <li>• Can't browse the internet (usually)</li>
              </ul>
            </div>
          </div>
          <div className="bg-gray-100 p-4 rounded-lg">
            <p className="text-center font-semibold">Remember: LLMs are incredibly sophisticated pattern-matching systems, not truly "thinking" beings!</p>
          </div>
        </div>
      )
    }
  ];

  const nextStep = () => {
    setCurrentStep((prev) => (prev + 1) % steps.length);
  };

  const prevStep = () => {
    setCurrentStep((prev) => (prev - 1 + steps.length) % steps.length);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">How AI Large Language Models Work</h1>
        <p className="text-gray-600">A simple, visual explanation</p>
      </div>

      {/* Progress indicator */}
      <div className="flex justify-center mb-8">
        <div className="flex space-x-2">
          {steps.map((_, index) => (
            <div
              key={index}
              className={`w-3 h-3 rounded-full cursor-pointer transition-colors ${
                index === currentStep ? 'bg-blue-500' : 'bg-gray-300'
              }`}
              onClick={() => setCurrentStep(index)}
            />
          ))}
        </div>
      </div>

      {/* Current step */}
      <div className="bg-white rounded-xl shadow-lg p-8 min-h-96">
        <div className="flex items-center justify-center mb-6">
          {steps[currentStep].icon}
          <h2 className="text-2xl font-bold ml-3 text-gray-800">{steps[currentStep].title}</h2>
        </div>
        
        <div className="mb-8">
          {steps[currentStep].content}
        </div>
      </div>

      {/* Navigation */}
      <div className="flex justify-between items-center mt-8">
        <button
          onClick={prevStep}
          className="flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
        >
          <ChevronLeft className="w-4 h-4 mr-1" />
          Previous
        </button>
        
        <span className="text-gray-500">
          {currentStep + 1} of {steps.length}
        </span>
        
        <button
          onClick={nextStep}
          className="flex items-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
        >
          Next
          <ChevronRight className="w-4 h-4 ml-1" />
        </button>
      </div>
    </div>
  );
};

export default LLMExplanation;
```
example two:
```
import React, { useState } from 'react';
import { ChevronRight, ChevronLeft, MapPin, Calculator, Lightbulb, Search, Brain } from 'lucide-react';

const EmbeddingsExplanation = () => {
  const [currentStep, setCurrentStep] = useState(0);

  const steps = [
    {
      title: "The Problem: Computers Don't Understand Words",
      icon: <Calculator className="w-8 h-8 text-red-500" />,
      content: (
        <div className="space-y-4">
          <p className="text-lg">Computers only understand numbers, but humans communicate with words. This creates a problem!</p>
          
          <div className="grid grid-cols-2 gap-6">
            <div className="bg-red-50 p-4 rounded-lg">
              <h4 className="font-semibold text-red-800 mb-3">What Humans See:</h4>
              <div className="space-y-2">
                <div className="bg-white p-2 rounded border">"cat"</div>
                <div className="bg-white p-2 rounded border">"dog"</div>
                <div className="bg-white p-2 rounded border">"happy"</div>
                <div className="bg-white p-2 rounded border">"king"</div>
              </div>
              <p className="text-sm mt-2 text-red-700">Words with meaning and relationships</p>
            </div>
            
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-semibold text-blue-800 mb-3">What Computers See:</h4>
              <div className="space-y-2">
                <div className="bg-white p-2 rounded border font-mono">??? ??? ???</div>
                <div className="bg-white p-2 rounded border font-mono">??? ??? ???</div>
                <div className="bg-white p-2 rounded border font-mono">??? ??? ??? ??? ???</div>
                <div className="bg-white p-2 rounded border font-mono">??? ??? ??? ???</div>
              </div>
              <p className="text-sm mt-2 text-blue-700">Just random characters with no meaning</p>
            </div>
          </div>

          <div className="bg-yellow-50 p-4 rounded-lg">
            <p className="font-semibold text-yellow-800">The Challenge:</p>
            <p className="text-yellow-700">How do we translate words into numbers while preserving their meaning and relationships?</p>
          </div>
        </div>
      )
    },
    {
      title: "The Solution: Embeddings as Coordinates",
      icon: <MapPin className="w-8 h-8 text-green-500" />,
      content: (
        <div className="space-y-4">
          <p className="text-lg">Embeddings turn words into coordinates in a high-dimensional space, like GPS coordinates for meaning!</p>
          
          <div className="bg-green-50 p-4 rounded-lg">
            <h4 className="font-semibold text-green-800 mb-3">Think of it like a map:</h4>
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-white p-4 rounded border">
                <h5 className="font-semibold mb-2">Real World GPS:</h5>
                <p className="text-sm">New York: (40.7128, -74.0060)</p>
                <p className="text-sm">London: (51.5074, -0.1278)</p>
                <p className="text-sm">Close cities = similar coordinates</p>
              </div>
              <div className="bg-white p-4 rounded border">
                <h5 className="font-semibold mb-2">Word Embeddings:</h5>
                <p className="text-sm">"cat": [0.2, -0.5, 0.8, ...]</p>
                <p className="text-sm">"dog": [0.3, -0.4, 0.7, ...]</p>
                <p className="text-sm">Similar words = similar numbers</p>
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border-2 border-green-200">
            <h4 className="font-semibold text-center mb-4">2D Visualization (Real embeddings use 100-1000+ dimensions!)</h4>
            <div className="relative bg-gray-50 h-64 rounded">
              {/* Simple 2D scatter plot visualization */}
              <div className="absolute" style={{left: '20%', top: '30%'}}>
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <div className="text-xs mt-1">cat</div>
              </div>
              <div className="absolute" style={{left: '25%', top: '35%'}}>
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <div className="text-xs mt-1">dog</div>
              </div>
              <div className="absolute" style={{left: '70%', top: '20%'}}>
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <div className="text-xs mt-1">happy</div>
              </div>
              <div className="absolute" style={{left: '75%', top: '25%'}}>
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <div className="text-xs mt-1">joy</div>
              </div>
              <div className="absolute" style={{left: '45%', top: '70%'}}>
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <div className="text-xs mt-1">king</div>
              </div>
              <div className="absolute" style={{left: '55%', top: '75%'}}>
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <div className="text-xs mt-1">queen</div>
              </div>
            </div>
            <p className="text-sm text-gray-600 mt-2 text-center">Similar words cluster together in the embedding space</p>
          </div>
        </div>
      )
    },
    {
      title: "How Are Embeddings Created?",
      icon: <Lightbulb className="w-8 h-8 text-purple-500" />,
      content: (
        <div className="space-y-4">
          <p className="text-lg">AI learns embeddings by looking at how words are used together in millions of sentences.</p>
          
          <div className="bg-purple-50 p-4 rounded-lg space-y-4">
            <h4 className="font-semibold text-purple-800">The Learning Process:</h4>
            
            <div className="bg-white p-4 rounded border">
              <h5 className="font-semibold mb-2">Step 1: Read lots of text</h5>
              <div className="text-sm space-y-1">
                <p>"The <strong>cat</strong> sat on the mat"</p>
                <p>"I have a pet <strong>cat</strong> named Whiskers"</p>
                <p>"The <strong>dog</strong> barked at the mailman"</p>
                <p>"My <strong>dog</strong> loves to fetch balls"</p>
              </div>
            </div>

            <div className="bg-white p-4 rounded border">
              <h5 className="font-semibold mb-2">Step 2: Learn patterns</h5>
              <p className="text-sm">AI notices that "cat" and "dog" often appear in similar contexts (pets, animals, homes) so they should have similar embeddings.</p>
            </div>

            <div className="bg-white p-4 rounded border">
              <h5 className="font-semibold mb-2">Step 3: Adjust numbers</h5>
              <p className="text-sm">The AI adjusts the coordinate numbers so that words used in similar ways end up close to each other in the embedding space.</p>
            </div>
          </div>

          <div className="bg-yellow-50 p-4 rounded-lg">
            <p className="font-semibold text-yellow-800">Key Insight:</p>
            <p className="text-yellow-700">"You shall know a word by the company it keeps" - words that appear in similar contexts get similar embeddings!</p>
          </div>
        </div>
      )
    },
    {
      title: "Amazing Properties of Embeddings",
      icon: <Search className="w-8 h-8 text-blue-500" />,
      content: (
        <div className="space-y-4">
          <p className="text-lg">Embeddings capture incredible relationships and can do "word math"!</p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-semibold text-blue-800 mb-3">Word Math Examples:</h4>
              <div className="space-y-2 text-sm">
                <div className="bg-white p-2 rounded">
                  <strong>King - Man + Woman = Queen</strong>
                </div>
                <div className="bg-white p-2 rounded">
                  <strong>Paris - France + Italy = Rome</strong>
                </div>
                <div className="bg-white p-2 rounded">
                  <strong>Walking - Walk + Swim = Swimming</strong>
                </div>
              </div>
              <p className="text-xs mt-2 text-blue-700">The math actually works with the coordinate numbers!</p>
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-semibold text-green-800 mb-3">What They Capture:</h4>
              <ul className="text-sm space-y-1 text-green-700">
                <li>• <strong>Similarity:</strong> "happy" ≈ "joyful"</li>
                <li>• <strong>Categories:</strong> fruits, animals, colors</li>
                <li>• <strong>Relationships:</strong> plural/singular, verb tenses</li>
                <li>• <strong>Context:</strong> "bank" (river) vs "bank" (money)</li>
                <li>• <strong>Analogies:</strong> hot:cold :: big:small</li>
              </ul>
            </div>
          </div>

          <div className="bg-orange-50 p-4 rounded-lg">
            <h4 className="font-semibold text-orange-800 mb-2">Real-World Applications:</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p><strong>Search engines:</strong> Find similar documents</p>
                <p><strong>Recommendations:</strong> "People who liked X also liked Y"</p>
              </div>
              <div>
                <p><strong>Translation:</strong> Match words across languages</p>
                <p><strong>Chatbots:</strong> Understand what you really mean</p>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      title: "Embeddings in Modern AI",
      icon: <Brain className="w-8 h-8 text-indigo-500" />,
      content: (
        <div className="space-y-4">
          <p className="text-lg">Today's AI systems use embeddings everywhere - they're the foundation of how AI understands language!</p>
          
          <div className="bg-indigo-50 p-4 rounded-lg space-y-4">
            <h4 className="font-semibold text-indigo-800">In Large Language Models (like me!):</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-white p-3 rounded border text-center">
                <div className="font-semibold text-indigo-800">Input</div>
                <div className="text-xs mt-1">Your words → embeddings</div>
              </div>
              <div className="bg-white p-3 rounded border text-center">
                <div className="font-semibold text-indigo-800">Processing</div>
                <div className="text-xs mt-1">AI works with embeddings</div>
              </div>
              <div className="bg-white p-3 rounded border text-center">
                <div className="font-semibold text-indigo-800">Output</div>
                <div className="text-xs mt-1">Embeddings → words</div>
              </div>
            </div>
          </div>

          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-semibold mb-2">Types of Embeddings:</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p><strong>Word embeddings:</strong> Individual words</p>
                <p><strong>Sentence embeddings:</strong> Entire sentences</p>
              </div>
              <div>
                <p><strong>Image embeddings:</strong> Pictures as numbers</p>
                <p><strong>Multimodal:</strong> Text + images together</p>
              </div>
            </div>
          </div>

          <div className="bg-green-100 p-4 rounded-lg">
            <div className="text-center">
              <p className="font-semibold text-green-800 text-lg">The Big Picture</p>
              <p className="text-green-700 mt-2">Embeddings are how AI bridges the gap between human language and computer math. They turn meaning into numbers while preserving relationships!</p>
            </div>
          </div>
        </div>
      )
    }
  ];

  const nextStep = () => {
    setCurrentStep((prev) => (prev + 1) % steps.length);
  };

  const prevStep = () => {
    setCurrentStep((prev) => (prev - 1 + steps.length) % steps.length);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">What Are Embeddings?</h1>
        <p className="text-gray-600">How AI turns words into numbers while keeping their meaning</p>
      </div>

      {/* Progress indicator */}
      <div className="flex justify-center mb-8">
        <div className="flex space-x-2">
          {steps.map((_, index) => (
            <div
              key={index}
              className={`w-3 h-3 rounded-full cursor-pointer transition-colors ${
                index === currentStep ? 'bg-blue-500' : 'bg-gray-300'
              }`}
              onClick={() => setCurrentStep(index)}
            />
          ))}
        </div>
      </div>

      {/* Current step */}
      <div className="bg-white rounded-xl shadow-lg p-8 min-h-96">
        <div className="flex items-center justify-center mb-6">
          {steps[currentStep].icon}
          <h2 className="text-2xl font-bold ml-3 text-gray-800">{steps[currentStep].title}</h2>
        </div>
        
        <div className="mb-8">
          {steps[currentStep].content}
        </div>
      </div>

      {/* Navigation */}
      <div className="flex justify-between items-center mt-8">
        <button
          onClick={prevStep}
          className="flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
        >
          <ChevronLeft className="w-4 h-4 mr-1" />
          Previous
        </button>
        
        <span className="text-gray-500">
          {currentStep + 1} of {steps.length}
        </span>
        
        <button
          onClick={nextStep}
          className="flex items-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
        >
          Next
          <ChevronRight className="w-4 h-4 ml-1" />
        </button>
      </div>
    </div>
  );
};

export default EmbeddingsExplanation;
```
example three:
```
import React, { useState } from 'react';
import { ChevronRight, ChevronLeft, TrendingUp, DollarSign, ShoppingCart, Scale, AlertTriangle } from 'lucide-react';

const InflationExplanation = () => {
  const [currentStep, setCurrentStep] = useState(0);

  const steps = [
    {
      title: "What Is Inflation?",
      icon: <TrendingUp className="w-8 h-8 text-red-500" />,
      content: (
        <div className="space-y-4">
          <p className="text-lg">Inflation is when the prices of goods and services go up over time, making your money worth less.</p>
          
          <div className="bg-red-50 p-4 rounded-lg">
            <h4 className="font-semibold text-red-800 mb-3">Simple Example:</h4>
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-white p-4 rounded border">
                <h5 className="font-semibold text-green-600">2020</h5>
                <p className="text-2xl">🍕 = $10</p>
                <p className="text-sm">Your $100 buys 10 pizzas</p>
              </div>
              <div className="bg-white p-4 rounded border">
                <h5 className="font-semibold text-red-600">2024</h5>
                <p className="text-2xl">🍕 = $12</p>
                <p className="text-sm">Your $100 buys only 8.3 pizzas</p>
              </div>
            </div>
          </div>

          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-800">Key Point:</h4>
            <p className="text-blue-700">Same amount of money → Less purchasing power</p>
            <p className="text-blue-700">This is why your grandparents talk about buying candy for 5 cents!</p>
          </div>

          <div className="bg-yellow-50 p-4 rounded-lg">
            <h4 className="font-semibold text-yellow-800">Normal vs. Problem:</h4>
            <p className="text-yellow-700"><strong>2-3% per year:</strong> Healthy, normal inflation</p>
            <p className="text-yellow-700"><strong>5%+ per year:</strong> Problematic, hurts people's budgets</p>
          </div>
        </div>
      )
    },
    {
      title: "What Causes Inflation?",
      icon: <Scale className="w-8 h-8 text-purple-500" />,
      content: (
        <div className="space-y-4">
          <p className="text-lg">Inflation happens when there's an imbalance between supply and demand, or when there's more money chasing the same goods.</p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-purple-50 p-4 rounded-lg">
              <h4 className="font-semibold text-purple-800 mb-3">Demand-Pull Inflation</h4>
              <div className="bg-white p-3 rounded border mb-2">
                <p className="text-sm"><strong>What happens:</strong> Everyone wants to buy more stuff</p>
                <p className="text-sm"><strong>Result:</strong> Prices go up because demand exceeds supply</p>
              </div>
              <p className="text-xs text-purple-700">Like concert tickets - high demand = high prices</p>
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-semibold text-green-800 mb-3">Cost-Push Inflation</h4>
              <div className="bg-white p-3 rounded border mb-2">
                <p className="text-sm"><strong>What happens:</strong> It costs more to make things</p>
                <p className="text-sm"><strong>Result:</strong> Companies raise prices to maintain profits</p>
              </div>
              <p className="text-xs text-green-700">Like gas prices rising → everything costs more to transport</p>
            </div>
          </div>

          <div className="bg-orange-50 p-4 rounded-lg">
            <h4 className="font-semibold text-orange-800 mb-3">Real-World Triggers:</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p>• <strong>Too much money printing</strong> by governments</p>
                <p>• <strong>Supply chain disruptions</strong> (like COVID-19)</p>
                <p>• <strong>Energy price spikes</strong> (oil, gas)</p>
              </div>
              <div>
                <p>• <strong>Strong economic growth</strong> (more spending)</p>
                <p>• <strong>Low unemployment</strong> (higher wages)</p>
                <p>• <strong>Natural disasters</strong> affecting production</p>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      title: "The Money Supply Connection",
      icon: <DollarSign className="w-8 h-8 text-green-500" />,
      content: (
        <div className="space-y-4">
          <p className="text-lg">One major cause of inflation is when there's more money in circulation without more goods to buy.</p>
          
          <div className="bg-green-50 p-4 rounded-lg">
            <h4 className="font-semibold text-green-800 mb-3">The Simple Equation:</h4>
            <div className="text-center bg-white p-4 rounded border text-lg">
              <p><strong>More Money + Same Goods = Higher Prices</strong></p>
            </div>
          </div>

          <div className="bg-blue-50 p-4 rounded-lg space-y-3">
            <h4 className="font-semibold text-blue-800">Think of it like an auction:</h4>
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-white p-3 rounded border">
                <h5 className="font-semibold text-sm">Scenario A:</h5>
                <p className="text-xs">5 people bidding on 1 car</p>
                <p className="text-xs">Each has $10,000</p>
                <p className="text-xs font-semibold">Winning bid: ~$10,000</p>
              </div>
              <div className="bg-white p-3 rounded border">
                <h5 className="font-semibold text-sm">Scenario B:</h5>
                <p className="text-xs">5 people bidding on 1 car</p>
                <p className="text-xs">Each has $20,000</p>
                <p className="text-xs font-semibold">Winning bid: ~$20,000</p>
              </div>
            </div>
            <p className="text-xs text-blue-700">Same car, more money available = higher price!</p>
          </div>

          <div className="bg-yellow-50 p-4 rounded-lg">
            <h4 className="font-semibold text-yellow-800 mb-2">How Governments Create Money:</h4>
            <ul className="text-sm space-y-1 text-yellow-700">
              <li>• <strong>Printing money</strong> (literally or digitally)</li>
              <li>• <strong>Lowering interest rates</strong> (makes borrowing cheaper)</li>
              <li>• <strong>Stimulus payments</strong> (putting cash in people's pockets)</li>
              <li>• <strong>Quantitative easing</strong> (buying bonds to inject money)</li>
            </ul>
          </div>
        </div>
      )
    },
    {
      title: "How Inflation Affects You",
      icon: <ShoppingCart className="w-8 h-8 text-blue-500" />,
      content: (
        <div className="space-y-4">
          <p className="text-lg">Inflation touches every part of your financial life - some effects are obvious, others are sneaky.</p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-red-50 p-4 rounded-lg">
              <h4 className="font-semibold text-red-800 mb-3">😟 The Bad Effects:</h4>
              <div className="space-y-2 text-sm">
                <div className="bg-white p-2 rounded">
                  <strong>Groceries cost more</strong> - Same cart, higher bill
                </div>
                <div className="bg-white p-2 rounded">
                  <strong>Rent increases</strong> - Housing gets more expensive
                </div>
                <div className="bg-white p-2 rounded">
                  <strong>Savings lose value</strong> - $1000 in the bank buys less next year
                </div>
                <div className="bg-white p-2 rounded">
                  <strong>Fixed incomes hurt</strong> - Retirees on pensions struggle
                </div>
              </div>
            </div>
            
            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-semibold text-green-800 mb-3">😊 Some Benefits:</h4>
              <div className="space-y-2 text-sm">
                <div className="bg-white p-2 rounded">
                  <strong>Debt becomes cheaper</strong> - Your mortgage is easier to pay with inflated dollars
                </div>
                <div className="bg-white p-2 rounded">
                  <strong>Wages often rise</strong> - (Though sometimes not as fast as prices)
                </div>
                <div className="bg-white p-2 rounded">
                  <strong>Assets increase in value</strong> - Houses, stocks often go up
                </div>
                <div className="bg-white p-2 rounded">
                  <strong>Economic growth signal</strong> - Often means economy is doing well
                </div>
              </div>
            </div>
          </div>

          <div className="bg-purple-50 p-4 rounded-lg">
            <h4 className="font-semibold text-purple-800 mb-3">Real Example - Your Monthly Budget:</h4>
            <div className="overflow-x-auto">
              <table className="w-full text-sm bg-white rounded">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="p-2 text-left">Expense</th>
                    <th className="p-2">2020</th>
                    <th className="p-2">2024</th>
                    <th className="p-2">% Change</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-t">
                    <td className="p-2">Rent</td>
                    <td className="p-2">$1,200</td>
                    <td className="p-2">$1,400</td>
                    <td className="p-2 text-red-600">+17%</td>
                  </tr>
                  <tr className="border-t">
                    <td className="p-2">Groceries</td>
                    <td className="p-2">$400</td>
                    <td className="p-2">$500</td>
                    <td className="p-2 text-red-600">+25%</td>
                  </tr>
                  <tr className="border-t">
                    <td className="p-2">Gas</td>
                    <td className="p-2">$150</td>
                    <td className="p-2">$200</td>
                    <td className="p-2 text-red-600">+33%</td>
                  </tr>
                  <tr className="border-t font-semibold">
                    <td className="p-2">Total</td>
                    <td className="p-2">$1,750</td>
                    <td className="p-2">$2,100</td>
                    <td className="p-2 text-red-600">+20%</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )
    },
    {
      title: "Fighting Inflation",
      icon: <AlertTriangle className="w-8 h-8 text-orange-500" />,
      content: (
        <div className="space-y-4">
          <p className="text-lg">Governments and central banks have tools to control inflation, though they don't always work perfectly or quickly.</p>
          
          <div className="bg-orange-50 p-4 rounded-lg space-y-4">
            <h4 className="font-semibold text-orange-800">The Federal Reserve's Toolkit:</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-white p-4 rounded border">
                <h5 className="font-semibold text-orange-700 mb-2">📈 Raise Interest Rates</h5>
                <p className="text-sm mb-2"><strong>How it works:</strong> Makes borrowing expensive, saving attractive</p>
                <p className="text-sm"><strong>Result:</strong> People spend less, demand drops, prices stabilize</p>
                <p className="text-xs text-gray-600 mt-1">Side effect: Can cause recession</p>
              </div>
              
              <div className="bg-white p-4 rounded border">
                <h5 className="font-semibold text-orange-700 mb-2">💰 Reduce Money Supply</h5>
                <p className="text-sm mb-2"><strong>How it works:</strong> Stop printing money, sell government bonds</p>
                <p className="text-sm"><strong>Result:</strong> Less money in circulation = less bidding power</p>
                <p className="text-xs text-gray-600 mt-1">Takes time to work</p>
              </div>
            </div>
          </div>

          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-3">Government Actions:</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <p>• <strong>Reduce spending</strong> (less money in economy)</p>
                <p>• <strong>Increase taxes</strong> (takes money out of circulation)</p>
                <p>• <strong>Fix supply chains</strong> (more goods available)</p>
              </div>
              <div>
                <p>• <strong>Strategic reserves</strong> (release oil, grain stocks)</p>
                <p>• <strong>Trade policies</strong> (reduce import tariffs)</p>
                <p>• <strong>Price controls</strong> (rarely used, often backfire)</p>
              </div>
            </div>
          </div>

          <div className="bg-yellow-50 p-4 rounded-lg">
            <h4 className="font-semibold text-yellow-800 mb-2">The Balancing Act:</h4>
            <p className="text-sm text-yellow-700">Fighting inflation is tricky because the tools that stop inflation can also:</p>
            <ul className="text-sm text-yellow-700 mt-2 space-y-1">
              <li>• Increase unemployment</li>
              <li>• Slow economic growth</li>
              <li>• Cause stock market drops</li>
              <li>• Make it harder for businesses to get loans</li>
            </ul>
            <p className="text-sm text-yellow-700 mt-2 font-semibold">It's like trying to cool down a room without making it too cold!</p>
          </div>
        </div>
      )
    },
    {
      title: "Protecting Yourself",
      icon: <ShoppingCart className="w-8 h-8 text-green-500" />,
      content: (
        <div className="space-y-4">
          <p className="text-lg">While you can't control inflation, you can take steps to protect your money and financial well-being.</p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-green-50 p-4 rounded-lg">
              <h4 className="font-semibold text-green-800 mb-3">💰 Investment Strategies:</h4>
              <div className="space-y-2 text-sm">
                <div className="bg-white p-2 rounded">
                  <strong>Real estate</strong> - Often rises with inflation
                </div>
                <div className="bg-white p-2 rounded">
                  <strong>Stocks</strong> - Companies can raise prices too
                </div>
                <div className="bg-white p-2 rounded">
                  <strong>TIPS bonds</strong> - Government bonds that adjust for inflation
                </div>
                <div className="bg-white p-2 rounded">
                  <strong>Commodities</strong> - Gold, oil, agricultural products
                </div>
              </div>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="font-semibold text-blue-800 mb-3">🛒 Smart Spending:</h4>
              <div className="space-y-2 text-sm">
                <div className="bg-white p-2 rounded">
                  <strong>Buy durable goods early</strong> - Get the car/appliance before prices rise more
                </div>
                <div className="bg-white p-2 rounded">
                  <strong>Lock in rates</strong> - Fixed mortgages protect against rising rates
                </div>
                <div className="bg-white p-2 rounded">
                  <strong>Bulk buy staples</strong> - Non-perishables when on sale
                </div>
                <div className="bg-white p-2 rounded">
                  <strong>Negotiate salary</strong> - Ask for raises to keep up
                </div>
              </div>
            </div>
          </div>

          <div className="bg-purple-50 p-4 rounded-lg">
            <h4 className="font-semibold text-purple-800 mb-3">⚠️ What NOT to Do:</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="space-y-1">
                <p>❌ <strong>Keep all money in savings</strong> - It loses value</p>
                <p>❌ <strong>Take on too much debt</strong> - Rates might rise</p>
                <p>❌ <strong>Panic buy everything</strong> - Can worsen inflation</p>
              </div>
              <div className="space-y-1">
                <p>❌ <strong>Ignore your budget</strong> - Track rising costs</p>
                <p>❌ <strong>Assume it's temporary</strong> - Plan for persistence</p>
                <p>❌ <strong>Make major financial decisions in panic</strong></p>
              </div>
            </div>
          </div>

          <div className="bg-gray-100 p-4 rounded-lg">
            <div className="text-center">
              <p className="font-semibold text-gray-800 text-lg">Remember</p>
              <p className="text-gray-700 mt-2">Inflation is a normal part of a healthy economy. The key is understanding it and adapting your financial strategy accordingly.</p>
            </div>
          </div>
        </div>
      )
    }
  ];

  const nextStep = () => {
    setCurrentStep((prev) => (prev + 1) % steps.length);
  };

  const prevStep = () => {
    setCurrentStep((prev) => (prev - 1 + steps.length) % steps.length);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-2">How Inflation Works</h1>
        <p className="text-gray-600">Understanding why prices go up and what you can do about it</p>
      </div>

      {/* Progress indicator */}
      <div className="flex justify-center mb-8">
        <div className="flex space-x-2">
          {steps.map((_, index) => (
            <div
              key={index}
              className={`w-3 h-3 rounded-full cursor-pointer transition-colors ${
                index === currentStep ? 'bg-blue-500' : 'bg-gray-300'
              }`}
              onClick={() => setCurrentStep(index)}
            />
          ))}
        </div>
      </div>

      {/* Current step */}
      <div className="bg-white rounded-xl shadow-lg p-8 min-h-96">
        <div className="flex items-center justify-center mb-6">
          {steps[currentStep].icon}
          <h2 className="text-2xl font-bold ml-3 text-gray-800">{steps[currentStep].title}</h2>
        </div>
        
        <div className="mb-8">
          {steps[currentStep].content}
        </div>
      </div>

      {/* Navigation */}
      <div className="flex justify-between items-center mt-8">
        <button
          onClick={prevStep}
          className="flex items-center px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
        >
          <ChevronLeft className="w-4 h-4 mr-1" />
          Previous
        </button>
        
        <span className="text-gray-500">
          {currentStep + 1} of {steps.length}
        </span>
        
        <button
          onClick={nextStep}
          className="flex items-center px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
        >
          Next
          <ChevronRight className="w-4 h-4 ml-1" />
        </button>
      </div>
    </div>
  );
};

export default InflationExplanation;
```
