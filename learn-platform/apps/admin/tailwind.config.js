const { createGlobPatternsForDependencies } = require('@nx/react/tailwind');
const { join } = require('path');
const baseConfig = require('../../tailwind.config.base.js');

/** @type {import('tailwindcss').Config} */
module.exports = {
  ...baseConfig,
  content: [
    ...baseConfig.content,
    join(__dirname, '{src,pages,components,app}/**/*!(*.stories|*.spec).{ts,tsx,html}'),
    // Include shared UI library
    join(__dirname, '../../libs/shared/ui/src/**/*!(*.stories|*.spec).{ts,tsx,html}'),
    // Include any other dependencies that might contain Tailwind classes
    ...createGlobPatternsForDependencies(__dirname),
  ],
};
