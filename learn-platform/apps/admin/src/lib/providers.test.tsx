/**
 * Tests for the TRPCProvider component
 */

import { render, screen } from '@testing-library/react';
import { TRPCProvider } from './providers';

// Mock the tRPC client configuration
jest.mock('./trpc', () => ({
  api: {
    Provider: ({ children }: { children: React.ReactNode }) => (
      <div data-testid="trpc-api-provider">{children}</div>
    ),
    createClient: jest.fn(() => ({})),
  },
  trpcClientConfig: {
    links: [],
  },
}));

// Mock React Query
jest.mock('@tanstack/react-query', () => ({
  QueryClient: jest.fn().mockImplementation(() => ({
    mount: jest.fn(),
    unmount: jest.fn(),
    clear: jest.fn(),
  })),
  QueryClientProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="query-client-provider">{children}</div>
  ),
}));

// Mock AuthProvider
jest.mock('../components/auth/auth-provider', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="auth-provider">{children}</div>
  ),
}));

describe('TRPCProvider', () => {
  it('should render children with all providers', () => {
    const TestChild = () => <div data-testid="test-child">Test Content</div>;

    render(
      <TRPCProvider>
        <TestChild />
      </TRPCProvider>
    );

    expect(screen.getByTestId('query-client-provider')).toBeInTheDocument();
    expect(screen.getByTestId('trpc-api-provider')).toBeInTheDocument();
    expect(screen.getByTestId('auth-provider')).toBeInTheDocument();
    expect(screen.getByTestId('test-child')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('should have proper provider nesting order', () => {
    const TestChild = () => <div data-testid="test-child">Test Content</div>;

    render(
      <TRPCProvider>
        <TestChild />
      </TRPCProvider>
    );

    const queryProvider = screen.getByTestId('query-client-provider');
    const trpcProvider = screen.getByTestId('trpc-api-provider');
    const authProvider = screen.getByTestId('auth-provider');
    const testChild = screen.getByTestId('test-child');

    // Check nesting: tRPC API > QueryClient > Auth > Children
    expect(trpcProvider).toContainElement(queryProvider);
    expect(queryProvider).toContainElement(authProvider);
    expect(authProvider).toContainElement(testChild);
  });

  it('should create a new QueryClient instance', () => {
    const { QueryClient } = require('@tanstack/react-query');

    render(
      <TRPCProvider>
        <div>Test</div>
      </TRPCProvider>
    );

    expect(QueryClient).toHaveBeenCalled();
  });

  it('should handle multiple children', () => {
    render(
      <TRPCProvider>
        <div data-testid="child-1">Child 1</div>
        <div data-testid="child-2">Child 2</div>
        <span data-testid="child-3">Child 3</span>
      </TRPCProvider>
    );

    expect(screen.getByTestId('child-1')).toBeInTheDocument();
    expect(screen.getByTestId('child-2')).toBeInTheDocument();
    expect(screen.getByTestId('child-3')).toBeInTheDocument();
  });

  it('should handle empty children', () => {
    render(<TRPCProvider>{null}</TRPCProvider>);

    expect(screen.getByTestId('query-client-provider')).toBeInTheDocument();
    expect(screen.getByTestId('trpc-api-provider')).toBeInTheDocument();
    expect(screen.getByTestId('auth-provider')).toBeInTheDocument();
  });

  it('should maintain provider state across re-renders', () => {
    const TestChild = ({ count }: { count: number }) => (
      <div data-testid="test-child">Count: {count}</div>
    );

    const { rerender } = render(
      <TRPCProvider>
        <TestChild count={1} />
      </TRPCProvider>
    );

    expect(screen.getByText('Count: 1')).toBeInTheDocument();

    rerender(
      <TRPCProvider>
        <TestChild count={2} />
      </TRPCProvider>
    );

    expect(screen.getByText('Count: 2')).toBeInTheDocument();
    expect(screen.getByTestId('query-client-provider')).toBeInTheDocument();
  });
});
