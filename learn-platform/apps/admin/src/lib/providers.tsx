/**
 * Client-side providers for the admin application
 * This file sets up tRPC and React Query providers for client-side rendering
 */

'use client';

import { useState } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { api, trpcClientConfig } from './trpc';
import { AuthProvider } from '../components/auth/auth-provider';

/**
 * Props for the TRPCProvider component
 */
interface TRPCProviderProps {
  children: React.ReactNode;
}

/**
 * Combined tRPC and React Query provider
 * This component wraps the app with both providers and handles client-side setup
 */
export function TRPCProvider({ children }: TRPCProviderProps) {
  // Create a stable QueryClient instance
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // With SSR, we usually want to set some default staleTime
            // above 0 to avoid refetching immediately on the client
            staleTime: 60 * 1000, // 1 minute
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            retry: (failureCount, error: any) => {
              // Don't retry on 4xx errors
              if (error?.data?.httpStatus >= 400 && error?.data?.httpStatus < 500) {
                return false;
              }
              // Retry up to 3 times for other errors
              return failureCount < 3;
            },
          },
          mutations: {
            retry: false,
          },
        },
      })
  );

  // Create a stable tRPC client instance
  const [trpcClient] = useState(() => api.createClient(trpcClientConfig));

  return (
    <api.Provider client={trpcClient} queryClient={queryClient}>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          {children}
        </AuthProvider>
      </QueryClientProvider>
    </api.Provider>
  );
}

/**
 * React Query DevTools (optional)
 * Uncomment the import and component below to enable React Query DevTools in development
 */
// import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

// export function DevTools() {
//   return (
//     <ReactQueryDevtools
//       initialIsOpen={false}
//       position="bottom-right"
//     />
//   );
// }
