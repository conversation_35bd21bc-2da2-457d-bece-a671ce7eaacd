/**
 * tRPC client configuration for the admin application
 * This file sets up the tRPC client with React Query integration
 */

import { createTRPCReact } from '@trpc/react-query';
import { createTRPCClient, httpBatchLink } from '@trpc/client';
import type { AppRouter } from '@learn-platform/trpc';

/**
 * Create the tRPC React hooks
 * This provides type-safe hooks for queries and mutations
 */
export const api = createTRPCReact<AppRouter>();
export const trpc = api; // Export as trpc for consistency with component imports

/**
 * Get the base URL for the tRPC API
 * In development, this points to the local Cloudflare Worker
 * In production, this should point to your deployed API
 */
function getBaseUrl() {
  // Always use the centralized API URL for tRPC requests
  // This ensures all requests go to the Cloudflare Workers API, not the Next.js app
  if (typeof window !== 'undefined') {
    // Browser - use the API URL from environment
    return process.env.NEXT_PUBLIC_API_URL || (process.env.NODE_ENV === 'development' ? 'http://localhost:8787' : 'https://learn-platform-api-dev.bm.workers.dev');
  }

  // Server-side rendering
  if (process.env.VERCEL_URL) {
    // Reference for vercel.com
    return `https://${process.env.VERCEL_URL}`;
  }

  if (process.env.NODE_ENV === 'development') {
    // Development - point to local Cloudflare Worker
    return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8787';
  }

  // Production fallback
  return process.env.NEXT_PUBLIC_API_URL || 'https://your-api-domain.com';
}

/**
 * Create a vanilla tRPC client (for server-side usage)
 */
export const trpcClient = createTRPCClient<AppRouter>({
  links: [
    httpBatchLink({
      url: `${getBaseUrl()}/trpc`,
      // Include credentials (cookies) for authentication
      fetch(url, options) {
        return fetch(url, {
          ...options,
          credentials: 'include', // Important: Include cookies for auth
        });
      },
      headers() {
        return {
          // Add any default headers here
          'Content-Type': 'application/json',
        };
      },
    }),
  ],
});

/**
 * tRPC client configuration for React Query
 * This is used by the TRPCProvider
 */
export const trpcClientConfig = {
  links: [
    httpBatchLink({
      url: `${getBaseUrl()}/trpc`,
      // Include credentials (cookies) for authentication
      fetch(url, options) {
        return fetch(url, {
          ...options,
          credentials: 'include', // Important: Include cookies for auth
        });
      },
      headers() {
        return {
          // Add any default headers here
          'Content-Type': 'application/json',
        };
      },
    }),
  ],
};
