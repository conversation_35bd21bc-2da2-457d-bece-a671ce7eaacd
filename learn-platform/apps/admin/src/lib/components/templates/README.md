# MultiStepExplain Component

A flexible, reusable React component for creating step-by-step explanations with various content types. Perfect for educational content, tutorials, and interactive explanations in the admin UI builder.

## Features

- 🎯 **9 Content Types**: Support for paragraph, infoBox, bulletList, numberedList, grid, comparison, table, scatterPlot, and keyValueGrid
- 🎨 **Consistent Styling**: Uses shadcn/ui components and Tailwind CSS for consistent design
- 📱 **Responsive Design**: Works on desktop, tablet, and mobile devices
- 🔄 **Interactive Navigation**: Previous/Next buttons and clickable progress indicators
- 🎭 **Customizable Icons**: Support for any React icon component
- 📦 **TypeScript Support**: Full type safety with comprehensive interfaces

## Installation

The component is already available in the admin project:

```tsx
import { MultiStepExplain } from '@/lib/components/templates';
```

## Basic Usage

```tsx
import { MultiStepExplain } from '@/lib/components/templates';
import { Brain, BookOpen } from 'lucide-react';

const steps = [
  {
    title: "Introduction",
    icon: <Brain className="w-8 h-8 text-blue-500" />,
    type: 'paragraph',
    data: "This is a simple paragraph explanation."
  },
  {
    title: "Key Points",
    icon: <BookOpen className="w-8 h-8 text-green-500" />,
    type: 'bulletList',
    data: [
      "First important point",
      "Second important point",
      "Third important point"
    ]
  }
];

<MultiStepExplain steps={steps} />
```

## Content Types

### 1. Paragraph
Simple text content, supports single string or array of strings.

```tsx
{
  type: 'paragraph',
  data: "Single paragraph text"
  // OR
  data: ["First paragraph", "Second paragraph"]
}
```

### 2. Info Box
Highlighted information box with optional heading.

```tsx
{
  type: 'infoBox',
  data: {
    heading: "Important Note", // optional
    lines: [
      "First line of information",
      "Second line of information"
    ]
  }
}
```

### 3. Bullet List
Unordered list with bullet points.

```tsx
{
  type: 'bulletList',
  data: [
    "First bullet point",
    "Second bullet point",
    "Third bullet point"
  ]
}
```

### 4. Numbered List
Ordered list with numbered steps.

```tsx
{
  type: 'numberedList',
  data: [
    "First step",
    "Second step", 
    "Third step"
  ]
}
```

### 5. Grid Layout
2 or 3 column grid for side-by-side content.

```tsx
{
  type: 'grid',
  data: [
    { title: "Section 1", content: "Content for section 1" },
    { title: "Section 2", content: "Content for section 2" },
    { title: "Section 3", content: "Content for section 3" }
  ]
}
```

### 6. Comparison
Before/after or scenario A vs B comparisons.

```tsx
{
  type: 'comparison',
  data: [
    {
      label: "Approach Comparison",
      before: "Old way of doing things",
      after: "New improved way"
    }
  ]
}
```

### 7. Table
Data table with headers and rows.

```tsx
{
  type: 'table',
  data: {
    headers: ["Name", "Age", "City"],
    rows: [
      ["John", 25, "New York"],
      ["Jane", 30, "Los Angeles"],
      ["Bob", 35, "Chicago"]
    ]
  }
}
```

### 8. Scatter Plot
Simple scatter plot visualization.

```tsx
{
  type: 'scatterPlot',
  data: {
    data: [
      { x: 10, y: 20, label: "Point 1" },
      { x: 30, y: 40, label: "Point 2" },
      { x: 50, y: 60, label: "Point 3" }
    ],
    width: 400,  // optional, default: 400
    height: 300  // optional, default: 300
  }
}
```

### 9. Key-Value Grid
Grid of key-value pairs for definitions or facts.

```tsx
{
  type: 'keyValueGrid',
  data: [
    { key: "Term 1", value: "Definition of term 1" },
    { key: "Term 2", value: "Definition of term 2" },
    { key: "Term 3", value: "Definition of term 3" }
  ]
}
```

## Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `steps` | `StepConfig[]` | Yes | Array of step configurations |
| `className` | `string` | No | Additional CSS classes |

## TypeScript Interfaces

```tsx
interface StepConfig {
  title: string;
  icon: ReactNode;
  type: 'paragraph' | 'infoBox' | 'bulletList' | 'numberedList' | 'grid' | 'comparison' | 'table' | 'scatterPlot' | 'keyValueGrid';
  data: any; // structured data matching the chosen type
}

interface MultiStepExplainProps {
  steps: StepConfig[];
  className?: string;
}
```

## Styling

The component uses Tailwind CSS classes and follows the design system established in the admin project. Colors and spacing are consistent with shadcn/ui components.

## Integration with Component Builder

This component is designed to be used as a template in the admin project's component builder. It provides a flexible way to create educational content without hardcoding the structure.

## Examples

See `MultiStepExplain.example.tsx` for a comprehensive example showing all content types in action.
