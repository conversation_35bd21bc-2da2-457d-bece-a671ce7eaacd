'use client';

import React from 'react';
import { Brain, Book<PERSON>pen, Zap, MessageSquare, TrendingUp } from 'lucide-react';
import { MultiStepExplain } from './MultiStepExplain';
import { StepConfig } from './types';

// Example usage of MultiStepExplain component
export function MultiStepExplainExample() {
  const exampleSteps: StepConfig[] = [
    {
      title: "Introduction to AI",
      icon: <Brain className="w-8 h-8 text-blue-500" />,
      type: 'paragraph',
      data: [
        "Artificial Intelligence (AI) is a fascinating field that aims to create machines capable of intelligent behavior.",
        "In this explanation, we'll explore the key concepts and components that make AI systems work."
      ]
    },
    {
      title: "Key Concepts",
      icon: <BookOpen className="w-8 h-8 text-green-500" />,
      type: 'infoBox',
      data: {
        heading: "Important AI Concepts",
        lines: [
          "Machine Learning: Algorithms that improve through experience",
          "Neural Networks: Computing systems inspired by biological neural networks",
          "Deep Learning: Machine learning using deep neural networks",
          "Natural Language Processing: AI's ability to understand human language"
        ]
      }
    },
    {
      title: "AI Applications",
      icon: <Zap className="w-8 h-8 text-purple-500" />,
      type: 'bulletList',
      data: [
        "Image recognition and computer vision",
        "Speech recognition and synthesis",
        "Recommendation systems",
        "Autonomous vehicles",
        "Medical diagnosis assistance",
        "Financial fraud detection"
      ]
    },
    {
      title: "How AI Learns",
      icon: <MessageSquare className="w-8 h-8 text-orange-500" />,
      type: 'numberedList',
      data: [
        "Data Collection: Gather large amounts of relevant data",
        "Data Preprocessing: Clean and prepare the data for training",
        "Model Training: Use algorithms to find patterns in the data",
        "Validation: Test the model on new, unseen data",
        "Deployment: Put the trained model into production use",
        "Monitoring: Continuously evaluate and improve performance"
      ]
    },
    {
      title: "AI vs Traditional Programming",
      icon: <TrendingUp className="w-8 h-8 text-red-500" />,
      type: 'comparison',
      data: [
        {
          label: "Programming Approach",
          before: "Traditional programming uses explicit rules and logic written by developers",
          after: "AI learns patterns from data and makes decisions based on those patterns"
        },
        {
          label: "Problem Solving",
          before: "Requires manual coding of every possible scenario and rule",
          after: "Automatically discovers solutions by analyzing examples and data"
        }
      ]
    },
    {
      title: "AI Performance Metrics",
      icon: <Brain className="w-8 h-8 text-indigo-500" />,
      type: 'table',
      data: {
        headers: ["Metric", "Description", "Good Range", "Use Case"],
        rows: [
          ["Accuracy", "Percentage of correct predictions", "85-95%", "Classification tasks"],
          ["Precision", "True positives / (True + False positives)", "80-90%", "Spam detection"],
          ["Recall", "True positives / (True positives + False negatives)", "80-90%", "Medical diagnosis"],
          ["F1-Score", "Harmonic mean of precision and recall", "80-90%", "Balanced evaluation"]
        ]
      }
    },
    {
      title: "AI Model Performance",
      icon: <Zap className="w-8 h-8 text-cyan-500" />,
      type: 'scatterPlot',
      data: {
        data: [
          { x: 10, y: 65, label: "Basic Model" },
          { x: 50, y: 78, label: "Improved Model" },
          { x: 100, y: 85, label: "Advanced Model" },
          { x: 200, y: 92, label: "State-of-art" },
          { x: 500, y: 95, label: "Enterprise Model" }
        ],
        width: 500,
        height: 300
      }
    },
    {
      title: "AI Technology Stack",
      icon: <BookOpen className="w-8 h-8 text-emerald-500" />,
      type: 'grid',
      data: [
        {
          title: "Data Layer",
          content: "Raw data, databases, data lakes, and data preprocessing pipelines that feed AI systems."
        },
        {
          title: "Model Layer",
          content: "Machine learning algorithms, neural networks, and trained models that process data."
        },
        {
          title: "Application Layer",
          content: "APIs, user interfaces, and applications that make AI accessible to end users."
        }
      ]
    },
    {
      title: "Key AI Terms",
      icon: <MessageSquare className="w-8 h-8 text-violet-500" />,
      type: 'keyValueGrid',
      data: [
        {
          key: "Algorithm",
          value: "A set of rules or instructions for solving a problem or completing a task"
        },
        {
          key: "Training Data",
          value: "The dataset used to teach an AI model how to make predictions or decisions"
        },
        {
          key: "Feature",
          value: "An individual measurable property of something being observed"
        },
        {
          key: "Overfitting",
          value: "When a model learns the training data too well and fails on new data"
        },
        {
          key: "Bias",
          value: "Systematic errors in AI predictions due to flawed training data or algorithms"
        },
        {
          key: "Inference",
          value: "The process of using a trained model to make predictions on new data"
        }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">
            MultiStepExplain Component Demo
          </h1>
          <p className="text-xl text-gray-600">
            A comprehensive example showing all content types
          </p>
        </div>
        
        <MultiStepExplain steps={exampleSteps} />
        
        <div className="mt-12 bg-white p-6 rounded-lg shadow-lg">
          <h2 className="text-2xl font-bold mb-4">Usage Example</h2>
          <pre className="bg-gray-100 p-4 rounded text-sm overflow-x-auto">
{`import { MultiStepExplain } from './lib/components/templates';
import { Brain } from 'lucide-react';

const steps = [
  {
    title: "Step Title",
    icon: <Brain className="w-8 h-8 text-blue-500" />,
    type: 'paragraph',
    data: "Your content here..."
  }
];

<MultiStepExplain steps={steps} />`}
          </pre>
        </div>
      </div>
    </div>
  );
}
