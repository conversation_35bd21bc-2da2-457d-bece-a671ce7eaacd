'use client';

import React, { useState } from 'react';
import { ChevronRight, ChevronLeft } from 'lucide-react';
import { Button } from '@learn-platform/shared-ui';
import { cn } from '@learn-platform/shared-ui';
import {
  MultiStepExplainProps,
  StepConfig,
  InfoBoxData,
  GridData,
  ComparisonData,
  TableData,
  ScatterPlotConfig,
  KeyValueData
} from './types';

export function MultiStepExplain({ steps, className }: MultiStepExplainProps) {
  const [currentStep, setCurrentStep] = useState(0);

  if (!steps || steps.length === 0) {
    return (
      <div className="max-w-4xl mx-auto p-6 bg-white">
        <div className="text-center text-gray-500">
          No steps provided
        </div>
      </div>
    );
  }

  const nextStep = () => {
    setCurrentStep((prev) => (prev + 1) % steps.length);
  };

  const prevStep = () => {
    setCurrentStep((prev) => (prev - 1 + steps.length) % steps.length);
  };

  const renderContent = (step: StepConfig) => {
    switch (step.type) {
      case 'paragraph':
        return renderParagraph(step.data);
      case 'infoBox':
        return renderInfoBox(step.data);
      case 'bulletList':
        return renderBulletList(step.data);
      case 'numberedList':
        return renderNumberedList(step.data);
      case 'grid':
        return renderGrid(step.data);
      case 'comparison':
        return renderComparison(step.data);
      case 'table':
        return renderTable(step.data);
      case 'scatterPlot':
        return renderScatterPlot(step.data);
      case 'keyValueGrid':
        return renderKeyValueGrid(step.data);
      default:
        return <div className="text-gray-500">Unknown content type: {step.type}</div>;
    }
  };

  const renderParagraph = (data: string | string[]) => {
    const paragraphs = Array.isArray(data) ? data : [data];
    return (
      <div className="space-y-4">
        {paragraphs.map((paragraph, index) => (
          <p key={index} className="text-lg leading-relaxed">
            {paragraph}
          </p>
        ))}
      </div>
    );
  };

  const renderInfoBox = (data: InfoBoxData) => {
    return (
      <div className="bg-blue-50 p-4 rounded-lg">
        {data.heading && (
          <h4 className="font-semibold text-blue-800 mb-3">{data.heading}</h4>
        )}
        <div className="space-y-2">
          {data.lines.map((line, index) => (
            <p key={index} className="text-blue-700">
              {line}
            </p>
          ))}
        </div>
      </div>
    );
  };

  const renderBulletList = (data: string[]) => {
    return (
      <ul className="space-y-2">
        {data.map((item, index) => (
          <li key={index} className="flex items-start">
            <span className="text-blue-500 mr-2">•</span>
            <span>{item}</span>
          </li>
        ))}
      </ul>
    );
  };

  const renderNumberedList = (data: string[]) => {
    return (
      <ol className="space-y-2">
        {data.map((item, index) => (
          <li key={index} className="flex items-start">
            <span className="bg-blue-200 rounded-full w-6 h-6 flex items-center justify-center text-sm font-bold mr-3 flex-shrink-0">
              {index + 1}
            </span>
            <span>{item}</span>
          </li>
        ))}
      </ol>
    );
  };

  const renderGrid = (data: GridData[]) => {
    const gridCols = data.length <= 2 ? 'grid-cols-1 md:grid-cols-2' : 'grid-cols-1 md:grid-cols-3';
    return (
      <div className={`grid ${gridCols} gap-4`}>
        {data.map((item, index) => (
          <div key={index} className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-semibold text-gray-800 mb-2">{item.title}</h4>
            <p className="text-gray-700">{item.content}</p>
          </div>
        ))}
      </div>
    );
  };

  const renderComparison = (data: ComparisonData[]) => {
    return (
      <div className="space-y-4">
        {data.map((item, index) => (
          <div key={index} className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-semibold text-gray-800 mb-3">{item.label}</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-red-50 p-3 rounded border">
                <h5 className="font-semibold text-red-800 text-sm mb-1">Before</h5>
                <p className="text-red-700 text-sm">{item.before}</p>
              </div>
              <div className="bg-green-50 p-3 rounded border">
                <h5 className="font-semibold text-green-800 text-sm mb-1">After</h5>
                <p className="text-green-700 text-sm">{item.after}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderTable = (data: TableData) => {
    return (
      <div className="overflow-x-auto">
        <table className="w-full text-sm bg-white rounded border">
          <thead className="bg-gray-100">
            <tr>
              {data.headers.map((header, index) => (
                <th key={index} className="p-3 text-left font-semibold">
                  {header}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {data.rows.map((row, rowIndex) => (
              <tr key={rowIndex} className="border-t">
                {row.map((cell, cellIndex) => (
                  <td key={cellIndex} className="p-3">
                    {cell}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  const renderScatterPlot = (config: ScatterPlotConfig) => {
    const { data, width = 400, height = 300 } = config;
    const maxX = Math.max(...data.map(d => d.x));
    const maxY = Math.max(...data.map(d => d.y));

    return (
      <div className="bg-gray-50 p-4 rounded-lg">
        <h4 className="font-semibold text-center mb-4">Scatter Plot Visualization</h4>
        <div
          className="relative bg-white border rounded mx-auto"
          style={{ width: `${width}px`, height: `${height}px` }}
        >
          {data.map((point, index) => {
            const x = (point.x / maxX) * (width - 40) + 20;
            const y = height - (point.y / maxY) * (height - 40) - 20;
            return (
              <div
                key={index}
                className="absolute w-3 h-3 bg-blue-500 rounded-full"
                style={{ left: `${x}px`, top: `${y}px` }}
                title={point.label}
              >
                <div className="absolute top-4 left-0 text-xs whitespace-nowrap">
                  {point.label}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderKeyValueGrid = (data: KeyValueData[]) => {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {data.map((item, index) => (
          <div key={index} className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">{item.key}</h4>
            <p className="text-blue-700">{item.value}</p>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className={cn("max-w-4xl mx-auto p-6 bg-white", className)}>
      {/* Progress indicator */}
      <div className="flex justify-center mb-8">
        <div className="flex space-x-2">
          {steps.map((_, index) => (
            <div
              key={index}
              className={cn(
                "w-3 h-3 rounded-full cursor-pointer transition-colors",
                index === currentStep ? 'bg-blue-500' : 'bg-gray-300'
              )}
              onClick={() => setCurrentStep(index)}
            />
          ))}
        </div>
      </div>

      {/* Current step */}
      <div className="bg-white rounded-xl shadow-lg p-8 min-h-96">
        <div className="flex items-center justify-center mb-6">
          {steps[currentStep].icon}
          <h2 className="text-2xl font-bold ml-3 text-gray-800">
            {steps[currentStep].title}
          </h2>
        </div>

        <div className="mb-8">
          {renderContent(steps[currentStep])}
        </div>
      </div>

      {/* Navigation */}
      <div className="flex justify-between items-center mt-8">
        <Button
          variant="outline"
          onClick={prevStep}
          className="flex items-center"
        >
          <ChevronLeft className="w-4 h-4 mr-1" />
          Previous
        </Button>

        <span className="text-gray-500">
          {currentStep + 1} of {steps.length}
        </span>

        <Button
          onClick={nextStep}
          className="flex items-center"
        >
          Next
          <ChevronRight className="w-4 h-4 ml-1" />
        </Button>
      </div>
    </div>
  );
}
