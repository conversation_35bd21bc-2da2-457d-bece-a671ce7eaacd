/**
 * Better-auth client configuration for the admin application
 * This file sets up the better-auth client for frontend authentication
 */

import { createAuthClient } from 'better-auth/client';

/**
 * Get the base URL for the auth API
 */
function getBaseUrl() {
  // Always use the centralized API URL for authentication endpoints
  // This ensures auth requests go to Cloudflare Workers API, not local Next.js routes
  if (typeof window !== 'undefined') {
    // Browser - use the API URL from environment
    return process.env.NEXT_PUBLIC_API_URL || (process.env.NODE_ENV === 'development' ? 'http://localhost:8787' : 'https://learn-platform-api-dev.bm.workers.dev');
  }

  // Server-side rendering
  if (process.env.NODE_ENV === 'development') {
    // Development - point to local Cloudflare Worker
    return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8787';
  }

  // Production - must use the deployed Cloudflare Workers API
  return process.env.NEXT_PUBLIC_API_URL || 'https://learn-platform-api-dev.bm.workers.dev';
}

/**
 * Create the better-auth client instance
 */
export const authClient = createAuthClient({
  baseURL: getBaseUrl(),
  // Additional client configuration can be added here
});

/**
 * Type-safe auth client
 */
export type AuthClient = typeof authClient;
