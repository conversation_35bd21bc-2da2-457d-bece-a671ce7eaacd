'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ProtectedRoute } from '../../components/auth/protected-route';
import { AdminLayout } from '../../lib/components/layout';
import { <PERSON><PERSON>, Card, CardHeader, CardTitle, CardContent } from '@learn-platform/shared-ui';
import { api } from '../../lib/trpc';
import { Plus, Edit, Trash2, Eye, EyeOff, Calendar, Clock, Megaphone } from 'lucide-react';

export default function ReleasesPage() {
  const router = useRouter();
  const [deleteConfirm, setDeleteConfirm] = useState<string | null>(null);

  // Fetch releases data
  const { data: releasesData, isLoading, refetch } = api.releases.getAllAdmin.useQuery();
  const releases = releasesData?.releases || [];

  // Mutations
  const publishMutation = api.releases.publish.useMutation({
    onSuccess: () => {
      refetch();
    },
  });

  const deleteMutation = api.releases.delete.useMutation({
    onSuccess: () => {
      refetch();
      setDeleteConfirm(null);
    },
  });

  const handlePublishToggle = async (id: string, isPublished: boolean) => {
    try {
      await publishMutation.mutateAsync({
        id,
        isPublished: !isPublished,
      });
    } catch (error) {
      console.error('Error toggling publish status:', error);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await deleteMutation.mutateAsync({ id });
    } catch (error) {
      console.error('Error deleting release:', error);
    }
  };

  const formatDate = (date: string | Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getStatusBadge = (isPublished: boolean) => {
    if (isPublished) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          <Eye className="w-3 h-3 mr-1" />
          Published
        </span>
      );
    }
    return (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
        <EyeOff className="w-3 h-3 mr-1" />
        Draft
      </span>
    );
  };

  if (isLoading) {
    return (
      <ProtectedRoute redirectTo="/login">
        <AdminLayout>
          <div className="space-y-6">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
              <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="h-24 bg-gray-200 rounded"></div>
                ))}
              </div>
            </div>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute redirectTo="/login">
      <AdminLayout>
        <div className="space-y-6">
          {/* Page Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Release Management</h1>
              <p className="text-gray-600">Manage system releases and notifications</p>
            </div>
            <Button
              onClick={() => router.push('/releases/new')}
              className="flex items-center space-x-2"
            >
              <Plus className="w-4 h-4" />
              <span>New Release</span>
            </Button>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Calendar className="w-4 h-4 text-blue-600" />
                    </div>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Total Releases</p>
                    <p className="text-2xl font-semibold text-gray-900">{releases.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                      <Eye className="w-4 h-4 text-green-600" />
                    </div>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Published</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {releases.filter(r => r.isPublished).length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                      <EyeOff className="w-4 h-4 text-yellow-600" />
                    </div>
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">Drafts</p>
                    <p className="text-2xl font-semibold text-gray-900">
                      {releases.filter(r => !r.isPublished).length}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Releases List */}
          <Card>
            <CardHeader>
              <CardTitle>All Releases</CardTitle>
            </CardHeader>
            <CardContent>
              {releases.length === 0 ? (
                <div className="text-center py-12">
                  <Megaphone className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No releases yet</h3>
                  <p className="text-gray-500 mb-6">Get started by creating your first release.</p>
                  <Button onClick={() => router.push('/releases/new')}>
                    <Plus className="w-4 h-4 mr-2" />
                    Create Release
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {releases.map((release) => (
                    <div
                      key={release.id}
                      className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="text-lg font-semibold text-gray-900">
                              {release.version}
                            </h3>
                            {getStatusBadge(release.isPublished)}
                          </div>
                          <p className="text-gray-600 mb-3 line-clamp-2">
                            {release.description}
                          </p>
                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            <div className="flex items-center">
                              <Calendar className="w-4 h-4 mr-1" />
                              Release Date: {formatDate(release.releaseDate)}
                            </div>
                            <div className="flex items-center">
                              <Clock className="w-4 h-4 mr-1" />
                              Created: {formatDate(release.createdAt)}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2 ml-4">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => router.push(`/releases/${release.id}`)}
                          >
                            <Edit className="w-4 h-4 mr-1" />
                            Edit
                          </Button>
                          <Button
                            variant={release.isPublished ? "outline" : "default"}
                            size="sm"
                            onClick={() => handlePublishToggle(release.id, release.isPublished)}
                            disabled={publishMutation.isLoading}
                          >
                            {release.isPublished ? (
                              <>
                                <EyeOff className="w-4 h-4 mr-1" />
                                Unpublish
                              </>
                            ) : (
                              <>
                                <Eye className="w-4 h-4 mr-1" />
                                Publish
                              </>
                            )}
                          </Button>
                          {deleteConfirm === release.id ? (
                            <div className="flex space-x-1">
                              <Button
                                variant="destructive"
                                size="sm"
                                onClick={() => handleDelete(release.id)}
                                disabled={deleteMutation.isLoading}
                              >
                                Confirm
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setDeleteConfirm(null)}
                              >
                                Cancel
                              </Button>
                            </div>
                          ) : (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setDeleteConfirm(release.id)}
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}
