'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ProtectedRoute } from '../../../components/auth/protected-route';
import { AdminLayout } from '../../../lib/components/layout';
import { <PERSON><PERSON>, Card, CardHeader, CardTitle, CardContent, Input, Label } from '@learn-platform/shared-ui';
import { api } from '../../../lib/trpc';
import { ArrowLeft, Save, Calendar } from 'lucide-react';

export default function NewReleasePage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    version: '',
    description: '',
    releaseDate: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const createMutation = api.releases.create.useMutation({
    onSuccess: () => {
      router.push('/releases');
    },
    onError: (error) => {
      console.error('Error creating release:', error);
      // Handle validation errors
      if (error.message.includes('version')) {
        setErrors({ version: error.message });
      } else {
        setErrors({ general: error.message });
      }
    },
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.version.trim()) {
      newErrors.version = 'Version is required';
    } else if (!/^v?\d+\.\d+\.\d+(-[a-zA-Z0-9.-]+)?$/.test(formData.version)) {
      newErrors.version = 'Invalid version format. Use semantic versioning (e.g., v1.0.0)';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    } else if (formData.description.length > 5000) {
      newErrors.description = 'Description must be less than 5000 characters';
    }

    if (!formData.releaseDate) {
      newErrors.releaseDate = 'Release date is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      await createMutation.mutateAsync({
        version: formData.version,
        description: formData.description,
        releaseDate: new Date(formData.releaseDate),
      });
    } catch (error) {
      // Error handling is done in the mutation onError callback
    }
  };

  return (
    <ProtectedRoute redirectTo="/login">
      <AdminLayout>
        <div className="space-y-6">
          {/* Page Header */}
          <div className="flex items-center space-x-4">
            <Button
              variant="outline"
              onClick={() => router.back()}
              className="flex items-center space-x-2"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Back</span>
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Create New Release</h1>
              <p className="text-gray-600">Add a new system release with version information</p>
            </div>
          </div>

          {/* Form */}
          <Card>
            <CardHeader>
              <CardTitle>Release Information</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* General Error */}
                {errors.general && (
                  <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                    <p className="text-sm text-red-600">{errors.general}</p>
                  </div>
                )}

                {/* Version */}
                <div className="space-y-2">
                  <Label htmlFor="version">Version *</Label>
                  <Input
                    id="version"
                    type="text"
                    placeholder="e.g., v1.2.0, v2.0.0-beta.1"
                    value={formData.version}
                    onChange={(e) => handleInputChange('version', e.target.value)}
                    className={errors.version ? 'border-red-300 focus:border-red-500' : ''}
                  />
                  {errors.version && (
                    <p className="text-sm text-red-600">{errors.version}</p>
                  )}
                  <p className="text-sm text-gray-500">
                    Use semantic versioning format (e.g., v1.0.0, v2.1.0-beta.1)
                  </p>
                </div>

                {/* Release Date */}
                <div className="space-y-2">
                  <Label htmlFor="releaseDate">Release Date *</Label>
                  <div className="relative">
                    <Input
                      id="releaseDate"
                      type="date"
                      value={formData.releaseDate}
                      onChange={(e) => handleInputChange('releaseDate', e.target.value)}
                      className={errors.releaseDate ? 'border-red-300 focus:border-red-500' : ''}
                    />
                    <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                  </div>
                  {errors.releaseDate && (
                    <p className="text-sm text-red-600">{errors.releaseDate}</p>
                  )}
                  <p className="text-sm text-gray-500">
                    The intended release date for this version
                  </p>
                </div>

                {/* Description */}
                <div className="space-y-2">
                  <Label htmlFor="description">Description *</Label>
                  <textarea
                    id="description"
                    rows={8}
                    placeholder="Describe what's new in this release, including features, bug fixes, and improvements..."
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                      errors.description ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''
                    }`}
                  />
                  {errors.description && (
                    <p className="text-sm text-red-600">{errors.description}</p>
                  )}
                  <div className="flex justify-between text-sm text-gray-500">
                    <span>Supports markdown formatting</span>
                    <span>{formData.description.length}/5000 characters</span>
                  </div>
                </div>

                {/* Form Actions */}
                <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => router.back()}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={createMutation.isLoading}
                    className="flex items-center space-x-2"
                  >
                    <Save className="w-4 h-4" />
                    <span>{createMutation.isLoading ? 'Creating...' : 'Create Release'}</span>
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>

          {/* Help Section */}
          <Card>
            <CardHeader>
              <CardTitle>Release Guidelines</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 text-sm text-gray-600">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Version Naming</h4>
                  <ul className="list-disc list-inside space-y-1">
                    <li>Use semantic versioning: MAJOR.MINOR.PATCH (e.g., v1.2.3)</li>
                    <li>Add pre-release identifiers for beta versions (e.g., v2.0.0-beta.1)</li>
                    <li>Always prefix with 'v' for consistency</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Description Best Practices</h4>
                  <ul className="list-disc list-inside space-y-1">
                    <li>Start with a brief summary of the release</li>
                    <li>List new features, improvements, and bug fixes</li>
                    <li>Include any breaking changes or migration notes</li>
                    <li>Use clear, user-friendly language</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Publishing</h4>
                  <p>
                    Releases are created as drafts by default. You can publish them later from the releases list
                    to make them visible to users.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}
