'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { ProtectedRoute } from '../../../components/auth/protected-route';
import { AdminLayout } from '../../../lib/components/layout';
import { Button, Card, CardHeader, CardTitle, CardContent, Input, Label } from '@learn-platform/shared-ui';
import { api } from '../../../lib/trpc';
import { ArrowLeft, Save, Calendar, Eye, EyeOff } from 'lucide-react';

interface EditReleasePageProps {
  params: Promise<{
    releaseId: string;
  }>;
}

export default function EditReleasePage({ params }: EditReleasePageProps) {
  const router = useRouter();
  const { releaseId } = use(params);

  const [formData, setFormData] = useState({
    version: '',
    description: '',
    releaseDate: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Fetch release data
  const { data: releaseData, isLoading } = api.releases.getById.useQuery(
    { id: releaseId },
    { enabled: !!releaseId }
  );
  const release = releaseData?.release;

  // Mutations
  const updateMutation = api.releases.update.useMutation({
    onSuccess: () => {
      router.push('/releases');
    },
    onError: (error) => {
      console.error('Error updating release:', error);
      if (error.message.includes('version')) {
        setErrors({ version: error.message });
      } else {
        setErrors({ general: error.message });
      }
    },
  });

  const publishMutation = api.releases.publish.useMutation({
    onSuccess: () => {
      router.push('/releases');
    },
  });

  // Initialize form data when release is loaded
  useEffect(() => {
    if (release) {
      setFormData({
        version: release.version,
        description: release.description,
        releaseDate: new Date(release.releaseDate).toISOString().split('T')[0],
      });
    }
  }, [release]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.version.trim()) {
      newErrors.version = 'Version is required';
    } else if (!/^v?\d+\.\d+\.\d+(-[a-zA-Z0-9.-]+)?$/.test(formData.version)) {
      newErrors.version = 'Invalid version format. Use semantic versioning (e.g., v1.0.0)';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    } else if (formData.description.length > 5000) {
      newErrors.description = 'Description must be less than 5000 characters';
    }

    if (!formData.releaseDate) {
      newErrors.releaseDate = 'Release date is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      await updateMutation.mutateAsync({
        id: releaseId,
        version: formData.version,
        description: formData.description,
        releaseDate: new Date(formData.releaseDate),
      });
    } catch (error) {
      // Error handling is done in the mutation onError callback
    }
  };

  const handlePublishToggle = async () => {
    if (!release) return;

    try {
      await publishMutation.mutateAsync({
        id: releaseId,
        isPublished: !release.isPublished,
      });
    } catch (error) {
      console.error('Error toggling publish status:', error);
    }
  };

  if (isLoading) {
    return (
      <ProtectedRoute redirectTo="/login">
        <AdminLayout>
          <div className="space-y-6">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
              <div className="h-96 bg-gray-200 rounded"></div>
            </div>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    );
  }

  if (!release) {
    return (
      <ProtectedRoute redirectTo="/login">
        <AdminLayout>
          <div className="space-y-6">
            <div className="text-center py-12">
              <h3 className="text-lg font-medium text-gray-900 mb-2">Release not found</h3>
              <p className="text-gray-500 mb-6">The release you're looking for doesn't exist.</p>
              <Button onClick={() => router.push('/releases')}>
                Back to Releases
              </Button>
            </div>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    );
  }

  const getStatusBadge = (isPublished: boolean) => {
    if (isPublished) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
          <Eye className="w-3 h-3 mr-1" />
          Published
        </span>
      );
    }
    return (
      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
        <EyeOff className="w-3 h-3 mr-1" />
        Draft
      </span>
    );
  };

  return (
    <ProtectedRoute redirectTo="/login">
      <AdminLayout>
        <div className="space-y-6">
          {/* Page Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                onClick={() => router.back()}
                className="flex items-center space-x-2"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Back</span>
              </Button>
              <div>
                <div className="flex items-center space-x-3 mb-1">
                  <h1 className="text-2xl font-bold text-gray-900">Edit Release</h1>
                  {getStatusBadge(release.isPublished)}
                </div>
                <p className="text-gray-600">Modify release information and publication status</p>
              </div>
            </div>
            <Button
              onClick={handlePublishToggle}
              disabled={publishMutation.isLoading}
              variant={release.isPublished ? "outline" : "default"}
              className="flex items-center space-x-2"
            >
              {release.isPublished ? (
                <>
                  <EyeOff className="w-4 h-4" />
                  <span>Unpublish</span>
                </>
              ) : (
                <>
                  <Eye className="w-4 h-4" />
                  <span>Publish</span>
                </>
              )}
            </Button>
          </div>

          {/* Form */}
          <Card>
            <CardHeader>
              <CardTitle>Release Information</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* General Error */}
                {errors.general && (
                  <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                    <p className="text-sm text-red-600">{errors.general}</p>
                  </div>
                )}

                {/* Version */}
                <div className="space-y-2">
                  <Label htmlFor="version">Version *</Label>
                  <Input
                    id="version"
                    type="text"
                    placeholder="e.g., v1.2.0, v2.0.0-beta.1"
                    value={formData.version}
                    onChange={(e) => handleInputChange('version', e.target.value)}
                    className={errors.version ? 'border-red-300 focus:border-red-500' : ''}
                  />
                  {errors.version && (
                    <p className="text-sm text-red-600">{errors.version}</p>
                  )}
                  <p className="text-sm text-gray-500">
                    Use semantic versioning format (e.g., v1.0.0, v2.1.0-beta.1)
                  </p>
                </div>

                {/* Release Date */}
                <div className="space-y-2">
                  <Label htmlFor="releaseDate">Release Date *</Label>
                  <div className="relative">
                    <Input
                      id="releaseDate"
                      type="date"
                      value={formData.releaseDate}
                      onChange={(e) => handleInputChange('releaseDate', e.target.value)}
                      className={errors.releaseDate ? 'border-red-300 focus:border-red-500' : ''}
                    />
                    <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
                  </div>
                  {errors.releaseDate && (
                    <p className="text-sm text-red-600">{errors.releaseDate}</p>
                  )}
                </div>

                {/* Description */}
                <div className="space-y-2">
                  <Label htmlFor="description">Description *</Label>
                  <textarea
                    id="description"
                    rows={8}
                    placeholder="Describe what's new in this release..."
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    className={`w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                      errors.description ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''
                    }`}
                  />
                  {errors.description && (
                    <p className="text-sm text-red-600">{errors.description}</p>
                  )}
                  <div className="flex justify-between text-sm text-gray-500">
                    <span>Supports markdown formatting</span>
                    <span>{formData.description.length}/5000 characters</span>
                  </div>
                </div>

                {/* Form Actions */}
                <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => router.back()}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={updateMutation.isLoading}
                    className="flex items-center space-x-2"
                  >
                    <Save className="w-4 h-4" />
                    <span>{updateMutation.isLoading ? 'Saving...' : 'Save Changes'}</span>
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}
