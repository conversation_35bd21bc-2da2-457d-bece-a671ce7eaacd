/**
 * Preview Modal for MultiStepExplain component
 * Shows the full interactive component in a modal
 */

'use client';

import React from 'react';
import { X } from 'lucide-react';
import { Button } from '@learn-platform/shared-ui';
import { MultiStepExplain } from '../../../../lib/components/templates';
import { StepConfig } from '../../../../lib/components/templates/types';
import { getIconByName, IconName } from '../../utils/iconMapping';

interface MultiStepPreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  steps: StepConfig[];
}

export const MultiStepPreviewModal: React.FC<MultiStepPreviewModalProps> = ({
  isOpen,
  onClose,
  steps,
}) => {
  if (!isOpen) return null;

  // Convert string icons to React components for preview
  const processedSteps = steps.map(step => ({
    ...step,
    icon: typeof step.icon === 'string' 
      ? getIconByName(step.icon as IconName, "w-8 h-8 text-blue-500")
      : step.icon
  }));

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Modal Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-800">
            MultiStepExplain Preview
          </h2>
          <Button
            variant="outline"
            size="sm"
            onClick={onClose}
            className="flex items-center"
          >
            <X className="w-4 h-4 mr-1" />
            Close
          </Button>
        </div>

        {/* Modal Content */}
        <div className="overflow-auto max-h-[calc(90vh-80px)]">
          {processedSteps.length > 0 ? (
            <div className="p-4">
              <MultiStepExplain steps={processedSteps} />
            </div>
          ) : (
            <div className="flex items-center justify-center h-64 text-gray-500">
              <div className="text-center">
                <div className="text-4xl mb-4">📚</div>
                <p className="text-lg">No steps to preview</p>
                <p className="text-sm">Add some steps to see the preview</p>
              </div>
            </div>
          )}
        </div>

        {/* Modal Footer */}
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <div className="flex justify-between items-center text-sm text-gray-600">
            <span>
              {processedSteps.length} step{processedSteps.length !== 1 ? 's' : ''} configured
            </span>
            <span>
              Use the properties panel to edit steps
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};
