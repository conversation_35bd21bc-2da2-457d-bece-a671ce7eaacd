/**
 * Explainer Builder - Main component for the explainer builder interface
 */

'use client';

import React, { useState } from 'react';
import { Button } from '@learn-platform/shared-ui';
import { Input } from '@learn-platform/shared-ui';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@learn-platform/shared-ui';
import { Save, Eye, FileText, Settings } from 'lucide-react';

import { useExplainerStore } from '../store/explainerStore';
import { StepNavigator } from './StepNavigator';
import { ContentTypeSelector } from './ContentTypeSelector';
import { BlockEditor } from './BlockEditor';
import { MultiStepExplain } from '../../../../lib/components/templates';
import { getIconElement } from '../../utils/iconMapping';
import { trpc } from '../../../../lib/trpc';

export const ExplainerBuilder: React.FC = () => {
  const {
    explainer,
    isPreviewMode,
    isSaving,
    togglePreview,
    updateExplainerInfo,
    exportToStepConfig
  } = useExplainerStore();

  const [showSettings, setShowSettings] = useState(false);
  const [editName, setEditName] = useState('');
  const [editDescription, setEditDescription] = useState('');

  // tRPC mutation for saving templates
  const updateTemplateMutation = trpc.templates.update.useMutation({
    onSuccess: () => {
      console.log('Template saved successfully');
    },
    onError: (error) => {
      console.error('Failed to save template:', error);
    },
  });

  if (!explainer) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-50">
        <div className="text-center text-gray-500">
          <div className="text-4xl mb-4">🚀</div>
          <h3 className="text-lg font-medium mb-2">No Explainer Loaded</h3>
          <p className="text-sm">
            Create a new explainer to get started
          </p>
        </div>
      </div>
    );
  }

  const handleOpenSettings = () => {
    setEditName(explainer.name);
    setEditDescription(explainer.description || '');
    setShowSettings(true);
  };

  const handleSaveSettings = () => {
    updateExplainerInfo({
      name: editName,
      description: editDescription
    });
    setShowSettings(false);
  };

  const handlePreview = () => {
    togglePreview();
  };

  const handleSave = () => {
    if (!explainer) return;

    updateTemplateMutation.mutate({
      id: explainer.id,
      name: explainer.name,
      description: explainer.description,
      steps: explainer.steps,
    });
  };

  const stepConfigs = exportToStepConfig().map(step => ({
    ...step,
    icon: getIconElement(step.icon as any, "w-8 h-8") || step.icon
  }));

  if (isPreviewMode) {
    return (
      <div className="h-full flex flex-col bg-white">
        {/* Preview Header */}
        <div className="border-b border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold text-gray-900">
                Preview: {explainer.name}
              </h2>
              <p className="text-sm text-gray-600">
                {explainer.description}
              </p>
            </div>
            <Button onClick={handlePreview} variant="outline">
              <FileText className="w-4 h-4 mr-2" />
              Back to Editor
            </Button>
          </div>
        </div>

        {/* Preview Content */}
        <div className="flex-1 overflow-y-auto p-6 bg-gray-50">
          <MultiStepExplain steps={stepConfigs} />
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div>
              <h1 className="text-xl font-semibold text-gray-900">
                {explainer.name}
              </h1>
              {explainer.description && (
                <p className="text-sm text-gray-600">
                  {explainer.description}
                </p>
              )}
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Dialog open={showSettings} onOpenChange={setShowSettings}>
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleOpenSettings}
                >
                  <Settings className="w-4 h-4 mr-2" />
                  Settings
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Explainer Settings</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Name
                    </label>
                    <Input
                      value={editName}
                      onChange={(e) => setEditName(e.target.value)}
                      placeholder="Explainer name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Description (optional)
                    </label>
                    <Input
                      value={editDescription}
                      onChange={(e) => setEditDescription(e.target.value)}
                      placeholder="Brief description"
                    />
                  </div>
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="outline"
                      onClick={() => setShowSettings(false)}
                    >
                      Cancel
                    </Button>
                    <Button onClick={handleSaveSettings}>
                      Save Changes
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>

            <Button
              variant="outline"
              size="sm"
              onClick={handlePreview}
              disabled={explainer.steps.length === 0}
            >
              <Eye className="w-4 h-4 mr-2" />
              Preview
            </Button>

            <Button
              size="sm"
              onClick={handleSave}
              disabled={updateTemplateMutation.isLoading}
            >
              <Save className="w-4 h-4 mr-2" />
              {updateTemplateMutation.isLoading ? 'Saving...' : 'Save'}
            </Button>
          </div>
        </div>
      </div>

      {/* Step Navigator */}
      <StepNavigator />

      {/* Main Editor */}
      <div className="flex-1 flex min-h-0">
        {/* Content Type Selector */}
        <ContentTypeSelector />

        {/* Block Editor */}
        <BlockEditor />
      </div>
    </div>
  );
};
