/**
 * <PERSON><PERSON><PERSON> Plot Editor - Inline editor for scatter plot content
 */

'use client';

import React, { useState } from 'react';
import { Button } from '@learn-platform/shared-ui';
import { Input } from '@learn-platform/shared-ui';
import { Check, X, Plus, Trash2 } from 'lucide-react';
import { ScatterPlotConfig, ScatterPlotData } from '../../../../lib/components/templates/types';

interface ScatterPlotEditorProps {
  data: ScatterPlotConfig;
  onSave: (data: ScatterPlotConfig) => void;
  onCancel: () => void;
}

export const ScatterPlotEditor: React.FC<ScatterPlotEditorProps> = ({
  data,
  onSave,
  onCancel
}) => {
  const [plotData, setPlotData] = useState<ScatterPlotData[]>(
    data.data || [{ x: 10, y: 20, label: 'Point 1' }]
  );
  const [width, setWidth] = useState(data.width || 400);
  const [height, setHeight] = useState(data.height || 300);

  const handlePointChange = (
    index: number,
    field: keyof ScatterPlotData,
    value: string | number
  ) => {
    const newData = [...plotData];
    newData[index] = {
      ...newData[index],
      [field]: field === 'label' ? value : Number(value)
    };
    setPlotData(newData);
  };

  const addPoint = () => {
    setPlotData([...plotData, {
      x: 0,
      y: 0,
      label: `Point ${plotData.length + 1}`
    }]);
  };

  const removePoint = (index: number) => {
    if (plotData.length <= 1) return;
    setPlotData(plotData.filter((_, i) => i !== index));
  };

  const handleSave = () => {
    onSave({
      data: plotData,
      width,
      height
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium text-gray-700">Scatter Plot Data</h4>
        <span className="text-xs text-gray-500">
          {plotData.length} point{plotData.length !== 1 ? 's' : ''}
        </span>
      </div>

      {/* Plot dimensions */}
      <div className="grid grid-cols-2 gap-3">
        <div>
          <label className="block text-xs font-medium text-gray-600 mb-1">
            Width (px)
          </label>
          <Input
            type="number"
            value={width}
            onChange={(e) => setWidth(Number(e.target.value))}
            min="200"
            max="800"
          />
        </div>
        <div>
          <label className="block text-xs font-medium text-gray-600 mb-1">
            Height (px)
          </label>
          <Input
            type="number"
            value={height}
            onChange={(e) => setHeight(Number(e.target.value))}
            min="200"
            max="600"
          />
        </div>
      </div>

      {/* Data points */}
      <div className="space-y-3">
        <label className="block text-sm font-medium text-gray-700">
          Data Points
        </label>

        {plotData.map((point, index) => (
          <div key={index} className="border border-gray-200 rounded-lg p-3">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-600">
                Point {index + 1}
              </span>
              {plotData.length > 1 && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-6 h-6 p-0 text-red-500"
                  onClick={() => removePoint(index)}
                >
                  <Trash2 className="w-3 h-3" />
                </Button>
              )}
            </div>

            <div className="grid grid-cols-3 gap-2">
              <div>
                <label className="block text-xs text-gray-600 mb-1">X</label>
                <Input
                  type="number"
                  value={point.x}
                  onChange={(e) => handlePointChange(index, 'x', e.target.value)}
                  placeholder="X coordinate"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-600 mb-1">Y</label>
                <Input
                  type="number"
                  value={point.y}
                  onChange={(e) => handlePointChange(index, 'y', e.target.value)}
                  placeholder="Y coordinate"
                />
              </div>
              <div>
                <label className="block text-xs text-gray-600 mb-1">Label</label>
                <Input
                  value={point.label}
                  onChange={(e) => handlePointChange(index, 'label', e.target.value)}
                  placeholder="Point label"
                />
              </div>
            </div>
          </div>
        ))}

        <Button
          variant="outline"
          size="sm"
          onClick={addPoint}
          className="flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          Add Point
        </Button>
      </div>

      <div className="flex items-center justify-end gap-2 pt-2 border-t border-gray-200">
        <Button
          variant="ghost"
          size="sm"
          onClick={onCancel}
          className="flex items-center gap-2"
        >
          <X className="w-4 h-4" />
          Cancel
        </Button>
        <Button
          size="sm"
          onClick={handleSave}
          className="flex items-center gap-2"
        >
          <Check className="w-4 h-4" />
          Save
        </Button>
      </div>
    </div>
  );
};
