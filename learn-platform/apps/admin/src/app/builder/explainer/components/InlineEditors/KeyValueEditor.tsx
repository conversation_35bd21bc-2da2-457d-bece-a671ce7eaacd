/**
 * Key-Value Editor - Inline editor for key-value grid content
 */

'use client';

import React, { useState } from 'react';
import { Button } from '@learn-platform/shared-ui';
import { Input } from '@learn-platform/shared-ui';
import { Check, X, Plus, Trash2 } from 'lucide-react';
import { KeyValueData } from '../../../../lib/components/templates/types';

interface KeyValueEditorProps {
  data: KeyValueData[];
  onSave: (data: KeyValueData[]) => void;
  onCancel: () => void;
}

export const KeyValueEditor: React.FC<KeyValueEditorProps> = ({
  data,
  onSave,
  onCancel
}) => {
  const [pairs, setPairs] = useState<KeyValueData[]>(data || [
    { key: '', value: '' }
  ]);

  const handlePairChange = (
    index: number,
    field: keyof KeyValueData,
    value: string
  ) => {
    const newPairs = [...pairs];
    newPairs[index] = { ...newPairs[index], [field]: value };
    setPairs(newPairs);
  };

  const addPair = () => {
    setPairs([...pairs, { key: '', value: '' }]);
  };

  const removePair = (index: number) => {
    if (pairs.length <= 1) return;
    setPairs(pairs.filter((_, i) => i !== index));
  };

  const handleSave = () => {
    const filteredPairs = pairs.filter(pair =>
      pair.key.trim() !== '' || pair.value.trim() !== ''
    );
    if (filteredPairs.length === 0) {
      filteredPairs.push({ key: 'Key', value: 'Value' });
    }
    onSave(filteredPairs);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      onCancel();
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium text-gray-700">Key-Value Pairs</h4>
        <span className="text-xs text-gray-500">
          {pairs.length} pair{pairs.length !== 1 ? 's' : ''}
        </span>
      </div>

      <div className="space-y-3">
        {pairs.map((pair, index) => (
          <div key={index} className="border border-gray-200 rounded-lg p-3">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-600">
                Pair {index + 1}
              </span>
              {pairs.length > 1 && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-6 h-6 p-0 text-red-500"
                  onClick={() => removePair(index)}
                >
                  <Trash2 className="w-3 h-3" />
                </Button>
              )}
            </div>

            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  Key
                </label>
                <Input
                  value={pair.key}
                  onChange={(e) => handlePairChange(index, 'key', e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Enter key..."
                  autoFocus={index === 0}
                />
              </div>

              <div>
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  Value
                </label>
                <Input
                  value={pair.value}
                  onChange={(e) => handlePairChange(index, 'value', e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Enter value..."
                />
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="flex items-center justify-between pt-2">
        <Button
          variant="outline"
          size="sm"
          onClick={addPair}
          className="flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          Add Pair
        </Button>

        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={onCancel}
            className="flex items-center gap-2"
          >
            <X className="w-4 h-4" />
            Cancel
          </Button>
          <Button
            size="sm"
            onClick={handleSave}
            className="flex items-center gap-2"
          >
            <Check className="w-4 h-4" />
            Save
          </Button>
        </div>
      </div>

      <div className="text-xs text-gray-500">
        Press Ctrl/Cmd + Enter to save, Escape to cancel
      </div>
    </div>
  );
};
