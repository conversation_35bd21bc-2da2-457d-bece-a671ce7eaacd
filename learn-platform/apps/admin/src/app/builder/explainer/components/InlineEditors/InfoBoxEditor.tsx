/**
 * Info Box Editor - Inline editor for info box content
 */

'use client';

import React, { useState } from 'react';
import { Button } from '@learn-platform/shared-ui';
import { Input } from '@learn-platform/shared-ui';
import { Textarea } from '@learn-platform/shared-ui';
import { Check, X, Plus, Trash2 } from 'lucide-react';
import { InfoBoxData } from '../../../../lib/components/templates/types';

interface InfoBoxEditorProps {
  data: InfoBoxData;
  onSave: (data: InfoBoxData) => void;
  onCancel: () => void;
}

export const InfoBoxEditor: React.FC<InfoBoxEditorProps> = ({
  data,
  onSave,
  onCancel
}) => {
  const [heading, setHeading] = useState(data.heading || '');
  const [lines, setLines] = useState<string[]>(data.lines || ['']);

  const handleLineChange = (index: number, value: string) => {
    const newLines = [...lines];
    newLines[index] = value;
    setLines(newLines);
  };

  const addLine = () => {
    setLines([...lines, '']);
  };

  const removeLine = (index: number) => {
    if (lines.length <= 1) return;
    const newLines = lines.filter((_, i) => i !== index);
    setLines(newLines);
  };

  const handleSave = () => {
    const filteredLines = lines.filter(line => line.trim() !== '');
    if (filteredLines.length === 0) {
      filteredLines.push('Add your information here...');
    }

    onSave({
      heading: heading.trim() || undefined,
      lines: filteredLines
    });
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      onCancel();
    }
  };

  return (
    <div className="space-y-4">
      {/* Heading */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Heading (optional)
        </label>
        <Input
          value={heading}
          onChange={(e) => setHeading(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Info box heading..."
          className="w-full"
        />
      </div>

      {/* Lines */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Information Lines
        </label>
        <div className="space-y-2">
          {lines.map((line, index) => (
            <div key={index} className="flex gap-2">
              <Textarea
                value={line}
                onChange={(e) => handleLineChange(index, e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder={`Information line ${index + 1}...`}
                className="flex-1 min-h-[60px] resize-none"
                autoFocus={index === 0 && !heading}
              />
              {lines.length > 1 && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-8 h-8 p-0 text-red-500 hover:text-red-700 self-start mt-1"
                  onClick={() => removeLine(index)}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              )}
            </div>
          ))}
        </div>

        <Button
          variant="outline"
          size="sm"
          onClick={addLine}
          className="flex items-center gap-2 mt-2"
        >
          <Plus className="w-4 h-4" />
          Add Line
        </Button>
      </div>

      {/* Actions */}
      <div className="flex items-center justify-end gap-2 pt-2 border-t border-gray-200">
        <Button
          variant="ghost"
          size="sm"
          onClick={onCancel}
          className="flex items-center gap-2"
        >
          <X className="w-4 h-4" />
          Cancel
        </Button>
        <Button
          size="sm"
          onClick={handleSave}
          className="flex items-center gap-2"
        >
          <Check className="w-4 h-4" />
          Save
        </Button>
      </div>

      <div className="text-xs text-gray-500">
        Press Ctrl/Cmd + Enter to save, Escape to cancel
      </div>
    </div>
  );
};
