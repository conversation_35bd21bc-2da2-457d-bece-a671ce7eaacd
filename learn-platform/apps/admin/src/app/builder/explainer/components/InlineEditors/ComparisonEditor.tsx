/**
 * Comparison Editor - Inline editor for comparison content
 */

'use client';

import React, { useState } from 'react';
import { Button } from '@learn-platform/shared-ui';
import { Input } from '@learn-platform/shared-ui';
import { Textarea } from '@learn-platform/shared-ui';
import { Check, X, Plus, Trash2 } from 'lucide-react';
import { ComparisonData } from '../../../../lib/components/templates/types';

interface ComparisonEditorProps {
  data: ComparisonData[];
  onSave: (data: ComparisonData[]) => void;
  onCancel: () => void;
}

export const ComparisonEditor: React.FC<ComparisonEditorProps> = ({
  data,
  onSave,
  onCancel
}) => {
  const [comparisons, setComparisons] = useState<ComparisonData[]>(data || [
    { label: '', before: '', after: '' }
  ]);

  const handleComparisonChange = (
    index: number,
    field: keyof ComparisonData,
    value: string
  ) => {
    const newComparisons = [...comparisons];
    newComparisons[index] = { ...newComparisons[index], [field]: value };
    setComparisons(newComparisons);
  };

  const addComparison = () => {
    setComparisons([...comparisons, { label: '', before: '', after: '' }]);
  };

  const removeComparison = (index: number) => {
    if (comparisons.length <= 1) return;
    const newComparisons = comparisons.filter((_, i) => i !== index);
    setComparisons(newComparisons);
  };

  const handleSave = () => {
    const filteredComparisons = comparisons.filter(comp =>
      comp.label.trim() !== '' || comp.before.trim() !== '' || comp.after.trim() !== ''
    );
    if (filteredComparisons.length === 0) {
      filteredComparisons.push({
        label: 'Comparison',
        before: 'Before',
        after: 'After'
      });
    }
    onSave(filteredComparisons);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) {
      e.preventDefault();
      handleSave();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      onCancel();
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium text-gray-700">Comparisons</h4>
        <span className="text-xs text-gray-500">
          {comparisons.length} comparison{comparisons.length !== 1 ? 's' : ''}
        </span>
      </div>

      <div className="space-y-4">
        {comparisons.map((comparison, index) => (
          <div key={index} className="border border-gray-200 rounded-lg p-4 space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-600">
                Comparison {index + 1}
              </span>
              {comparisons.length > 1 && (
                <Button
                  variant="ghost"
                  size="sm"
                  className="w-8 h-8 p-0 text-red-500 hover:text-red-700"
                  onClick={() => removeComparison(index)}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              )}
            </div>

            <div className="space-y-3">
              <div>
                <label className="block text-xs font-medium text-gray-600 mb-1">
                  Label
                </label>
                <Input
                  value={comparison.label}
                  onChange={(e) => handleComparisonChange(index, 'label', e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Comparison label..."
                  autoFocus={index === 0}
                />
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-xs font-medium text-gray-600 mb-1">
                    Before
                  </label>
                  <Textarea
                    value={comparison.before}
                    onChange={(e) => handleComparisonChange(index, 'before', e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder="Before state..."
                    className="min-h-[80px] resize-none"
                  />
                </div>

                <div>
                  <label className="block text-xs font-medium text-gray-600 mb-1">
                    After
                  </label>
                  <Textarea
                    value={comparison.after}
                    onChange={(e) => handleComparisonChange(index, 'after', e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder="After state..."
                    className="min-h-[80px] resize-none"
                  />
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="flex items-center justify-between pt-2">
        <Button
          variant="outline"
          size="sm"
          onClick={addComparison}
          className="flex items-center gap-2"
        >
          <Plus className="w-4 h-4" />
          Add Comparison
        </Button>

        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={onCancel}
            className="flex items-center gap-2"
          >
            <X className="w-4 h-4" />
            Cancel
          </Button>
          <Button
            size="sm"
            onClick={handleSave}
            className="flex items-center gap-2"
          >
            <Check className="w-4 h-4" />
            Save
          </Button>
        </div>
      </div>

      <div className="text-xs text-gray-500">
        Press Ctrl/Cmd + Enter to save, Escape to cancel
      </div>
    </div>
  );
};
