import '@testing-library/jest-dom';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => '/',
}));

// Mock Next.js Link component
jest.mock('next/link', () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const MockLink = ({ children, href, ...props }: any) => {
    const React = require('react');
    return React.createElement('a', { href, ...props }, children);
  };
  MockLink.displayName = 'MockLink';
  return MockLink;
});

// Mock CSS modules
jest.mock('./app/page.module.css', () => ({
  page: 'page',
  wrapper: 'wrapper',
  container: 'container',
}));

// Mock global CSS
jest.mock('./app/global.css', () => ({}));

// Mock environment variables
process.env.NEXT_PUBLIC_API_URL = 'http://localhost:8787';
process.env.BETTER_AUTH_URL = 'http://localhost:3001';

// Mock fetch for API calls
global.fetch = jest.fn();

// Mock Request for Node.js environments (following auth lib pattern)
if (typeof global.Request === 'undefined') {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (global as any).Request = class MockRequest {
    url: string;
    method: string;
    headers: Map<string, string>;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    body?: any;

    constructor(url: string, init: RequestInit = {}) {
      this.url = url;
      this.method = init.method || 'GET';
      this.headers = new Map();
      this.body = init.body;

      if (init.headers) {
        if (init.headers instanceof Headers) {
          init.headers.forEach((value, key) => {
            this.headers.set(key.toLowerCase(), value);
          });
        } else if (Array.isArray(init.headers)) {
          init.headers.forEach(([key, value]) => {
            this.headers.set(key.toLowerCase(), value);
          });
        } else {
          Object.entries(init.headers).forEach(([key, value]) => {
            this.headers.set(key.toLowerCase(), value);
          });
        }
      }
    }
  };
}

// Mock Response for Node.js environments
if (typeof global.Response === 'undefined') {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  (global as any).Response = class MockResponse {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    body: any;
    status: number;
    ok: boolean;
    headers: Map<string, string>;

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    constructor(body?: any, init: ResponseInit = {}) {
      this.body = body;
      this.status = init.status || 200;
      this.ok = this.status >= 200 && this.status < 300;
      this.headers = new Map();

      if (init.headers) {
        Object.entries(init.headers).forEach(([key, value]) => {
          this.headers.set(key.toLowerCase(), value);
        });
      }
    }
  };
}

// Mock ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks();
});
