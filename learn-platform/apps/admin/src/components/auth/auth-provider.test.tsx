/**
 * Tests for the AuthProvider component
 */

import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import { AuthProvider, useAuth } from './auth-provider';

// Mock the auth client at module level
jest.mock('../../lib/auth-client', () => ({
  authClient: {
    getSession: jest.fn(),
    signOut: jest.fn(),
  },
}));

// Test component that uses the auth context
const TestComponent = () => {
  const { user, isLoading, isAuthenticated, signOut, refreshSession } =
    useAuth();

  return (
    <div>
      <div data-testid="loading">{isLoading ? 'loading' : 'not-loading'}</div>
      <div data-testid="authenticated">
        {isAuthenticated ? 'authenticated' : 'not-authenticated'}
      </div>
      <div data-testid="user">{user ? user.name : 'no-user'}</div>
      <button onClick={signOut} data-testid="sign-out">
        Sign Out
      </button>
      <button onClick={refreshSession} data-testid="refresh">
        Refresh
      </button>
    </div>
  );
};

describe('AuthProvider', () => {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let mockAuthClient: any;

  beforeEach(() => {
    jest.clearAllMocks();
    // Get the mocked auth client
    const { authClient } = require('../../lib/auth-client');
    mockAuthClient = authClient;
  });

  it('should provide initial loading state', async () => {
    mockAuthClient.getSession.mockResolvedValue({ data: null });

    await act(async () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );
    });

    expect(screen.getByTestId('loading')).toHaveTextContent('loading');
    expect(screen.getByTestId('authenticated')).toHaveTextContent(
      'not-authenticated'
    );
    expect(screen.getByTestId('user')).toHaveTextContent('no-user');
  });

  it('should handle successful session retrieval', async () => {
    const mockUser = {
      id: 'user-1',
      email: '<EMAIL>',
      name: 'Test User',
      emailVerified: true,
    };

    mockAuthClient.getSession.mockResolvedValue({
      data: { user: mockUser },
    });

    await act(async () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );
    });

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
    });

    expect(screen.getByTestId('authenticated')).toHaveTextContent(
      'authenticated'
    );
    expect(screen.getByTestId('user')).toHaveTextContent('Test User');
  });

  it('should handle session retrieval failure', async () => {
    mockAuthClient.getSession.mockRejectedValue(new Error('Session failed'));

    // Suppress console.error for this test since we expect an error
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

    await act(async () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );
    });

    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
    });

    expect(screen.getByTestId('authenticated')).toHaveTextContent(
      'not-authenticated'
    );
    expect(screen.getByTestId('user')).toHaveTextContent('no-user');

    // Verify that the error was logged
    expect(consoleSpy).toHaveBeenCalledWith(
      'Failed to refresh session:',
      expect.any(Error)
    );
    consoleSpy.mockRestore();
  });

  it('should handle sign out', async () => {
    const mockUser = {
      id: 'user-1',
      email: '<EMAIL>',
      name: 'Test User',
      emailVerified: true,
    };

    mockAuthClient.getSession.mockResolvedValue({
      data: { user: mockUser },
    });
    mockAuthClient.signOut.mockResolvedValue({});

    await act(async () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );
    });

    // Wait for initial load
    await waitFor(() => {
      expect(screen.getByTestId('authenticated')).toHaveTextContent(
        'authenticated'
      );
    });

    // Sign out
    await act(async () => {
      screen.getByTestId('sign-out').click();
    });

    expect(mockAuthClient.signOut).toHaveBeenCalled();
    expect(screen.getByTestId('authenticated')).toHaveTextContent(
      'not-authenticated'
    );
    expect(screen.getByTestId('user')).toHaveTextContent('no-user');
  });

  it('should handle sign out failure gracefully', async () => {
    const mockUser = {
      id: 'user-1',
      email: '<EMAIL>',
      name: 'Test User',
      emailVerified: true,
    };

    mockAuthClient.getSession.mockResolvedValue({
      data: { user: mockUser },
    });
    mockAuthClient.signOut.mockRejectedValue(new Error('Sign out failed'));

    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

    await act(async () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );
    });

    // Wait for initial load
    await waitFor(() => {
      expect(screen.getByTestId('authenticated')).toHaveTextContent(
        'authenticated'
      );
    });

    // Sign out
    await act(async () => {
      screen.getByTestId('sign-out').click();
    });

    expect(consoleSpy).toHaveBeenCalledWith(
      'Failed to sign out:',
      expect.any(Error)
    );
    consoleSpy.mockRestore();
  });

  it('should refresh session when requested', async () => {
    const mockUser = {
      id: 'user-1',
      email: '<EMAIL>',
      name: 'Test User',
      emailVerified: true,
    };

    mockAuthClient.getSession
      .mockResolvedValueOnce({ data: null })
      .mockResolvedValueOnce({ data: { user: mockUser } });

    await act(async () => {
      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      );
    });

    // Wait for initial load (no user)
    await waitFor(() => {
      expect(screen.getByTestId('loading')).toHaveTextContent('not-loading');
    });

    expect(screen.getByTestId('authenticated')).toHaveTextContent(
      'not-authenticated'
    );

    // Refresh session
    await act(async () => {
      screen.getByTestId('refresh').click();
    });

    expect(screen.getByTestId('authenticated')).toHaveTextContent(
      'authenticated'
    );
    expect(screen.getByTestId('user')).toHaveTextContent('Test User');
  });

  it('should throw error when useAuth is used outside provider', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

    expect(() => {
      render(<TestComponent />);
    }).toThrow('useAuth must be used within an AuthProvider');

    consoleSpy.mockRestore();
  });
});
