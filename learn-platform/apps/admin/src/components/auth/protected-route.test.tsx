/**
 * Tests for the ProtectedRoute component
 */

import { render, screen, waitFor } from '@testing-library/react';
import { ProtectedRoute } from './protected-route';
import { useAuth } from './auth-provider';

// Mock the useAuth hook
jest.mock('./auth-provider', () => ({
  useAuth: jest.fn(),
}));

// Mock Next.js router
const mockPush = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
}));

const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>;

describe('ProtectedRoute', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render children when user is authenticated', () => {
    mockUseAuth.mockReturnValue({
      user: {
        id: 'user-1',
        email: '<EMAIL>',
        name: 'Test User',
        emailVerified: true,
      },
      isLoading: false,
      isAuthenticated: true,
      signOut: jest.fn(),
      refreshSession: jest.fn(),
    });

    render(
      <ProtectedRoute>
        <div data-testid="protected-content">Protected Content</div>
      </ProtectedRoute>
    );

    expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    expect(screen.getByText('Protected Content')).toBeInTheDocument();
  });

  it('should show loading spinner when loading', () => {
    mockUseAuth.mockReturnValue({
      user: null,
      isLoading: true,
      isAuthenticated: false,
      signOut: jest.fn(),
      refreshSession: jest.fn(),
    });

    const { container } = render(
      <ProtectedRoute>
        <div data-testid="protected-content">Protected Content</div>
      </ProtectedRoute>
    );

    expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
    // Look for the loading spinner by its specific class
    const spinner = container.querySelector('.animate-spin');
    expect(spinner).toBeInTheDocument();
    expect(spinner).toHaveClass('rounded-full', 'h-32', 'w-32', 'border-b-2', 'border-blue-600');
  });

  it('should redirect to login when not authenticated', async () => {
    mockUseAuth.mockReturnValue({
      user: null,
      isLoading: false,
      isAuthenticated: false,
      signOut: jest.fn(),
      refreshSession: jest.fn(),
    });

    render(
      <ProtectedRoute>
        <div data-testid="protected-content">Protected Content</div>
      </ProtectedRoute>
    );

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/login');
    });
  });

  it('should redirect to custom path when specified', async () => {
    mockUseAuth.mockReturnValue({
      user: null,
      isLoading: false,
      isAuthenticated: false,
      signOut: jest.fn(),
      refreshSession: jest.fn(),
    });

    render(
      <ProtectedRoute redirectTo="/custom-login">
        <div data-testid="protected-content">Protected Content</div>
      </ProtectedRoute>
    );

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/custom-login');
    });
  });

  it('should render custom fallback when loading', () => {
    mockUseAuth.mockReturnValue({
      user: null,
      isLoading: true,
      isAuthenticated: false,
      signOut: jest.fn(),
      refreshSession: jest.fn(),
    });

    const CustomFallback = () => (
      <div data-testid="custom-fallback">Custom Loading...</div>
    );

    render(
      <ProtectedRoute fallback={<CustomFallback />}>
        <div data-testid="protected-content">Protected Content</div>
      </ProtectedRoute>
    );

    expect(screen.getByTestId('custom-fallback')).toBeInTheDocument();
    expect(screen.getByText('Custom Loading...')).toBeInTheDocument();
    expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
  });

  it('should not redirect when loading', () => {
    mockUseAuth.mockReturnValue({
      user: null,
      isLoading: true,
      isAuthenticated: false,
      signOut: jest.fn(),
      refreshSession: jest.fn(),
    });

    render(
      <ProtectedRoute>
        <div data-testid="protected-content">Protected Content</div>
      </ProtectedRoute>
    );

    expect(mockPush).not.toHaveBeenCalled();
  });

  it('should handle authentication state changes', async () => {
    // Start with loading state
    mockUseAuth.mockReturnValue({
      user: null,
      isLoading: true,
      isAuthenticated: false,
      signOut: jest.fn(),
      refreshSession: jest.fn(),
    });

    const { rerender } = render(
      <ProtectedRoute>
        <div data-testid="protected-content">Protected Content</div>
      </ProtectedRoute>
    );

    // Should show loading
    expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();

    // Change to authenticated
    mockUseAuth.mockReturnValue({
      user: {
        id: 'user-1',
        email: '<EMAIL>',
        name: 'Test User',
        emailVerified: true,
      },
      isLoading: false,
      isAuthenticated: true,
      signOut: jest.fn(),
      refreshSession: jest.fn(),
    });

    rerender(
      <ProtectedRoute>
        <div data-testid="protected-content">Protected Content</div>
      </ProtectedRoute>
    );

    // Should show content
    expect(screen.getByTestId('protected-content')).toBeInTheDocument();
  });
});
