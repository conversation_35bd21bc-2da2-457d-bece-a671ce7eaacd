//@ts-check

const { composePlugins, withNx } = require('@nx/next');

/**
 * @type {import('@nx/next/plugins/with-nx').WithNxOptions}
 **/
const nextConfig = {
  // Use this to set Nx-specific options
  // See: https://nx.dev/recipes/next/next-config-setup
  nx: {},

  // For GitHub Actions + Vercel CLI deployment, let Vercel handle file tracing
  // outputFileTracingRoot causes path duplication when running CLI from app directory
  // outputFileTracingRoot: path.join(__dirname, '../../'),

  // Transpile shared packages for proper bundling
  transpilePackages: [
    '@learn-platform/shared-ui',
    '@learn-platform/shared-styles',
    '@learn-platform/trpc',
    '@learn-platform/auth',
    '@learn-platform/theme'
  ],

  webpack: (config) => {
    // Add CSS path mapping support
    config.resolve.alias = {
      ...config.resolve.alias,
      '@learn-platform/shared-styles': require('path').resolve(__dirname, '../../libs/shared/styles/src'),
    };
    return config;
  },
};

const plugins = [
  // Add more Next.js plugins to this list if needed.
  withNx,
];

module.exports = composePlugins(...plugins)(nextConfig);
