{"name": "admin", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/admin", "projectType": "application", "tags": [], "// targets": "to see all targets run: nx show project admin --web", "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/apps/admin"], "options": {"jestConfig": "apps/admin/jest.config.ts", "passWithNoTests": true}}}}