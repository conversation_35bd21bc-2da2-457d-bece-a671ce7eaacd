{"name": "@learn-platform/web", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev --port 3000", "build": "next build", "start": "next start", "lint": "next lint", "vercel-build": "next build", "preview": "opennextjs-cloudflare build && opennextjs-cloudflare preview", "deploy": "opennextjs-cloudflare build && opennextjs-cloudflare deploy", "upload": "opennextjs-cloudflare build && opennextjs-cloudflare upload", "deploy:preview": "opennextjs-cloudflare build && wrangler deploy --env preview", "deploy:production": "opennextjs-cloudflare build && wrangler deploy --env production", "cf-typegen": "wrangler types --env-interface CloudflareEnv cloudflare-env.d.ts"}, "dependencies": {"next": "~15.2.4", "react": "19.0.0", "react-dom": "19.0.0", "@learn-platform/version": "*", "@learn-platform/theme": "*"}, "devDependencies": {"@opennextjs/cloudflare": "^1.0.0", "wrangler": "^4.20.0"}}