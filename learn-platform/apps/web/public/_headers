# Static asset caching headers for Cloudflare Workers
# These headers optimize caching for Next.js static assets

# Cache Next.js static assets for 1 year (immutable)
/_next/static/*
  Cache-Control: public,max-age=31536000,immutable

# Cache other static assets for 1 hour
/favicon.ico
  Cache-Control: public,max-age=3600

# Cache images for 1 day
/*.png
  Cache-Control: public,max-age=86400

/*.jpg
  Cache-Control: public,max-age=86400

/*.jpeg
  Cache-Control: public,max-age=86400

/*.gif
  Cache-Control: public,max-age=86400

/*.svg
  Cache-Control: public,max-age=86400

/*.webp
  Cache-Control: public,max-age=86400

# Cache fonts for 1 year
/*.woff
  Cache-Control: public,max-age=31536000

/*.woff2
  Cache-Control: public,max-age=31536000

/*.ttf
  Cache-Control: public,max-age=31536000

/*.otf
  Cache-Control: public,max-age=31536000
