{
  // Cloudflare Workers configuration for Next.js web application
  // This configuration enables deployment to Cloudflare Workers using @opennextjs/cloudflare

  "$schema": "node_modules/wrangler/config-schema.json",
  "name": "learn-platform-web",
  "main": ".open-next/worker.js",
  "compatibility_date": "2024-12-30",
  "compatibility_flags": [
    "nodejs_compat",
    "global_fetch_strictly_public"
  ],

  // Cloudflare Workers static assets configuration
  "assets": {
    "directory": ".open-next/assets",
    "binding": "ASSETS"
  },

  // Service binding for self-reference (required for OpenNext)
  "services": [
    {
      "binding": "WORKER_SELF_REFERENCE",
      "service": "learn-platform-web"
    }
  ],

  // Environment-specific configurations
  "env": {
    // Preview environment (for dev branch and testing)
    "preview": {
      "name": "learn-platform-web-preview",
      "services": [
        {
          "binding": "WORKER_SELF_REFERENCE",
          "service": "learn-platform-web-preview"
        }
      ],
      "vars": {
        "NODE_ENV": "production",
        "NEXT_PUBLIC_APP_ENV": "preview",
        "NEXT_PUBLIC_API_URL": "https://learn-platform-api-dev.bm.workers.dev",
        "BETTER_AUTH_TRUSTED_ORIGINS": "https://learn-platform-web-preview.bm.workers.dev",
        "BETTER_AUTH_URL": "https://learn-platform-web-preview.bm.workers.dev"
      }
      // Note: Sensitive environment variables like DATABASE_URL, BETTER_AUTH_SECRET
      // should be configured in the Cloudflare Workers dashboard under Settings > Environment variables
      // or passed via GitHub Secrets in the deployment workflow
    },

    // Production environment (for main branch)
    "production": {
      "name": "learn-platform-web-prod",
      "services": [
        {
          "binding": "WORKER_SELF_REFERENCE",
          "service": "learn-platform-web-prod"
        }
      ],
      "vars": {
        "NODE_ENV": "production",
        "NEXT_PUBLIC_APP_ENV": "production",
        "NEXT_PUBLIC_API_URL": "https://learn-platform-api-prod.bm.workers.dev",
        "BETTER_AUTH_TRUSTED_ORIGINS": "https://learn-platform-web-prod.bm.workers.dev",
        "BETTER_AUTH_URL": "https://learn-platform-web-prod.bm.workers.dev"
      }
      // Production-specific configuration
      // Custom domains can be configured in Cloudflare Workers dashboard
    }
  },

  // Local development configuration
  "dev": {
    "port": 3000,
    "local_protocol": "http",
    "upstream_protocol": "https"
  },

  // Optional: R2 bucket for Next.js incremental cache (recommended for production)
  "r2_buckets": [
    // Uncomment to enable R2-based caching for better performance
    // {
    //   "binding": "NEXT_INC_CACHE_R2_BUCKET",
    //   "bucket_name": "learn-platform-web-cache"
    // }
  ],

  // Optional: KV namespaces (add as needed for additional caching or data storage)
  "kv_namespaces": [
    // {
    //   "binding": "CACHE",
    //   "id": "your-kv-namespace-id",
    //   "preview_id": "your-preview-kv-namespace-id"
    // }
  ],

  // Optional: D1 databases (add as needed)
  "d1_databases": [
    // {
    //   "binding": "DB",
    //   "database_name": "your-database-name",
    //   "database_id": "your-database-id"
    // }
  ]
}
