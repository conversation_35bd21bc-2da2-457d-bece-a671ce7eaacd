{"name": "web", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/web", "projectType": "application", "tags": [], "// targets": "to see all targets run: nx show project web --web", "targets": {"build": {"executor": "nx:run-commands", "dependsOn": ["^build"], "cache": true, "inputs": ["default", "^production", {"externalDependencies": ["next"]}], "outputs": ["{workspaceRoot}/apps/web/.next/!(cache)/**/*", "{workspaceRoot}/apps/web/.next/!(cache)"], "options": {"cwd": "apps/web", "command": "../../node_modules/.bin/next build"}}, "dev": {"executor": "nx:run-commands", "continuous": true, "options": {"cwd": "apps/web", "command": "../../node_modules/.bin/next dev"}}, "start": {"executor": "nx:run-commands", "continuous": true, "dependsOn": ["build"], "options": {"cwd": "apps/web", "command": "../../node_modules/.bin/next start"}}, "serve-static": {"executor": "nx:run-commands", "continuous": true, "dependsOn": ["build"], "options": {"cwd": "apps/web", "command": "../../node_modules/.bin/next start"}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/web/src/**/*.{ts,tsx,js,jsx}", "apps/web/*.{ts,tsx,js,jsx}", "!apps/web/.next/**/*", "!apps/web/.open-next/**/*", "!apps/web/node_modules/**/*", "!apps/web/dist/**/*", "!apps/web/build/**/*"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/apps/web"], "options": {"jestConfig": "apps/web/jest.config.ts", "passWithNoTests": true}}}}