{"extends": "../../tsconfig.base.json", "compilerOptions": {"jsx": "preserve", "strict": true, "noEmit": true, "emitDeclarationOnly": false, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "incremental": true, "plugins": [{"name": "next"}]}, "include": ["**/*.js", "**/*.jsx", "**/*.ts", "**/*.tsx", "../../dist/apps/web/.next/types/**/*.ts", "../web/.next/types/**/*.ts", "next-env.d.ts", ".next/types/**/*.ts"], "exclude": ["node_modules"]}