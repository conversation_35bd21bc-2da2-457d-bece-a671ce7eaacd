# Authentication Debug Configuration
# Copy this file to .env.local and uncomment the variables you want to enable

# Enable comprehensive auth debugging (default: enabled in development)
# AUTH_DEBUG=true

# Set debug log level: debug, info, warn, error (default: info)
# AUTH_LOG_LEVEL=debug

# Enable database operation logging (default: false)
# AUTH_LOG_DATABASE=true

# Enable request/response logging (default: true)
# AUTH_LOG_REQUESTS=true
# AUTH_LOG_RESPONSES=true

# Better-auth configuration
BETTER_AUTH_URL=http://localhost:3000
# BETTER_AUTH_SECRET=your-secret-key-here
# BETTER_AUTH_TRUSTED_ORIGINS=http://localhost:3001,http://localhost:8787

# Database configuration
# DATABASE_URL=your-database-url-here
