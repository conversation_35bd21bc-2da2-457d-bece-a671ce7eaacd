/**
 * Caching utilities for performance optimization
 * Provides in-memory caching, localStorage caching, and cache invalidation strategies
 */

// Cache configuration
interface CacheConfig {
  ttl?: number; // Time to live in milliseconds
  maxSize?: number; // Maximum number of items
  storage?: 'memory' | 'localStorage' | 'sessionStorage';
}

// Cache item interface
interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccessed: number;
}

// In-memory cache implementation
class MemoryCache<T> {
  private cache = new Map<string, CacheItem<T>>();
  private maxSize: number;
  private defaultTTL: number;

  constructor(config: CacheConfig = {}) {
    this.maxSize = config.maxSize || 100;
    this.defaultTTL = config.ttl || 5 * 60 * 1000; // 5 minutes default
  }

  set(key: string, data: T, ttl?: number): void {
    const now = Date.now();
    const itemTTL = ttl || this.defaultTTL;

    // Remove expired items if cache is full
    if (this.cache.size >= this.maxSize) {
      this.cleanup();
      
      // If still full, remove least recently used item
      if (this.cache.size >= this.maxSize) {
        this.removeLRU();
      }
    }

    this.cache.set(key, {
      data,
      timestamp: now,
      ttl: itemTTL,
      accessCount: 0,
      lastAccessed: now,
    });
  }

  get(key: string): T | null {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }

    const now = Date.now();
    
    // Check if item has expired
    if (now - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    // Update access statistics
    item.accessCount++;
    item.lastAccessed = now;

    return item.data;
  }

  has(key: string): boolean {
    return this.get(key) !== null;
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }

  private cleanup(): void {
    const now = Date.now();
    
    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > item.ttl) {
        this.cache.delete(key);
      }
    }
  }

  private removeLRU(): void {
    let oldestKey = '';
    let oldestTime = Date.now();

    for (const [key, item] of this.cache.entries()) {
      if (item.lastAccessed < oldestTime) {
        oldestTime = item.lastAccessed;
        oldestKey = key;
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey);
    }
  }

  // Get cache statistics
  getStats() {
    const items = Array.from(this.cache.values());
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      totalAccesses: items.reduce((sum, item) => sum + item.accessCount, 0),
      averageAge: items.length > 0 
        ? items.reduce((sum, item) => sum + (Date.now() - item.timestamp), 0) / items.length
        : 0,
    };
  }
}

// Storage-based cache implementation
class StorageCache<T> {
  private storage: Storage;
  private prefix: string;
  private defaultTTL: number;

  constructor(storage: Storage, prefix = 'cache_', ttl = 5 * 60 * 1000) {
    this.storage = storage;
    this.prefix = prefix;
    this.defaultTTL = ttl;
  }

  set(key: string, data: T, ttl?: number): void {
    const item: CacheItem<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL,
      accessCount: 0,
      lastAccessed: Date.now(),
    };

    try {
      this.storage.setItem(this.prefix + key, JSON.stringify(item));
    } catch (error) {
      // Handle storage quota exceeded
      console.warn('Storage cache full, clearing old items:', error);
      this.cleanup();
      
      try {
        this.storage.setItem(this.prefix + key, JSON.stringify(item));
      } catch (retryError) {
        console.error('Failed to store cache item:', retryError);
      }
    }
  }

  get(key: string): T | null {
    try {
      const itemStr = this.storage.getItem(this.prefix + key);
      
      if (!itemStr) {
        return null;
      }

      const item: CacheItem<T> = JSON.parse(itemStr);
      const now = Date.now();

      // Check if item has expired
      if (now - item.timestamp > item.ttl) {
        this.storage.removeItem(this.prefix + key);
        return null;
      }

      // Update access statistics
      item.accessCount++;
      item.lastAccessed = now;
      this.storage.setItem(this.prefix + key, JSON.stringify(item));

      return item.data;
    } catch (error) {
      console.error('Error reading from storage cache:', error);
      return null;
    }
  }

  has(key: string): boolean {
    return this.get(key) !== null;
  }

  delete(key: string): boolean {
    try {
      this.storage.removeItem(this.prefix + key);
      return true;
    } catch (error) {
      console.error('Error deleting from storage cache:', error);
      return false;
    }
  }

  clear(): void {
    try {
      const keysToRemove: string[] = [];
      
      for (let i = 0; i < this.storage.length; i++) {
        const key = this.storage.key(i);
        if (key && key.startsWith(this.prefix)) {
          keysToRemove.push(key);
        }
      }

      keysToRemove.forEach(key => this.storage.removeItem(key));
    } catch (error) {
      console.error('Error clearing storage cache:', error);
    }
  }

  private cleanup(): void {
    try {
      const now = Date.now();
      const keysToRemove: string[] = [];

      for (let i = 0; i < this.storage.length; i++) {
        const key = this.storage.key(i);
        
        if (key && key.startsWith(this.prefix)) {
          const itemStr = this.storage.getItem(key);
          
          if (itemStr) {
            try {
              const item: CacheItem<any> = JSON.parse(itemStr);
              
              if (now - item.timestamp > item.ttl) {
                keysToRemove.push(key);
              }
            } catch (parseError) {
              // Remove corrupted items
              keysToRemove.push(key);
            }
          }
        }
      }

      keysToRemove.forEach(key => this.storage.removeItem(key));
    } catch (error) {
      console.error('Error during cache cleanup:', error);
    }
  }
}

// Cache factory
export function createCache<T>(config: CacheConfig = {}) {
  const storage = config.storage || 'memory';

  switch (storage) {
    case 'localStorage':
      return new StorageCache<T>(localStorage, 'lp_cache_', config.ttl);
    case 'sessionStorage':
      return new StorageCache<T>(sessionStorage, 'lp_session_', config.ttl);
    default:
      return new MemoryCache<T>(config);
  }
}

// Global cache instances
export const memoryCache = createCache({ storage: 'memory', maxSize: 100 });
export const persistentCache = createCache({ storage: 'localStorage', ttl: 24 * 60 * 60 * 1000 }); // 24 hours
export const sessionCache = createCache({ storage: 'sessionStorage', ttl: 60 * 60 * 1000 }); // 1 hour

// Cache keys for different data types
export const CacheKeys = {
  USER_PROFILE: 'user_profile',
  LEARNING_CONTENT: (id: string) => `learning_content_${id}`,
  USER_LEARNING_LIST: (userId: string) => `user_learning_${userId}`,
  AI_MODELS: 'ai_models',
  CONTENT_STATS: (userId: string) => `content_stats_${userId}`,
  SEARCH_RESULTS: (query: string) => `search_${btoa(query)}`,
} as const;

// Utility functions for common caching patterns
export function cacheAsync<T>(
  key: string,
  asyncFn: () => Promise<T>,
  cache = memoryCache,
  ttl?: number
): Promise<T> {
  return new Promise((resolve, reject) => {
    // Try to get from cache first
    const cached = cache.get(key);
    
    if (cached !== null) {
      resolve(cached);
      return;
    }

    // If not in cache, execute async function
    asyncFn()
      .then(result => {
        cache.set(key, result, ttl);
        resolve(result);
      })
      .catch(reject);
  });
}

// React hook for cached data
export function useCachedData<T>(
  key: string,
  fetchFn: () => Promise<T>,
  dependencies: any[] = [],
  cache = memoryCache
) {
  const [data, setData] = React.useState<T | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState<Error | null>(null);

  React.useEffect(() => {
    let isMounted = true;

    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Check cache first
        const cached = cache.get(key);
        
        if (cached !== null) {
          if (isMounted) {
            setData(cached);
            setLoading(false);
          }
          return;
        }

        // Fetch fresh data
        const result = await fetchFn();
        
        if (isMounted) {
          cache.set(key, result);
          setData(result);
          setLoading(false);
        }
      } catch (err) {
        if (isMounted) {
          setError(err instanceof Error ? err : new Error('Unknown error'));
          setLoading(false);
        }
      }
    };

    loadData();

    return () => {
      isMounted = false;
    };
  }, [key, ...dependencies]);

  const invalidate = React.useCallback(() => {
    cache.delete(key);
  }, [key, cache]);

  const refresh = React.useCallback(async () => {
    cache.delete(key);
    setLoading(true);
    
    try {
      const result = await fetchFn();
      cache.set(key, result);
      setData(result);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Unknown error'));
    } finally {
      setLoading(false);
    }
  }, [key, fetchFn, cache]);

  return { data, loading, error, invalidate, refresh };
}

// Cache invalidation utilities
export function invalidateUserCache(userId: string) {
  memoryCache.delete(CacheKeys.USER_LEARNING_LIST(userId));
  memoryCache.delete(CacheKeys.CONTENT_STATS(userId));
  persistentCache.delete(CacheKeys.USER_LEARNING_LIST(userId));
  persistentCache.delete(CacheKeys.CONTENT_STATS(userId));
}

export function invalidateContentCache(contentId: string) {
  memoryCache.delete(CacheKeys.LEARNING_CONTENT(contentId));
  persistentCache.delete(CacheKeys.LEARNING_CONTENT(contentId));
}

// Cache warming utilities
export function warmCache() {
  // Pre-load commonly accessed data
  if (typeof window !== 'undefined') {
    // Warm AI models cache
    cacheAsync(CacheKeys.AI_MODELS, async () => {
      // This would fetch AI models from API
      return [];
    }, persistentCache, 24 * 60 * 60 * 1000); // 24 hours
  }
}
