/**
 * Performance optimization utilities
 * Provides lazy loading, caching, and performance monitoring
 */

'use client';

import React, { Suspense, lazy, ComponentType, ReactNode } from 'react';
import { Loader2 } from 'lucide-react';

// Generic loading component
export function LoadingSpinner({ size = 'default', text }: { size?: 'sm' | 'default' | 'lg'; text?: string }) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    default: 'h-6 w-6',
    lg: 'h-8 w-8',
  };

  return (
    <div className="flex items-center justify-center p-4">
      <div className="flex flex-col items-center space-y-2">
        <Loader2 className={`${sizeClasses[size]} animate-spin text-blue-500`} />
        {text && <p className="text-sm text-gray-600">{text}</p>}
      </div>
    </div>
  );
}

// Enhanced loading component for specific contexts
export function ComponentLoadingFallback({
  text = 'Loading...',
  height = 'auto'
}: {
  text?: string;
  height?: string | number;
}) {
  return (
    <div
      className="flex items-center justify-center bg-gray-50 rounded-lg border border-gray-200"
      style={{ height: typeof height === 'number' ? `${height}px` : height, minHeight: '100px' }}
    >
      <LoadingSpinner text={text} />
    </div>
  );
}

// Lazy loading wrapper with error boundary
export function LazyWrapper<P extends object>({
  Component,
  fallback,
  errorFallback,
  ...props
}: {
  Component: ComponentType<P>;
  fallback?: ReactNode;
  errorFallback?: ReactNode;
} & P) {
  const defaultFallback = <ComponentLoadingFallback />;
  const defaultErrorFallback = (
    <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
      <p className="text-red-700">Failed to load component. Please try refreshing the page.</p>
    </div>
  );

  return (
    <Suspense fallback={fallback || defaultFallback}>
      <ErrorBoundary fallback={errorFallback || defaultErrorFallback}>
        <Component {...props} />
      </ErrorBoundary>
    </Suspense>
  );
}

// Simple error boundary for lazy components
class ErrorBoundary extends React.Component<
  { children: ReactNode; fallback: ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: ReactNode; fallback: ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Lazy component error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback;
    }

    return this.props.children;
  }
}

// Lazy loaded components for the learning platform
export const LazyComponents = {
  // Learning components
  LearningInputForm: lazy(() =>
    import('../components/learn/LearningInputForm').then(module => ({
      default: module.LearningInputForm
    }))
  ),

  LearningContentCard: lazy(() =>
    import('../components/learn/LearningContentCard').then(module => ({
      default: module.LearningContentCard
    }))
  ),

  MultiStepExplain: lazy(() =>
    import('../../admin/src/lib/components/templates/MultiStepExplain').then(module => ({
      default: module.MultiStepExplain
    }))
  ),

  AdvancedSearchFilters: lazy(() =>
    import('../components/learn/AdvancedSearchFilters').then(module => ({
      default: module.AdvancedSearchFilters
    }))
  ),

  FeedbackModal: lazy(() =>
    import('../components/learn/FeedbackModal').then(module => ({
      default: module.FeedbackModal
    }))
  ),

  ShareContentModal: lazy(() =>
    import('../components/learn/ShareContentModal').then(module => ({
      default: module.ShareContentModal
    }))
  ),

  ProgressTracker: lazy(() =>
    import('../components/learn/ProgressTracker').then(module => ({
      default: module.ProgressTracker
    }))
  ),
};

// Hook for intersection observer (lazy loading on scroll)
export function useIntersectionObserver(
  ref: React.RefObject<Element>,
  options: IntersectionObserverInit = {}
) {
  const [isIntersecting, setIsIntersecting] = React.useState(false);
  const [hasIntersected, setHasIntersected] = React.useState(false);

  React.useEffect(() => {
    const element = ref.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
        if (entry.isIntersecting && !hasIntersected) {
          setHasIntersected(true);
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options,
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [ref, options, hasIntersected]);

  return { isIntersecting, hasIntersected };
}

// Lazy loading component that loads when in viewport
export function LazyOnScroll({
  children,
  fallback,
  height = 200,
  className = '',
}: {
  children: ReactNode;
  fallback?: ReactNode;
  height?: number;
  className?: string;
}) {
  const ref = React.useRef<HTMLDivElement>(null);
  const { hasIntersected } = useIntersectionObserver(ref, {
    threshold: 0.1,
    rootMargin: '100px',
  });

  return (
    <div
      ref={ref}
      className={className}
      style={{ minHeight: hasIntersected ? 'auto' : `${height}px` }}
    >
      {hasIntersected ? (
        children
      ) : (
        fallback || <ComponentLoadingFallback height={height} />
      )}
    </div>
  );
}

// Image lazy loading component
export function LazyImage({
  src,
  alt,
  className = '',
  placeholder,
  ...props
}: React.ImgHTMLAttributes<HTMLImageElement> & {
  placeholder?: string;
}) {
  const [isLoaded, setIsLoaded] = React.useState(false);
  const [hasError, setHasError] = React.useState(false);
  const imgRef = React.useRef<HTMLImageElement>(null);
  const { hasIntersected } = useIntersectionObserver(imgRef);

  const handleLoad = () => {
    setIsLoaded(true);
  };

  const handleError = () => {
    setHasError(true);
  };

  if (hasError) {
    return (
      <div className={`bg-gray-200 flex items-center justify-center ${className}`}>
        <span className="text-gray-500 text-sm">Failed to load image</span>
      </div>
    );
  }

  return (
    <div ref={imgRef} className={`relative ${className}`}>
      {hasIntersected && (
        <>
          {!isLoaded && (
            <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
              {placeholder && (
                <span className="text-gray-500 text-sm">{placeholder}</span>
              )}
            </div>
          )}
          <img
            src={src}
            alt={alt}
            onLoad={handleLoad}
            onError={handleError}
            className={`transition-opacity duration-300 ${
              isLoaded ? 'opacity-100' : 'opacity-0'
            } ${className}`}
            {...props}
          />
        </>
      )}
    </div>
  );
}

// Code splitting utility for dynamic imports
export function createLazyComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  fallback?: ReactNode
) {
  const LazyComponent = lazy(importFn);

  return function WrappedLazyComponent(props: React.ComponentProps<T>) {
    return (
      <Suspense fallback={fallback || <ComponentLoadingFallback />}>
        <LazyComponent {...props} />
      </Suspense>
    );
  };
}

// Performance monitoring hook
export function usePerformanceMonitor(componentName: string) {
  React.useEffect(() => {
    const startTime = performance.now();

    return () => {
      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // Log performance metrics in development
      if (process.env.NODE_ENV === 'development') {
        console.log(`${componentName} render time: ${renderTime.toFixed(2)}ms`);
      }

      // In production, send to analytics service
      if (process.env.NODE_ENV === 'production' && renderTime > 100) {
        // Example: analytics.track('slow_component_render', { componentName, renderTime });
        console.warn(`Slow render detected: ${componentName} took ${renderTime.toFixed(2)}ms`);
      }
    };
  }, [componentName]);
}

// Bundle size analyzer (development only)
export function analyzeBundleSize() {
  if (process.env.NODE_ENV !== 'development') return;

  // This would integrate with webpack-bundle-analyzer or similar
  console.log('Bundle analysis would run here in development');
}

// Memory usage monitor
export function useMemoryMonitor() {
  React.useEffect(() => {
    if (process.env.NODE_ENV !== 'development') return;

    const checkMemory = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        console.log('Memory usage:', {
          used: `${(memory.usedJSHeapSize / 1048576).toFixed(2)} MB`,
          total: `${(memory.totalJSHeapSize / 1048576).toFixed(2)} MB`,
          limit: `${(memory.jsHeapSizeLimit / 1048576).toFixed(2)} MB`,
        });
      }
    };

    const interval = setInterval(checkMemory, 10000); // Check every 10 seconds

    return () => clearInterval(interval);
  }, []);
}
