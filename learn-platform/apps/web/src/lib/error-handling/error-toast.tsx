/**
 * Error Toast Notification System
 * Provides user-friendly error notifications with recovery actions
 */

'use client';

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { X, AlertTriangle, AlertCircle, Info, CheckCircle } from 'lucide-react';
import { Button } from '@learn-platform/shared-ui';
import { classifyError, AppError, RecoveryAction } from './error-utils';

// Toast types
export enum ToastType {
  ERROR = 'error',
  WARNING = 'warning',
  INFO = 'info',
  SUCCESS = 'success',
}

// Toast interface
export interface Toast {
  id: string;
  type: ToastType;
  title: string;
  message: string;
  actions?: RecoveryAction[];
  duration?: number;
  persistent?: boolean;
}

// Toast context
interface ToastContextType {
  toasts: Toast[];
  showToast: (toast: Omit<Toast, 'id'>) => void;
  showError: (error: any, context?: Record<string, any>) => void;
  showSuccess: (message: string, title?: string) => void;
  showWarning: (message: string, title?: string) => void;
  showInfo: (message: string, title?: string) => void;
  dismissToast: (id: string) => void;
  clearAllToasts: () => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

// Toast provider component
export function ToastProvider({ children }: { children: ReactNode }) {
  const [toasts, setToasts] = useState<Toast[]>([]);

  const showToast = useCallback((toast: Omit<Toast, 'id'>) => {
    const id = `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const newToast: Toast = { ...toast, id };

    setToasts(prev => [...prev, newToast]);

    // Auto-dismiss non-persistent toasts
    if (!toast.persistent) {
      const duration = toast.duration || (toast.type === ToastType.ERROR ? 8000 : 5000);
      setTimeout(() => {
        dismissToast(id);
      }, duration);
    }
  }, []);

  const showError = useCallback((error: any, context?: Record<string, any>) => {
    const appError = classifyError(error);
    
    showToast({
      type: ToastType.ERROR,
      title: 'Error',
      message: appError.userMessage,
      actions: appError.recoveryActions,
      persistent: appError.severity === 'CRITICAL',
    });

    // Log error for monitoring
    console.error('Error displayed in toast:', { appError, context });
  }, [showToast]);

  const showSuccess = useCallback((message: string, title = 'Success') => {
    showToast({
      type: ToastType.SUCCESS,
      title,
      message,
      duration: 4000,
    });
  }, [showToast]);

  const showWarning = useCallback((message: string, title = 'Warning') => {
    showToast({
      type: ToastType.WARNING,
      title,
      message,
      duration: 6000,
    });
  }, [showToast]);

  const showInfo = useCallback((message: string, title = 'Info') => {
    showToast({
      type: ToastType.INFO,
      title,
      message,
      duration: 5000,
    });
  }, [showToast]);

  const dismissToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  const clearAllToasts = useCallback(() => {
    setToasts([]);
  }, []);

  return (
    <ToastContext.Provider
      value={{
        toasts,
        showToast,
        showError,
        showSuccess,
        showWarning,
        showInfo,
        dismissToast,
        clearAllToasts,
      }}
    >
      {children}
      <ToastContainer />
    </ToastContext.Provider>
  );
}

// Hook to use toast context
export function useToast() {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
}

// Toast container component
function ToastContainer() {
  const { toasts, dismissToast } = useToast();

  if (toasts.length === 0) {
    return null;
  }

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full">
      {toasts.map(toast => (
        <ToastItem
          key={toast.id}
          toast={toast}
          onDismiss={() => dismissToast(toast.id)}
        />
      ))}
    </div>
  );
}

// Individual toast item component
function ToastItem({ toast, onDismiss }: { toast: Toast; onDismiss: () => void }) {
  const getToastStyles = () => {
    switch (toast.type) {
      case ToastType.ERROR:
        return 'bg-red-50 border-red-200 text-red-800';
      case ToastType.WARNING:
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case ToastType.INFO:
        return 'bg-blue-50 border-blue-200 text-blue-800';
      case ToastType.SUCCESS:
        return 'bg-green-50 border-green-200 text-green-800';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };

  const getIcon = () => {
    switch (toast.type) {
      case ToastType.ERROR:
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      case ToastType.WARNING:
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
      case ToastType.INFO:
        return <Info className="h-5 w-5 text-blue-500" />;
      case ToastType.SUCCESS:
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      default:
        return <Info className="h-5 w-5 text-gray-500" />;
    }
  };

  return (
    <div
      className={`
        ${getToastStyles()}
        border rounded-lg shadow-lg p-4 animate-in slide-in-from-right-full
        transition-all duration-300 ease-in-out
      `}
    >
      <div className="flex items-start">
        <div className="flex-shrink-0 mr-3">
          {getIcon()}
        </div>
        
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-semibold mb-1">
            {toast.title}
          </h4>
          <p className="text-sm opacity-90">
            {toast.message}
          </p>
          
          {toast.actions && toast.actions.length > 0 && (
            <div className="mt-3 space-x-2">
              {toast.actions.map((action, index) => (
                <Button
                  key={index}
                  size="sm"
                  variant={action.primary ? "default" : "outline"}
                  onClick={() => {
                    action.action();
                    onDismiss();
                  }}
                  className="text-xs"
                >
                  {action.label}
                </Button>
              ))}
            </div>
          )}
        </div>
        
        <button
          onClick={onDismiss}
          className="flex-shrink-0 ml-2 p-1 rounded-md hover:bg-black/10 transition-colors"
          aria-label="Dismiss notification"
        >
          <X className="h-4 w-4" />
        </button>
      </div>
    </div>
  );
}

// Hook for handling errors with toast notifications
export function useErrorToast() {
  const { showError } = useToast();

  const handleError = useCallback((error: any, context?: Record<string, any>) => {
    showError(error, context);
  }, [showError]);

  return { handleError };
}

// Higher-order component for automatic error handling
export function withErrorToast<P extends object>(
  Component: React.ComponentType<P>
) {
  return function WrappedComponent(props: P) {
    const { handleError } = useErrorToast();

    // Add error handling to component props if they exist
    const enhancedProps = {
      ...props,
      onError: handleError,
    } as P;

    return <Component {...enhancedProps} />;
  };
}

// Utility function to show error toast from anywhere
export function showErrorToast(error: any, context?: Record<string, any>) {
  // This is a fallback for cases where useToast hook can't be used
  // In practice, you should use the hook within components
  const appError = classifyError(error);
  
  // Create a temporary toast element
  const toastElement = document.createElement('div');
  toastElement.className = 'fixed top-4 right-4 z-50 max-w-sm bg-red-50 border border-red-200 rounded-lg shadow-lg p-4';
  toastElement.innerHTML = `
    <div class="flex items-start">
      <div class="flex-shrink-0 mr-3">
        <svg class="h-5 w-5 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      </div>
      <div class="flex-1">
        <h4 class="text-sm font-semibold text-red-800 mb-1">Error</h4>
        <p class="text-sm text-red-700">${appError.userMessage}</p>
      </div>
      <button onclick="this.parentElement.parentElement.remove()" class="flex-shrink-0 ml-2 p-1 rounded-md hover:bg-red-100 transition-colors">
        <svg class="h-4 w-4 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>
  `;

  document.body.appendChild(toastElement);

  // Auto-remove after 8 seconds
  setTimeout(() => {
    if (toastElement.parentElement) {
      toastElement.remove();
    }
  }, 8000);
}
