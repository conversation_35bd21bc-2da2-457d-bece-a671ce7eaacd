/**
 * Error handling utilities
 * Provides functions for error classification, user-friendly messages, and recovery suggestions
 */

import { TRPCError } from '@trpc/server';

// Error types
export enum ErrorType {
  NETWORK = 'NETWORK',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  VALIDATION = 'VALIDATION',
  AI_SERVICE = 'AI_SERVICE',
  DATABASE = 'DATABASE',
  RATE_LIMIT = 'RATE_LIMIT',
  NOT_FOUND = 'NOT_FOUND',
  SERVER = 'SERVER',
  UNKNOWN = 'UNKNOWN',
}

// Error severity levels
export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

// Structured error interface
export interface AppError {
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  userMessage: string;
  recoveryActions: RecoveryAction[];
  originalError?: Error;
  context?: Record<string, any>;
  timestamp: string;
  errorId: string;
}

// Recovery action interface
export interface RecoveryAction {
  label: string;
  action: () => void;
  primary?: boolean;
}

/**
 * Classify error based on error object
 */
export function classifyError(error: any): AppError {
  const errorId = generateErrorId();
  const timestamp = new Date().toISOString();

  // Handle tRPC errors
  if (error instanceof TRPCError || error?.data?.code) {
    return classifyTRPCError(error, errorId, timestamp);
  }

  // Handle network errors
  if (error?.name === 'NetworkError' || error?.code === 'NETWORK_ERROR') {
    return {
      type: ErrorType.NETWORK,
      severity: ErrorSeverity.MEDIUM,
      message: 'Network connection failed',
      userMessage: 'Please check your internet connection and try again.',
      recoveryActions: [
        {
          label: 'Retry',
          action: () => window.location.reload(),
          primary: true,
        },
        {
          label: 'Check Connection',
          action: () => window.open('https://www.google.com', '_blank'),
        },
      ],
      originalError: error,
      timestamp,
      errorId,
    };
  }

  // Handle AI service errors
  if (error?.message?.includes('AI') || error?.code === 'AI_ERROR') {
    return {
      type: ErrorType.AI_SERVICE,
      severity: ErrorSeverity.HIGH,
      message: 'AI service error',
      userMessage: 'The AI service is temporarily unavailable. Please try again in a few minutes.',
      recoveryActions: [
        {
          label: 'Try Again',
          action: () => window.location.reload(),
          primary: true,
        },
        {
          label: 'Use Different Model',
          action: () => {
            // Navigate to settings to change AI model
            window.location.href = '/dashboard/settings';
          },
        },
      ],
      originalError: error,
      timestamp,
      errorId,
    };
  }

  // Handle authentication errors
  if (error?.status === 401 || error?.code === 'UNAUTHORIZED') {
    return {
      type: ErrorType.AUTHENTICATION,
      severity: ErrorSeverity.HIGH,
      message: 'Authentication failed',
      userMessage: 'Your session has expired. Please sign in again.',
      recoveryActions: [
        {
          label: 'Sign In',
          action: () => window.location.href = '/auth/signin',
          primary: true,
        },
      ],
      originalError: error,
      timestamp,
      errorId,
    };
  }

  // Handle authorization errors
  if (error?.status === 403 || error?.code === 'FORBIDDEN') {
    return {
      type: ErrorType.AUTHORIZATION,
      severity: ErrorSeverity.MEDIUM,
      message: 'Access denied',
      userMessage: 'You don\'t have permission to access this resource.',
      recoveryActions: [
        {
          label: 'Go Back',
          action: () => window.history.back(),
          primary: true,
        },
        {
          label: 'Go to Dashboard',
          action: () => window.location.href = '/dashboard',
        },
      ],
      originalError: error,
      timestamp,
      errorId,
    };
  }

  // Handle validation errors
  if (error?.status === 400 || error?.code === 'BAD_REQUEST') {
    return {
      type: ErrorType.VALIDATION,
      severity: ErrorSeverity.LOW,
      message: 'Validation error',
      userMessage: error?.message || 'Please check your input and try again.',
      recoveryActions: [
        {
          label: 'Fix Input',
          action: () => {
            // Focus on first invalid field if available
            const firstInvalidField = document.querySelector('[aria-invalid="true"]') as HTMLElement;
            if (firstInvalidField) {
              firstInvalidField.focus();
            }
          },
          primary: true,
        },
      ],
      originalError: error,
      timestamp,
      errorId,
    };
  }

  // Handle rate limit errors
  if (error?.status === 429 || error?.code === 'TOO_MANY_REQUESTS') {
    return {
      type: ErrorType.RATE_LIMIT,
      severity: ErrorSeverity.MEDIUM,
      message: 'Rate limit exceeded',
      userMessage: 'You\'re making requests too quickly. Please wait a moment and try again.',
      recoveryActions: [
        {
          label: 'Wait and Retry',
          action: () => {
            setTimeout(() => window.location.reload(), 5000);
          },
          primary: true,
        },
      ],
      originalError: error,
      timestamp,
      errorId,
    };
  }

  // Handle not found errors
  if (error?.status === 404 || error?.code === 'NOT_FOUND') {
    return {
      type: ErrorType.NOT_FOUND,
      severity: ErrorSeverity.LOW,
      message: 'Resource not found',
      userMessage: 'The requested resource could not be found.',
      recoveryActions: [
        {
          label: 'Go Back',
          action: () => window.history.back(),
          primary: true,
        },
        {
          label: 'Go to Dashboard',
          action: () => window.location.href = '/dashboard',
        },
      ],
      originalError: error,
      timestamp,
      errorId,
    };
  }

  // Default unknown error
  return {
    type: ErrorType.UNKNOWN,
    severity: ErrorSeverity.MEDIUM,
    message: 'An unexpected error occurred',
    userMessage: 'Something went wrong. Please try again or contact support if the problem persists.',
    recoveryActions: [
      {
        label: 'Try Again',
        action: () => window.location.reload(),
        primary: true,
      },
      {
        label: 'Contact Support',
        action: () => window.open('mailto:<EMAIL>', '_blank'),
      },
    ],
    originalError: error,
    timestamp,
    errorId,
  };
}

/**
 * Classify tRPC specific errors
 */
function classifyTRPCError(error: any, errorId: string, timestamp: string): AppError {
  const code = error?.data?.code || error?.code;
  const message = error?.message || 'Unknown tRPC error';

  switch (code) {
    case 'UNAUTHORIZED':
      return {
        type: ErrorType.AUTHENTICATION,
        severity: ErrorSeverity.HIGH,
        message: 'Authentication required',
        userMessage: 'Please sign in to continue.',
        recoveryActions: [
          {
            label: 'Sign In',
            action: () => window.location.href = '/auth/signin',
            primary: true,
          },
        ],
        originalError: error,
        timestamp,
        errorId,
      };

    case 'FORBIDDEN':
      return {
        type: ErrorType.AUTHORIZATION,
        severity: ErrorSeverity.MEDIUM,
        message: 'Access forbidden',
        userMessage: 'You don\'t have permission to perform this action.',
        recoveryActions: [
          {
            label: 'Go Back',
            action: () => window.history.back(),
            primary: true,
          },
        ],
        originalError: error,
        timestamp,
        errorId,
      };

    case 'BAD_REQUEST':
      return {
        type: ErrorType.VALIDATION,
        severity: ErrorSeverity.LOW,
        message: 'Invalid request',
        userMessage: message,
        recoveryActions: [
          {
            label: 'Try Again',
            action: () => window.location.reload(),
            primary: true,
          },
        ],
        originalError: error,
        timestamp,
        errorId,
      };

    case 'NOT_FOUND':
      return {
        type: ErrorType.NOT_FOUND,
        severity: ErrorSeverity.LOW,
        message: 'Resource not found',
        userMessage: 'The requested item could not be found.',
        recoveryActions: [
          {
            label: 'Go Back',
            action: () => window.history.back(),
            primary: true,
          },
        ],
        originalError: error,
        timestamp,
        errorId,
      };

    case 'TOO_MANY_REQUESTS':
      return {
        type: ErrorType.RATE_LIMIT,
        severity: ErrorSeverity.MEDIUM,
        message: 'Rate limit exceeded',
        userMessage: 'Please wait a moment before trying again.',
        recoveryActions: [
          {
            label: 'Wait and Retry',
            action: () => {
              setTimeout(() => window.location.reload(), 5000);
            },
            primary: true,
          },
        ],
        originalError: error,
        timestamp,
        errorId,
      };

    default:
      return {
        type: ErrorType.SERVER,
        severity: ErrorSeverity.HIGH,
        message: 'Server error',
        userMessage: 'A server error occurred. Please try again later.',
        recoveryActions: [
          {
            label: 'Try Again',
            action: () => window.location.reload(),
            primary: true,
          },
        ],
        originalError: error,
        timestamp,
        errorId,
      };
  }
}

/**
 * Generate unique error ID
 */
function generateErrorId(): string {
  return `err-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Log error to monitoring service
 */
export function logError(error: AppError): void {
  const logData = {
    ...error,
    userAgent: navigator.userAgent,
    url: window.location.href,
    userId: getCurrentUserId(), // Implement this based on your auth system
  };

  // In production, send to error monitoring service
  if (process.env.NODE_ENV === 'production') {
    // Example: Sentry, LogRocket, etc.
    console.error('Application Error:', logData);
  } else {
    console.error('Application Error:', logData);
  }
}

/**
 * Get current user ID (implement based on your auth system)
 */
function getCurrentUserId(): string | null {
  // This should be implemented based on your authentication system
  // For example, reading from localStorage, context, or cookies
  return null;
}

/**
 * Create user-friendly error message based on error type
 */
export function getErrorMessage(error: any): string {
  const appError = classifyError(error);
  return appError.userMessage;
}

/**
 * Get recovery actions for an error
 */
export function getRecoveryActions(error: any): RecoveryAction[] {
  const appError = classifyError(error);
  return appError.recoveryActions;
}
