'use client';

import { useState, useEffect } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { Button } from '@learn-platform/shared-ui';
import { useAuth } from '../../../components/auth/auth-provider';
import { getVersionInfo, type VersionInfo } from '@learn-platform/version';
import {
  Home,
  BookOpen,
  Library,
  Settings,
  LogOut,
  Menu,
  X,
  Brain,
  TrendingUp,
  Bell,
  FileQuestion
} from 'lucide-react';

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
}

const navigationItems: NavigationItem[] = [
  { name: 'Dashboard', href: '/dashboard', icon: Home },
  { name: 'Learn', href: '/dashboard/learn', icon: Brain },
  { name: 'My Learning', href: '/dashboard/my-learning', icon: Library },
  { name: 'Quizzes', href: '/dashboard/quizzes', icon: FileQuestion },
  { name: 'Progress', href: '/dashboard/progress', icon: TrendingUp },
  { name: 'Notifications', href: '/dashboard/notifications', icon: Bell },
  { name: 'Setting<PERSON>', href: '/dashboard/settings', icon: Settings },
];

interface SidebarProps {
  isOpen: boolean;
  onToggle: () => void;
}

export function Sidebar({ isOpen, onToggle }: SidebarProps) {
  const pathname = usePathname();
  const router = useRouter();
  const { user, signOut } = useAuth();
  const [isSigningOut, setIsSigningOut] = useState(false);
  const [versionInfo, setVersionInfo] = useState<VersionInfo | null>(null);

  useEffect(() => {
    try {
      // Get version info from the web app's package.json
      // The service will use the current working directory's package.json by default
      const info = getVersionInfo();
      setVersionInfo(info);
    } catch (error) {
      console.error('Failed to get version info:', error);
      // Fallback to default version info
      setVersionInfo({
        version: '0.0.1',
        gitHash: 'unknown',
        gitHashShort: 'unknown'
      });
    }
  }, []);

  const handleSignOut = async () => {
    setIsSigningOut(true);
    try {
      await signOut();
      router.push('/login');
    } catch (error) {
      console.error('Sign out error:', error);
    } finally {
      setIsSigningOut(false);
    }
  };

  const handleNavigation = (href: string) => {
    router.push(href);
    // Close mobile menu after navigation
    if (typeof window !== 'undefined' && window.innerWidth < 1024) {
      onToggle();
    }
  };

  // Handle escape key to close sidebar on mobile
  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === 'Escape' && isOpen) {
      onToggle();
    }
  };

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 sidebar-overlay lg:hidden"
          onClick={onToggle}
          aria-hidden="true"
        />
      )}

      {/* Sidebar */}
      <div
        className={`
          fixed top-0 left-0 h-full w-64 bg-background border-r border-border sidebar-container
          sidebar-transition lg:translate-x-0 lg:h-screen z-50
          flex flex-col dashboard-sidebar
          ${isOpen ? 'translate-x-0' : '-translate-x-full'}
        `}
        role="navigation"
        aria-label="Main navigation"
        onKeyDown={handleKeyDown}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-border flex-shrink-0">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">LP</span>
            </div>
            <span className="font-semibold text-foreground">Learning Platform</span>
          </div>

          {/* Mobile close button */}
          <Button
            variant="ghost"
            size="icon"
            onClick={onToggle}
            className="lg:hidden dashboard-nav-button"
            aria-label="Close navigation menu"
          >
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 overflow-y-auto p-4">
          <ul className="space-y-2">
            {navigationItems.map((item) => {
              const isActive = pathname === item.href;
              const Icon = item.icon;

              return (
                <li key={item.name}>
                  <button
                    onClick={() => handleNavigation(item.href)}
                    className={`
                      w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left
                      transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2
                      ${isActive
                        ? 'bg-accent text-accent-foreground border border-border'
                        : 'text-foreground hover:bg-accent hover:text-accent-foreground'
                      }
                    `}
                    aria-current={isActive ? 'page' : undefined}
                  >
                    <Icon className={`h-5 w-5 flex-shrink-0 ${isActive ? 'text-primary' : 'text-muted-foreground'}`} />
                    <span className="font-medium">{item.name}</span>
                  </button>
                </li>
              );
            })}
          </ul>
        </nav>

        {/* Footer */}
        <div className="border-t border-border p-4 flex-shrink-0">
          {/* User info */}
          {user && (
            <div className="mb-4 p-3 bg-muted rounded-lg">
              <p className="text-sm font-medium text-foreground truncate">
                {user.name}
              </p>
              <p className="text-xs text-muted-foreground truncate">
                {user.email}
              </p>
            </div>
          )}

          {/* Logout button */}
          <Button
            variant="outline"
            onClick={handleSignOut}
            disabled={isSigningOut}
            className="w-full mb-4"
          >
            <LogOut className="h-4 w-4 mr-2" />
            {isSigningOut ? 'Signing out...' : 'Sign Out'}
          </Button>

          {/* Version */}
          <div className="text-center">
            <p className="text-xs text-muted-foreground">
              Learning Platform
            </p>
            <p className="text-xs text-muted-foreground">
              v{versionInfo?.version || '0.0.1'}
            </p>
            {versionInfo?.gitHashShort && versionInfo.gitHashShort !== 'unknown' && (
              <p className="text-xs text-muted-foreground mt-1">
                {versionInfo.gitHashShort}
              </p>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
