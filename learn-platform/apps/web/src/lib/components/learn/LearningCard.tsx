'use client';

import { api } from '../../trpc';
import { Book<PERSON><PERSON>, Clock, Users, TrendingUp, FileText, Eye } from 'lucide-react';
import Link from 'next/link';
import { cn } from '@learn-platform/shared-ui';

interface LearningCardProps {
  className?: string;
}

export function LearningCard({ className }: LearningCardProps) {
  const { data: learningData, isLoading, error } = api.learningContent.getAll.useQuery({
    limit: 100, // Get more data for better statistics
    offset: 0,
  });

  if (isLoading) {
    return (
      <div className={cn('bg-white dark:bg-gray-800 shadow rounded-lg p-6', className)}>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <BookOpen className="h-6 w-6 text-purple-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Learning Content</h3>
          </div>
        </div>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded mb-2"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-600 rounded mb-4"></div>
          <div className="grid grid-cols-2 gap-4">
            <div className="h-16 bg-gray-200 dark:bg-gray-600 rounded"></div>
            <div className="h-16 bg-gray-200 dark:bg-gray-600 rounded"></div>
            <div className="h-16 bg-gray-200 dark:bg-gray-600 rounded"></div>
            <div className="h-16 bg-gray-200 dark:bg-gray-600 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn('bg-white dark:bg-gray-800 shadow rounded-lg p-6', className)}>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <BookOpen className="h-6 w-6 text-purple-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Learning Content</h3>
          </div>
        </div>
        <div className="text-center py-8">
          <div className="text-red-500 mb-2">⚠️</div>
          <p className="text-sm text-gray-600 dark:text-gray-400">Failed to load learning content</p>
        </div>
      </div>
    );
  }

  const content = learningData?.content || [];
  
  if (content.length === 0) {
    return (
      <div className={cn('bg-white dark:bg-gray-800 shadow rounded-lg p-6', className)}>
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <BookOpen className="h-6 w-6 text-purple-600 mr-2" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Learning Content</h3>
          </div>
          <Link 
            href="/dashboard/my-learning" 
            className="text-sm text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300 font-medium"
          >
            View All →
          </Link>
        </div>
        <div className="text-center py-8">
          <div className="text-4xl mb-2">📚</div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">No learning content yet</p>
          <Link 
            href="/dashboard/learn" 
            className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 hover:bg-purple-200 dark:hover:bg-purple-800 transition-colors"
          >
            Create Content →
          </Link>
        </div>
      </div>
    );
  }

  // Calculate statistics
  const totalContent = content.length;
  const publicContent = content.filter((item: { isPublic: boolean }) => item.isPublic).length;
  const privateContent = totalContent - publicContent;
  const avgReadingTime = Math.round(
    content.reduce((sum: number, item: { estimatedReadingTime?: number }) => sum + (item.estimatedReadingTime || 0), 0) / totalContent
  );
  
  // Calculate difficulty distribution
  const difficultyStats = {
    beginner: content.filter((item: { learningLevel: string }) => item.learningLevel === 'beginner').length,
    intermediate: content.filter((item: { learningLevel: string }) => item.learningLevel === 'intermediate').length,
    advanced: content.filter((item: { learningLevel: string }) => item.learningLevel === 'advanced').length,
  };
  
  const mostCommonLevel = Object.entries(difficultyStats).reduce((a, b) => 
    difficultyStats[a[0] as keyof typeof difficultyStats] > difficultyStats[b[0] as keyof typeof difficultyStats] ? a : b
  )[0];

  return (
    <div className={cn('bg-white dark:bg-gray-800 shadow rounded-lg p-6', className)}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <BookOpen className="h-6 w-6 text-purple-600 mr-2" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">Learning Content</h3>
        </div>
        <Link 
          href="/dashboard/my-learning" 
          className="text-sm text-purple-600 hover:text-purple-700 dark:text-purple-400 dark:hover:text-purple-300 font-medium"
        >
          View All →
        </Link>
      </div>

      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
          <div className="flex items-center justify-center mb-1">
            <FileText className="h-4 w-4 text-purple-600 mr-1" />
          </div>
          <div className="text-2xl font-bold text-purple-600">{totalContent}</div>
          <div className="text-xs text-gray-600 dark:text-gray-400">Total Content</div>
        </div>
        
        <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <div className="flex items-center justify-center mb-1">
            <Eye className="h-4 w-4 text-green-600 mr-1" />
          </div>
          <div className="text-2xl font-bold text-green-600">{publicContent}</div>
          <div className="text-xs text-gray-600 dark:text-gray-400">Public</div>
        </div>
        
        <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <div className="flex items-center justify-center mb-1">
            <Clock className="h-4 w-4 text-blue-600 mr-1" />
          </div>
          <div className="text-2xl font-bold text-blue-600">{avgReadingTime}m</div>
          <div className="text-xs text-gray-600 dark:text-gray-400">Avg Reading</div>
        </div>
        
        <div className="text-center p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
          <div className="flex items-center justify-center mb-1">
            <TrendingUp className="h-4 w-4 text-orange-600 mr-1" />
          </div>
          <div className="text-2xl font-bold text-orange-600 capitalize">{mostCommonLevel}</div>
          <div className="text-xs text-gray-600 dark:text-gray-400">Most Common</div>
        </div>
      </div>

      {/* Progress bar showing public vs private content */}
      <div className="mb-3">
        <div className="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
          <span>Content Visibility</span>
          <span>{Math.round((publicContent / totalContent) * 100)}% Public</span>
        </div>
        <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
          <div 
            className="bg-gradient-to-r from-green-500 to-green-600 h-2 rounded-full transition-all duration-300"
            style={{ width: `${(publicContent / totalContent) * 100}%` }}
          ></div>
        </div>
      </div>

      {/* Quick stat */}
      <div className="text-center">
        <div className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-200">
          <Users className="h-3 w-3 mr-1" />
          {privateContent} Private Content
        </div>
      </div>
    </div>
  );
}