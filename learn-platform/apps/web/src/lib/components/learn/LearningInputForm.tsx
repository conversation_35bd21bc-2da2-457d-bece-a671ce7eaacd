'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@learn-platform/shared-ui';
import { Brain, Loader2, Lightbulb, Target, BookOpen } from 'lucide-react';

// Validation schema
const learningInputSchema = z.object({
  topic: z.string().min(3, 'Topic must be at least 3 characters').max(200, 'Topic must be less than 200 characters'),
  learningLevel: z.enum(['beginner', 'intermediate', 'advanced']),
  preferredContentTypes: z.array(z.string()).min(1, 'Please select at least one content type'),
  focusAreas: z.string().max(500, 'Focus areas must be less than 500 characters').optional(),
});

type LearningInputData = z.infer<typeof learningInputSchema>;

const contentTypeOptions = [
  { id: 'paragraph', label: 'Text Explanations', description: 'Clear, detailed written explanations' },
  { id: 'bulletList', label: 'Bullet Points', description: 'Key points in easy-to-scan lists' },
  { id: 'numberedList', label: 'Step-by-Step', description: 'Sequential, numbered instructions' },
  { id: 'infoBox', label: 'Info Boxes', description: 'Highlighted important information' },
  { id: 'comparison', label: 'Comparisons', description: 'Before/after or side-by-side comparisons' },
  { id: 'grid', label: 'Visual Grids', description: 'Organized information in grid format' },
  { id: 'table', label: 'Data Tables', description: 'Structured data in table format' },
  { id: 'keyValueGrid', label: 'Key-Value Pairs', description: 'Definition-style explanations' },
];

interface LearningInputFormProps {
  onSubmit?: (data: LearningInputData) => void;
  isLoading?: boolean;
  initialValues?: Partial<LearningInputData>;
}

export function LearningInputForm({ onSubmit, isLoading = false, initialValues }: LearningInputFormProps) {
  const defaultContentTypes = initialValues?.preferredContentTypes || ['paragraph', 'bulletList'];
  const [selectedContentTypes, setSelectedContentTypes] = useState<string[]>(defaultContentTypes);

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    setValue,
    watch,
    reset,
  } = useForm<LearningInputData>({
    resolver: zodResolver(learningInputSchema),
    defaultValues: {
      topic: initialValues?.topic || '',
      learningLevel: initialValues?.learningLevel || 'beginner',
      preferredContentTypes: defaultContentTypes,
      focusAreas: initialValues?.focusAreas || '',
    },
    mode: 'onChange',
  });

  const watchedTopic = watch('topic');

  // Update form when initialValues change
  useEffect(() => {
    if (initialValues) {
      const newContentTypes = initialValues.preferredContentTypes || ['paragraph', 'bulletList'];
      setSelectedContentTypes(newContentTypes);
      reset({
        topic: initialValues.topic || '',
        learningLevel: initialValues.learningLevel || 'beginner',
        preferredContentTypes: newContentTypes,
        focusAreas: initialValues.focusAreas || '',
      });
    }
  }, [initialValues, reset]);

  const handleContentTypeChange = (contentTypeId: string, checked: boolean) => {
    let newSelection: string[];
    if (checked) {
      newSelection = [...selectedContentTypes, contentTypeId];
    } else {
      newSelection = selectedContentTypes.filter(id => id !== contentTypeId);
    }
    setSelectedContentTypes(newSelection);
    setValue('preferredContentTypes', newSelection, { shouldValidate: true });
  };

  const handleFormSubmit = (data: LearningInputData) => {
    if (onSubmit) {
      onSubmit(data);
    } else {
      console.log('Form submitted:', data);
      // TODO: Integrate with AI generation API
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      <div className="mb-6">
        <div className="flex items-center mb-2">
          <Brain className="h-6 w-6 text-blue-600 mr-2" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Create Learning Content</h2>
        </div>
        <p className="text-gray-600 dark:text-gray-300">
          Tell us what you want to learn, and we&apos;ll generate personalized AI-powered content for you.
        </p>
      </div>

      <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
        {/* Topic Input */}
        <div>
          <label htmlFor="topic" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            <Target className="h-4 w-4 inline mr-1" />
            What do you want to learn? *
          </label>
          <input
            {...register('topic')}
            type="text"
            id="topic"
            placeholder="e.g., How does machine learning work?, JavaScript async/await, Photosynthesis process..."
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${
              errors.topic ? 'border-red-300 dark:border-red-600' : 'border-gray-300 dark:border-gray-600'
            }`}
            disabled={isLoading}
          />
          {errors.topic && (
            <p className="mt-1 text-sm text-red-600">{errors.topic.message}</p>
          )}
          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            {watchedTopic?.length || 0}/200 characters
          </p>
        </div>

        {/* Learning Level */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            <BookOpen className="h-4 w-4 inline mr-1" />
            Learning Level *
          </label>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
            {[
              { value: 'beginner', label: 'Beginner', description: 'New to this topic' },
              { value: 'intermediate', label: 'Intermediate', description: 'Some experience' },
              { value: 'advanced', label: 'Advanced', description: 'Deep understanding' },
            ].map((level) => (
              <label key={level.value} className="relative">
                <input
                  {...register('learningLevel')}
                  type="radio"
                  value={level.value}
                  className="sr-only"
                  disabled={isLoading}
                />
                <div className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                  watch('learningLevel') === level.value
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-400'
                    : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 bg-white dark:bg-gray-700'
                }`}>
                  <div className="font-medium text-sm text-gray-900 dark:text-white">{level.label}</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">{level.description}</div>
                </div>
              </label>
            ))}
          </div>
        </div>

        {/* Preferred Content Types */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            <Lightbulb className="h-4 w-4 inline mr-1" />
            Preferred Content Types * (Select at least one)
          </label>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {contentTypeOptions.map((option) => (
              <label key={option.id} className="relative">
                <input
                  type="checkbox"
                  checked={selectedContentTypes.includes(option.id)}
                  onChange={(e) => handleContentTypeChange(option.id, e.target.checked)}
                  className="sr-only"
                  disabled={isLoading}
                />
                <div className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                  selectedContentTypes.includes(option.id)
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-400'
                    : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 bg-white dark:bg-gray-700'
                }`}>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium text-sm text-gray-900 dark:text-white">{option.label}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">{option.description}</div>
                    </div>
                    {selectedContentTypes.includes(option.id) && (
                      <div className="text-blue-600 dark:text-blue-400">✓</div>
                    )}
                  </div>
                </div>
              </label>
            ))}
          </div>
          {errors.preferredContentTypes && (
            <p className="mt-1 text-sm text-red-600">{errors.preferredContentTypes.message}</p>
          )}
        </div>

        {/* Focus Areas */}
        <div>
          <label htmlFor="focusAreas" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Specific Focus Areas (Optional)
          </label>
          <textarea
            {...register('focusAreas')}
            id="focusAreas"
            rows={3}
            placeholder="Any specific aspects you want to focus on? e.g., practical examples, historical context, real-world applications..."
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${
              errors.focusAreas ? 'border-red-300 dark:border-red-600' : 'border-gray-300 dark:border-gray-600'
            }`}
            disabled={isLoading}
          />
          {errors.focusAreas && (
            <p className="mt-1 text-sm text-red-600">{errors.focusAreas.message}</p>
          )}
          <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            {watch('focusAreas')?.length || 0}/500 characters
          </p>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end">
          <Button
            type="submit"
            disabled={!isValid || isLoading}
            className="bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Generating Content...
              </>
            ) : (
              <>
                <Brain className="h-4 w-4 mr-2" />
                Generate Learning Content
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
