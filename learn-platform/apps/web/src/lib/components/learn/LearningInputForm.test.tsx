/**
 * Component tests for LearningInputForm
 * Tests form validation, user interactions, and error handling
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { LearningInputForm } from './LearningInputForm';

// Mock the tRPC client
jest.mock('@/lib/trpc', () => ({
  api: {
    learningContent: {
      generateWithAI: {
        useMutation: jest.fn(() => ({
          mutate: jest.fn(),
          isLoading: false,
          error: null,
        })),
      },
    },
  },
}));

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(() => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
  })),
}));

// Mock react-hook-form
jest.mock('react-hook-form', () => ({
  useForm: jest.fn(() => ({
    register: jest.fn(() => ({})),
    handleSubmit: jest.fn((fn) => fn),
    formState: { errors: {}, isSubmitting: false },
    watch: jest.fn(),
    setValue: jest.fn(),
    reset: jest.fn(),
  })),
}));

describe('LearningInputForm', () => {
  const mockOnSubmit = jest.fn();
  const mockOnCancel = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render all form fields', () => {
    render(
      <LearningInputForm 
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    // Check for main form elements
    expect(screen.getByLabelText(/topic/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/learning level/i)).toBeInTheDocument();
    expect(screen.getByText(/preferred content types/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/focus areas/i)).toBeInTheDocument();
    
    // Check for buttons
    expect(screen.getByRole('button', { name: /generate content/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
  });

  it('should display validation errors for empty required fields', async () => {
    const user = userEvent.setup();
    
    render(
      <LearningInputForm 
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    // Try to submit without filling required fields
    const submitButton = screen.getByRole('button', { name: /generate content/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/topic is required/i)).toBeInTheDocument();
    });
  });

  it('should validate topic length', async () => {
    const user = userEvent.setup();
    
    render(
      <LearningInputForm 
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    const topicInput = screen.getByLabelText(/topic/i);
    
    // Test minimum length
    await user.type(topicInput, 'ab'); // Too short
    
    const submitButton = screen.getByRole('button', { name: /generate content/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/topic must be at least 3 characters/i)).toBeInTheDocument();
    });

    // Test maximum length
    await user.clear(topicInput);
    await user.type(topicInput, 'a'.repeat(201)); // Too long
    
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/topic must be at most 200 characters/i)).toBeInTheDocument();
    });
  });

  it('should require at least one content type selection', async () => {
    const user = userEvent.setup();
    
    render(
      <LearningInputForm 
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    // Fill topic but don't select any content types
    const topicInput = screen.getByLabelText(/topic/i);
    await user.type(topicInput, 'Valid topic');

    const submitButton = screen.getByRole('button', { name: /generate content/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/select at least one content type/i)).toBeInTheDocument();
    });
  });

  it('should allow selecting multiple content types', async () => {
    const user = userEvent.setup();
    
    render(
      <LearningInputForm 
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    // Select multiple content types
    const paragraphCheckbox = screen.getByLabelText(/paragraph/i);
    const bulletListCheckbox = screen.getByLabelText(/bullet list/i);
    
    await user.click(paragraphCheckbox);
    await user.click(bulletListCheckbox);

    expect(paragraphCheckbox).toBeChecked();
    expect(bulletListCheckbox).toBeChecked();
  });

  it('should validate focus areas length', async () => {
    const user = userEvent.setup();
    
    render(
      <LearningInputForm 
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    const focusAreasInput = screen.getByLabelText(/focus areas/i);
    await user.type(focusAreasInput, 'a'.repeat(501)); // Too long

    const submitButton = screen.getByRole('button', { name: /generate content/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/focus areas must be at most 500 characters/i)).toBeInTheDocument();
    });
  });

  it('should submit form with valid data', async () => {
    const user = userEvent.setup();
    
    render(
      <LearningInputForm 
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    // Fill out the form
    const topicInput = screen.getByLabelText(/topic/i);
    await user.type(topicInput, 'How does machine learning work?');

    const levelSelect = screen.getByLabelText(/learning level/i);
    await user.selectOptions(levelSelect, 'beginner');

    const paragraphCheckbox = screen.getByLabelText(/paragraph/i);
    await user.click(paragraphCheckbox);

    const focusAreasInput = screen.getByLabelText(/focus areas/i);
    await user.type(focusAreasInput, 'practical examples');

    // Submit the form
    const submitButton = screen.getByRole('button', { name: /generate content/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        topic: 'How does machine learning work?',
        learningLevel: 'beginner',
        preferredContentTypes: ['paragraph'],
        focusAreas: 'practical examples',
      });
    });
  });

  it('should call onCancel when cancel button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <LearningInputForm 
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    await user.click(cancelButton);

    expect(mockOnCancel).toHaveBeenCalled();
  });

  it('should show loading state during submission', () => {
    // Mock loading state
    const { api } = require('@/lib/trpc');
    api.learningContent.generateWithAI.useMutation.mockReturnValue({
      mutate: jest.fn(),
      isLoading: true,
      error: null,
    });

    render(
      <LearningInputForm 
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    const submitButton = screen.getByRole('button', { name: /generating/i });
    expect(submitButton).toBeDisabled();
    expect(screen.getByText(/generating/i)).toBeInTheDocument();
  });

  it('should display error message when submission fails', () => {
    // Mock error state
    const { api } = require('@/lib/trpc');
    api.learningContent.generateWithAI.useMutation.mockReturnValue({
      mutate: jest.fn(),
      isLoading: false,
      error: { message: 'Failed to generate content' },
    });

    render(
      <LearningInputForm 
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    expect(screen.getByText(/failed to generate content/i)).toBeInTheDocument();
  });

  it('should reset form when reset button is clicked', async () => {
    const user = userEvent.setup();
    const mockReset = jest.fn();
    
    // Mock useForm to return reset function
    const { useForm } = require('react-hook-form');
    useForm.mockReturnValue({
      register: jest.fn(() => ({})),
      handleSubmit: jest.fn((fn) => fn),
      formState: { errors: {}, isSubmitting: false },
      watch: jest.fn(),
      setValue: jest.fn(),
      reset: mockReset,
    });

    render(
      <LearningInputForm 
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    // Fill out some fields
    const topicInput = screen.getByLabelText(/topic/i);
    await user.type(topicInput, 'Test topic');

    // Click reset button (if it exists)
    const resetButton = screen.queryByRole('button', { name: /reset/i });
    if (resetButton) {
      await user.click(resetButton);
      expect(mockReset).toHaveBeenCalled();
    }
  });

  it('should provide helpful placeholder text', () => {
    render(
      <LearningInputForm 
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    const topicInput = screen.getByLabelText(/topic/i);
    expect(topicInput).toHaveAttribute('placeholder', expect.stringMatching(/what would you like to learn/i));

    const focusAreasInput = screen.getByLabelText(/focus areas/i);
    expect(focusAreasInput).toHaveAttribute('placeholder', expect.stringMatching(/specific aspects/i));
  });

  it('should show character count for topic field', async () => {
    const user = userEvent.setup();
    
    render(
      <LearningInputForm 
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    const topicInput = screen.getByLabelText(/topic/i);
    await user.type(topicInput, 'Test topic');

    // Should show character count
    expect(screen.getByText(/10\/200/)).toBeInTheDocument();
  });

  it('should show character count for focus areas field', async () => {
    const user = userEvent.setup();
    
    render(
      <LearningInputForm 
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    const focusAreasInput = screen.getByLabelText(/focus areas/i);
    await user.type(focusAreasInput, 'Test focus areas');

    // Should show character count
    expect(screen.getByText(/18\/500/)).toBeInTheDocument();
  });
});
