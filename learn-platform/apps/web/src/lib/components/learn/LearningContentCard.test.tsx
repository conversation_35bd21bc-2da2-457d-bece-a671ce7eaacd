/**
 * Component tests for LearningContentCard
 * Tests content display, user interactions, and action buttons
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { LearningContentCard } from './LearningContentCard';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(() => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
  })),
}));

// Mock the tRPC client
jest.mock('@/lib/trpc', () => ({
  api: {
    learningContent: {
      delete: {
        useMutation: jest.fn(() => ({
          mutate: jest.fn(),
          isLoading: false,
          error: null,
        })),
      },
      duplicate: {
        useMutation: jest.fn(() => ({
          mutate: jest.fn(),
          isLoading: false,
          error: null,
        })),
      },
      togglePublic: {
        useMutation: jest.fn(() => ({
          mutate: jest.fn(),
          isLoading: false,
          error: null,
        })),
      },
    },
  },
}));

// Mock date formatting
jest.mock('date-fns', () => ({
  formatDistanceToNow: jest.fn(() => '2 days ago'),
  format: jest.fn(() => 'Jan 1, 2024'),
}));

describe('LearningContentCard', () => {
  const mockContent = {
    id: 'content-1',
    title: 'Introduction to Machine Learning',
    description: 'Learn the basics of machine learning algorithms and applications.',
    learningLevel: 'beginner' as const,
    estimatedReadingTime: 15,
    isPublic: false,
    tags: ['machine-learning', 'ai', 'beginner'],
    userId: 'user-1',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    steps: [
      {
        id: 'step-1',
        title: 'What is Machine Learning?',
        icon: '🤖',
        blocks: [
          {
            id: 'block-1',
            type: 'paragraph',
            data: 'Machine learning is a subset of artificial intelligence...',
            isEditing: false,
          },
        ],
      },
    ],
    aiMetadata: {
      aiModel: 'openai/gpt-4o',
      generatedAt: '2024-01-01T00:00:00Z',
      contentTypes: ['paragraph', 'bulletList'],
      originalPrompt: 'Explain machine learning basics',
    },
  };

  const mockOnView = jest.fn();
  const mockOnEdit = jest.fn();
  const mockOnDelete = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render content information correctly', () => {
    render(
      <LearningContentCard
        content={mockContent}
        onView={mockOnView}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    );

    expect(screen.getByText('Introduction to Machine Learning')).toBeInTheDocument();
    expect(screen.getByText('Learn the basics of machine learning algorithms and applications.')).toBeInTheDocument();
    expect(screen.getByText('beginner')).toBeInTheDocument();
    expect(screen.getByText('15 min read')).toBeInTheDocument();
    expect(screen.getByText('2 days ago')).toBeInTheDocument();
  });

  it('should display tags correctly', () => {
    render(
      <LearningContentCard
        content={mockContent}
        onView={mockOnView}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    );

    expect(screen.getByText('machine-learning')).toBeInTheDocument();
    expect(screen.getByText('ai')).toBeInTheDocument();
    expect(screen.getByText('beginner')).toBeInTheDocument();
  });

  it('should show public/private status', () => {
    // Test private content
    render(
      <LearningContentCard
        content={mockContent}
        onView={mockOnView}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    );

    expect(screen.getByText(/private/i)).toBeInTheDocument();

    // Test public content
    const publicContent = { ...mockContent, isPublic: true };
    render(
      <LearningContentCard
        content={publicContent}
        onView={mockOnView}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    );

    expect(screen.getByText(/public/i)).toBeInTheDocument();
  });

  it('should call onView when view button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <LearningContentCard
        content={mockContent}
        onView={mockOnView}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    );

    const viewButton = screen.getByRole('button', { name: /view/i });
    await user.click(viewButton);

    expect(mockOnView).toHaveBeenCalledWith(mockContent.id);
  });

  it('should call onEdit when edit button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <LearningContentCard
        content={mockContent}
        onView={mockOnView}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    );

    const editButton = screen.getByRole('button', { name: /edit/i });
    await user.click(editButton);

    expect(mockOnEdit).toHaveBeenCalledWith(mockContent.id);
  });

  it('should show delete confirmation dialog', async () => {
    const user = userEvent.setup();
    
    render(
      <LearningContentCard
        content={mockContent}
        onView={mockOnView}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    );

    const deleteButton = screen.getByRole('button', { name: /delete/i });
    await user.click(deleteButton);

    // Should show confirmation dialog
    expect(screen.getByText(/are you sure you want to delete/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /confirm delete/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
  });

  it('should call onDelete when delete is confirmed', async () => {
    const user = userEvent.setup();
    
    render(
      <LearningContentCard
        content={mockContent}
        onView={mockOnView}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    );

    const deleteButton = screen.getByRole('button', { name: /delete/i });
    await user.click(deleteButton);

    const confirmButton = screen.getByRole('button', { name: /confirm delete/i });
    await user.click(confirmButton);

    expect(mockOnDelete).toHaveBeenCalledWith(mockContent.id);
  });

  it('should cancel delete when cancel button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <LearningContentCard
        content={mockContent}
        onView={mockOnView}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    );

    const deleteButton = screen.getByRole('button', { name: /delete/i });
    await user.click(deleteButton);

    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    await user.click(cancelButton);

    // Dialog should be closed
    expect(screen.queryByText(/are you sure you want to delete/i)).not.toBeInTheDocument();
    expect(mockOnDelete).not.toHaveBeenCalled();
  });

  it('should show duplicate button and handle duplication', async () => {
    const user = userEvent.setup();
    
    render(
      <LearningContentCard
        content={mockContent}
        onView={mockOnView}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    );

    const duplicateButton = screen.getByRole('button', { name: /duplicate/i });
    await user.click(duplicateButton);

    // Should trigger duplication
    const { api } = require('@/lib/trpc');
    expect(api.learningContent.duplicate.useMutation).toHaveBeenCalled();
  });

  it('should show share button for public content', () => {
    const publicContent = { ...mockContent, isPublic: true };
    
    render(
      <LearningContentCard
        content={publicContent}
        onView={mockOnView}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    );

    expect(screen.getByRole('button', { name: /share/i })).toBeInTheDocument();
  });

  it('should not show share button for private content', () => {
    render(
      <LearningContentCard
        content={mockContent}
        onView={mockOnView}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    );

    expect(screen.queryByRole('button', { name: /share/i })).not.toBeInTheDocument();
  });

  it('should show loading state during actions', () => {
    // Mock loading state for delete
    const { api } = require('@/lib/trpc');
    api.learningContent.delete.useMutation.mockReturnValue({
      mutate: jest.fn(),
      isLoading: true,
      error: null,
    });

    render(
      <LearningContentCard
        content={mockContent}
        onView={mockOnView}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    );

    // Delete button should show loading state
    const deleteButton = screen.getByRole('button', { name: /deleting/i });
    expect(deleteButton).toBeDisabled();
  });

  it('should display error messages', () => {
    // Mock error state
    const { api } = require('@/lib/trpc');
    api.learningContent.delete.useMutation.mockReturnValue({
      mutate: jest.fn(),
      isLoading: false,
      error: { message: 'Failed to delete content' },
    });

    render(
      <LearningContentCard
        content={mockContent}
        onView={mockOnView}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    );

    expect(screen.getByText(/failed to delete content/i)).toBeInTheDocument();
  });

  it('should handle toggle public/private status', async () => {
    const user = userEvent.setup();
    
    render(
      <LearningContentCard
        content={mockContent}
        onView={mockOnView}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    );

    const toggleButton = screen.getByRole('button', { name: /make public/i });
    await user.click(toggleButton);

    const { api } = require('@/lib/trpc');
    expect(api.learningContent.togglePublic.useMutation).toHaveBeenCalled();
  });

  it('should show step count information', () => {
    render(
      <LearningContentCard
        content={mockContent}
        onView={mockOnView}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    );

    expect(screen.getByText(/1 step/i)).toBeInTheDocument();
  });

  it('should show AI model information', () => {
    render(
      <LearningContentCard
        content={mockContent}
        onView={mockOnView}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    );

    expect(screen.getByText(/gpt-4o/i)).toBeInTheDocument();
  });

  it('should handle missing optional data gracefully', () => {
    const minimalContent = {
      ...mockContent,
      description: '',
      tags: [],
      aiMetadata: null,
    };

    render(
      <LearningContentCard
        content={minimalContent}
        onView={mockOnView}
        onEdit={mockOnEdit}
        onDelete={mockOnDelete}
      />
    );

    // Should still render without errors
    expect(screen.getByText('Introduction to Machine Learning')).toBeInTheDocument();
  });
});
