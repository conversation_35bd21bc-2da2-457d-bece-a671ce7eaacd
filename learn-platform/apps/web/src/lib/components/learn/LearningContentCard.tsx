'use client';

import React, { useState } from 'react';
import { Button } from '@learn-platform/shared-ui';
import { Clock, Target, Eye, MoreHorizontal, BookOpen, Share, Trash2 } from 'lucide-react';
import Link from 'next/link';
import { LevelIndicator } from './LevelIndicator';

interface LearningContentCardProps {
  content: {
    id: string;
    title: string;
    description: string;
    learningLevel: 'beginner' | 'intermediate' | 'advanced';
    estimatedReadingTime: number;
    isPublic: boolean;
    tags: string[];
    createdAt: string;
    updatedAt: string;
    steps: any[];
  };
  progress?: {
    completionPercentage: number;
    isCompleted: boolean;
    currentStepIndex: number;
    totalTimeSpent: number;
  };
  onDelete?: (id: string) => void;
  onShare?: (content: any) => void;
}

export function LearningContentCard({ content, progress, onDelete, onShare }: LearningContentCardProps) {
  const [showActions, setShowActions] = useState(false);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'beginner':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 dark:border dark:border-green-700';
      case 'intermediate':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300 dark:border dark:border-yellow-700';
      case 'advanced':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300 dark:border dark:border-red-700';
      default:
        return 'bg-muted text-muted-foreground dark:bg-gray-700 dark:text-gray-300 dark:border dark:border-gray-600';
    }
  };

  const getLevelTextColor = (level: string) => {
    switch (level) {
      case 'beginner':
        return 'text-green-800 dark:text-green-300';
      case 'intermediate':
        return 'text-yellow-800 dark:text-yellow-300';
      case 'advanced':
        return 'text-red-800 dark:text-red-300';
      default:
        return 'text-muted-foreground dark:text-gray-300';
    }
  };



  return (
    <div className={`bg-card dark:bg-gray-800 rounded-lg shadow-sm border transition-all duration-200 flex flex-col h-full ${
      progress?.isCompleted 
        ? 'border-border dark:border-gray-700 hover:shadow-lg hover:shadow-gray-200 dark:hover:shadow-xl dark:hover:shadow-gray-900/40 hover:scale-[1.02] dark:hover:bg-gray-750' 
        : 'border-border dark:border-gray-700 hover:shadow-md hover:shadow-gray-200 dark:hover:shadow-xl dark:hover:shadow-gray-900/40 hover:scale-[1.02] dark:hover:bg-gray-750'
    }`}>
      {/* Card Header */}
      <div className="p-6 pb-4">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <Link href={`/dashboard/learn/${content.id}`}>
              <h3 className="text-lg font-semibold text-card-foreground hover:text-primary transition-colors cursor-pointer line-clamp-2 flex items-start gap-2">
                {progress?.isCompleted && (
                  <span className="text-green-600 dark:text-green-500 mt-0.5 flex-shrink-0">✓</span>
                )}
                <span>{content.title}</span>
              </h3>
            </Link>
            {content.description && (
              <p className="text-muted-foreground text-sm mt-2 line-clamp-3">
                {content.description}
              </p>
            )}
          </div>

          {/* Actions Menu */}
          <div className="relative ml-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowActions(!showActions)}
              className="p-2 dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 dark:hover:border-gray-500"
            >
              <MoreHorizontal className="h-4 w-4" />
            </Button>

            {showActions && (
              <div className="absolute right-0 top-full mt-1 w-48 bg-popover dark:bg-gray-800 rounded-md shadow-lg border border-border dark:border-gray-700 z-10">
                <div className="py-1">
                  <Link href={`/dashboard/learn/${content.id}`}>
                    <button className="flex items-center w-full px-4 py-2 text-sm text-popover-foreground dark:text-gray-200 hover:bg-accent dark:hover:bg-gray-700 hover:text-accent-foreground">
                      <Eye className="h-4 w-4 mr-2" />
                      View Content
                    </button>
                  </Link>
                  {onShare && (
                    <button
                      onClick={() => {
                        onShare(content);
                        setShowActions(false);
                      }}
                      className="flex items-center w-full px-4 py-2 text-sm text-popover-foreground dark:text-gray-200 hover:bg-accent dark:hover:bg-gray-700 hover:text-accent-foreground"
                    >
                      <Share className="h-4 w-4 mr-2" />
                      Share
                    </button>
                  )}
                  {onDelete && (
                    <button
                      onClick={() => {
                        onDelete(content.id);
                        setShowActions(false);
                      }}
                      className="flex items-center w-full px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </button>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Card Content */}
      <div className="px-6 pb-4 flex-grow">
        {/* Tags */}
        {content.tags && content.tags.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-4">
            {content.tags.slice(0, 3).map((tag, index) => (
              <span
                key={index}
                className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 dark:border dark:border-blue-700"
              >
                {tag}
              </span>
            ))}
            {content.tags.length > 3 && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-muted text-muted-foreground">
                +{content.tags.length - 3} more
              </span>
            )}
          </div>
        )}

        {/* Metadata - 2x2 Grid */}
        <div className="grid grid-cols-2 gap-3 text-sm text-muted-foreground">
          <div className="flex items-center space-x-1">
            <Clock className="h-4 w-4" />
            <span>{content.estimatedReadingTime} min</span>
          </div>
          <div className="flex items-center space-x-2 justify-end">
            <LevelIndicator level={content.learningLevel as 'beginner' | 'intermediate' | 'advanced'} size="sm" />
            <span className={`text-xs font-medium ${getLevelTextColor(content.learningLevel)}`}>
              {content.learningLevel}
            </span>
          </div>
          <div className="flex items-center space-x-1">
            <BookOpen className="h-4 w-4" />
            <span>{content.steps?.length || 0} steps</span>
          </div>
          <div className="flex items-center space-x-1 justify-end">
            {content.isPublic && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300 dark:border dark:border-green-700 mr-2">
                Public
              </span>
            )}
            <span className="text-xs">
              {formatDate(content.createdAt)}
            </span>
          </div>
        </div>
      </div>

      {/* Card Footer */}
      <div className={`px-6 py-4 rounded-b-lg border-t transition-colors ${
         progress?.isCompleted 
           ? 'bg-green-50 dark:bg-green-950/20 border-green-200 dark:border-green-700' 
           : 'bg-muted dark:bg-gray-900/50 border-border dark:border-gray-700'
       }`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 text-sm">
            {progress?.isCompleted ? (
               <div className="flex items-center space-x-2 text-green-700 dark:text-green-400">
                 <span className="font-medium">✓ Completed</span>
               </div>
            ) : (
              <>
                <span className="text-muted-foreground">Progress: {progress?.completionPercentage || 0}%</span>
                <div className="w-16 bg-border dark:bg-gray-600 rounded-full h-1.5">
                  <div
                    className="bg-primary h-1.5 rounded-full transition-all duration-300"
                    style={{ width: `${progress?.completionPercentage || 0}%` }}
                  ></div>
                </div>
              </>
            )}
          </div>

          <Link href={`/dashboard/learn/${content.id}`}>
            <Button 
               size="sm" 
               className={progress?.isCompleted 
                 ? "bg-green-100 text-green-700 hover:bg-green-200 dark:bg-green-900/20 dark:text-green-300 dark:hover:bg-green-900/30" 
                 : "bg-orange-100 text-orange-700 hover:bg-orange-200 dark:bg-orange-900/20 dark:text-orange-300 dark:hover:bg-orange-900/30"
               }
            >
              {progress?.isCompleted ? 'Review' : progress?.completionPercentage ? 'Continue' : 'Start Learning'}
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}

// Click outside handler to close actions menu
export function useClickOutside(ref: React.RefObject<HTMLElement>, handler: () => void) {
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        handler();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [ref, handler]);
}
