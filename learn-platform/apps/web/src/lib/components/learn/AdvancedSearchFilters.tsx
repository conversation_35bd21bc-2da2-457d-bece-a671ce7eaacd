'use client';

import React, { useState } from 'react';
import { Button } from '@learn-platform/shared-ui';
import {
  Search,
  Filter,
  X,
  Calendar,
  Tag,
  Clock,
  BookOpen,
  ChevronDown,
  ChevronUp,
  Save,
  Trash2,
  Grid,
  List
} from 'lucide-react';

interface SearchFilters {
  search: string;
  learningLevel: 'all' | 'beginner' | 'intermediate' | 'advanced';
  dateRange: {
    from: string;
    to: string;
  };
  tags: string[];
  contentTypes: string[];
  readingTimeRange: {
    min: number;
    max: number;
  };
  isPublic?: boolean;
  completionStatus?: 'all' | 'completed' | 'in-progress';
}

interface AdvancedSearchFiltersProps {
  filters: SearchFilters;
  onFiltersChange: (filters: SearchFilters) => void;
  availableTags?: string[];
  savedSearches?: Array<{ id: string; name: string; filters: SearchFilters }>;
  onSaveSearch?: (name: string, filters: SearchFilters) => void;
  onLoadSearch?: (filters: SearchFilters) => void;
  onDeleteSearch?: (id: string) => void;
  viewMode?: 'grid' | 'list';
  onViewModeChange?: (mode: 'grid' | 'list') => void;
}

const contentTypeOptions = [
  { id: 'paragraph', label: 'Paragraphs' },
  { id: 'bulletList', label: 'Bullet Lists' },
  { id: 'numberedList', label: 'Numbered Lists' },
  { id: 'infoBox', label: 'Info Boxes' },
  { id: 'grid', label: 'Grids' },
  { id: 'comparison', label: 'Comparisons' },
  { id: 'table', label: 'Tables' },
  { id: 'scatterPlot', label: 'Charts' },
  { id: 'keyValueGrid', label: 'Key-Value Pairs' },
];

export function AdvancedSearchFilters({
  filters,
  onFiltersChange,
  availableTags = [],
  savedSearches = [],
  onSaveSearch,
  onLoadSearch,
  onDeleteSearch,
  viewMode = 'grid',
  onViewModeChange,
}: AdvancedSearchFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [saveSearchName, setSaveSearchName] = useState('');
  const [showSaveDialog, setShowSaveDialog] = useState(false);

  const updateFilters = (updates: Partial<SearchFilters>) => {
    onFiltersChange({ ...filters, ...updates });
  };

  const handleTagToggle = (tag: string) => {
    const newTags = filters.tags.includes(tag)
      ? filters.tags.filter(t => t !== tag)
      : [...filters.tags, tag];
    updateFilters({ tags: newTags });
  };

  const handleContentTypeToggle = (type: string) => {
    const newTypes = filters.contentTypes.includes(type)
      ? filters.contentTypes.filter(t => t !== type)
      : [...filters.contentTypes, type];
    updateFilters({ contentTypes: newTypes });
  };

  const clearAllFilters = () => {
    onFiltersChange({
      search: '',
      learningLevel: 'all',
      dateRange: { from: '', to: '' },
      tags: [],
      contentTypes: [],
      readingTimeRange: { min: 0, max: 300 },
    });
  };

  const hasActiveFilters = () => {
    return (
      filters.search ||
      filters.learningLevel !== 'all' ||
      filters.dateRange.from ||
      filters.dateRange.to ||
      filters.tags.length > 0 ||
      filters.contentTypes.length > 0 ||
      filters.readingTimeRange.min > 0 ||
      filters.readingTimeRange.max < 300 ||
      filters.isPublic !== undefined
    );
  };

  const handleSaveSearch = () => {
    if (saveSearchName.trim() && onSaveSearch) {
      onSaveSearch(saveSearchName.trim(), filters);
      setSaveSearchName('');
      setShowSaveDialog(false);
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
      {/* Basic Search */}
      <div className="flex flex-col lg:flex-row gap-4 mb-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search your learning content..."
              value={filters.search}
              onChange={(e) => updateFilters({ search: e.target.value })}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center space-x-2"
          >
            <Filter className="h-4 w-4" />
            <span>Advanced Filters</span>
            {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </Button>

          {hasActiveFilters() && (
            <Button variant="outline" onClick={clearAllFilters}>
              <X className="h-4 w-4 mr-2" />
              Clear All
            </Button>
          )}
        </div>
      </div>

      {/* Completion Status Filter and View Mode Toggle */}
      {filters.completionStatus !== undefined && (
        <div className="flex items-center justify-between flex-wrap gap-4">
          <div className="flex items-center gap-4">
            {/* Completion Status Filter */}
            <div className="flex gap-2">
              <button
                onClick={() => updateFilters({ completionStatus: 'all' })}
                className={`px-3 py-1.5 text-sm font-medium rounded-md transition-colors ${
                  filters.completionStatus === 'all'
                    ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300'
                    : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200'
                }`}
              >
                All
              </button>
              <button
                onClick={() => updateFilters({ completionStatus: 'completed' })}
                className={`px-3 py-1.5 text-sm font-medium rounded-md transition-colors ${
                  filters.completionStatus === 'completed'
                    ? 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-300'
                    : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200'
                }`}
              >
                Completed
              </button>
              <button
                onClick={() => updateFilters({ completionStatus: 'in-progress' })}
                className={`px-3 py-1.5 text-sm font-medium rounded-md transition-colors ${
                  filters.completionStatus === 'in-progress'
                    ? 'bg-orange-100 text-orange-700 dark:bg-orange-900/20 dark:text-orange-300'
                    : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200'
                }`}
              >
                In Progress
              </button>
            </div>

            {/* View Mode Toggle */}
            {onViewModeChange && (
              <div className="flex border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
                <button
                  onClick={() => onViewModeChange('grid')}
                  className={`px-3 py-2 ${
                    viewMode === 'grid'
                      ? 'bg-blue-500 text-white'
                      : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
                  }`}
                >
                  <Grid className="h-4 w-4" />
                </button>
                <button
                  onClick={() => onViewModeChange('list')}
                  className={`px-3 py-2 ${
                    viewMode === 'list'
                      ? 'bg-blue-500 text-white'
                      : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
                  }`}
                >
                  <List className="h-4 w-4" />
                </button>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Advanced Filters */}
      {isExpanded && (
        <div className="space-y-6 border-t pt-6">
          {/* Quick Filters Row */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Learning Level */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Learning Level
              </label>
              <select
                value={filters.learningLevel}
                onChange={(e) => updateFilters({ learningLevel: e.target.value as any })}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Levels</option>
                <option value="beginner">Beginner</option>
                <option value="intermediate">Intermediate</option>
                <option value="advanced">Advanced</option>
              </select>
            </div>

            {/* Visibility */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Visibility
              </label>
              <select
                value={filters.isPublic === undefined ? 'all' : filters.isPublic ? 'public' : 'private'}
                onChange={(e) => {
                  const value = e.target.value;
                  updateFilters({
                    isPublic: value === 'all' ? undefined : value === 'public'
                  });
                }}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">All Content</option>
                <option value="public">Public Only</option>
                <option value="private">Private Only</option>
              </select>
            </div>

            {/* Reading Time */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                <Clock className="h-4 w-4 inline mr-1" />
                Reading Time (minutes)
              </label>
              <div className="flex items-center space-x-2">
                <input
                  type="number"
                  min="0"
                  max="300"
                  value={filters.readingTimeRange.min}
                  onChange={(e) => updateFilters({
                    readingTimeRange: {
                      ...filters.readingTimeRange,
                      min: parseInt(e.target.value) || 0
                    }
                  })}
                  className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  placeholder="Min"
                />
                <span className="text-gray-500 dark:text-gray-400">to</span>
                <input
                  type="number"
                  min="0"
                  max="300"
                  value={filters.readingTimeRange.max}
                  onChange={(e) => updateFilters({
                    readingTimeRange: {
                      ...filters.readingTimeRange,
                      max: parseInt(e.target.value) || 300
                    }
                  })}
                  className="w-20 px-2 py-1 border border-gray-300 dark:border-gray-600 rounded text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  placeholder="Max"
                />
              </div>
            </div>
          </div>

          {/* Date Range */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <Calendar className="h-4 w-4 inline mr-1" />
              Date Range
            </label>
            <div className="flex items-center space-x-2">
              <input
                type="date"
                value={filters.dateRange.from}
                onChange={(e) => updateFilters({
                  dateRange: { ...filters.dateRange, from: e.target.value }
                })}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <span className="text-gray-500 dark:text-gray-400">to</span>
              <input
                type="date"
                value={filters.dateRange.to}
                onChange={(e) => updateFilters({
                  dateRange: { ...filters.dateRange, to: e.target.value }
                })}
                className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Tags */}
          {availableTags.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                <Tag className="h-4 w-4 inline mr-1" />
                Tags
              </label>
              <div className="flex flex-wrap gap-2">
                {availableTags.map((tag) => (
                  <button
                    key={tag}
                    onClick={() => handleTagToggle(tag)}
                    className={`px-3 py-1 rounded-full text-sm border transition-colors ${
                      filters.tags.includes(tag)
                        ? 'bg-blue-100 dark:bg-blue-900/30 border-blue-300 dark:border-blue-600 text-blue-800 dark:text-blue-300'
                        : 'bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                    }`}
                  >
                    {tag}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Content Types */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <BookOpen className="h-4 w-4 inline mr-1" />
              Content Types
            </label>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
              {contentTypeOptions.map((type) => (
                <label key={type.id} className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={filters.contentTypes.includes(type.id)}
                    onChange={() => handleContentTypeToggle(type.id)}
                    className="rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500 bg-white dark:bg-gray-700"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">{type.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Saved Searches */}
          <div className="flex items-center justify-between pt-4 border-t">
            <div className="flex items-center space-x-2">
              {savedSearches.length > 0 && (
                <select
                  onChange={(e) => {
                    const search = savedSearches.find(s => s.id === e.target.value);
                    if (search && onLoadSearch) {
                      onLoadSearch(search.filters);
                    }
                  }}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                  defaultValue=""
                >
                  <option value="">Load Saved Search...</option>
                  {savedSearches.map((search) => (
                    <option key={search.id} value={search.id}>
                      {search.name}
                    </option>
                  ))}
                </select>
              )}
            </div>

            <div className="flex items-center space-x-2">
              {showSaveDialog ? (
                <div className="flex items-center space-x-2">
                  <input
                    type="text"
                    value={saveSearchName}
                    onChange={(e) => setSaveSearchName(e.target.value)}
                    placeholder="Search name..."
                    className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-sm w-40 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                    onKeyPress={(e) => e.key === 'Enter' && handleSaveSearch()}
                  />
                  <Button size="sm" onClick={handleSaveSearch}>
                    Save
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      setShowSaveDialog(false);
                      setSaveSearchName('');
                    }}
                  >
                    Cancel
                  </Button>
                </div>
              ) : (
                onSaveSearch && hasActiveFilters() && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowSaveDialog(true)}
                  >
                    <Save className="h-4 w-4 mr-2" />
                    Save Search
                  </Button>
                )
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
