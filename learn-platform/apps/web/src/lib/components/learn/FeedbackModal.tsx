'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@learn-platform/shared-ui';
import { 
  X, 
  Star, 
  ThumbsUp, 
  ThumbsDown, 
  MessageSquare,
  RefreshCw,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { api } from '../../trpc';

interface FeedbackModalProps {
  isOpen: boolean;
  onClose: () => void;
  contentId: string;
  contentTitle: string;
}

interface FeedbackData {
  rating: number;
  feedbackText: string;
  isHelpful?: boolean;
  suggestedImprovements: {
    contentQuality: boolean;
    clarity: boolean;
    examples: boolean;
    length: boolean;
    difficulty: boolean;
    other: string;
  };
  requestRegeneration: boolean;
  regenerationReason: string;
}

export function FeedbackModal({ isOpen, onClose, contentId, contentTitle }: FeedbackModalProps) {
  const [feedback, setFeedback] = useState<FeedbackData>({
    rating: 0,
    feedbackText: '',
    isHelpful: undefined,
    suggestedImprovements: {
      contentQuality: false,
      clarity: false,
      examples: false,
      length: false,
      difficulty: false,
      other: '',
    },
    requestRegeneration: false,
    regenerationReason: '',
  });

  const [step, setStep] = useState<'rating' | 'details' | 'improvements' | 'success'>('rating');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch existing feedback
  const { data: existingFeedback, refetch } = api.learningFeedback.getFeedback.useQuery(
    { contentId },
    { enabled: isOpen }
  );

  // Submit feedback mutation
  const submitFeedbackMutation = api.learningFeedback.submitFeedback.useMutation({
    onSuccess: () => {
      setStep('success');
      refetch();
    },
    onError: (error) => {
      alert(`Failed to submit feedback: ${error.message}`);
      setIsSubmitting(false);
    },
  });

  // Update feedback mutation
  const updateFeedbackMutation = api.learningFeedback.updateFeedback.useMutation({
    onSuccess: () => {
      setStep('success');
      refetch();
    },
    onError: (error) => {
      alert(`Failed to update feedback: ${error.message}`);
      setIsSubmitting(false);
    },
  });

  // Load existing feedback when modal opens
  useEffect(() => {
    if (isOpen && existingFeedback?.feedback) {
      const existing = existingFeedback.feedback;
      setFeedback({
        rating: existing.rating,
        feedbackText: existing.feedbackText || '',
        isHelpful: existing.isHelpful,
        suggestedImprovements: {
          contentQuality: existing.suggestedImprovements?.contentQuality || false,
          clarity: existing.suggestedImprovements?.clarity || false,
          examples: existing.suggestedImprovements?.examples || false,
          length: existing.suggestedImprovements?.length || false,
          difficulty: existing.suggestedImprovements?.difficulty || false,
          other: existing.suggestedImprovements?.other || '',
        },
        requestRegeneration: existing.requestRegeneration || false,
        regenerationReason: existing.regenerationReason || '',
      });
      setStep('details');
    } else if (isOpen) {
      // Reset for new feedback
      setFeedback({
        rating: 0,
        feedbackText: '',
        isHelpful: undefined,
        suggestedImprovements: {
          contentQuality: false,
          clarity: false,
          examples: false,
          length: false,
          difficulty: false,
          other: '',
        },
        requestRegeneration: false,
        regenerationReason: '',
      });
      setStep('rating');
    }
  }, [isOpen, existingFeedback]);

  if (!isOpen) return null;

  const handleSubmit = async () => {
    setIsSubmitting(true);

    const feedbackData = {
      contentId,
      rating: feedback.rating,
      feedbackText: feedback.feedbackText || undefined,
      isHelpful: feedback.isHelpful,
      suggestedImprovements: feedback.suggestedImprovements,
      requestRegeneration: feedback.requestRegeneration,
      regenerationReason: feedback.regenerationReason || undefined,
    };

    try {
      if (existingFeedback?.feedback) {
        await updateFeedbackMutation.mutateAsync({
          feedbackId: existingFeedback.feedback.id,
          ...feedbackData,
        });
      } else {
        await submitFeedbackMutation.mutateAsync(feedbackData);
      }
    } catch (error) {
      // Error handling is done in mutation callbacks
    }
  };

  const renderStarRating = () => (
    <div className="flex justify-center space-x-2 mb-6">
      {[1, 2, 3, 4, 5].map((star) => (
        <button
          key={star}
          onClick={() => setFeedback({ ...feedback, rating: star })}
          className="transition-colors"
        >
          <Star
            className={`h-8 w-8 ${
              star <= feedback.rating
                ? 'text-yellow-400 fill-current'
                : 'text-gray-300 dark:text-gray-600 hover:text-yellow-200 dark:hover:text-yellow-300'
            }`}
          />
        </button>
      ))}
    </div>
  );

  const renderRatingStep = () => (
    <div className="text-center">
      <h3 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Rate this learning content</h3>
      <p className="text-gray-600 dark:text-gray-300 mb-6">How would you rate the overall quality of this content?</p>
      
      {renderStarRating()}
      
      <div className="flex justify-center space-x-4">
        <Button
          variant="outline"
          onClick={onClose}
        >
          Skip
        </Button>
        <Button
          onClick={() => setStep('details')}
          disabled={feedback.rating === 0}
        >
          Continue
        </Button>
      </div>
    </div>
  );

  const renderDetailsStep = () => (
    <div>
      <h3 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Share your feedback</h3>
      
      {/* Rating Display */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Your Rating</label>
        {renderStarRating()}
      </div>

      {/* Helpful Toggle */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
          Was this content helpful?
        </label>
        <div className="flex space-x-4">
          <button
            onClick={() => setFeedback({ ...feedback, isHelpful: true })}
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
              feedback.isHelpful === true
                ? 'border-green-500 dark:border-green-400 bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300'
                : 'border-gray-300 dark:border-gray-600 hover:border-green-300 dark:hover:border-green-400 text-gray-700 dark:text-gray-300'
            }`}
          >
            <ThumbsUp className="h-4 w-4" />
            <span>Yes</span>
          </button>
          <button
            onClick={() => setFeedback({ ...feedback, isHelpful: false })}
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${
              feedback.isHelpful === false
                ? 'border-red-500 dark:border-red-400 bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300'
                : 'border-gray-300 dark:border-gray-600 hover:border-red-300 dark:hover:border-red-400 text-gray-700 dark:text-gray-300'
            }`}
          >
            <ThumbsDown className="h-4 w-4" />
            <span>No</span>
          </button>
        </div>
      </div>

      {/* Feedback Text */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Additional Comments (Optional)
        </label>
        <textarea
          value={feedback.feedbackText}
          onChange={(e) => setFeedback({ ...feedback, feedbackText: e.target.value })}
          placeholder="Share your thoughts about this content..."
          className="w-full h-24 p-3 border border-gray-300 dark:border-gray-600 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
        />
      </div>

      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={() => setStep('rating')}
        >
          Back
        </Button>
        <div className="space-x-2">
          <Button
            variant="outline"
            onClick={() => setStep('improvements')}
          >
            Suggest Improvements
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Submitting...' : existingFeedback?.feedback ? 'Update Feedback' : 'Submit Feedback'}
          </Button>
        </div>
      </div>
    </div>
  );

  const renderImprovementsStep = () => (
    <div>
      <h3 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">Suggest Improvements</h3>
      
      <div className="space-y-4 mb-6">
        <p className="text-gray-600 dark:text-gray-300">What aspects could be improved? (Select all that apply)</p>
        
        {Object.entries({
          contentQuality: 'Content Quality',
          clarity: 'Clarity & Understanding',
          examples: 'Examples & Illustrations',
          length: 'Content Length',
          difficulty: 'Difficulty Level',
        }).map(([key, label]) => (
          <label key={key} className="flex items-center space-x-3 cursor-pointer">
            <input
              type="checkbox"
              checked={feedback.suggestedImprovements[key as keyof typeof feedback.suggestedImprovements]}
              onChange={(e) => setFeedback({
                ...feedback,
                suggestedImprovements: {
                  ...feedback.suggestedImprovements,
                  [key]: e.target.checked,
                },
              })}
              className="rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500 dark:bg-gray-700"
            />
            <span className="text-gray-900 dark:text-white">{label}</span>
          </label>
        ))}
        
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Other suggestions:
          </label>
          <input
            type="text"
            value={feedback.suggestedImprovements.other}
            onChange={(e) => setFeedback({
              ...feedback,
              suggestedImprovements: {
                ...feedback.suggestedImprovements,
                other: e.target.value,
              },
            })}
            placeholder="Describe other improvements..."
            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
          />
        </div>
      </div>

      {/* Regeneration Request */}
      <div className="mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
        <label className="flex items-center space-x-3 cursor-pointer mb-3">
          <input
            type="checkbox"
            checked={feedback.requestRegeneration}
            onChange={(e) => setFeedback({
              ...feedback,
              requestRegeneration: e.target.checked,
            })}
            className="rounded border-gray-300 dark:border-gray-600 text-yellow-600 focus:ring-yellow-500 dark:bg-gray-700"
          />
          <div className="flex items-center space-x-2">
            <RefreshCw className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
            <span className="font-medium text-gray-900 dark:text-white">Request content regeneration</span>
          </div>
        </label>
        
        {feedback.requestRegeneration && (
          <textarea
            value={feedback.regenerationReason}
            onChange={(e) => setFeedback({
              ...feedback,
              regenerationReason: e.target.value,
            })}
            placeholder="Please explain why you'd like this content regenerated..."
            className="w-full h-20 p-3 border border-yellow-300 dark:border-yellow-600 rounded-lg resize-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
          />
        )}
      </div>

      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={() => setStep('details')}
        >
          Back
        </Button>
        <Button
          onClick={handleSubmit}
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Submitting...' : existingFeedback?.feedback ? 'Update Feedback' : 'Submit Feedback'}
        </Button>
      </div>
    </div>
  );

  const renderSuccessStep = () => (
    <div className="text-center">
      <CheckCircle className="h-16 w-16 text-green-500 dark:text-green-400 mx-auto mb-4" />
      <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">Thank you for your feedback!</h3>
      <p className="text-gray-600 dark:text-gray-300 mb-6">
        Your feedback helps us improve the quality of AI-generated learning content.
      </p>
      <Button onClick={onClose}>
        Close
      </Button>
    </div>
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Content Feedback</h2>
            <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">{contentTitle}</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {step === 'rating' && renderRatingStep()}
          {step === 'details' && renderDetailsStep()}
          {step === 'improvements' && renderImprovementsStep()}
          {step === 'success' && renderSuccessStep()}
        </div>
      </div>
    </div>
  );
}
