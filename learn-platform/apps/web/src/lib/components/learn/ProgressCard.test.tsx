/**
 * Tests for ProgressCard component
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ProgressCard } from './ProgressCard';
import { api } from '../../trpc';

// Mock the tRPC API
jest.mock('../../trpc', () => ({
  api: {
    learningProgress: {
      getStats: {
        useQuery: jest.fn(),
      },
    },
  },
}));

// Mock Next.js Link component
jest.mock('next/link', () => {
  return function MockLink({ children, href }: { children: React.ReactNode; href: string }) {
    return <a href={href}>{children}</a>;
  };
});

const mockApi = api as jest.Mocked<typeof api>;

describe('ProgressCard', () => {
  let queryClient: QueryClient;

  beforeEach(() => {
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    });
    jest.clearAllMocks();
  });

  const renderWithProviders = (component: React.ReactElement) => {
    return render(
      <QueryClientProvider client={queryClient}>
        {component}
      </QueryClientProvider>
    );
  };

  it('should render loading state', () => {
    mockApi.learningProgress.getStats.useQuery.mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null,
    } as any);

    renderWithProviders(<ProgressCard />);

    expect(screen.getByTestId('progress-card-loading') || document.querySelector('.animate-pulse')).toBeInTheDocument();
  });

  it('should render error state', () => {
    mockApi.learningProgress.getStats.useQuery.mockReturnValue({
      data: undefined,
      isLoading: false,
      error: new Error('Failed to fetch'),
    } as any);

    renderWithProviders(<ProgressCard />);

    expect(screen.getByText('Unable to load progress data')).toBeInTheDocument();
  });

  it('should render no data state', () => {
    mockApi.learningProgress.getStats.useQuery.mockReturnValue({
      data: {
        stats: {
          totalContent: 0,
          completedContent: 0,
          inProgressContent: 0,
          totalTimeSpent: 0,
          averageCompletion: 0,
          recentActivity: [],
        },
      },
      isLoading: false,
      error: null,
    } as any);

    renderWithProviders(<ProgressCard />);

    expect(screen.getByText('No learning content yet. Create your first learning content to start tracking progress!')).toBeInTheDocument();
    expect(screen.getByText('Get Started')).toBeInTheDocument();
  });

  it('should render progress data correctly', () => {
    const mockStats = {
      totalContent: 5,
      completedContent: 2,
      inProgressContent: 1,
      totalTimeSpent: 3600, // 1 hour
      averageCompletion: 60,
      recentActivity: [],
    };

    mockApi.learningProgress.getStats.useQuery.mockReturnValue({
      data: { stats: mockStats },
      isLoading: false,
      error: null,
    } as any);

    renderWithProviders(<ProgressCard />);

    expect(screen.getByText('5')).toBeInTheDocument(); // Total Content
    expect(screen.getByText('2')).toBeInTheDocument(); // Completed
    expect(screen.getByText('60%')).toBeInTheDocument(); // Average Progress
    expect(screen.getByText('1h')).toBeInTheDocument(); // Time Spent
    expect(screen.getByText('1 in progress')).toBeInTheDocument(); // In Progress
  });

  it('should format time correctly', () => {
    const testCases = [
      { seconds: 30, expected: '30s' },
      { seconds: 90, expected: '2m' },
      { seconds: 3600, expected: '1h' },
      { seconds: 3660, expected: '1h 1m' },
    ];

    testCases.forEach(({ seconds, expected }) => {
      const mockStats = {
        totalContent: 1,
        completedContent: 0,
        inProgressContent: 0,
        totalTimeSpent: seconds,
        averageCompletion: 0,
        recentActivity: [],
      };

      mockApi.learningProgress.getStats.useQuery.mockReturnValue({
        data: { stats: mockStats },
        isLoading: false,
        error: null,
      } as any);

      const { unmount } = renderWithProviders(<ProgressCard />);
      expect(screen.getByText(expected)).toBeInTheDocument();
      unmount();
    });
  });

  it('should have correct navigation links', () => {
    const mockStats = {
      totalContent: 1,
      completedContent: 0,
      inProgressContent: 0,
      totalTimeSpent: 0,
      averageCompletion: 0,
      recentActivity: [],
    };

    mockApi.learningProgress.getStats.useQuery.mockReturnValue({
      data: { stats: mockStats },
      isLoading: false,
      error: null,
    } as any);

    renderWithProviders(<ProgressCard />);

    const viewDetailsLink = screen.getByText('View Details →');
    expect(viewDetailsLink.closest('a')).toHaveAttribute('href', '/dashboard/progress');
  });

  it('should apply custom className', () => {
    mockApi.learningProgress.getStats.useQuery.mockReturnValue({
      data: {
        stats: {
          totalContent: 0,
          completedContent: 0,
          inProgressContent: 0,
          totalTimeSpent: 0,
          averageCompletion: 0,
          recentActivity: [],
        },
      },
      isLoading: false,
      error: null,
    } as any);

    const { container } = renderWithProviders(<ProgressCard className="custom-class" />);
    
    expect(container.firstChild).toHaveClass('custom-class');
  });

  it('should handle missing stats gracefully', () => {
    mockApi.learningProgress.getStats.useQuery.mockReturnValue({
      data: null,
      isLoading: false,
      error: null,
    } as any);

    renderWithProviders(<ProgressCard />);

    expect(screen.getByText('Start your learning journey')).toBeInTheDocument();
  });
});
