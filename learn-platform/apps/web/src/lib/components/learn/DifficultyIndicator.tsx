'use client';

import { cn } from '@learn-platform/shared-ui';

interface DifficultyIndicatorProps {
  difficulty: 'easy' | 'medium' | 'hard';
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export function DifficultyIndicator({ difficulty, className, size = 'md' }: DifficultyIndicatorProps) {
  // Determine number of bars based on difficulty
  const getBarCount = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return 1;
      case 'medium':
        return 2;
      case 'hard':
        return 3;
      default:
        return 1;
    }
  };

  // Get color classes based on difficulty
  const getColorClasses = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return {
          active: 'bg-green-500',
          inactive: 'bg-gray-200 dark:bg-gray-600'
        };
      case 'medium':
        return {
          active: 'bg-yellow-500',
          inactive: 'bg-gray-200 dark:bg-gray-600'
        };
      case 'hard':
        return {
          active: 'bg-red-500',
          inactive: 'bg-gray-200 dark:bg-gray-600'
        };
      default:
        return {
          active: 'bg-green-500',
          inactive: 'bg-gray-200 dark:bg-gray-600'
        };
    }
  };

  // Get size classes
  const getSizeClasses = (size: string) => {
    switch (size) {
      case 'sm':
        return {
          container: 'gap-0.5',
          bar: 'w-1 h-3'
        };
      case 'md':
        return {
          container: 'gap-1',
          bar: 'w-1.5 h-4'
        };
      case 'lg':
        return {
          container: 'gap-1.5',
          bar: 'w-2 h-5'
        };
      default:
        return {
          container: 'gap-1',
          bar: 'w-1.5 h-4'
        };
    }
  };

  const barCount = getBarCount(difficulty);
  const colorClasses = getColorClasses(difficulty);
  const sizeClasses = getSizeClasses(size);
  const totalBars = 3; // Always show 3 bars total

  return (
    <div className={cn(
      'flex items-end',
      sizeClasses.container,
      className
    )}>
      {Array.from({ length: totalBars }, (_, index) => (
        <div
          key={index}
          className={cn(
            'rounded-sm transition-colors duration-200',
            sizeClasses.bar,
            index < barCount ? colorClasses.active : colorClasses.inactive
          )}
        />
      ))}
    </div>
  );
}