'use client';

import { cn } from '@learn-platform/shared-ui';
import { Trophy, Target, Clock, RotateCcw, Eye, X, CheckCircle, XCircle, AlertCircle } from 'lucide-react';
import type { QuizResultsProps } from './types';

export function QuizResults({
  quiz,
  attempt,
  score,
  questionResults,
  onRetake,
  onReview,
  onExit,
  isRetaking = false,
  className
}: QuizResultsProps) {


  // Calculate performance metrics
  const accuracyPercentage = score.percentage;
  const timePerQuestion = attempt.totalTimeSpent / quiz.questions.length;
  const efficiency = quiz.estimatedDuration > 0
    ? Math.min(100, (quiz.estimatedDuration * 60) / attempt.totalTimeSpent * 100)
    : 100;

  // Performance level
  const getPerformanceLevel = (percentage: number) => {
    if (percentage >= 90) return { level: 'Excellent', color: 'text-green-600 dark:text-green-400', bgColor: 'bg-green-100 dark:bg-green-900/30' };
    if (percentage >= 80) return { level: 'Good', color: 'text-blue-600 dark:text-blue-400', bgColor: 'bg-blue-100 dark:bg-blue-900/30' };
    if (percentage >= 70) return { level: 'Fair', color: 'text-yellow-600 dark:text-yellow-400', bgColor: 'bg-yellow-100 dark:bg-yellow-900/30' };
    return { level: 'Needs Improvement', color: 'text-red-600 dark:text-red-400', bgColor: 'bg-red-100 dark:bg-red-900/30' };
  };

  const performance = getPerformanceLevel(accuracyPercentage);

  // Format time
  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  // Group questions by result
  const correctQuestions = questionResults.filter(q => q.isCorrect);
  const incorrectQuestions = questionResults.filter(q => !q.isCorrect);

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="text-center">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full mb-4">
          <Trophy className="h-8 w-8 text-blue-600 dark:text-blue-400" />
        </div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Quiz Complete!</h1>
        <p className="text-gray-600 dark:text-gray-300">Here&apos;s how you performed on &quot;{quiz.title}&quot;</p>
      </div>

      {/* Score Overview */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border dark:border-gray-700 p-6">
        <div className="text-center mb-6">
          <div className="text-6xl font-bold text-gray-900 dark:text-white mb-2">
            {score.percentage}%
          </div>
          <div className={cn(
            "inline-flex items-center px-3 py-1 rounded-full text-sm font-medium",
            performance.bgColor,
            performance.color
          )}>
            {performance.level}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Accuracy */}
          <div className="text-center">
            <div className="flex items-center justify-center w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full mx-auto mb-3">
              <Target className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {score.correctAnswers}/{score.totalQuestions}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Correct Answers</div>
          </div>

          {/* Time */}
          <div className="text-center">
            <div className="flex items-center justify-center w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full mx-auto mb-3">
              <Clock className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {formatTime(attempt.totalTimeSpent)}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Total Time</div>
          </div>

          {/* Points */}
          <div className="text-center">
            <div className="flex items-center justify-center w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-full mx-auto mb-3">
              <Trophy className="h-6 w-6 text-purple-600 dark:text-purple-400" />
            </div>
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {score.earnedPoints}/{score.totalPoints}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Points Earned</div>
          </div>
        </div>
      </div>

      {/* Detailed Stats */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border dark:border-gray-700 p-6">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Performance Details</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Question Breakdown</h3>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Correct</span>
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400" />
                  <span className="text-sm font-medium text-gray-900 dark:text-white">{correctQuestions.length}</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Incorrect</span>
                <div className="flex items-center space-x-2">
                  <XCircle className="h-4 w-4 text-red-500 dark:text-red-400" />
                  <span className="text-sm font-medium text-gray-900 dark:text-white">{incorrectQuestions.length}</span>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Time Analysis</h3>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Average per question</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">{formatTime(Math.round(timePerQuestion))}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Efficiency</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">{Math.round(efficiency)}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Question Results Summary */}
      {questionResults.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border dark:border-gray-700 p-6">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Question Results</h2>

          <div className="space-y-3 max-h-64 overflow-y-auto">
            {questionResults.map((result, index) => {
              const question = quiz.questions.find(q => q.id === result.questionId);
              if (!question) return null;

              return (
                <div
                  key={result.questionId}
                  className={cn(
                    "flex items-center justify-between p-3 rounded-lg border",
                    result.isCorrect 
                      ? "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800" 
                      : "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800"
                  )}
                >
                  <div className="flex items-center space-x-3">
                    {result.isCorrect ? (
                      <CheckCircle className="h-5 w-5 text-green-500 dark:text-green-400" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500 dark:text-red-400" />
                    )}
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        Question {index + 1}
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400 capitalize">
                        {question.type.replace(/([A-Z])/g, ' $1').toLowerCase()}
                      </div>
                    </div>
                  </div>

                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {result.pointsEarned}/{question.points} pts
                    </div>
                    {result.feedback && (
                      <div className="text-xs text-gray-600 dark:text-gray-400 max-w-xs truncate">
                        {result.feedback}
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <button
          onClick={onReview}
          className="flex items-center justify-center space-x-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
        >
          <Eye className="h-4 w-4" />
          <span>Review Answers</span>
        </button>

        {onRetake && (
          <button
            onClick={onRetake}
            disabled={isRetaking}
            className="flex items-center justify-center space-x-2 px-6 py-3 bg-green-600 hover:bg-green-700 dark:bg-green-600 dark:hover:bg-green-700 disabled:bg-gray-400 dark:disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg font-medium transition-colors"
          >
            {isRetaking ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Preparing Retake...</span>
              </>
            ) : (
              <>
                <RotateCcw className="h-4 w-4" />
                <span>Retake Quiz</span>
              </>
            )}
          </button>
        )}

        <button
          onClick={onExit}
          className="flex items-center justify-center space-x-2 px-6 py-3 bg-gray-600 hover:bg-gray-700 dark:bg-gray-600 dark:hover:bg-gray-700 text-white rounded-lg font-medium transition-colors"
        >
          <X className="h-4 w-4" />
          <span>Exit</span>
        </button>
      </div>

      {/* Motivational message */}
      <div className="text-center">
        {accuracyPercentage >= 90 ? (
          <p className="text-green-600 dark:text-green-400 font-medium">🎉 Outstanding performance! You&apos;ve mastered this topic.</p>
        ) : accuracyPercentage >= 80 ? (
          <p className="text-blue-600 dark:text-blue-400 font-medium">👏 Great job! You have a solid understanding.</p>
        ) : accuracyPercentage >= 70 ? (
          <p className="text-yellow-600 dark:text-yellow-400 font-medium">👍 Good effort! Consider reviewing the material.</p>
        ) : (
          <p className="text-red-600 dark:text-red-400 font-medium">💪 Keep practicing! Review the content and try again.</p>
        )}
      </div>
    </div>
  );
}
