'use client';

import { useState } from 'react';
import { cn } from '@learn-platform/shared-ui';
import { X, Star, MessageSquare, Send } from 'lucide-react';
import type { QuizFeedbackModalProps, QuizFeedback } from './types';

export function QuizFeedbackModal({
  isOpen,
  onClose,
  onSubmit,
  quiz,
  attempt,
  isSubmitting
}: QuizFeedbackModalProps) {
  const [feedback, setFeedback] = useState<Partial<QuizFeedback>>({
    quizId: quiz.id,
    attemptId: attempt?.id,
    questionFeedback: [],
    isAnonymous: false,
  });

  const handleRatingChange = (field: keyof QuizFeedback, rating: number) => {
    setFeedback(prev => ({ ...prev, [field]: rating }));
  };

  const handleTextChange = (field: keyof QuizFeedback, value: string) => {
    setFeedback(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit(feedback as QuizFeedback);
  };

  const StarRating = ({ 
    value, 
    onChange, 
    label 
  }: { 
    value?: number; 
    onChange: (rating: number) => void; 
    label: string;
  }) => (
    <div className="space-y-2">
      <label className="text-sm font-medium text-gray-700 dark:text-gray-300">{label}</label>
      <div className="flex space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() => onChange(star)}
            className={cn(
              "p-1 rounded transition-colors",
              value && star <= value
                ? "text-yellow-400 hover:text-yellow-500"
                : "text-gray-300 hover:text-gray-400 dark:text-gray-600 dark:hover:text-gray-500"
            )}
          >
            <Star className="h-5 w-5 fill-current" />
          </button>
        ))}
      </div>
    </div>
  );

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Quiz Feedback</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">Help us improve this quiz</p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Overall Ratings */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Overall Experience</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <StarRating
                label="Overall Rating"
                value={feedback.overallRating}
                onChange={(rating) => handleRatingChange('overallRating', rating)}
              />
              
              <StarRating
                label="Difficulty Level"
                value={feedback.difficultyRating}
                onChange={(rating) => handleRatingChange('difficultyRating', rating)}
              />
              
              <StarRating
                label="Question Clarity"
                value={feedback.clarityRating}
                onChange={(rating) => handleRatingChange('clarityRating', rating)}
              />
              
              <StarRating
                label="Content Relevance"
                value={feedback.relevanceRating}
                onChange={(rating) => handleRatingChange('relevanceRating', rating)}
              />
            </div>
          </div>

          {/* Text Feedback */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">Additional Feedback</h3>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Comments
              </label>
              <textarea
                value={feedback.comments || ''}
                onChange={(e) => handleTextChange('comments', e.target.value)}
                placeholder="What did you think about this quiz? Any specific feedback?"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:border-blue-400 resize-none bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                rows={4}
                maxLength={1000}
              />
              <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {(feedback.comments || '').length}/1000 characters
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Suggestions for Improvement
              </label>
              <textarea
                value={feedback.suggestions || ''}
                onChange={(e) => handleTextChange('suggestions', e.target.value)}
                placeholder="How could we make this quiz better?"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:border-blue-400 resize-none bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                rows={3}
                maxLength={1000}
              />
              <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {(feedback.suggestions || '').length}/1000 characters
              </div>
            </div>
          </div>

          {/* Privacy Option */}
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="anonymous"
              checked={feedback.isAnonymous}
              onChange={(e) => setFeedback(prev => ({ ...prev, isAnonymous: e.target.checked }))}
              className="rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500 dark:bg-gray-700 dark:checked:bg-blue-600"
            />
            <label htmlFor="anonymous" className="text-sm text-gray-700 dark:text-gray-300">
              Submit feedback anonymously
            </label>
          </div>

          {/* Quiz Info */}
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">Quiz Information</h4>
            <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
              <div>Quiz: {quiz.title}</div>
              <div>Difficulty: {quiz.difficulty}</div>
              <div>Questions: {quiz.questions.length}</div>
              {attempt && (
                <div>Your Score: {attempt.score?.percentage}%</div>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-lg font-medium transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className={cn(
                "flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors",
                isSubmitting
                  ? "bg-gray-400 dark:bg-gray-600 text-white cursor-not-allowed"
                  : "bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white"
              )}
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent" />
                  <span>Submitting...</span>
                </>
              ) : (
                <>
                  <Send className="h-4 w-4" />
                  <span>Submit Feedback</span>
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
