/**
 * Quiz component types and interfaces
 * Matches the AI-generated quiz schemas for type safety
 */

import { ReactNode } from 'react';

// Quiz difficulty levels
export type QuizDifficulty = 'easy' | 'medium' | 'hard';

// Quiz types
export type QuizType =
  | 'flashcard'
  | 'multipleChoice'
  | 'trueFalse'
  | 'fillInBlank'
  | 'matching'
  | 'freeText'
  | 'ordering';

// Base quiz question interface
export interface BaseQuestion {
  id: string;
  type: QuizType;
  difficulty: QuizDifficulty;
  sourceStepId: string;
  sourceContent: string;
  points: number;
}

// Specific question types
export interface FlashcardQuestion extends BaseQuestion {
  type: 'flashcard';
  front: string;
  back: string;
  hint?: string;
}

export interface MultipleChoiceQuestion extends BaseQuestion {
  type: 'multipleChoice';
  question: string;
  options: string[];
  correctAnswerIndex: number;
  explanation?: string;
}

export interface TrueFalseQuestion extends BaseQuestion {
  type: 'trueFalse';
  statement: string;
  correctAnswer: boolean;
  explanation?: string;
}

export interface FillInBlankQuestion extends BaseQuestion {
  type: 'fillInBlank';
  text: string;
  blanks: Array<{
    position: number;
    correctAnswer: string;
    acceptableAnswers?: string[];
    caseSensitive: boolean;
  }>;
  hint?: string;
}

export interface MatchingQuestion extends BaseQuestion {
  type: 'matching';
  instruction: string;
  pairs: Array<{
    left: string;
    right: string;
  }>;
}

export interface FreeTextQuestion extends BaseQuestion {
  type: 'freeText';
  question: string;
  answerType: 'short' | 'long';
  maxLength: number;
  sampleAnswer: string;
  evaluationCriteria: string[];
}

export interface OrderingQuestion extends BaseQuestion {
  type: 'ordering';
  instruction: string;
  items: string[];
  correctOrder: number[];
  orderType: 'chronological' | 'logical' | 'priority' | 'process';
}

// Union type for all questions
export type QuizQuestion =
  | FlashcardQuestion
  | MultipleChoiceQuestion
  | TrueFalseQuestion
  | FillInBlankQuestion
  | MatchingQuestion
  | FreeTextQuestion
  | OrderingQuestion;

// Quiz data structure
export interface Quiz {
  id: string;
  title: string;
  description: string;
  learningContentId: string;
  questions: QuizQuestion[];
  estimatedDuration: number;
  totalPoints: number;
  difficulty: QuizDifficulty;
  isPublic: boolean;
  allowRetakes: boolean;
  showCorrectAnswers: boolean;
  shuffleQuestions: boolean;
  timeLimit?: number;
  metadata: {
    generatedAt: string;
    aiModel: string;
    sourceStepsUsed: string[];
    difficultyDistribution: Record<string, number>;
    typeDistribution: Record<string, number>;
  };
}

// Quiz attempt and progress
export interface QuizAttempt {
  id: string;
  quizId: string;
  userId: string;
  startedAt: string;
  completedAt?: string;
  isCompleted: boolean;
  answers: QuizAnswer[];
  totalTimeSpent: number;
  score?: QuizScore;
  questionResults?: QuestionResult[];
}

export interface QuizAnswer {
  questionId: string;
  questionType: QuizType;
  answer: any;
  timeSpent: number;
  isCorrect?: boolean;
  pointsEarned?: number;
}

export interface QuizScore {
  totalPoints: number;
  earnedPoints: number;
  percentage: number;
  correctAnswers: number;
  totalQuestions: number;
}

export interface QuestionResult {
  questionId: string;
  isCorrect: boolean;
  pointsEarned: number;
  feedback?: string;
}

export interface QuizProgress {
  id: string;
  attemptId: string;
  currentQuestionIndex: number;
  questionsAnswered: number;
  totalQuestions: number;
  timeSpentSoFar: number;
  lastActiveAt: string;
  currentAnswers: Array<{
    questionId: string;
    answer: any;
    timeSpent: number;
    isTemporary?: boolean;
  }>;
  bookmarkedQuestions: string[];
  questionNotes: Record<string, string>;
}

// Component props interfaces
export interface QuizComponentProps {
  quiz: Quiz;
  attempt?: QuizAttempt;
  progress?: QuizProgress;
  onAnswerSubmit: (questionId: string, answer: any, timeSpent: number) => void;
  onQuizComplete: () => void;
  onQuizExit: () => void;
  className?: string;
}

export interface QuestionComponentProps<T extends QuizQuestion = QuizQuestion> {
  question: T;
  userAnswer?: any;
  isAnswered: boolean;
  isReviewMode: boolean;
  showCorrectAnswer: boolean;
  onAnswerChange: (answer: any) => void;
  onAnswerSubmit: (answer: any, timeSpent: number) => void;
  className?: string;
}

export interface QuizNavigationProps {
  currentQuestionIndex: number;
  totalQuestions: number;
  answeredQuestions: Set<number>;
  bookmarkedQuestions: Set<number>;
  onQuestionSelect: (index: number) => void;
  onPrevious: () => void;
  onNext: () => void;
  onBookmarkToggle: (index: number) => void;
  canGoNext: boolean;
  canGoPrevious: boolean;
  className?: string;
}

export interface QuizProgressBarProps {
  currentQuestion: number;
  totalQuestions: number;
  answeredQuestions: number;
  timeSpent: number;
  timeLimit?: number;
  className?: string;
}

export interface QuizResultsProps {
  quiz: Quiz;
  attempt: QuizAttempt;
  score: QuizScore;
  questionResults: QuestionResult[];
  onRetake?: () => void;
  onReview: () => void;
  onExit: () => void;
  isRetaking?: boolean;
  className?: string;
}

// Quiz generation and configuration
export interface QuizGenerationConfig {
  learningContentId: string;
  quizTypes: QuizType[];
  difficulty: QuizDifficulty;
  questionsPerType: number;
  includeHints: boolean;
  includeExplanations: boolean;
  timeLimit?: number;
  shuffleQuestions: boolean;
}

export interface QuizSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onGenerate: (config: QuizGenerationConfig) => void;
  learningContentId: string;
  isGenerating: boolean;
}

// Quiz feedback
export interface QuizFeedback {
  quizId: string;
  attemptId?: string;
  difficultyRating?: number;
  clarityRating?: number;
  relevanceRating?: number;
  overallRating?: number;
  comments?: string;
  suggestions?: string;
  questionFeedback: Array<{
    questionId: string;
    rating: number;
    comment?: string;
    reportedIssue?: 'unclear' | 'incorrect' | 'too_hard' | 'too_easy' | 'irrelevant';
  }>;
  isAnonymous: boolean;
}

export interface QuizFeedbackModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (feedback: QuizFeedback) => void;
  quiz: Quiz;
  attempt?: QuizAttempt;
  isSubmitting: boolean;
}

// Utility types
export interface QuizStats {
  totalAttempts: number;
  completedAttempts: number;
  averageScore: number;
  averageTimeSpent: number;
  bestScore: number;
  lastAttemptDate?: string;
}

export interface QuizHistoryItem {
  id: string;
  quizId: string;
  quizTitle: string;
  startedAt: string;
  completedAt?: string;
  isCompleted: boolean;
  score?: QuizScore;
  totalTimeSpent: number;
  difficulty: QuizDifficulty;
}
