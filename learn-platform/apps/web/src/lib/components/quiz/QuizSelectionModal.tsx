'use client';

import { useState } from 'react';
import { cn } from '@learn-platform/shared-ui';
import { X, Zap, Clock, Settings, CheckCircle, Circle } from 'lucide-react';
import type { QuizSelectionModalProps, QuizGenerationConfig, QuizType, QuizDifficulty } from './types';

const QUIZ_TYPES = [
  {
    type: 'flashcard' as QuizType,
    name: 'Flashcards',
    description: 'Term and definition cards for memorization',
    icon: '🃏',
    estimatedTime: '30s per card',
    difficulty: 'Easy to use'
  },
  {
    type: 'multipleChoice' as QuizType,
    name: 'Multiple Choice',
    description: 'Questions with 4 options, one correct answer',
    icon: '📝',
    estimatedTime: '1 min per question',
    difficulty: 'Medium complexity'
  },
  {
    type: 'trueFalse' as QuizType,
    name: 'True or False',
    description: 'Statements to evaluate as true or false',
    icon: '✅',
    estimatedTime: '30s per question',
    difficulty: 'Quick assessment'
  },
  {
    type: 'fillInBlank' as QuizType,
    name: 'Fill in the Blank',
    description: 'Complete sentences with missing words',
    icon: '📄',
    estimatedTime: '1.5 min per question',
    difficulty: 'Medium complexity'
  },
  {
    type: 'matching' as QuizType,
    name: 'Matching Pairs',
    description: 'Match terms with definitions or descriptions',
    icon: '🔗',
    estimatedTime: '2 min per set',
    difficulty: 'Interactive'
  },
  {
    type: 'freeText' as QuizType,
    name: 'Free Text Answer',
    description: 'Short or long-form written responses',
    icon: '✍️',
    estimatedTime: '3 min per question',
    difficulty: 'High complexity'
  },
  {
    type: 'ordering' as QuizType,
    name: 'Ordering/Sequencing',
    description: 'Arrange items in the correct order',
    icon: '📊',
    estimatedTime: '2 min per question',
    difficulty: 'Interactive'
  }
];

export function QuizSelectionModal({
  isOpen,
  onClose,
  onGenerate,
  learningContentId,
  isGenerating
}: QuizSelectionModalProps) {
  const [config, setConfig] = useState<QuizGenerationConfig>({
    learningContentId,
    quizTypes: ['multipleChoice', 'trueFalse'],
    difficulty: 'medium',
    questionsPerType: 3,
    includeHints: true,
    includeExplanations: true,
    shuffleQuestions: false,
  });

  const handleQuizTypeToggle = (type: QuizType) => {
    setConfig(prev => ({
      ...prev,
      quizTypes: prev.quizTypes.includes(type)
        ? prev.quizTypes.filter(t => t !== type)
        : [...prev.quizTypes, type]
    }));
  };

  const handleGenerate = () => {
    if (config.quizTypes.length === 0) return;
    onGenerate(config);
  };

  const estimatedTotalQuestions = config.quizTypes.length * config.questionsPerType;
  const estimatedDuration = Math.ceil(estimatedTotalQuestions * 1.5); // Average 1.5 minutes per question

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 dark:bg-black dark:bg-opacity-70 flex items-center justify-center p-4 z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Generate Quiz</h2>
            <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
              Create a personalized quiz from your learning content
            </p>
          </div>
          <button
            onClick={onClose}
            disabled={isGenerating}
            className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Quiz Types Selection */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Select Quiz Types
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {QUIZ_TYPES.map((quizType) => {
                const isSelected = config.quizTypes.includes(quizType.type);
                
                return (
                  <button
                    key={quizType.type}
                    onClick={() => handleQuizTypeToggle(quizType.type)}
                    disabled={isGenerating}
                    className={cn(
                      "p-4 rounded-lg border-2 text-left transition-all duration-200 hover:shadow-sm",
                      isSelected
                        ? "border-blue-300 dark:border-blue-500 bg-blue-50 dark:bg-blue-900/20"
                        : "border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 hover:border-gray-300 dark:hover:border-gray-500"
                    )}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="text-2xl">{quizType.icon}</div>
                      <div>
                        {isSelected ? (
                          <CheckCircle className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                        ) : (
                          <Circle className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                        )}
                      </div>
                    </div>
                    
                    <h4 className="font-medium text-gray-900 dark:text-white mb-1">
                      {quizType.name}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                      {quizType.description}
                    </p>
                    
                    <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                      <span>{quizType.estimatedTime}</span>
                      <span>{quizType.difficulty}</span>
                    </div>
                  </button>
                );
              })}
            </div>
            
            {config.quizTypes.length === 0 && (
              <p className="text-sm text-red-600 dark:text-red-400 mt-2">
                Please select at least one quiz type
              </p>
            )}
          </div>

          {/* Configuration Options */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Difficulty */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Difficulty Level
              </label>
              <div className="space-y-2">
                {(['easy', 'medium', 'hard'] as QuizDifficulty[]).map((difficulty) => (
                  <button
                    key={difficulty}
                    onClick={() => setConfig(prev => ({ ...prev, difficulty }))}
                    disabled={isGenerating}
                    className={cn(
                      "w-full p-3 rounded-lg border text-left transition-colors",
                      config.difficulty === difficulty
                        ? "border-blue-300 dark:border-blue-500 bg-blue-50 dark:bg-blue-900/20 text-blue-900 dark:text-blue-100"
                        : "border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600"
                    )}
                  >
                    <div className="font-medium capitalize">{difficulty}</div>
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      {difficulty === 'easy' && 'Basic recall and recognition'}
                      {difficulty === 'medium' && 'Understanding and application'}
                      {difficulty === 'hard' && 'Analysis and critical thinking'}
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Questions per Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Questions per Type
              </label>
              <div className="space-y-3">
                <input
                  type="range"
                  min="1"
                  max="10"
                  value={config.questionsPerType}
                  onChange={(e) => setConfig(prev => ({ 
                    ...prev, 
                    questionsPerType: parseInt(e.target.value) 
                  }))}
                  disabled={isGenerating}
                  className="w-full"
                />
                <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
                  <span>1</span>
                  <span className="font-medium">{config.questionsPerType} questions</span>
                  <span>10</span>
                </div>
                
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
                  <div className="text-sm text-gray-700 dark:text-gray-300">
                    <div>Total questions: <span className="font-medium">{estimatedTotalQuestions}</span></div>
                    <div>Estimated time: <span className="font-medium">{estimatedDuration} minutes</span></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Additional Options */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              Additional Options
            </h3>
            <div className="space-y-3">
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={config.includeHints}
                  onChange={(e) => setConfig(prev => ({ 
                    ...prev, 
                    includeHints: e.target.checked 
                  }))}
                  disabled={isGenerating}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <div>
                  <div className="text-sm font-medium text-gray-900 dark:text-white">Include Hints</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">Provide helpful hints for difficult questions</div>
                </div>
              </label>

              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={config.includeExplanations}
                  onChange={(e) => setConfig(prev => ({ 
                    ...prev, 
                    includeExplanations: e.target.checked 
                  }))}
                  disabled={isGenerating}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <div>
                  <div className="text-sm font-medium text-gray-900 dark:text-white">Include Explanations</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">Show detailed explanations after answering</div>
                </div>
              </label>

              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={config.shuffleQuestions}
                  onChange={(e) => setConfig(prev => ({ 
                    ...prev, 
                    shuffleQuestions: e.target.checked 
                  }))}
                  disabled={isGenerating}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <div>
                  <div className="text-sm font-medium text-gray-900 dark:text-white">Shuffle Questions</div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">Randomize the order of questions</div>
                </div>
              </label>

              <div>
                <label className="flex items-center space-x-3 mb-2">
                  <input
                    type="checkbox"
                    checked={!!config.timeLimit}
                    onChange={(e) => setConfig(prev => ({ 
                      ...prev, 
                      timeLimit: e.target.checked ? estimatedDuration : undefined
                    }))}
                    disabled={isGenerating}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <div>
                    <div className="text-sm font-medium text-gray-900 dark:text-white">Set Time Limit</div>
                    <div className="text-xs text-gray-600 dark:text-gray-400">Add a time constraint to the quiz</div>
                  </div>
                </label>
                
                {config.timeLimit && (
                  <div className="ml-6 space-y-2">
                    <input
                      type="range"
                      min={Math.ceil(estimatedDuration * 0.5)}
                      max={estimatedDuration * 2}
                      value={config.timeLimit}
                      onChange={(e) => setConfig(prev => ({ 
                        ...prev, 
                        timeLimit: parseInt(e.target.value) 
                      }))}
                      disabled={isGenerating}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-gray-600 dark:text-gray-400">
                      <span>{Math.ceil(estimatedDuration * 0.5)} min</span>
                      <span className="font-medium">{config.timeLimit} minutes</span>
                      <span>{estimatedDuration * 2} min</span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 dark:border-gray-700">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4" />
              <span>Estimated generation time: 30-60 seconds</span>
            </div>
          </div>
          
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              disabled={isGenerating}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-lg font-medium transition-colors disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              onClick={handleGenerate}
              disabled={config.quizTypes.length === 0 || isGenerating}
              className={cn(
                "flex items-center space-x-2 px-6 py-2 rounded-lg font-medium transition-colors",
                config.quizTypes.length === 0 || isGenerating
                  ? "bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed"
                  : "bg-blue-600 hover:bg-blue-700 dark:bg-blue-600 dark:hover:bg-blue-700 text-white"
              )}
            >
              {isGenerating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent" />
                  <span>Generating...</span>
                </>
              ) : (
                <>
                  <Zap className="h-4 w-4" />
                  <span>Generate Quiz</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
