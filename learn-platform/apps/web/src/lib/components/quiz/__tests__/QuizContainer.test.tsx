/**
 * @jest-environment jsdom
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { jest } from '@jest/globals';
import { QuizContainer } from '../QuizContainer';
import type { Quiz, QuizAttempt, QuizProgress } from '../types';

// Mock the child components
jest.mock('../QuizNavigation', () => ({
  QuizNavigation: ({ onNext, onPrevious }: any) => (
    <div data-testid="quiz-navigation">
      <button onClick={onPrevious} data-testid="nav-previous">Previous</button>
      <button onClick={onNext} data-testid="nav-next">Next</button>
    </div>
  ),
}));

jest.mock('../QuizProgressBar', () => ({
  QuizProgressBar: ({ currentQuestion, totalQuestions }: any) => (
    <div data-testid="quiz-progress-bar">
      Progress: {currentQuestion}/{totalQuestions}
    </div>
  ),
}));

jest.mock('../QuestionRenderer', () => ({
  QuestionRenderer: ({ question, onAnswerSubmit }: any) => (
    <div data-testid="question-renderer">
      <h3>{question.question || question.front || question.statement}</h3>
      <button 
        onClick={() => onAnswerSubmit('test-answer', 30)}
        data-testid="submit-answer"
      >
        Submit Answer
      </button>
    </div>
  ),
}));

jest.mock('../QuizResults', () => ({
  QuizResults: ({ onRetake, onReview, onExit }: any) => (
    <div data-testid="quiz-results">
      <button onClick={onRetake} data-testid="retake-quiz">Retake</button>
      <button onClick={onReview} data-testid="review-quiz">Review</button>
      <button onClick={onExit} data-testid="exit-quiz">Exit</button>
    </div>
  ),
}));

// Mock quiz data
const mockQuiz: Quiz = {
  id: 'quiz-1',
  title: 'Test Quiz',
  description: 'A test quiz for unit testing',
  learningContentId: 'content-1',
  questions: [
    {
      id: 'q1',
      type: 'multipleChoice',
      difficulty: 'medium',
      sourceStepId: 'step-1',
      sourceContent: 'Test content',
      points: 10,
      question: 'What is 2 + 2?',
      options: ['3', '4', '5', '6'],
      correctAnswerIndex: 1,
    },
    {
      id: 'q2',
      type: 'trueFalse',
      difficulty: 'easy',
      sourceStepId: 'step-2',
      sourceContent: 'Test content 2',
      points: 5,
      statement: 'The sky is blue',
      correctAnswer: true,
    },
  ],
  estimatedDuration: 10,
  totalPoints: 15,
  difficulty: 'medium',
  isPublic: false,
  allowRetakes: true,
  showCorrectAnswers: true,
  shuffleQuestions: false,
  metadata: {
    generatedAt: '2024-01-01T00:00:00Z',
    aiModel: 'test-model',
    sourceStepsUsed: ['step-1', 'step-2'],
    difficultyDistribution: { medium: 1, easy: 1 },
    typeDistribution: { multipleChoice: 1, trueFalse: 1 },
  },
};

const mockAttempt: QuizAttempt = {
  id: 'attempt-1',
  quizId: 'quiz-1',
  userId: 'user-1',
  startedAt: '2024-01-01T00:00:00Z',
  completedAt: '2024-01-01T00:10:00Z',
  isCompleted: true,
  answers: [],
  totalTimeSpent: 600,
  score: {
    totalPoints: 15,
    earnedPoints: 12,
    percentage: 80,
    correctAnswers: 1,
    totalQuestions: 2,
  },
  questionResults: [
    {
      questionId: 'q1',
      isCorrect: false,
      pointsEarned: 0,
      feedback: 'Incorrect answer',
    },
    {
      questionId: 'q2',
      isCorrect: true,
      pointsEarned: 5,
      feedback: 'Correct!',
    },
  ],
};

const mockProgress: QuizProgress = {
  id: 'progress-1',
  attemptId: 'attempt-1',
  currentQuestionIndex: 0,
  questionsAnswered: 0,
  totalQuestions: 2,
  timeSpentSoFar: 0,
  lastActiveAt: '2024-01-01T00:00:00Z',
  currentAnswers: [],
  bookmarkedQuestions: [],
  questionNotes: {},
};

describe('QuizContainer', () => {
  const mockProps = {
    quiz: mockQuiz,
    onAnswerSubmit: jest.fn(),
    onQuizComplete: jest.fn(),
    onQuizExit: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Mock Date.now for consistent timing
    jest.spyOn(Date, 'now').mockReturnValue(1640995200000); // 2022-01-01T00:00:00Z
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('renders quiz container with initial question', () => {
    render(<QuizContainer {...mockProps} />);
    
    expect(screen.getByText('Test Quiz')).toBeInTheDocument();
    expect(screen.getByText('A test quiz for unit testing')).toBeInTheDocument();
    expect(screen.getByTestId('quiz-progress-bar')).toBeInTheDocument();
    expect(screen.getByTestId('question-renderer')).toBeInTheDocument();
    expect(screen.getByTestId('quiz-navigation')).toBeInTheDocument();
  });

  it('displays quiz metadata correctly', () => {
    render(<QuizContainer {...mockProps} />);
    
    expect(screen.getByText('10 min • 15 points')).toBeInTheDocument();
    expect(screen.getByText('Question 1 of 2')).toBeInTheDocument();
  });

  it('handles answer submission', async () => {
    render(<QuizContainer {...mockProps} />);
    
    const submitButton = screen.getByTestId('submit-answer');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(mockProps.onAnswerSubmit).toHaveBeenCalledWith('q1', 'test-answer', 30);
    });
  });

  it('navigates between questions', async () => {
    render(<QuizContainer {...mockProps} />);
    
    // Submit first answer to enable navigation
    const submitButton = screen.getByTestId('submit-answer');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      // Should auto-advance to next question after submission
      expect(screen.getByText('Progress: 2/2')).toBeInTheDocument();
    });
  });

  it('shows complete quiz button when all questions answered', async () => {
    render(<QuizContainer {...mockProps} />);
    
    // Answer first question
    const submitButton = screen.getByTestId('submit-answer');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      // Should be on second question now
      fireEvent.click(screen.getByTestId('submit-answer'));
    });
    
    await waitFor(() => {
      expect(screen.getByText('Complete Quiz')).toBeInTheDocument();
    });
  });

  it('handles quiz completion', async () => {
    render(<QuizContainer {...mockProps} />);
    
    // Answer both questions
    const submitButton = screen.getByTestId('submit-answer');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      fireEvent.click(screen.getByTestId('submit-answer'));
    });
    
    await waitFor(() => {
      const completeButton = screen.getByText('Complete Quiz');
      fireEvent.click(completeButton);
      expect(mockProps.onQuizComplete).toHaveBeenCalled();
    });
  });

  it('shows results when quiz is completed', () => {
    render(<QuizContainer {...mockProps} attempt={mockAttempt} />);
    
    expect(screen.getByTestId('quiz-results')).toBeInTheDocument();
    expect(screen.queryByTestId('question-renderer')).not.toBeInTheDocument();
  });

  it('handles quiz exit', () => {
    render(<QuizContainer {...mockProps} />);
    
    const exitButton = screen.getByText('Exit Quiz');
    fireEvent.click(exitButton);
    
    expect(mockProps.onQuizExit).toHaveBeenCalled();
  });

  it('tracks time spent on quiz', () => {
    jest.useFakeTimers();
    render(<QuizContainer {...mockProps} />);
    
    // Advance time by 30 seconds
    jest.advanceTimersByTime(30000);
    
    // Time tracking is internal, but we can verify it doesn't crash
    expect(screen.getByTestId('quiz-progress-bar')).toBeInTheDocument();
    
    jest.useRealTimers();
  });

  it('handles review mode correctly', () => {
    const reviewAttempt = { ...mockAttempt, isCompleted: true };
    render(<QuizContainer {...mockProps} attempt={reviewAttempt} />);
    
    // In review mode, should show results
    expect(screen.getByTestId('quiz-results')).toBeInTheDocument();
  });

  it('initializes with progress data', () => {
    const progressWithAnswers = {
      ...mockProgress,
      currentQuestionIndex: 1,
      questionsAnswered: 1,
      currentAnswers: [
        { questionId: 'q1', answer: 1, timeSpent: 30 }
      ],
    };
    
    render(<QuizContainer {...mockProps} progress={progressWithAnswers} />);
    
    // Should start on second question based on progress
    expect(screen.getByText('Progress: 2/2')).toBeInTheDocument();
  });

  it('handles bookmarked questions', () => {
    const progressWithBookmarks = {
      ...mockProgress,
      bookmarkedQuestions: ['q1'],
    };
    
    render(<QuizContainer {...mockProps} progress={progressWithBookmarks} />);
    
    // Component should render without errors
    expect(screen.getByTestId('quiz-navigation')).toBeInTheDocument();
  });
});
