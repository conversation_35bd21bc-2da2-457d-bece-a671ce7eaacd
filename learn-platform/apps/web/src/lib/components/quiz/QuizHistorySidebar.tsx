'use client';

import { useState, useMemo } from 'react';
import { cn } from '@learn-platform/shared-ui';
import { api } from '../../../lib/trpc';
import {
  FileQuestion,
  Clock,
  Trophy,
  TrendingUp,
  Calendar,
  ChevronRight,
  RotateCcw,
  Eye
} from 'lucide-react';
import type { QuizHistoryItem, QuizStats } from './types';

interface QuizHistorySidebarProps {
  learningContentId: string;
  onQuizSelect?: (quizId: string) => void;
  onRetakeQuiz?: (quizId: string) => void;
  onGenerateNew?: () => void;
  className?: string;
}



export function QuizHistorySidebar({
  learningContentId,
  onQuizSelect,
  onRetakeQuiz,
  onGenerateNew,
  className
}: QuizHistorySidebarProps) {
  const [expandedQuiz, setExpandedQuiz] = useState<string | null>(null);

  // Fetch quiz history for this specific learning content
  const { data: historyData, isLoading, error } = api.quiz.getHistoryByLearningContent.useQuery({
    learningContentId,
    limit: 20,
    offset: 0,
  });

  const quizHistory = historyData?.attempts || [];
  const stats = historyData?.stats || {
    totalAttempts: 0,
    completedAttempts: 0,
    averageScore: 0,
    averageTimeSpent: 0,
    bestScore: 0,
    lastAttemptDate: undefined,
  };



  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getScoreColor = (percentage: number) => {
    if (percentage >= 90) return 'text-green-600';
    if (percentage >= 80) return 'text-blue-600';
    if (percentage >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className={cn("bg-white dark:bg-gray-800 rounded-lg shadow-sm border dark:border-gray-700 p-6 space-y-6", className)}>
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Quiz History</h3>
          <button
            onClick={onGenerateNew}
            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium"
          >
            Generate New
          </button>
        </div>
        <div className="text-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 dark:border-blue-400 mx-auto mb-2"></div>
          <p className="text-gray-500 dark:text-gray-400 text-sm">Loading quiz history...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={cn("bg-white dark:bg-gray-800 rounded-lg shadow-sm border dark:border-gray-700 p-6 space-y-6", className)}>
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Quiz History</h3>
          <button
            onClick={onGenerateNew}
            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium"
          >
            Generate New
          </button>
        </div>
        <div className="text-center py-8">
          <p className="text-red-600 dark:text-red-400 text-sm">Failed to load quiz history</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("bg-white dark:bg-gray-800 rounded-lg shadow-sm border dark:border-gray-700 p-6 space-y-6", className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Quiz History</h3>
        <button
          onClick={onGenerateNew}
          className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium"
        >
          Generate New
        </button>
      </div>

      {/* Stats Overview */}
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Your Progress</h4>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <div className="text-2xl font-bold text-gray-900 dark:text-white">{stats.completedAttempts}</div>
            <div className="text-gray-600 dark:text-gray-400">Completed</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">{Math.round(stats.bestScore)}%</div>
            <div className="text-gray-600 dark:text-gray-400">Best Score</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-gray-900 dark:text-white">{Math.round(stats.averageScore)}%</div>
            <div className="text-gray-600 dark:text-gray-400">Average</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-gray-900 dark:text-white">{formatTime(stats.averageTimeSpent)}</div>
            <div className="text-gray-600 dark:text-gray-400">Avg Time</div>
          </div>
        </div>
      </div>

      {/* Quiz History List */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Recent Quizzes</h4>

        {quizHistory.length === 0 ? (
          <div className="text-center py-8">
            <FileQuestion className="h-12 w-12 text-gray-400 dark:text-gray-500 mx-auto mb-3" />
            <p className="text-gray-500 dark:text-gray-400 text-sm">No quizzes yet</p>
            <button
              onClick={onGenerateNew}
              className="mt-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium"
            >
              Generate your first quiz
            </button>
          </div>
        ) : (
          <div className="space-y-2">
            {quizHistory.map((quiz) => (
              <div
                key={quiz.id}
                className="border border-gray-200 dark:border-gray-600 rounded-lg p-3 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <h5 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {quiz.quizTitle}
                      </h5>
                      <span className={cn(
                        "px-2 py-1 text-xs rounded-full",
                        getDifficultyColor(quiz.difficulty)
                      )}>
                        {quiz.difficulty}
                      </span>
                    </div>

                    <div className="flex items-center space-x-4 mt-1 text-xs text-gray-500 dark:text-gray-400">
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3" />
                        <span>{formatDate(quiz.startedAt)}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="h-3 w-3" />
                        <span>{formatTime(quiz.totalTimeSpent)}</span>
                      </div>
                    </div>

                    {quiz.isCompleted && quiz.score && (
                      <div className="flex items-center space-x-4 mt-2">
                        <div className={cn("text-sm font-medium", getScoreColor(quiz.score.percentage))}>
                          {quiz.score.percentage}%
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {quiz.score.correctAnswers}/{quiz.score.totalQuestions} correct
                        </div>
                      </div>
                    )}

                    {!quiz.isCompleted && (
                      <div className="text-xs text-yellow-600 dark:text-yellow-400 mt-1">
                        In Progress
                      </div>
                    )}
                  </div>

                  <button
                    onClick={() => setExpandedQuiz(expandedQuiz === quiz.id ? null : quiz.id)}
                    className="text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                  >
                    <ChevronRight className={cn(
                      "h-4 w-4 transition-transform",
                      expandedQuiz === quiz.id && "rotate-90"
                    )} />
                  </button>
                </div>

                {/* Expanded Actions */}
                {expandedQuiz === quiz.id && (
                  <div className="mt-3 pt-3 border-t border-gray-100 dark:border-gray-600">
                    <div className="flex space-x-2">
                      {quiz.isCompleted ? (
                        <>
                          <button
                            onClick={() => onQuizSelect?.(quiz.quizId)}
                            className="flex items-center space-x-1 px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                          >
                            <Eye className="h-3 w-3" />
                            <span>Review</span>
                          </button>
                          <button
                            onClick={() => onRetakeQuiz?.(quiz.quizId)}
                            className="flex items-center space-x-1 px-3 py-1 text-xs bg-green-100 text-green-700 rounded hover:bg-green-200 transition-colors"
                          >
                            <RotateCcw className="h-3 w-3" />
                            <span>Retake</span>
                          </button>
                        </>
                      ) : (
                        <button
                          onClick={() => onQuizSelect?.(quiz.quizId)}
                          className="flex items-center space-x-1 px-3 py-1 text-xs bg-yellow-100 text-yellow-700 rounded hover:bg-yellow-200 transition-colors"
                        >
                          <span>Continue</span>
                        </button>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Performance Insights */}
      {stats.completedAttempts > 0 && (
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <TrendingUp className="h-4 w-4 text-blue-600 dark:text-blue-400" />
            <h4 className="text-sm font-medium text-blue-900 dark:text-blue-200">Insights</h4>
          </div>
          <div className="text-sm text-blue-800 dark:text-blue-300">
            {stats.averageScore >= 85 ? (
              <p>Excellent performance! You're mastering this content.</p>
            ) : stats.averageScore >= 70 ? (
              <p>Good progress! Consider reviewing challenging topics.</p>
            ) : (
              <p>Keep practicing! Focus on areas where you scored lower.</p>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
