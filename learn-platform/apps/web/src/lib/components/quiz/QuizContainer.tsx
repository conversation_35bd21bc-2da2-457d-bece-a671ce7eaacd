'use client';

import { useState, useEffect, useCallback } from 'react';
import { cn } from '@learn-platform/shared-ui';
import { QuizNavigation } from './QuizNavigation';
import { QuizProgressBar } from './QuizProgressBar';
import { QuizResults } from './QuizResults';
import { QuestionRenderer } from './QuestionRenderer';
import { QuizFeedbackModal } from './QuizFeedbackModal';
import type {
  Quiz,
  QuizAttempt,
  QuizProgress,
  QuizComponentProps,
  QuizAnswer,
  QuizFeedback
} from './types';

interface QuizContainerProps extends QuizComponentProps {
  onFeedbackSubmit?: (feedback: QuizFeedback) => void;
  onRetake?: () => void;
  isSubmittingAnswer?: boolean;
  isCompletingQuiz?: boolean;
  isRetaking?: boolean;
}

export function QuizContainer({
  quiz,
  attempt,
  progress,
  onAnswerSubmit,
  onQuizComplete,
  onQuizExit,
  onFeedbackSubmit,
  onRetake,
  isSubmittingAnswer = false,
  isCompletingQuiz = false,
  isRetaking = false,
  className
}: QuizContainerProps) {
  // State management
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(
    progress?.currentQuestionIndex || 0
  );
  const [userAnswers, setUserAnswers] = useState<Map<string, any>>(new Map());
  const [answeredQuestions, setAnsweredQuestions] = useState<Set<number>>(new Set());
  const [bookmarkedQuestions, setBookmarkedQuestions] = useState<Set<number>>(
    new Set(progress?.bookmarkedQuestions?.map(id =>
      quiz.questions.findIndex(q => q.id === id)
    ).filter(index => index !== -1) || [])
  );
  const [questionStartTime, setQuestionStartTime] = useState<number>(Date.now());
  const [totalTimeSpent, setTotalTimeSpent] = useState(progress?.timeSpentSoFar || 0);
  const [showResultsView, setShowResultsView] = useState(false);
  const [showFeedbackModal, setShowFeedbackModal] = useState(false);

  // Reset results view when attempt changes (important for retakes)
  useEffect(() => {
    if (attempt && !attempt.isCompleted) {
      setShowResultsView(false);
    }
  }, [attempt?.id, attempt?.isCompleted]);

  // Initialize answers from progress or attempt
  useEffect(() => {
    const initialAnswers = new Map<string, any>();
    const initialAnswered = new Set<number>();

    // Load from progress (current session)
    if (progress?.currentAnswers) {
      progress.currentAnswers.forEach(answer => {
        initialAnswers.set(answer.questionId, answer.answer);
        const questionIndex = quiz.questions.findIndex(q => q.id === answer.questionId);
        if (questionIndex !== -1) {
          initialAnswered.add(questionIndex);
        }
      });
    }

    // Load from completed attempt (review mode)
    if (attempt?.answers) {
      attempt.answers.forEach(answer => {
        initialAnswers.set(answer.questionId, answer.answer);
        const questionIndex = quiz.questions.findIndex(q => q.id === answer.questionId);
        if (questionIndex !== -1) {
          initialAnswered.add(questionIndex);
        }
      });
    }

    setUserAnswers(initialAnswers);
    setAnsweredQuestions(initialAnswered);
  }, [quiz.questions, progress, attempt]);

  // Timer effect
  useEffect(() => {
    if (attempt?.isCompleted) return;

    const interval = setInterval(() => {
      setTotalTimeSpent(prev => prev + 1);
    }, 1000);

    return () => clearInterval(interval);
  }, [attempt?.isCompleted]);

  // Navigation handlers
  const handleQuestionSelect = useCallback((index: number) => {
    if (index >= 0 && index < quiz.questions.length) {
      setCurrentQuestionIndex(index);
      setQuestionStartTime(Date.now());
    }
  }, [quiz.questions.length]);

  const handlePrevious = useCallback(() => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(prev => prev - 1);
      setQuestionStartTime(Date.now());
    }
  }, [currentQuestionIndex]);

  const handleNext = useCallback(() => {
    if (currentQuestionIndex < quiz.questions.length - 1) {
      setCurrentQuestionIndex(prev => prev + 1);
      setQuestionStartTime(Date.now());
    }
  }, [currentQuestionIndex, quiz.questions.length]);

  const handleBookmarkToggle = useCallback((index: number) => {
    setBookmarkedQuestions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
  }, []);

  // Answer handling
  const handleAnswerChange = useCallback((answer: any) => {
    const currentQuestion = quiz.questions[currentQuestionIndex];
    setUserAnswers(prev => new Map(prev.set(currentQuestion.id, answer)));
  }, [quiz.questions, currentQuestionIndex]);

  const handleAnswerSubmit = useCallback((answer: any, timeSpent: number) => {
    const currentQuestion = quiz.questions[currentQuestionIndex];

    // Update local state
    setUserAnswers(prev => new Map(prev.set(currentQuestion.id, answer)));
    setAnsweredQuestions(prev => new Set(prev.add(currentQuestionIndex)));

    // Submit to parent
    onAnswerSubmit(currentQuestion.id, answer, timeSpent);

    // Auto-advance to next question if not on last question
    if (currentQuestionIndex < quiz.questions.length - 1) {
      setTimeout(() => {
        handleNext();
      }, 500); // Small delay to show feedback
    }
  }, [quiz.questions, currentQuestionIndex, onAnswerSubmit, handleNext]);

  const handleQuizComplete = useCallback(() => {
    setShowResultsView(true);
    onQuizComplete();
  }, [onQuizComplete]);

  const handleFeedbackSubmit = useCallback((feedback: QuizFeedback) => {
    if (onFeedbackSubmit) {
      onFeedbackSubmit(feedback);
    }
    setShowFeedbackModal(false);
  }, [onFeedbackSubmit]);

  // Check if quiz is complete
  const isQuizComplete = answeredQuestions.size === quiz.questions.length;
  const canGoNext = currentQuestionIndex < quiz.questions.length - 1;
  const canGoPrevious = currentQuestionIndex > 0;

  // Current question
  const currentQuestion = quiz.questions[currentQuestionIndex];
  const currentAnswer = userAnswers.get(currentQuestion?.id);
  const isCurrentAnswered = answeredQuestions.has(currentQuestionIndex);

  // Review mode check
  const isReviewMode = attempt?.isCompleted || false;

  // Time limit check
  const timeRemaining = quiz.timeLimit ? (quiz.timeLimit * 60) - totalTimeSpent : undefined;
  const isTimeUp = timeRemaining !== undefined && timeRemaining <= 0;

  // Auto-complete if time is up
  useEffect(() => {
    if (isTimeUp && !attempt?.isCompleted) {
      handleQuizComplete();
    }
  }, [isTimeUp, attempt?.isCompleted, handleQuizComplete]);

  // Show results if user explicitly wants to see results or if just completed
  if (showResultsView && attempt?.score) {


    return (
      <div className={cn("max-w-4xl mx-auto p-6", className)}>
        <QuizResults
          quiz={quiz}
          attempt={attempt}
          score={attempt.score}
          questionResults={attempt.questionResults || []}
          onRetake={quiz.allowRetakes && onRetake ? onRetake : undefined}
          onReview={() => setShowResultsView(false)}
          onExit={onQuizExit}
          isRetaking={isRetaking}
        />

        {onFeedbackSubmit && (
          <div className="mt-6 text-center">
            <button
              onClick={() => setShowFeedbackModal(true)}
              className="text-blue-600 hover:text-blue-800 underline"
            >
              Provide Feedback
            </button>
          </div>
        )}

        <QuizFeedbackModal
          isOpen={showFeedbackModal}
          onClose={() => setShowFeedbackModal(false)}
          onSubmit={handleFeedbackSubmit}
          quiz={quiz}
          attempt={attempt}
          isSubmitting={false}
        />
      </div>
    );
  }

  return (
    <div className={cn("max-w-4xl mx-auto p-6", className)}>
      {/* Progress Bar */}
      <QuizProgressBar
        currentQuestion={currentQuestionIndex + 1}
        totalQuestions={quiz.questions.length}
        answeredQuestions={answeredQuestions.size}
        timeSpent={totalTimeSpent}
        timeLimit={quiz.timeLimit}
        className="mb-6"
      />

      {/* Quiz Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border dark:border-gray-700 p-6 mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">{quiz.title}</h1>
        <p className="text-gray-600 dark:text-gray-300 mb-4">{quiz.description}</p>

        <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
          <span>Question {currentQuestionIndex + 1} of {quiz.questions.length}</span>
          <span>
            {quiz.estimatedDuration} min • {quiz.totalPoints} points
          </span>
        </div>
      </div>

      {/* Question Content */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border dark:border-gray-700 p-6 mb-6">
        {currentQuestion && (
          <QuestionRenderer
            question={currentQuestion}
            userAnswer={currentAnswer}
            isAnswered={isCurrentAnswered}
            isReviewMode={isReviewMode}
            showCorrectAnswer={isCurrentAnswered || (isReviewMode && quiz.showCorrectAnswers)}
            onAnswerChange={handleAnswerChange}
            onAnswerSubmit={handleAnswerSubmit}
          />
        )}
      </div>

      {/* Navigation */}
      <QuizNavigation
        currentQuestionIndex={currentQuestionIndex}
        totalQuestions={quiz.questions.length}
        answeredQuestions={answeredQuestions}
        bookmarkedQuestions={bookmarkedQuestions}
        onQuestionSelect={handleQuestionSelect}
        onPrevious={handlePrevious}
        onNext={handleNext}
        onBookmarkToggle={handleBookmarkToggle}
        canGoNext={canGoNext}
        canGoPrevious={canGoPrevious}
        className="mb-6"
      />

      {/* Complete Quiz Button or Review Results Button */}
      {isQuizComplete && !attempt?.isCompleted && (
        <div className="text-center">
          <button
            onClick={handleQuizComplete}
            disabled={isCompletingQuiz}
            className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed text-white px-8 py-3 rounded-lg font-medium transition-colors"
          >
            {isCompletingQuiz ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline-block"></div>
                Completing Quiz...
              </>
            ) : (
              'Complete Quiz'
            )}
          </button>
        </div>
      )}

      {/* Review Results Button for completed quizzes */}
      {attempt?.isCompleted && attempt?.score && !showResultsView && (
        <div className="text-center">
          <button
            onClick={() => setShowResultsView(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-medium transition-colors"
          >
            Review Results
          </button>
        </div>
      )}

      {/* Exit Button */}
      <div className="text-center mt-4">
        <button
          onClick={onQuizExit}
          className="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 underline"
        >
          Exit Quiz
        </button>
      </div>
    </div>
  );
}
