'use client';

import { useState, useEffect } from 'react';
import { cn } from '@learn-platform/shared-ui';
import { CheckCircle, XCircle, AlertCircle, Send, Lightbulb, Eye, EyeOff } from 'lucide-react';
import type { QuestionComponentProps, FillInBlankQuestion as FillInBlankQuestionType } from '../types';

type FillInBlankQuestionProps = QuestionComponentProps<FillInBlankQuestionType>

export function FillInBlankQuestion({
  question,
  userAnswer,
  isAnswered,
  isReviewMode,
  showCorrectAnswer,
  onAnswerChange,
  onAnswerSubmit,
  className
}: FillInBlankQuestionProps) {
  const [answers, setAnswers] = useState<string[]>(
    Array.isArray(userAnswer) ? userAnswer : new Array(question.blanks.length).fill('')
  );
  const [startTime] = useState(Date.now());
  const [hasSubmitted, setHasSubmitted] = useState(isAnswered);
  const [showHint, setShowHint] = useState(false);

  // Update answers when userAnswer changes
  useEffect(() => {
    if (Array.isArray(userAnswer)) {
      setAnswers(userAnswer);
    }
  }, [userAnswer]);

  const handleAnswerChange = (index: number, value: string) => {
    if (isReviewMode || hasSubmitted) return;
    
    const newAnswers = [...answers];
    newAnswers[index] = value;
    setAnswers(newAnswers);
    onAnswerChange(newAnswers);
  };

  const handleSubmit = () => {
    if (hasSubmitted || isReviewMode) return;
    
    const timeSpent = Math.round((Date.now() - startTime) / 1000);
    setHasSubmitted(true);
    onAnswerSubmit(answers, timeSpent);
  };

  // Check if answer is correct for a specific blank
  const isBlankCorrect = (index: number) => {
    if (!hasSubmitted && !showCorrectAnswer) return null;
    
    const blank = question.blanks[index];
    const userAnswer = answers[index]?.trim() || '';
    
    if (blank.caseSensitive) {
      if (userAnswer === blank.correctAnswer) return true;
      if (blank.acceptableAnswers?.includes(userAnswer)) return true;
    } else {
      if (userAnswer.toLowerCase() === blank.correctAnswer.toLowerCase()) return true;
      if (blank.acceptableAnswers?.some(answer => 
        answer.toLowerCase() === userAnswer.toLowerCase()
      )) return true;
    }
    
    return false;
  };

  // Parse text and render with input fields
  const renderTextWithBlanks = () => {
    const parts = question.text.split('_____');
    const elements: React.ReactNode[] = [];
    
    parts.forEach((part, index) => {
      // Add text part
      if (part) {
        elements.push(
          <span key={`text-${index}`} className="text-gray-900 dark:text-gray-100">
            {part}
          </span>
        );
      }
      
      // Add input field if not the last part
      if (index < parts.length - 1) {
        const blankIndex = index;
        const isCorrect = isBlankCorrect(blankIndex);
        
        elements.push(
          <span key={`blank-${index}`} className="inline-block mx-1">
            <input
              type="text"
              value={answers[blankIndex] || ''}
              onChange={(e) => handleAnswerChange(blankIndex, e.target.value)}
              disabled={isReviewMode || hasSubmitted}
              placeholder={`Blank ${blankIndex + 1}`}
              className={cn(
                "inline-block px-3 py-1 border-b-2 bg-transparent text-center font-medium",
                "focus:outline-none focus:border-blue-500 dark:focus:border-blue-400 transition-colors",
                "min-w-[100px] max-w-[200px]",
                "text-gray-900 dark:text-gray-100",
                isCorrect === true ? "border-green-500 dark:border-green-400 text-green-700 dark:text-green-300" :
                isCorrect === false ? "border-red-500 dark:border-red-400 text-red-700 dark:text-red-300" :
                hasSubmitted || isReviewMode ? "border-gray-300 dark:border-gray-600" :
                "border-gray-400 dark:border-gray-500 hover:border-gray-500 dark:hover:border-gray-400"
              )}
            />
            {(hasSubmitted || showCorrectAnswer) && (
              <div className="inline-block ml-2">
                {isCorrect ? (
                  <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-500 dark:text-red-400" />
                )}
              </div>
            )}
          </span>
        );
      }
    });
    
    return elements;
  };

  // Calculate score
  const correctBlanks = question.blanks.filter((_, index) => isBlankCorrect(index)).length;
  const totalBlanks = question.blanks.length;
  const allCorrect = correctBlanks === totalBlanks;
  const canSubmit = answers.some(answer => answer.trim() !== '') && !hasSubmitted && !isReviewMode;

  return (
    <div className={cn("space-y-6", className)}>
      {/* Question */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Fill in the Blanks
        </h2>
        
        {/* Question metadata */}
        <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mb-6">
          <span>Fill in the Blank</span>
          <span>•</span>
          <span>{question.points} point{question.points !== 1 ? 's' : ''}</span>
          <span>•</span>
          <span className="capitalize">{question.difficulty}</span>
          <span>•</span>
          <span>{totalBlanks} blank{totalBlanks !== 1 ? 's' : ''}</span>
        </div>
      </div>

      {/* Text with blanks */}
      <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-6">
        <div className="text-lg leading-relaxed">
          {renderTextWithBlanks()}
        </div>
      </div>

      {/* Hint Section */}
      {question.hint && (
        <div className="text-center">
          <button
            onClick={() => setShowHint(!showHint)}
            className="flex items-center space-x-2 mx-auto px-3 py-2 text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors"
          >
            {showHint ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            <span>{showHint ? 'Hide Hint' : 'Show Hint'}</span>
          </button>
          
          {showHint && (
            <div className="mt-3 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg">
              <div className="flex items-start space-x-2">
                <Lightbulb className="h-5 w-5 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-yellow-800 dark:text-yellow-200">
                  {question.hint}
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Submit button */}
      {!isReviewMode && !hasSubmitted && (
        <div className="flex justify-center">
          <button
            onClick={handleSubmit}
            disabled={!canSubmit}
            className={cn(
              "flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-colors",
              canSubmit
                ? "bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white"
                : "bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed"
            )}
          >
            <Send className="h-4 w-4" />
            <span>Submit Answers</span>
          </button>
        </div>
      )}

      {/* Feedback */}
      {(hasSubmitted || showCorrectAnswer) && (
        <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
          {allCorrect ? (
            <div className="flex items-start space-x-3">
              <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0" />
              <div>
                <div className="font-medium text-green-800 dark:text-green-200 mb-1">Excellent!</div>
                <div className="text-sm text-green-700 dark:text-green-300">
                  All blanks filled correctly ({correctBlanks}/{totalBlanks})
                </div>
              </div>
            </div>
          ) : correctBlanks > 0 ? (
            <div className="flex items-start space-x-3">
              <AlertCircle className="h-6 w-6 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0" />
              <div>
                <div className="font-medium text-yellow-800 dark:text-yellow-200 mb-1">Partially Correct</div>
                <div className="text-sm text-yellow-700 dark:text-yellow-300 mb-3">
                  {correctBlanks} out of {totalBlanks} blanks correct
                </div>
                <div className="space-y-2">
                  {question.blanks.map((blank, index) => {
                    const isCorrect = isBlankCorrect(index);
                    if (isCorrect) return null;
                    
                    return (
                      <div key={index} className="text-sm text-gray-700 dark:text-gray-300">
                        <span className="font-medium">Blank {index + 1}:</span> {blank.correctAnswer}
                        {blank.acceptableAnswers && blank.acceptableAnswers.length > 0 && (
                          <span className="text-gray-600 dark:text-gray-400">
                            {' '}(also accepts: {blank.acceptableAnswers.join(', ')})
                          </span>
                        )}
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          ) : (
            <div className="flex items-start space-x-3">
              <XCircle className="h-6 w-6 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
              <div>
                <div className="font-medium text-red-800 dark:text-red-200 mb-1">Incorrect</div>
                <div className="text-sm text-red-700 dark:text-red-300 mb-3">
                  None of the blanks were filled correctly
                </div>
                <div className="space-y-2">
                  {question.blanks.map((blank, index) => (
                    <div key={index} className="text-sm text-gray-700 dark:text-gray-300">
                      <span className="font-medium">Blank {index + 1}:</span> {blank.correctAnswer}
                      {blank.acceptableAnswers && blank.acceptableAnswers.length > 0 && (
                        <span className="text-gray-600 dark:text-gray-400">
                          {' '}(also accepts: {blank.acceptableAnswers.join(', ')})
                        </span>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Instructions */}
      {!hasSubmitted && !isReviewMode && (
        <div className="text-center text-sm text-gray-500 dark:text-gray-400">
          <p>Fill in the blanks with the appropriate words or phrases</p>
          {question.blanks.some(blank => !blank.caseSensitive) && (
            <p className="mt-1">Note: Answers are not case-sensitive</p>
          )}
        </div>
      )}

      {/* Review mode indicator */}
      {isReviewMode && (
        <div className="flex items-center justify-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
          <AlertCircle className="h-4 w-4" />
          <span>Review Mode - Answers cannot be changed</span>
        </div>
      )}
    </div>
  );
}
