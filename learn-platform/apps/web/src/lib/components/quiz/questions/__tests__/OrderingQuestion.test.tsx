/**
 * @jest-environment jsdom
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { jest } from '@jest/globals';
import { OrderingQuestion } from '../OrderingQuestion';
import type { OrderingQuestion as OrderingQuestionType } from '../../types';

// Mock the shared UI utilities
jest.mock('@learn-platform/shared-ui', () => ({
  cn: (...classes: any[]) => classes.filter(Boolean).join(' '),
}));

// Mock Lucide React icons
jest.mock('lucide-react', () => ({
  CheckCircle: () => <div data-testid="check-circle-icon" />,
  XCircle: () => <div data-testid="x-circle-icon" />,
  AlertCircle: () => <div data-testid="alert-circle-icon" />,
  Send: () => <div data-testid="send-icon" />,
  GripVertical: () => <div data-testid="grip-vertical-icon" />,
  ArrowUp: () => <div data-testid="arrow-up-icon" />,
  ArrowDown: () => <div data-testid="arrow-down-icon" />,
}));

const mockOrderingQuestion1: OrderingQuestionType = {
  id: 'ordering-1',
  type: 'ordering',
  difficulty: 'medium',
  sourceStepId: 'step-1',
  sourceContent: 'Test content',
  points: 10,
  instruction: 'Arrange the following steps in chronological order:',
  items: ['First step', 'Second step', 'Third step'],
  correctOrder: [0, 1, 2],
  orderType: 'chronological',
};

const mockOrderingQuestion2: OrderingQuestionType = {
  id: 'ordering-2',
  type: 'ordering',
  difficulty: 'medium',
  sourceStepId: 'step-2',
  sourceContent: 'Different test content',
  points: 15,
  instruction: 'Arrange the following items in logical order:',
  items: ['Alpha', 'Beta', 'Gamma', 'Delta'],
  correctOrder: [0, 1, 2, 3],
  orderType: 'logical',
};

const defaultProps = {
  question: mockOrderingQuestion1,
  userAnswer: undefined,
  isAnswered: false,
  isReviewMode: false,
  showCorrectAnswer: false,
  onAnswerChange: jest.fn(),
  onAnswerSubmit: jest.fn(),
};

describe('OrderingQuestion', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock console.log to avoid test output noise
    jest.spyOn(console, 'log').mockImplementation(() => {});
    // Mock Math.random for consistent shuffling in tests
    jest.spyOn(Math, 'random').mockReturnValue(0.5);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('renders ordering question with shuffled items initially', () => {
    render(<OrderingQuestion {...defaultProps} />);

    expect(screen.getByText('Ordering')).toBeInTheDocument();
    expect(screen.getByText('Arrange the following steps in chronological order:')).toBeInTheDocument();
    expect(screen.getByText('First step')).toBeInTheDocument();
    expect(screen.getByText('Second step')).toBeInTheDocument();
    expect(screen.getByText('Third step')).toBeInTheDocument();
    expect(screen.getByText('Submit Order')).toBeInTheDocument();
  });

  it('updates content when question changes (bug fix test)', async () => {
    const { rerender } = render(<OrderingQuestion {...defaultProps} />);

    // Initially shows first question items
    expect(screen.getByText('First step')).toBeInTheDocument();
    expect(screen.getByText('Second step')).toBeInTheDocument();
    expect(screen.getByText('Third step')).toBeInTheDocument();
    expect(screen.queryByText('Alpha')).not.toBeInTheDocument();

    // Change to second question
    rerender(<OrderingQuestion {...defaultProps} question={mockOrderingQuestion2} />);

    // Should now show second question items
    await waitFor(() => {
      expect(screen.getByText('Alpha')).toBeInTheDocument();
      expect(screen.getByText('Beta')).toBeInTheDocument();
      expect(screen.getByText('Gamma')).toBeInTheDocument();
      expect(screen.getByText('Delta')).toBeInTheDocument();
    });

    // Should not show first question items anymore
    expect(screen.queryByText('First step')).not.toBeInTheDocument();
    expect(screen.queryByText('Second step')).not.toBeInTheDocument();
    expect(screen.queryByText('Third step')).not.toBeInTheDocument();
  });

  it('resets submission state when question changes', async () => {
    const { rerender } = render(<OrderingQuestion {...defaultProps} />);

    // Submit the first question
    fireEvent.click(screen.getByText('Submit Order'));

    await waitFor(() => {
      expect(defaultProps.onAnswerSubmit).toHaveBeenCalled();
    });

    // Submit button should be gone after submission
    expect(screen.queryByText('Submit Order')).not.toBeInTheDocument();

    // Change to second question
    rerender(<OrderingQuestion {...defaultProps} question={mockOrderingQuestion2} />);

    // Submit button should be available again for new question
    await waitFor(() => {
      expect(screen.getByText('Submit Order')).toBeInTheDocument();
    });
  });

  it('handles user answer correctly when provided', () => {
    const userAnswer = [2, 0, 1]; // Third, First, Second
    render(<OrderingQuestion {...defaultProps} userAnswer={userAnswer} />);

    // Should display all items from the question
    expect(screen.getByText('Third step')).toBeInTheDocument();
    expect(screen.getByText('First step')).toBeInTheDocument();
    expect(screen.getByText('Second step')).toBeInTheDocument();

    // The component should render without errors when userAnswer is provided
    expect(screen.getByText('Submit Order')).toBeInTheDocument();
  });

  it('shows correct feedback when submitted with correct order', async () => {
    const userAnswer = [0, 1, 2]; // Correct order
    render(<OrderingQuestion
      {...defaultProps}
      userAnswer={userAnswer}
      isAnswered={true}
      showCorrectAnswer={true}
    />);

    expect(screen.getByText('Correct!')).toBeInTheDocument();
    expect(screen.getByText('Items are arranged in the correct order')).toBeInTheDocument();
  });

  it('shows incorrect feedback with correct order when submitted incorrectly', async () => {
    const userAnswer = [2, 1, 0]; // Incorrect order
    render(<OrderingQuestion
      {...defaultProps}
      userAnswer={userAnswer}
      isAnswered={true}
      showCorrectAnswer={true}
    />);

    expect(screen.getByText('Incorrect')).toBeInTheDocument();
    expect(screen.getByText('The items are not in the correct order')).toBeInTheDocument();
    expect(screen.getByText('Correct order:')).toBeInTheDocument();
  });

  it('disables interaction in review mode', () => {
    render(<OrderingQuestion {...defaultProps} isReviewMode={true} />);

    expect(screen.getByText('Review Mode - Order cannot be changed')).toBeInTheDocument();
    expect(screen.queryByText('Submit Order')).not.toBeInTheDocument();

    // Drag handles should not be present
    expect(screen.queryByTestId('grip-vertical-icon')).not.toBeInTheDocument();
  });

  it('handles move up/down buttons correctly', () => {
    render(<OrderingQuestion {...defaultProps} />);

    const upButtons = screen.getAllByTestId('arrow-up-icon');
    const downButtons = screen.getAllByTestId('arrow-down-icon');

    // Click move down on first item
    fireEvent.click(downButtons[0]);

    expect(defaultProps.onAnswerChange).toHaveBeenCalled();
  });
});
