'use client';

import { useState, useEffect } from 'react';
import { cn } from '@learn-platform/shared-ui';
import { CheckCircle, XCircle, AlertCircle, Send } from 'lucide-react';
import type { QuestionComponentProps, TrueFalseQuestion as TrueFalseQuestionType } from '../types';

interface TrueFalseQuestionProps extends QuestionComponentProps<TrueFalseQuestionType> {}

export function TrueFalseQuestion({
  question,
  userAnswer,
  isAnswered,
  isReviewMode,
  showCorrectAnswer,
  onAnswerChange,
  onAnswerSubmit,
  className
}: TrueFalseQuestionProps) {
  const [selectedAnswer, setSelectedAnswer] = useState<boolean | null>(
    typeof userAnswer === 'boolean' ? userAnswer : null
  );
  const [startTime] = useState(Date.now());
  const [hasSubmitted, setHasSubmitted] = useState(isAnswered);

  // Update selected answer when userAnswer changes
  useEffect(() => {
    if (typeof userAnswer === 'boolean') {
      setSelectedAnswer(userAnswer);
    }
  }, [userAnswer]);

  const handleAnswerSelect = (answer: boolean) => {
    if (isReviewMode || hasSubmitted) return;
    
    setSelectedAnswer(answer);
    onAnswerChange(answer);
  };

  const handleSubmit = () => {
    if (selectedAnswer === null || hasSubmitted || isReviewMode) return;
    
    const timeSpent = Math.round((Date.now() - startTime) / 1000);
    setHasSubmitted(true);
    onAnswerSubmit(selectedAnswer, timeSpent);
  };

  const getButtonStatus = (buttonValue: boolean) => {
    if (!showCorrectAnswer && !hasSubmitted) {
      return selectedAnswer === buttonValue ? 'selected' : 'default';
    }

    const isCorrect = buttonValue === question.correctAnswer;
    const isSelected = selectedAnswer === buttonValue;

    if (showCorrectAnswer || hasSubmitted) {
      if (isCorrect) return 'correct';
      if (isSelected && !isCorrect) return 'incorrect';
      return 'default';
    }

    return selectedAnswer === buttonValue ? 'selected' : 'default';
  };

  const getButtonStyles = (status: string) => {
    switch (status) {
      case 'correct':
        return 'bg-green-100 dark:bg-green-900/30 border-green-300 dark:border-green-600 text-green-800 dark:text-green-200 shadow-sm';
      case 'incorrect':
        return 'bg-red-100 dark:bg-red-900/30 border-red-300 dark:border-red-600 text-red-800 dark:text-red-200 shadow-sm';
      case 'selected':
        return 'bg-blue-100 dark:bg-blue-900/30 border-blue-300 dark:border-blue-600 text-blue-800 dark:text-blue-200 shadow-sm';
      default:
        return 'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:shadow-sm';
    }
  };

  const getButtonIcon = (status: string) => {
    switch (status) {
      case 'correct':
        return <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />;
      case 'incorrect':
        return <XCircle className="h-6 w-6 text-red-600 dark:text-red-400" />;
      case 'selected':
        return <div className="h-6 w-6 bg-blue-600 dark:bg-blue-500 rounded-full flex items-center justify-center">
          <div className="h-3 w-3 bg-white rounded-full" />
        </div>;
      default:
        return <div className="h-6 w-6 border-2 border-gray-300 dark:border-gray-600 rounded-full" />;
    }
  };

  const canSubmit = selectedAnswer !== null && !hasSubmitted && !isReviewMode;
  const isCorrect = selectedAnswer === question.correctAnswer;

  return (
    <div className={cn("space-y-6", className)}>
      {/* Question */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
          True or False
        </h2>
        
        {/* Question metadata */}
        <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mb-6">
          <span>True/False</span>
          <span>•</span>
          <span>{question.points} point{question.points !== 1 ? 's' : ''}</span>
          <span>•</span>
          <span className="capitalize">{question.difficulty}</span>
        </div>

        {/* Statement */}
        <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-6 mb-6">
          <p className="text-lg text-gray-900 dark:text-gray-100 leading-relaxed">
            {question.statement}
          </p>
        </div>
      </div>

      {/* Answer Options */}
      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        {/* True Button */}
        <button
          onClick={() => handleAnswerSelect(true)}
          disabled={isReviewMode || hasSubmitted}
          className={cn(
            "flex items-center justify-center space-x-3 px-8 py-6 rounded-lg border-2 transition-all duration-200",
            "min-w-[200px] text-lg font-medium",
            getButtonStyles(getButtonStatus(true)),
            (isReviewMode || hasSubmitted) ? "cursor-default" : "cursor-pointer"
          )}
        >
          {getButtonIcon(getButtonStatus(true))}
          <span>True</span>
        </button>

        {/* False Button */}
        <button
          onClick={() => handleAnswerSelect(false)}
          disabled={isReviewMode || hasSubmitted}
          className={cn(
            "flex items-center justify-center space-x-3 px-8 py-6 rounded-lg border-2 transition-all duration-200",
            "min-w-[200px] text-lg font-medium",
            getButtonStyles(getButtonStatus(false)),
            (isReviewMode || hasSubmitted) ? "cursor-default" : "cursor-pointer"
          )}
        >
          {getButtonIcon(getButtonStatus(false))}
          <span>False</span>
        </button>
      </div>

      {/* Submit button */}
      {!isReviewMode && !hasSubmitted && (
        <div className="flex justify-center">
          <button
            onClick={handleSubmit}
            disabled={!canSubmit}
            className={cn(
              "flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-colors",
              canSubmit
                ? "bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white"
                : "bg-gray-300 dark:bg-gray-600 text-gray-500 dark:text-gray-400 cursor-not-allowed"
            )}
          >
            <Send className="h-4 w-4" />
            <span>Submit Answer</span>
          </button>
        </div>
      )}

      {/* Feedback */}
      {(hasSubmitted || showCorrectAnswer) && selectedAnswer !== null && (
        <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
          {isCorrect ? (
            <div className="flex items-start space-x-3">
              <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0" />
              <div>
                <div className="font-medium text-green-800 dark:text-green-200 mb-1">Correct!</div>
                <div className="text-sm text-green-700 dark:text-green-300 mb-2">
                  The statement is <strong>{question.correctAnswer ? 'True' : 'False'}</strong>
                </div>
                {question.explanation && (
                  <div className="text-sm text-green-700 dark:text-green-300">
                    {question.explanation}
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="flex items-start space-x-3">
              <XCircle className="h-6 w-6 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
              <div>
                <div className="font-medium text-red-800 dark:text-red-200 mb-1">Incorrect</div>
                <div className="text-sm text-red-700 dark:text-red-300 mb-2">
                  The correct answer is <strong>{question.correctAnswer ? 'True' : 'False'}</strong>
                </div>
                {question.explanation && (
                  <div className="text-sm text-red-700 dark:text-red-300">
                    {question.explanation}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Instructions */}
      {!hasSubmitted && !isReviewMode && (
        <div className="text-center text-sm text-gray-500 dark:text-gray-400">
          <p>Read the statement carefully and select True or False</p>
        </div>
      )}

      {/* Review mode indicator */}
      {isReviewMode && (
        <div className="flex items-center justify-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
          <AlertCircle className="h-4 w-4" />
          <span>Review Mode - Answer cannot be changed</span>
        </div>
      )}
    </div>
  );
}
