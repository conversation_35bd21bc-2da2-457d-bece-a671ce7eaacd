import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { TrueFalseQuestion } from '../TrueFalseQuestion';
import type { TrueFalseQuestion as TrueFalseQuestionType } from '../../types';

// Mock question data
const mockQuestion: TrueFalseQuestionType = {
  id: 'q1',
  type: 'trueFalse',
  difficulty: 'medium',
  sourceStepId: 'step-1',
  sourceContent: 'Test content about deductive inference',
  points: 5,
  statement: 'Deductive inference involves making broad generalizations from specific observations.',
  correctAnswer: false,
  explanation: 'Deductive inference is about drawing specific conclusions from general information, not making broad generalizations.',
};

const defaultProps = {
  question: mockQuestion,
  userAnswer: undefined,
  isAnswered: false,
  isReviewMode: false,
  showCorrectAnswer: false,
  onAnswerChange: jest.fn(),
  onAnswerSubmit: jest.fn(),
  className: '',
};

describe('TrueFalseQuestion', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders question statement and options', () => {
    render(<TrueFalseQuestion {...defaultProps} />);

    expect(screen.getByText('True or False')).toBeInTheDocument();
    expect(screen.getByText(mockQuestion.statement)).toBeInTheDocument();
    expect(screen.getByText('True')).toBeInTheDocument();
    expect(screen.getByText('False')).toBeInTheDocument();
    expect(screen.getByText('Submit Answer')).toBeInTheDocument();
  });

  it('handles answer selection', () => {
    render(<TrueFalseQuestion {...defaultProps} />);

    const trueButton = screen.getByText('True');
    fireEvent.click(trueButton);

    expect(defaultProps.onAnswerChange).toHaveBeenCalledWith(true);
  });

  it('submits answer with correct time tracking', async () => {
    jest.useFakeTimers();
    render(<TrueFalseQuestion {...defaultProps} />);

    // Select an answer
    const falseButton = screen.getByText('False');
    fireEvent.click(falseButton);

    // Advance time by 30 seconds
    jest.advanceTimersByTime(30000);

    // Submit the answer
    const submitButton = screen.getByText('Submit Answer');
    fireEvent.click(submitButton);

    expect(defaultProps.onAnswerSubmit).toHaveBeenCalledWith(false, 30);
    jest.useRealTimers();
  });

  it('shows correct feedback for wrong answer when answered', async () => {
    render(<TrueFalseQuestion {...defaultProps} isAnswered={true} userAnswer={true} showCorrectAnswer={true} />);

    // Should show incorrect feedback since user answered true but correct answer is false
    expect(screen.getByText('Incorrect')).toBeInTheDocument();
    expect(screen.getByText(/The correct answer is/)).toBeInTheDocument();
    expect(screen.getByText(mockQuestion.explanation!)).toBeInTheDocument();
  });

  it('shows correct feedback for right answer when answered', async () => {
    render(<TrueFalseQuestion {...defaultProps} isAnswered={true} userAnswer={false} showCorrectAnswer={true} />);

    // Should show correct feedback since user answered false and correct answer is false
    expect(screen.getByText('Correct!')).toBeInTheDocument();
    expect(screen.getByText(/The statement is/)).toBeInTheDocument();
    expect(screen.getByText(mockQuestion.explanation!)).toBeInTheDocument();
  });

  it('shows button styling for correct/incorrect answers when showCorrectAnswer is true', () => {
    render(<TrueFalseQuestion {...defaultProps} isAnswered={true} userAnswer={true} showCorrectAnswer={true} />);

    const buttons = screen.getAllByRole('button');
    const trueButton = buttons.find(button => button.textContent?.includes('True'));
    const falseButton = buttons.find(button => button.textContent?.includes('False'));

    // True button should have incorrect styling (red) since user selected it but it's wrong
    expect(trueButton).toHaveClass('bg-red-100', 'border-red-300', 'text-red-800');

    // False button should have correct styling (green) since it's the correct answer
    expect(falseButton).toHaveClass('bg-green-100', 'border-green-300', 'text-green-800');
  });

  it('disables interaction in review mode', () => {
    render(<TrueFalseQuestion {...defaultProps} isReviewMode={true} />);

    const trueButton = screen.getByText('True').closest('button');
    const falseButton = screen.getByText('False').closest('button');

    expect(trueButton).toBeDisabled();
    expect(falseButton).toBeDisabled();
    expect(screen.getByText('Review Mode - Answer cannot be changed')).toBeInTheDocument();
    expect(screen.queryByText('Submit Answer')).not.toBeInTheDocument();
  });

  it('prevents submission when no answer is selected', () => {
    render(<TrueFalseQuestion {...defaultProps} />);

    const submitButton = screen.getByRole('button', { name: /submit answer/i });
    expect(submitButton).toBeDisabled();
  });

  it('prevents resubmission after answer is submitted', async () => {
    render(<TrueFalseQuestion {...defaultProps} />);

    // Select and submit an answer
    const trueButton = screen.getByText('True');
    fireEvent.click(trueButton);

    const submitButton = screen.getByText('Submit Answer');
    fireEvent.click(submitButton);

    // Submit button should disappear after submission
    await waitFor(() => {
      expect(screen.queryByText('Submit Answer')).not.toBeInTheDocument();
    });
  });

  it('shows feedback immediately after submission when showCorrectAnswer is true', async () => {
    render(<TrueFalseQuestion {...defaultProps} showCorrectAnswer={true} />);

    // Select wrong answer
    const trueButton = screen.getByText('True');
    fireEvent.click(trueButton);

    // Submit the answer
    const submitButton = screen.getByText('Submit Answer');
    fireEvent.click(submitButton);

    // Should show feedback immediately
    await waitFor(() => {
      expect(screen.getByText('Incorrect')).toBeInTheDocument();
      expect(screen.getByText(/The correct answer is/)).toBeInTheDocument();
    });
  });
});
