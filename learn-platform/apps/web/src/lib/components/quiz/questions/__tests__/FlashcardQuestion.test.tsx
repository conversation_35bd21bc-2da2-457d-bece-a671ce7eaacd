/**
 * @jest-environment jsdom
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { jest } from '@jest/globals';
import { FlashcardQuestion } from '../FlashcardQuestion';
import type { FlashcardQuestion as FlashcardQuestionType } from '../../types';

// Mock the shared UI utilities
jest.mock('@learn-platform/shared-ui', () => ({
  cn: (...classes: any[]) => classes.filter(Boolean).join(' '),
}));

// Mock Lucide React icons
jest.mock('lucide-react', () => ({
  RotateCcw: () => <div data-testid="rotate-icon" />,
  Eye: () => <div data-testid="eye-icon" />,
  EyeOff: () => <div data-testid="eye-off-icon" />,
  CheckCircle: () => <div data-testid="check-circle-icon" />,
  XCircle: () => <div data-testid="x-circle-icon" />,
  Lightbulb: () => <div data-testid="lightbulb-icon" />,
}));

const mockFlashcardQuestion: FlashcardQuestionType = {
  id: 'flashcard-1',
  type: 'flashcard',
  difficulty: 'medium',
  sourceStepId: 'step-1',
  sourceContent: 'Test content',
  points: 10,
  front: 'What is React?',
  back: 'A JavaScript library for building user interfaces',
  hint: 'It starts with R and is used for web development',
};

const defaultProps = {
  question: mockFlashcardQuestion,
  userAnswer: null,
  isAnswered: false,
  isReviewMode: false,
  showCorrectAnswer: false,
  onAnswerChange: jest.fn(),
  onAnswerSubmit: jest.fn(),
};

describe('FlashcardQuestion', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock console.log to avoid test output noise
    jest.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('renders flashcard with front content initially', () => {
    render(<FlashcardQuestion {...defaultProps} />);

    expect(screen.getByText('Flashcard')).toBeInTheDocument();
    expect(screen.getByText('What is React?')).toBeInTheDocument();
    expect(screen.getByText('Click to flip')).toBeInTheDocument();
    expect(screen.queryByText('A JavaScript library for building user interfaces')).not.toBeInTheDocument();
  });

  it('flips to show back content when clicked', async () => {
    render(<FlashcardQuestion {...defaultProps} />);

    // Find the card container and click it
    const cardContainer = screen.getByText('What is React?').closest('[style*="transform"]');
    expect(cardContainer).toBeInTheDocument();

    fireEvent.click(cardContainer!);

    // Wait for the flip animation and state update
    await waitFor(() => {
      expect(screen.getByText('A JavaScript library for building user interfaces')).toBeInTheDocument();
    });

    // The "Click to flip" text should be hidden when flipped
    expect(screen.queryByText('Click to flip')).not.toBeInTheDocument();
  });

  it('shows self-assessment options after flipping', async () => {
    render(<FlashcardQuestion {...defaultProps} />);

    // Click to flip the card
    const cardContainer = screen.getByText('What is React?').closest('[style*="transform"]');
    fireEvent.click(cardContainer!);

    await waitFor(() => {
      expect(screen.getByText('How did you do?')).toBeInTheDocument();
      expect(screen.getByText('I got it right')).toBeInTheDocument();
      expect(screen.getByText('I need to study this')).toBeInTheDocument();
    });
  });

  it('handles self-assessment correctly', async () => {
    render(<FlashcardQuestion {...defaultProps} />);

    // Flip the card first
    const cardContainer = screen.getByText('What is React?').closest('[style*="transform"]');
    fireEvent.click(cardContainer!);

    await waitFor(() => {
      expect(screen.getByText('I got it right')).toBeInTheDocument();
    });

    // Click "I got it right"
    fireEvent.click(screen.getByText('I got it right'));

    expect(defaultProps.onAnswerChange).toHaveBeenCalledWith('correct');
    expect(defaultProps.onAnswerSubmit).toHaveBeenCalledWith('correct', expect.any(Number));
  });

  it('shows hint when hint button is clicked', async () => {
    render(<FlashcardQuestion {...defaultProps} />);

    // Click show hint button
    fireEvent.click(screen.getByText('Show Hint'));

    await waitFor(() => {
      expect(screen.getByText('It starts with R and is used for web development')).toBeInTheDocument();
      expect(screen.getByText('Hide Hint')).toBeInTheDocument();
    });
  });

  it('auto-flips in review mode', () => {
    render(<FlashcardQuestion {...defaultProps} isReviewMode={true} userAnswer="correct" />);

    // Should show the back content immediately in review mode
    expect(screen.getByText('A JavaScript library for building user interfaces')).toBeInTheDocument();
    expect(screen.getByText('You marked this as correct')).toBeInTheDocument();
  });

  it('prevents flipping in review mode', () => {
    render(<FlashcardQuestion {...defaultProps} isReviewMode={true} />);

    // Try to click the card in review mode
    const cardContainer = screen.getByText('What is React?').closest('[style*="transform"]');
    fireEvent.click(cardContainer!);

    // Should not show self-assessment options in review mode
    expect(screen.queryByText('How did you do?')).not.toBeInTheDocument();
  });

  it('shows reset button after assessment', async () => {
    render(<FlashcardQuestion {...defaultProps} />);

    // Flip and assess
    const cardContainer = screen.getByText('What is React?').closest('[style*="transform"]');
    fireEvent.click(cardContainer!);

    await waitFor(() => {
      fireEvent.click(screen.getByText('I got it right'));
    });

    expect(screen.getByText('Reset and try again')).toBeInTheDocument();
  });

  it('resets card state when reset button is clicked', async () => {
    render(<FlashcardQuestion {...defaultProps} />);

    // Flip, assess, then reset
    const cardContainer = screen.getByText('What is React?').closest('[style*="transform"]');
    fireEvent.click(cardContainer!);

    await waitFor(() => {
      fireEvent.click(screen.getByText('I got it right'));
    });

    fireEvent.click(screen.getByText('Reset and try again'));

    // Should be back to initial state
    expect(screen.getByText('Click to flip')).toBeInTheDocument();
    expect(screen.queryByText('How did you do?')).not.toBeInTheDocument();
    expect(defaultProps.onAnswerChange).toHaveBeenLastCalledWith(null);
  });
});
