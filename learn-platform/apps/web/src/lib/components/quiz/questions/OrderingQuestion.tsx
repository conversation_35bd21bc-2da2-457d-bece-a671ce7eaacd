'use client';

import { useState, useEffect } from 'react';
import { cn } from '@learn-platform/shared-ui';
import { CheckCircle, XCircle, AlertCircle, Send, GripVertical, ArrowUp, ArrowDown } from 'lucide-react';
import type { QuestionComponentProps, OrderingQuestion as OrderingQuestionType } from '../types';

type OrderingQuestionProps = QuestionComponentProps<OrderingQuestionType>

export function OrderingQuestion({
  question,
  userAnswer,
  isAnswered,
  isReviewMode,
  showCorrectAnswer,
  onAnswerChange,
  onAnswerSubmit,
  className
}: OrderingQuestionProps) {
  const [orderedItems, setOrderedItems] = useState<string[]>(() => {
    if (Array.isArray(userAnswer)) {
      // userAnswer contains indices, convert to items
      return userAnswer.map(index => question.items[index]);
    }
    // Start with shuffled items
    return [...question.items].sort(() => Math.random() - 0.5);
  });
  const [startTime, setStartTime] = useState(Date.now());
  const [hasSubmitted, setHasSubmitted] = useState(isAnswered);
  const [currentQuestionId, setCurrentQuestionId] = useState(question.id);
  
  // Enhanced drag and drop state
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);
  const [dropPosition, setDropPosition] = useState<'above' | 'below' | null>(null);
  const [animatingIndex, setAnimatingIndex] = useState<number | null>(null);
  const [animationDirection, setAnimationDirection] = useState<'up' | 'down' | null>(null);

  // Reset component state when question changes or userAnswer updates
  useEffect(() => {
    // Check if this is a new question
    const isNewQuestion = currentQuestionId !== question.id;

    if (isNewQuestion) {
      // Reset state for new question
      setCurrentQuestionId(question.id);
      setStartTime(Date.now());
      setHasSubmitted(isAnswered);
      setDraggedIndex(null);
      setDragOverIndex(null);
      setDropPosition(null);
      setAnimatingIndex(null);
      setAnimationDirection(null);
    }

    // Update ordered items based on userAnswer or reset for new question
    if (Array.isArray(userAnswer)) {
      const items = userAnswer.map(index => question.items[index]);
      setOrderedItems(items);
    } else if (isNewQuestion) {
      // For new questions without userAnswer, start with shuffled items
      setOrderedItems([...question.items].sort(() => Math.random() - 0.5));
    }
  }, [question.id, question.items, userAnswer, isAnswered, currentQuestionId]);

  const handleDragStart = (e: React.DragEvent, index: number) => {
    if (isReviewMode || hasSubmitted) return;

    setDraggedIndex(index);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/plain', index.toString());
  };

  const handleDragEnd = () => {
    // Reset drag state
    setDraggedIndex(null);
    setDragOverIndex(null);
    setDropPosition(null);
  };

  const handleDragOver = (e: React.DragEvent, index: number) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    
    if (draggedIndex === null || draggedIndex === index) return;
    
    // Calculate drop position based on mouse position
    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
    const midpoint = rect.top + rect.height / 2;
    const mouseY = e.clientY;
    
    const position = mouseY < midpoint ? 'above' : 'below';
    
    setDragOverIndex(index);
    setDropPosition(position);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    // Only clear if we're leaving the container entirely
    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
    const mouseX = e.clientX;
    const mouseY = e.clientY;
    
    if (mouseX < rect.left || mouseX > rect.right || mouseY < rect.top || mouseY > rect.bottom) {
      setDragOverIndex(null);
      setDropPosition(null);
    }
  };

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();

    if (draggedIndex === null || isReviewMode || hasSubmitted) return;

    // Calculate the actual drop position
    let actualDropIndex = dropIndex;
    if (dropPosition === 'below') {
      actualDropIndex = dropIndex + 1;
    }
    
    // Adjust for the removed item
    if (draggedIndex < actualDropIndex) {
      actualDropIndex -= 1;
    }

    const newItems = [...orderedItems];
    const draggedItem = newItems[draggedIndex];

    // Determine animation direction and set animation on destination
    const direction = draggedIndex < actualDropIndex ? 'down' : 'up';
    setAnimatingIndex(actualDropIndex); // Changed: animate destination instead of origin
    setAnimationDirection(direction);

    // Remove dragged item
    newItems.splice(draggedIndex, 1);

    // Insert at new position
    newItems.splice(actualDropIndex, 0, draggedItem);

    setOrderedItems(newItems);

    // Convert back to indices for answer
    const indices = newItems.map(item => question.items.indexOf(item));
    onAnswerChange(indices);

    // Reset drag state
    setDraggedIndex(null);
    setDragOverIndex(null);
    setDropPosition(null);
    
    // Clear animation after a short delay
    setTimeout(() => {
      setAnimatingIndex(null);
      setAnimationDirection(null);
    }, 300);
  };

  const moveItem = (fromIndex: number, direction: 'up' | 'down') => {
    if (isReviewMode || hasSubmitted) return;

    const toIndex = direction === 'up' ? fromIndex - 1 : fromIndex + 1;
    if (toIndex < 0 || toIndex >= orderedItems.length) return;

    // Set animation state on destination
    setAnimatingIndex(toIndex); // Changed: animate destination instead of origin
    setAnimationDirection(direction);

    const newItems = [...orderedItems];
    [newItems[fromIndex], newItems[toIndex]] = [newItems[toIndex], newItems[fromIndex]];

    setOrderedItems(newItems);

    // Convert back to indices for answer
    const indices = newItems.map(item => question.items.indexOf(item));
    onAnswerChange(indices);
    
    // Clear animation after a short delay
    setTimeout(() => {
      setAnimatingIndex(null);
      setAnimationDirection(null);
    }, 300);
  };

  const handleSubmit = () => {
    if (hasSubmitted || isReviewMode) return;

    const timeSpent = Math.round((Date.now() - startTime) / 1000);
    const indices = orderedItems.map(item => question.items.indexOf(item));
    setHasSubmitted(true);
    onAnswerSubmit(indices, timeSpent);
  };

  // Check if the order is correct
  const isOrderCorrect = () => {
    if (!hasSubmitted && !showCorrectAnswer) return null;

    const userIndices = orderedItems.map(item => question.items.indexOf(item));
    return JSON.stringify(userIndices) === JSON.stringify(question.correctOrder);
  };

  // Get the correct order for display
  const getCorrectOrder = () => {
    return question.correctOrder.map(index => question.items[index]);
  };

  const isCorrect = isOrderCorrect();
  const canSubmit = !hasSubmitted && !isReviewMode;

  return (
    <div className={cn("space-y-6", className)}>
      {/* Question */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Ordering
        </h2>

        {/* Question metadata */}
        <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mb-6">
          <span>Ordering/Sequencing</span>
          <span>•</span>
          <span>{question.points} point{question.points !== 1 ? 's' : ''}</span>
          <span>•</span>
          <span className="capitalize">{question.difficulty}</span>
          <span>•</span>
          <span className="capitalize">{question.orderType}</span>
        </div>

        {/* Instructions */}
        <div className="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-4 mb-6">
          <p className="text-blue-800 dark:text-blue-200 text-sm">
            {question.instruction}
          </p>
        </div>
      </div>

      {/* Ordering Interface */}
      <div className="max-w-2xl mx-auto">
        <div className="space-y-3">
          {orderedItems.map((item, index) => {
            const isAnimating = animatingIndex === index;
            const animDirection = animationDirection;
            const isDragged = draggedIndex === index;
            const isDraggedOver = dragOverIndex === index;
            const showDropZoneAbove = isDraggedOver && dropPosition === 'above' && !isDragged;
            const showDropZoneBelow = isDraggedOver && dropPosition === 'below' && !isDragged;
            
            return (
              <div key={`${item}-${index}`} className="relative">
                {/* Drop zone indicator above */}
                {showDropZoneAbove && (
                  <div className="absolute -top-2 left-0 right-0 h-1 bg-blue-500 rounded-full z-10 animate-pulse">
                    <div className="absolute -top-1 left-1/2 transform -translate-x-1/2">
                      <div className="w-3 h-3 bg-blue-500 rounded-full flex items-center justify-center">
                        <ArrowUp className="h-2 w-2 text-white" />
                      </div>
                    </div>
                  </div>
                )}
                
                <div
                  draggable={!isReviewMode && !hasSubmitted}
                  onDragStart={(e) => handleDragStart(e, index)}
                  onDragEnd={handleDragEnd}
                  onDragOver={(e) => handleDragOver(e, index)}
                  onDragLeave={handleDragLeave}
                  onDrop={(e) => handleDrop(e, index)}
                  className={cn(
                    "flex items-center space-x-3 p-4 rounded-lg border-2 transition-all duration-300 ease-in-out relative",
                    isDragged ? "opacity-50 scale-105 shadow-lg ring-2 ring-blue-400 ring-opacity-50" : "opacity-100",
                    isDraggedOver && !isDragged ? "bg-blue-50 border-blue-300 dark:bg-blue-900/20 dark:border-blue-600" : 
                    isReviewMode || hasSubmitted ? "bg-gray-50 border-gray-300 dark:bg-gray-800 dark:border-gray-600" :
                    "bg-white border-gray-300 hover:border-gray-400 cursor-move dark:bg-gray-800 dark:border-gray-600 dark:hover:border-gray-500",
                    // Animation classes
                    isAnimating && animDirection === 'up' && "animate-bounce-up",
                    isAnimating && animDirection === 'down' && "animate-bounce-down",
                    isAnimating && "ring-2 ring-blue-400 ring-opacity-50 shadow-lg scale-105"
                  )}
                  style={{
                    transform: isAnimating 
                      ? animDirection === 'up' 
                        ? 'translateY(-8px)' 
                        : 'translateY(8px)'
                      : 'translateY(0px)',
                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
                  }}
                >
                  {/* Drag handle */}
                  {!isReviewMode && !hasSubmitted && (
                    <div className="flex-shrink-0">
                      <GripVertical className={cn(
                        "h-5 w-5 text-gray-400 dark:text-gray-500 transition-colors duration-200",
                        isDragged && "text-blue-500 dark:text-blue-400",
                        isAnimating && "text-blue-500 dark:text-blue-400"
                      )} />
                    </div>
                  )}

                  {/* Order number */}
                  <div className={cn(
                    "flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-all duration-300",
                    isReviewMode || hasSubmitted ? "bg-gray-200 text-gray-600 dark:bg-gray-600 dark:text-gray-300" :
                    "bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-300",
                    (isAnimating || isDragged) && "bg-blue-500 text-white dark:bg-blue-400 scale-110 shadow-md"
                  )}>
                    {index + 1}
                  </div>

                  {/* Item content */}
                  <div className={cn(
                    "flex-1 text-gray-900 dark:text-gray-100 transition-all duration-300",
                    (isAnimating || isDragged) && "font-medium text-blue-900 dark:text-blue-100"
                  )}>
                    {item}
                  </div>

                  {/* Move buttons */}
                  {!isReviewMode && !hasSubmitted && (
                    <div className="flex flex-col space-y-1">
                      <button
                        onClick={() => moveItem(index, 'up')}
                        disabled={index === 0}
                        className={cn(
                          "p-1 rounded transition-all duration-200",
                          index === 0 ? "text-gray-300 cursor-not-allowed dark:text-gray-600" :
                          "text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-700",
                          isAnimating && animDirection === 'up' && "bg-blue-100 text-blue-600 dark:bg-blue-900/50 dark:text-blue-400 scale-110"
                        )}
                      >
                        <ArrowUp className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => moveItem(index, 'down')}
                        disabled={index === orderedItems.length - 1}
                        className={cn(
                          "p-1 rounded transition-all duration-200",
                          index === orderedItems.length - 1 ? "text-gray-300 cursor-not-allowed dark:text-gray-600" :
                          "text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-700",
                          isAnimating && animDirection === 'down' && "bg-blue-100 text-blue-600 dark:bg-blue-900/50 dark:text-blue-400 scale-110"
                        )}
                      >
                        <ArrowDown className="h-4 w-4" />
                      </button>
                    </div>
                  )}
                  
                  {/* Visual indicator for items being pushed */}
                  {isDraggedOver && !isDragged && (
                    <div className="absolute inset-0 pointer-events-none">
                      <div className={cn(
                        "absolute left-2 w-1 bg-blue-500 rounded-full transition-all duration-200",
                        dropPosition === 'above' ? "top-0 h-1/2" : "bottom-0 h-1/2"
                      )} />
                    </div>
                  )}
                </div>
                
                {/* Drop zone indicator below */}
                {showDropZoneBelow && (
                  <div className="absolute -bottom-2 left-0 right-0 h-1 bg-blue-500 rounded-full z-10 animate-pulse">
                    <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2">
                      <div className="w-3 h-3 bg-blue-500 rounded-full flex items-center justify-center">
                        <ArrowDown className="h-2 w-2 text-white" />
                      </div>
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Submit button */}
      {!isReviewMode && !hasSubmitted && (
        <div className="flex justify-center">
          <button
            onClick={handleSubmit}
            disabled={!canSubmit}
            className={cn(
              "flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-colors",
              canSubmit
                ? "bg-blue-600 hover:bg-blue-700 text-white dark:bg-blue-600 dark:hover:bg-blue-700"
                : "bg-gray-300 text-gray-500 cursor-not-allowed dark:bg-gray-600 dark:text-gray-400"
            )}
          >
            <Send className="h-4 w-4" />
            <span>Submit Order</span>
          </button>
        </div>
      )}

      {/* Feedback */}
      {(hasSubmitted || showCorrectAnswer) && (
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          {isCorrect ? (
            <div className="flex items-start space-x-3">
              <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0" />
              <div>
                <div className="font-medium text-green-800 dark:text-green-200 mb-1">Correct!</div>
                <div className="text-sm text-green-700 dark:text-green-300">
                  Items are arranged in the correct order
                </div>
              </div>
            </div>
          ) : (
            <div className="flex items-start space-x-3">
              <XCircle className="h-6 w-6 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
              <div>
                <div className="font-medium text-red-800 dark:text-red-200 mb-1">Incorrect</div>
                <div className="text-sm text-red-700 dark:text-red-300 mb-3">
                  The items are not in the correct order
                </div>
                <div>
                  <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Correct order:</div>
                  <div className="space-y-1">
                    {getCorrectOrder().map((item, index) => (
                      <div key={index} className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                        <span className="w-6 h-6 bg-green-100 text-green-700 rounded-full flex items-center justify-center text-xs font-medium dark:bg-green-900/50 dark:text-green-300">
                          {index + 1}
                        </span>
                        <span>{item}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Instructions */}
      {!hasSubmitted && !isReviewMode && (
        <div className="text-center text-sm text-gray-500 dark:text-gray-400">
          <p>Drag items to reorder them, or use the arrow buttons to move items up and down</p>
        </div>
      )}

      {/* Review mode indicator */}
      {isReviewMode && (
        <div className="flex items-center justify-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
          <AlertCircle className="h-4 w-4" />
          <span>Review Mode - Order cannot be changed</span>
        </div>
      )}
    </div>
  );
}
