'use client';

import { useState, useEffect } from 'react';
import { cn } from '@learn-platform/shared-ui';
import { CheckCircle, XCircle, AlertCircle, Send, GripVertical, ArrowRight } from 'lucide-react';
import type { QuestionComponentProps, MatchingQuestion as MatchingQuestionType } from '../types';

interface MatchingQuestionProps extends QuestionComponentProps<MatchingQuestionType> {}

interface MatchPair {
  left: string;
  right: string;
}

export function MatchingQuestion({
  question,
  userAnswer,
  isAnswered,
  isReviewMode,
  showCorrectAnswer,
  onAnswerChange,
  onAnswerSubmit,
  className
}: MatchingQuestionProps) {
  const [matches, setMatches] = useState<MatchPair[]>(
    Array.isArray(userAnswer) ? userAnswer : []
  );
  const [startTime] = useState(Date.now());
  const [hasSubmitted, setHasSubmitted] = useState(isAnswered);
  const [draggedItem, setDraggedItem] = useState<{ type: 'left' | 'right'; item: string } | null>(null);
  const [dropZone, setDropZone] = useState<string | null>(null);

  // Shuffle right items for display
  const [shuffledRightItems] = useState(() => {
    const items = [...question.pairs.map(p => p.right)];
    return items.sort(() => Math.random() - 0.5);
  });

  // Update matches when userAnswer changes
  useEffect(() => {
    if (Array.isArray(userAnswer)) {
      setMatches(userAnswer);
    }
  }, [userAnswer]);

  const handleDragStart = (e: React.DragEvent, type: 'left' | 'right', item: string) => {
    if (isReviewMode || hasSubmitted) return;
    
    setDraggedItem({ type, item });
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDragEnter = (e: React.DragEvent, leftItem: string) => {
    e.preventDefault();
    setDropZone(leftItem);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDropZone(null);
  };

  const handleDrop = (e: React.DragEvent, leftItem: string) => {
    e.preventDefault();
    setDropZone(null);
    
    if (!draggedItem || draggedItem.type !== 'right' || isReviewMode || hasSubmitted) return;

    const newMatches = matches.filter(m => m.left !== leftItem);
    newMatches.push({ left: leftItem, right: draggedItem.item });
    
    setMatches(newMatches);
    onAnswerChange(newMatches);
    setDraggedItem(null);
  };

  const handleRemoveMatch = (leftItem: string) => {
    if (isReviewMode || hasSubmitted) return;
    
    const newMatches = matches.filter(m => m.left !== leftItem);
    setMatches(newMatches);
    onAnswerChange(newMatches);
  };

  const handleSubmit = () => {
    if (hasSubmitted || isReviewMode || matches.length === 0) return;
    
    const timeSpent = Math.round((Date.now() - startTime) / 1000);
    setHasSubmitted(true);
    onAnswerSubmit(matches, timeSpent);
  };

  // Check if a match is correct
  const isMatchCorrect = (leftItem: string, rightItem: string) => {
    if (!hasSubmitted && !showCorrectAnswer) return null;
    
    const correctPair = question.pairs.find(p => p.left === leftItem);
    return correctPair?.right === rightItem;
  };

  // Get the user's match for a left item
  const getUserMatch = (leftItem: string) => {
    return matches.find(m => m.left === leftItem)?.right;
  };

  // Get available right items (not yet matched)
  const getAvailableRightItems = () => {
    const matchedRightItems = matches.map(m => m.right);
    return shuffledRightItems.filter(item => !matchedRightItems.includes(item));
  };

  const correctMatches = matches.filter(match => 
    isMatchCorrect(match.left, match.right)
  ).length;
  const totalPairs = question.pairs.length;
  const allCorrect = correctMatches === totalPairs && matches.length === totalPairs;
  const canSubmit = matches.length > 0 && !hasSubmitted && !isReviewMode;

  return (
    <div className={cn("space-y-6", className)}>
      {/* Question */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Matching
        </h2>
        
        {/* Question metadata */}
        <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mb-6">
          <span>Matching Pairs</span>
          <span>•</span>
          <span>{question.points} point{question.points !== 1 ? 's' : ''}</span>
          <span>•</span>
          <span className="capitalize">{question.difficulty}</span>
          <span>•</span>
          <span>{totalPairs} pair{totalPairs !== 1 ? 's' : ''}</span>
        </div>

        {/* Instructions */}
        <div className="bg-blue-50 dark:bg-blue-900/30 rounded-lg p-4 mb-6">
          <p className="text-blue-800 dark:text-blue-200 text-sm">
            {question.instruction}
          </p>
        </div>
      </div>

      {/* Matching Interface */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Items to match */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Items</h3>
          {question.pairs.map((pair, index) => {
            const userMatch = getUserMatch(pair.left);
            const isCorrect = userMatch ? isMatchCorrect(pair.left, userMatch) : null;
            
            return (
              <div
                key={index}
                className={cn(
                  "p-4 rounded-lg border-2 transition-all duration-200",
                  dropZone === pair.left ? "border-blue-400 bg-blue-50 dark:border-blue-500 dark:bg-blue-900/30" :
                  isCorrect === true ? "border-green-300 bg-green-50 dark:border-green-600 dark:bg-green-900/30" :
                  isCorrect === false ? "border-red-300 bg-red-50 dark:border-red-600 dark:bg-red-900/30" :
                  userMatch ? "border-blue-300 bg-blue-50 dark:border-blue-600 dark:bg-blue-900/30" :
                  "border-gray-300 bg-white dark:border-gray-600 dark:bg-gray-800"
                )}
                onDragOver={handleDragOver}
                onDragEnter={(e) => handleDragEnter(e, pair.left)}
                onDragLeave={handleDragLeave}
                onDrop={(e) => handleDrop(e, pair.left)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="font-medium text-gray-900 dark:text-gray-100 mb-1">
                      {pair.left}
                    </div>
                    {userMatch && (
                      <div className="flex items-center space-x-2">
                        <ArrowRight className="h-4 w-4 text-gray-400 dark:text-gray-500" />
                        <span className={cn(
                          "text-sm",
                          isCorrect === true ? "text-green-700 dark:text-green-300" :
                          isCorrect === false ? "text-red-700 dark:text-red-300" :
                          "text-blue-700 dark:text-blue-300"
                        )}>
                          {userMatch}
                        </span>
                        {isCorrect !== null && (
                          <div>
                            {isCorrect ? (
                              <CheckCircle className="h-4 w-4 text-green-500 dark:text-green-400" />
                            ) : (
                              <XCircle className="h-4 w-4 text-red-500 dark:text-red-400" />
                            )}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                  
                  {userMatch && !isReviewMode && !hasSubmitted && (
                    <button
                      onClick={() => handleRemoveMatch(pair.left)}
                      className="text-gray-400 hover:text-red-500 dark:text-gray-500 dark:hover:text-red-400 transition-colors"
                    >
                      <XCircle className="h-4 w-4" />
                    </button>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Center Column - Visual separator */}
        <div className="hidden lg:flex items-center justify-center">
          <div className="text-gray-400 dark:text-gray-500">
            <ArrowRight className="h-8 w-8" />
          </div>
        </div>

        {/* Right Column - Available options */}
        <div className="space-y-3">
          <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Options</h3>
          {getAvailableRightItems().map((item, index) => (
            <div
              key={index}
              draggable={!isReviewMode && !hasSubmitted}
              onDragStart={(e) => handleDragStart(e, 'right', item)}
              className={cn(
                "p-4 rounded-lg border-2 transition-all duration-200",
                "bg-gray-50 border-gray-300 text-gray-900 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-100",
                !isReviewMode && !hasSubmitted && "cursor-move hover:bg-gray-100 hover:border-gray-400 dark:hover:bg-gray-600 dark:hover:border-gray-500"
              )}
            >
              <div className="flex items-center space-x-2">
                {!isReviewMode && !hasSubmitted && (
                  <GripVertical className="h-4 w-4 text-gray-400 dark:text-gray-500" />
                )}
                <span>{item}</span>
              </div>
            </div>
          ))}
          
          {/* Show correct answers in review mode */}
          {(hasSubmitted || showCorrectAnswer) && (
            <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Correct Matches:</h4>
              <div className="space-y-2">
                {question.pairs.map((pair, index) => (
                  <div key={index} className="flex items-center space-x-2 text-sm">
                    <span className="text-gray-900 dark:text-gray-100">{pair.left}</span>
                    <ArrowRight className="h-3 w-3 text-gray-400 dark:text-gray-500" />
                    <span className="text-gray-700 dark:text-gray-300">{pair.right}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Submit button */}
      {!isReviewMode && !hasSubmitted && (
        <div className="flex justify-center">
          <button
            onClick={handleSubmit}
            disabled={!canSubmit}
            className={cn(
              "flex items-center space-x-2 px-6 py-3 rounded-lg font-medium transition-colors",
              canSubmit
                ? "bg-blue-600 hover:bg-blue-700 text-white dark:bg-blue-600 dark:hover:bg-blue-700"
                : "bg-gray-300 text-gray-500 cursor-not-allowed dark:bg-gray-600 dark:text-gray-400"
            )}
          >
            <Send className="h-4 w-4" />
            <span>Submit Matches</span>
          </button>
        </div>
      )}

      {/* Feedback */}
      {(hasSubmitted || showCorrectAnswer) && (
        <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
          {allCorrect ? (
            <div className="flex items-start space-x-3">
              <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400 mt-0.5 flex-shrink-0" />
              <div>
                <div className="font-medium text-green-800 dark:text-green-200 mb-1">Perfect!</div>
                <div className="text-sm text-green-700 dark:text-green-300">
                  All pairs matched correctly ({correctMatches}/{totalPairs})
                </div>
              </div>
            </div>
          ) : correctMatches > 0 ? (
            <div className="flex items-start space-x-3">
              <AlertCircle className="h-6 w-6 text-yellow-600 dark:text-yellow-400 mt-0.5 flex-shrink-0" />
              <div>
                <div className="font-medium text-yellow-800 dark:text-yellow-200 mb-1">Partially Correct</div>
                <div className="text-sm text-yellow-700 dark:text-yellow-300">
                  {correctMatches} out of {totalPairs} pairs matched correctly
                </div>
              </div>
            </div>
          ) : (
            <div className="flex items-start space-x-3">
              <XCircle className="h-6 w-6 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
              <div>
                <div className="font-medium text-red-800 dark:text-red-200 mb-1">Incorrect</div>
                <div className="text-sm text-red-700 dark:text-red-300">
                  None of the pairs were matched correctly
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Instructions */}
      {!hasSubmitted && !isReviewMode && (
        <div className="text-center text-sm text-gray-500 dark:text-gray-400">
          <p>Drag items from the right column to match them with items on the left</p>
        </div>
      )}

      {/* Review mode indicator */}
      {isReviewMode && (
        <div className="flex items-center justify-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
          <AlertCircle className="h-4 w-4" />
          <span>Review Mode - Matches cannot be changed</span>
        </div>
      )}
    </div>
  );
}
