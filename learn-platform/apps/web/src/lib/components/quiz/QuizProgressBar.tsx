'use client';

import { cn } from '@learn-platform/shared-ui';
import { Clock, CheckCircle, Circle } from 'lucide-react';
import type { QuizProgressBarProps } from './types';

export function QuizProgressBar({
  currentQuestion,
  totalQuestions,
  answeredQuestions,
  timeSpent,
  timeLimit,
  className
}: QuizProgressBarProps) {
  // Calculate progress percentage
  const progressPercentage = (answeredQuestions / totalQuestions) * 100;
  const currentPercentage = (currentQuestion / totalQuestions) * 100;

  // Format time
  const formatTime = (seconds: number): string => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  // Calculate time remaining if there's a time limit
  const timeRemaining = timeLimit ? (timeLimit * 60) - timeSpent : undefined;
  const isTimeWarning = timeRemaining !== undefined && timeRemaining <= 300; // 5 minutes
  const isTimeCritical = timeRemaining !== undefined && timeRemaining <= 60; // 1 minute

  return (
    <div className={cn("bg-white dark:bg-gray-800 rounded-lg shadow-sm border dark:border-gray-700 p-4", className)}>
      {/* Header with stats */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-5 w-5 text-green-500 dark:text-green-400" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {answeredQuestions} of {totalQuestions} answered
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <Circle className="h-5 w-5 text-blue-500 dark:text-blue-400" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Question {currentQuestion}
            </span>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          {/* Time spent */}
          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4 text-gray-500 dark:text-gray-400" />
            <span className="text-sm text-gray-600 dark:text-gray-300">
              {formatTime(timeSpent)}
            </span>
          </div>

          {/* Time remaining (if time limit exists) */}
          {timeRemaining !== undefined && (
            <div className={cn(
              "flex items-center space-x-2 px-2 py-1 rounded text-sm font-medium",
              isTimeCritical 
                ? "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400" 
                : isTimeWarning 
                ? "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-400" 
                : "bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-400"
            )}>
              <Clock className="h-4 w-4" />
              <span>
                {timeRemaining > 0 ? formatTime(timeRemaining) : "Time's up!"}
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Progress bars */}
      <div className="space-y-3">
        {/* Overall progress */}
        <div>
          <div className="flex items-center justify-between mb-1">
            <span className="text-xs font-medium text-gray-600 dark:text-gray-400">Overall Progress</span>
            <span className="text-xs text-gray-500 dark:text-gray-400">{Math.round(progressPercentage)}%</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
            <div
              className="bg-green-500 dark:bg-green-400 h-2 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
        </div>

        {/* Current position indicator */}
        <div>
          <div className="flex items-center justify-between mb-1">
            <span className="text-xs font-medium text-gray-600 dark:text-gray-400">Current Position</span>
            <span className="text-xs text-gray-500 dark:text-gray-400">Question {currentQuestion}</span>
          </div>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 relative">
            {/* Background progress */}
            <div
              className="bg-blue-200 dark:bg-blue-600/50 h-2 rounded-full transition-all duration-300 ease-out"
              style={{ width: `${currentPercentage}%` }}
            />
            {/* Current position marker */}
            <div
              className="absolute top-0 h-2 w-1 bg-blue-600 dark:bg-blue-400 rounded-full transition-all duration-300 ease-out"
              style={{ left: `${Math.max(0, currentPercentage - 0.5)}%` }}
            />
          </div>
        </div>

        {/* Time progress (if time limit exists) */}
        {timeLimit && (
          <div>
            <div className="flex items-center justify-between mb-1">
              <span className="text-xs font-medium text-gray-600 dark:text-gray-400">Time Progress</span>
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {Math.round((timeSpent / (timeLimit * 60)) * 100)}%
              </span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div
                className={cn(
                  "h-2 rounded-full transition-all duration-300 ease-out",
                  isTimeCritical 
                    ? "bg-red-500 dark:bg-red-400" 
                    : isTimeWarning 
                    ? "bg-yellow-500 dark:bg-yellow-400" 
                    : "bg-blue-500 dark:bg-blue-400"
                )}
                style={{ width: `${Math.min(100, (timeSpent / (timeLimit * 60)) * 100)}%` }}
              />
            </div>
          </div>
        )}
      </div>

      {/* Quick stats */}
      <div className="flex items-center justify-between mt-4 pt-3 border-t border-gray-100 dark:border-gray-700">
        <div className="text-xs text-gray-500 dark:text-gray-400">
          <span className="font-medium">{totalQuestions - answeredQuestions}</span> questions remaining
        </div>
        
        {timeLimit && (
          <div className="text-xs text-gray-500 dark:text-gray-400">
            Estimated completion: {formatTime(Math.max(0, (totalQuestions - currentQuestion + 1) * 60))}
          </div>
        )}
      </div>
    </div>
  );
}
