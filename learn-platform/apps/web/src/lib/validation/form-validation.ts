/**
 * Enhanced form validation utilities
 * Provides real-time validation, suggestions, and user feedback
 */

import { z } from 'zod';

// Common validation patterns
export const ValidationPatterns = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  strongPassword: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
  url: /^https?:\/\/.+/,
  slug: /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
};

// Learning content validation schemas
export const learningContentValidation = {
  topic: z
    .string()
    .min(3, 'Topic must be at least 3 characters long')
    .max(200, 'Topic must be at most 200 characters long')
    .refine(
      (value) => value.trim().length >= 3,
      'Topic must contain at least 3 non-whitespace characters'
    ),

  learningLevel: z.enum(['beginner', 'intermediate', 'advanced'], {
    errorMap: () => ({ message: 'Please select a valid learning level' }),
  }),

  preferredContentTypes: z
    .array(z.string())
    .min(1, 'Please select at least one content type')
    .max(5, 'Please select at most 5 content types'),

  focusAreas: z
    .string()
    .max(500, 'Focus areas must be at most 500 characters long')
    .optional(),
};

// Topic suggestions based on categories
export const topicSuggestions = {
  technology: [
    'How does machine learning work?',
    'Introduction to blockchain technology',
    'Understanding cloud computing',
    'Basics of cybersecurity',
    'Web development fundamentals',
    'Mobile app development',
    'Data science for beginners',
    'Artificial intelligence overview',
  ],
  science: [
    'How does photosynthesis work?',
    'Understanding quantum physics',
    'Climate change basics',
    'Human anatomy overview',
    'Chemistry fundamentals',
    'Astronomy for beginners',
    'Genetics and DNA',
    'Environmental science',
  ],
  business: [
    'Digital marketing strategies',
    'Project management basics',
    'Financial planning fundamentals',
    'Leadership skills development',
    'Customer service excellence',
    'Supply chain management',
    'Business analytics',
    'Entrepreneurship guide',
  ],
  health: [
    'Nutrition and healthy eating',
    'Exercise and fitness basics',
    'Mental health awareness',
    'Sleep hygiene importance',
    'Stress management techniques',
    'First aid basics',
    'Preventive healthcare',
    'Mindfulness and meditation',
  ],
  education: [
    'Effective study techniques',
    'Time management skills',
    'Critical thinking development',
    'Research methodology',
    'Academic writing skills',
    'Presentation skills',
    'Learning strategies',
    'Memory improvement techniques',
  ],
};

// Content type descriptions and examples
export const contentTypeInfo = {
  paragraph: {
    name: 'Paragraph',
    description: 'Text-based explanations and descriptions',
    example: 'Detailed explanations with clear, readable text',
    icon: '📝',
  },
  infoBox: {
    name: 'Info Box',
    description: 'Highlighted important information',
    example: 'Key points, tips, or warnings in a highlighted box',
    icon: '💡',
  },
  bulletList: {
    name: 'Bullet List',
    description: 'Unordered lists of items or points',
    example: '• Point one\n• Point two\n• Point three',
    icon: '📋',
  },
  numberedList: {
    name: 'Numbered List',
    description: 'Ordered step-by-step instructions',
    example: '1. First step\n2. Second step\n3. Third step',
    icon: '🔢',
  },
  grid: {
    name: 'Grid Layout',
    description: 'Information organized in a grid format',
    example: 'Cards or tiles with related information',
    icon: '⚏',
  },
  comparison: {
    name: 'Comparison',
    description: 'Before/after or side-by-side comparisons',
    example: 'Comparing different approaches or solutions',
    icon: '⚖️',
  },
  table: {
    name: 'Table',
    description: 'Structured data in rows and columns',
    example: 'Data organized in a tabular format',
    icon: '📊',
  },
  scatterPlot: {
    name: 'Scatter Plot',
    description: 'Visual data representation',
    example: 'Data points plotted on a graph',
    icon: '📈',
  },
  keyValueGrid: {
    name: 'Key-Value Grid',
    description: 'Definitions or term explanations',
    example: 'Term: Definition pairs in a grid layout',
    icon: '🗂️',
  },
};

// Validation result interface
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

// Real-time validation function
export function validateTopicRealTime(value: string): ValidationResult {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    suggestions: [],
  };

  // Check length
  if (value.length === 0) {
    result.isValid = false;
    result.errors.push('Topic is required');
    result.suggestions.push('Try starting with "How to..." or "What is..."');
    return result;
  }

  if (value.length < 3) {
    result.isValid = false;
    result.errors.push('Topic must be at least 3 characters long');
    return result;
  }

  if (value.length > 200) {
    result.isValid = false;
    result.errors.push('Topic must be at most 200 characters long');
    return result;
  }

  // Check for meaningful content
  const trimmedValue = value.trim();
  if (trimmedValue.length < 3) {
    result.isValid = false;
    result.errors.push('Topic must contain meaningful content');
    return result;
  }

  // Warnings for better topics
  if (value.length < 10) {
    result.warnings.push('Consider adding more detail to your topic');
  }

  if (!value.includes(' ')) {
    result.warnings.push('Consider using multiple words for better results');
  }

  // Suggestions based on content
  if (value.toLowerCase().includes('how')) {
    result.suggestions.push('Great! "How" questions often generate excellent learning content');
  }

  if (value.toLowerCase().includes('what')) {
    result.suggestions.push('Perfect! "What" questions are ideal for explanatory content');
  }

  if (value.toLowerCase().includes('why')) {
    result.suggestions.push('Excellent! "Why" questions create engaging educational content');
  }

  // Check for question format
  if (!value.includes('?') && (value.toLowerCase().startsWith('how') || value.toLowerCase().startsWith('what') || value.toLowerCase().startsWith('why'))) {
    result.suggestions.push('Consider adding a question mark (?) to make it a clear question');
  }

  return result;
}

// Get topic suggestions based on input
export function getTopicSuggestions(input: string, category?: string): string[] {
  const allSuggestions = category 
    ? topicSuggestions[category as keyof typeof topicSuggestions] || []
    : Object.values(topicSuggestions).flat();

  if (!input || input.length < 2) {
    return allSuggestions.slice(0, 5);
  }

  // Filter suggestions based on input
  const filtered = allSuggestions.filter(suggestion =>
    suggestion.toLowerCase().includes(input.toLowerCase())
  );

  // If no matches, return suggestions that start with similar words
  if (filtered.length === 0) {
    const words = input.toLowerCase().split(' ');
    const firstWord = words[0];
    
    return allSuggestions.filter(suggestion =>
      suggestion.toLowerCase().startsWith(firstWord)
    ).slice(0, 5);
  }

  return filtered.slice(0, 5);
}

// Validate content types selection
export function validateContentTypes(selectedTypes: string[]): ValidationResult {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    suggestions: [],
  };

  if (selectedTypes.length === 0) {
    result.isValid = false;
    result.errors.push('Please select at least one content type');
    result.suggestions.push('Start with "Paragraph" for text-based content');
    return result;
  }

  if (selectedTypes.length > 5) {
    result.isValid = false;
    result.errors.push('Please select at most 5 content types');
    return result;
  }

  // Suggestions for better combinations
  if (selectedTypes.length === 1) {
    result.suggestions.push('Consider adding more content types for variety');
  }

  if (selectedTypes.includes('paragraph') && selectedTypes.includes('bulletList')) {
    result.suggestions.push('Great combination! Text and lists work well together');
  }

  if (selectedTypes.includes('comparison') && !selectedTypes.includes('paragraph')) {
    result.suggestions.push('Consider adding "Paragraph" to explain the comparison');
  }

  return result;
}

// Character count with visual feedback
export function getCharacterCountInfo(current: number, max: number) {
  const percentage = (current / max) * 100;
  
  return {
    current,
    max,
    remaining: max - current,
    percentage,
    status: percentage > 90 ? 'danger' : percentage > 75 ? 'warning' : 'normal',
    color: percentage > 90 ? 'text-red-500' : percentage > 75 ? 'text-yellow-500' : 'text-gray-500',
  };
}

// Focus areas validation and suggestions
export function validateFocusAreas(value: string): ValidationResult {
  const result: ValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    suggestions: [],
  };

  if (value.length > 500) {
    result.isValid = false;
    result.errors.push('Focus areas must be at most 500 characters long');
    return result;
  }

  if (value.length > 0 && value.length < 10) {
    result.warnings.push('Consider adding more detail to your focus areas');
  }

  // Suggestions for better focus areas
  if (value.length === 0) {
    result.suggestions.push('Optional: Add specific aspects you want to focus on');
  } else {
    result.suggestions.push('Great! Specific focus areas help generate targeted content');
  }

  return result;
}

// Export validation schema for react-hook-form
export const learningInputSchema = z.object({
  topic: learningContentValidation.topic,
  learningLevel: learningContentValidation.learningLevel,
  preferredContentTypes: learningContentValidation.preferredContentTypes,
  focusAreas: learningContentValidation.focusAreas,
});
