/**
 * TypeScript type definitions for progress tracking system
 */

export interface ProgressStats {
  totalContent: number;
  completedContent: number;
  inProgressContent: number;
  totalTimeSpent: number; // in seconds
  averageCompletion: number; // percentage 0-100
  recentActivity: LearningProgress[];
}

export interface LearningProgress {
  id: string;
  contentId: string;
  userId: string;
  currentStepIndex: number;
  completedSteps: number[];
  totalTimeSpent: number; // in seconds
  completionPercentage: number; // 0-100
  isCompleted: boolean;
  bookmarks: ProgressBookmark[];
  notes: ProgressNote[];
  lastAccessedAt: Date | string;
  sessionCount: number;
  createdAt: Date | string;
  updatedAt: Date | string;
}

export interface ProgressBookmark {
  stepIndex: number;
  note?: string;
  timestamp: string;
}

export interface ProgressNote {
  stepIndex: number;
  content: string;
  timestamp: string;
}

export interface LearningContentWithProgress {
  id: string;
  title: string;
  description: string;
  learningLevel: 'beginner' | 'intermediate' | 'advanced';
  estimatedReadingTime: number;
  isPublic: boolean;
  tags: string[];
  createdAt: string;
  updatedAt: string;
  steps: LearningStep[];
  progress?: LearningProgress;
}

export interface LearningStep {
  id: string;
  title: string;
  icon: string;
  blocks: LearningBlock[];
}

export interface LearningBlock {
  id: string;
  type: string;
  data: any;
  isEditing?: boolean;
}

// API Response Types
export interface ProgressStatsResponse {
  success: boolean;
  stats: ProgressStats;
  message?: string;
}

export interface ProgressResponse {
  success: boolean;
  progress: LearningProgress | null;
  message?: string;
}

export interface UpdateProgressInput {
  contentId: string;
  currentStepIndex: number;
  timeSpent?: number;
  completedSteps?: number[];
}

export interface AddBookmarkInput {
  contentId: string;
  stepIndex: number;
  note?: string;
}

export interface AddNoteInput {
  contentId: string;
  stepIndex: number;
  content: string;
}

export interface RemoveBookmarkInput {
  contentId: string;
  stepIndex: number;
}

export interface UpdateNoteInput {
  contentId: string;
  stepIndex: number;
  noteIndex: number;
  content: string;
}

export interface DeleteNoteInput {
  contentId: string;
  stepIndex: number;
  noteIndex: number;
}

// Component Props Types
export interface ProgressCardProps {
  className?: string;
}

export interface ProgressTrackerProps {
  contentId: string;
  totalSteps: number;
  currentStep: number;
  onStepChange?: (step: number) => void;
  onProgressUpdate?: (progress: LearningProgress) => void;
}

export interface LearningContentCardProps {
  content: LearningContentWithProgress;
  progress?: LearningProgress;
  onDelete?: (id: string) => void;
  onShare?: (content: LearningContentWithProgress) => void;
}

// Utility Types
export type ProgressTimeRange = 'week' | 'month' | 'all';

export interface ProgressFilter {
  timeRange: ProgressTimeRange;
  completionStatus?: 'all' | 'completed' | 'in-progress' | 'not-started';
  sortBy?: 'recent' | 'completion' | 'time-spent' | 'alphabetical';
  sortOrder?: 'asc' | 'desc';
}

// Progress Analytics Types
export interface ProgressAnalytics {
  dailyProgress: DailyProgressData[];
  weeklyProgress: WeeklyProgressData[];
  monthlyProgress: MonthlyProgressData[];
  completionTrends: CompletionTrendData[];
}

export interface DailyProgressData {
  date: string;
  timeSpent: number;
  stepsCompleted: number;
  contentAccessed: number;
}

export interface WeeklyProgressData {
  week: string;
  totalTimeSpent: number;
  totalStepsCompleted: number;
  contentCompleted: number;
  averageSessionTime: number;
}

export interface MonthlyProgressData {
  month: string;
  totalTimeSpent: number;
  totalContentCompleted: number;
  averageCompletionRate: number;
  streakDays: number;
}

export interface CompletionTrendData {
  period: string;
  completionRate: number;
  timeEfficiency: number;
  engagementScore: number;
}

// Error Types
export interface ProgressError {
  code: string;
  message: string;
  details?: any;
}

// Loading States
export interface ProgressLoadingState {
  isLoading: boolean;
  isUpdating: boolean;
  isError: boolean;
  error?: ProgressError;
}
