/**
 * Example component demonstrating tRPC usage in the web application
 * This file shows how to use the tRPC hooks for queries and mutations
 */

'use client';

import { api } from '../lib/trpc';

export function ExampleTRPCUsage() {
  // Example query using tRPC
  const healthQuery = api.health.useQuery();

  // Example query with input
  const greetingQuery = api.greeting.useQuery(
    { name: 'Web App User' },
    {
      // Optional: only run query when name is provided
      enabled: true,
    }
  );

  // Example mutation (if you have any in your router)
  // const createSomethingMutation = api.createSomething.useMutation({
  //   onSuccess: (data) => {
  //     console.log('Created:', data);
  //   },
  //   onError: (error) => {
  //     console.error('Error:', error);
  //   },
  // });

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">tRPC Integration Example</h2>

      {/* Health Check Query */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">Health Check</h3>
        {healthQuery.isLoading && <p>Loading health status...</p>}
        {healthQuery.error && (
          <p className="text-red-600">Error: {healthQuery.error.message}</p>
        )}
        {healthQuery.data && (
          <div className="bg-green-100 p-3 rounded">
            <p><strong>Status:</strong> {healthQuery.data.status}</p>
            <p><strong>Message:</strong> {healthQuery.data.message}</p>
            <p><strong>Timestamp:</strong> {healthQuery.data.timestamp}</p>
            <p><strong>Request ID:</strong> {healthQuery.data.requestId}</p>
          </div>
        )}
      </div>

      {/* Greeting Query */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold mb-2">Greeting</h3>
        {greetingQuery.isLoading && <p>Loading greeting...</p>}
        {greetingQuery.error && (
          <p className="text-red-600">Error: {greetingQuery.error.message}</p>
        )}
        {greetingQuery.data && (
          <div className="bg-blue-100 p-3 rounded">
            <p><strong>Message:</strong> {greetingQuery.data.message}</p>
            <p><strong>User Agent:</strong> {greetingQuery.data.userAgent}</p>
            <p><strong>Timestamp:</strong> {greetingQuery.data.timestamp}</p>
          </div>
        )}
      </div>

      {/* Refresh Button */}
      <button
        onClick={() => {
          healthQuery.refetch();
          greetingQuery.refetch();
        }}
        className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
      >
        Refresh Data
      </button>
    </div>
  );
}
