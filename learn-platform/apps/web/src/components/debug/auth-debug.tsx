'use client';

import { useEffect, useState } from 'react';

/**
 * Debug component to show auth client configuration
 * This helps verify what URL the auth client is actually using
 */
export function AuthDebug() {
  const [debugInfo, setDebugInfo] = useState<{
    nodeEnv: string;
    nextPublicApiUrl: string | undefined;
    authClientBaseUrl: string;
    currentUrl: string;
  } | null>(null);

  useEffect(() => {
    // Get the base URL logic from auth-client.ts
    function getBaseUrl() {
      if (typeof window !== 'undefined') {
        return process.env.NEXT_PUBLIC_API_URL || (process.env.NODE_ENV === 'development' ? 'http://localhost:8787' : 'https://learn-platform-api-dev.bm.workers.dev');
      }
      
      if (process.env.NODE_ENV === 'development') {
        return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8787';
      }
      
      return process.env.NEXT_PUBLIC_API_URL || 'https://learn-platform-api-dev.bm.workers.dev';
    }

    setDebugInfo({
      nodeEnv: process.env.NODE_ENV || 'unknown',
      nextPublicApiUrl: process.env.NEXT_PUBLIC_API_URL,
      authClientBaseUrl: getBaseUrl(),
      currentUrl: window.location.href,
    });
  }, []);

  if (!debugInfo) {
    return <div>Loading debug info...</div>;
  }

  return (
    <div className="bg-gray-100 p-4 rounded-lg text-sm font-mono">
      <h3 className="font-bold mb-2">Auth Client Debug Info</h3>
      <div className="space-y-1">
        <div><strong>NODE_ENV:</strong> {debugInfo.nodeEnv}</div>
        <div><strong>NEXT_PUBLIC_API_URL:</strong> {debugInfo.nextPublicApiUrl || '(not set)'}</div>
        <div><strong>Auth Client Base URL:</strong> {debugInfo.authClientBaseUrl}</div>
        <div><strong>Current URL:</strong> {debugInfo.currentUrl}</div>
      </div>
      
      <div className="mt-4 p-2 bg-yellow-100 rounded">
        <strong>Expected for Production:</strong>
        <div>NEXT_PUBLIC_API_URL should be: https://learn-platform-api-dev.bm.workers.dev</div>
        <div>Auth Client should use: https://learn-platform-api-dev.bm.workers.dev</div>
      </div>
      
      {debugInfo.nextPublicApiUrl !== 'https://learn-platform-api-dev.bm.workers.dev' && (
        <div className="mt-2 p-2 bg-red-100 rounded text-red-700">
          <strong>⚠️ Issue Detected:</strong> NEXT_PUBLIC_API_URL is not set correctly!
          <div>This will cause auth requests to go to the wrong endpoint.</div>
        </div>
      )}
    </div>
  );
}
