'use client';

import { useState } from 'react';
import Link from 'next/link';
import { <PERSON><PERSON>, <PERSON>, CardHeader, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@learn-platform/shared-ui';
import { useAuth } from '../components/auth/auth-provider';
import { MultiStepExplain } from '../lib/components/templates/MultiStepExplain';
import { Brain, BookOpen, TrendingUp, ArrowRight, CheckCircle, Users, Zap, Target } from 'lucide-react';
import { colors } from '@learn-platform/shared-styles';

// Demo data for the MultiStepExplain component
const demoSteps = [
  {
    title: "Welcome to AI-Powered Learning",
    icon: <Brain className="h-8 w-8 text-blue-600" />,
    type: "paragraph" as const,
    data: [
      "Experience the future of personalized education with our AI-driven learning platform.",
      "Our system adapts to your learning style and pace, creating custom content that helps you master any subject efficiently."
    ]
  },
  {
    title: "Interactive Learning Steps",
    icon: <Target className="h-8 w-8 text-green-600" />,
    type: "numberedList" as const,
    data: [
      "Choose your learning topic and difficulty level",
      "AI generates personalized, step-by-step content",
      "Progress through interactive lessons at your own pace",
      "Track your understanding with built-in assessments",
      "Review and reinforce key concepts"
    ]
  },
  {
    title: "Learning Features",
    icon: <Zap className="h-8 w-8 text-purple-600" />,
    type: "grid" as const,
    data: [
      {
        title: "Multi-Format Content",
        content: "Learn through text, interactive elements, comparisons, and visual aids tailored to your topic."
      },
      {
        title: "Progress Tracking",
        content: "Monitor your learning journey with detailed analytics and milestone achievements."
      },
      {
        title: "Adaptive Learning",
        content: "Content difficulty adjusts based on your performance and learning preferences."
      }
    ]
  }
];

export default function LandingPage() {
  const { user, isAuthenticated, signOut } = useAuth();
  const [showDemo, setShowDemo] = useState(false);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Navigation */}
      <nav className="bg-white/80 backdrop-blur-md border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <Brain className="h-8 w-8 text-blue-600" />
              <span className="text-xl font-bold text-gray-900">Learning Platform</span>
            </div>
            
            <div className="flex items-center space-x-4">
              {isAuthenticated ? (
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-gray-600">Welcome, {user?.name}!</span>
                  <Link href="/dashboard">
                    <Button variant="outline">Dashboard</Button>
                  </Link>
                  <Button variant="ghost" onClick={signOut}>Sign Out</Button>
                </div>
              ) : (
                <div className="flex space-x-3">
                  <Link href="/login">
                    <Button variant="outline">Sign In</Button>
                  </Link>
                  <Link href="/register">
                    <Button>Get Started</Button>
                  </Link>
                </div>
              )}
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Learn Anything with
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
                {" "}AI-Powered
              </span>
              <br />Personalized Education
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              Experience adaptive learning that adjusts to your pace and style. 
              Our AI creates custom content, interactive lessons, and tracks your progress 
              to help you master any subject efficiently.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
              {!isAuthenticated ? (
                <>
                  <Link href="/register">
                    <Button size="lg" className="w-full sm:w-auto">
                      Start Learning Free
                      <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </Link>
                  <Button 
                    size="lg" 
                    variant="outline" 
                    onClick={() => setShowDemo(!showDemo)}
                    className="w-full sm:w-auto"
                  >
                    See How It Works
                  </Button>
                </>
              ) : (
                <>
                  <Link href="/dashboard/learn">
                    <Button size="lg" className="w-full sm:w-auto">
                      Continue Learning
                      <ArrowRight className="ml-2 h-5 w-5" />
                    </Button>
                  </Link>
                  <Link href="/dashboard">
                    <Button size="lg" variant="outline" className="w-full sm:w-auto">
                      Go to Dashboard
                    </Button>
                  </Link>
                </>
              )}
            </div>

            {/* Demo Section */}
            {showDemo && (
              <div className="mt-12 bg-white rounded-2xl shadow-xl p-8 max-w-5xl mx-auto">
                <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">
                  Interactive Learning Experience
                </h3>
                <MultiStepExplain 
                  steps={demoSteps}
                  className="bg-transparent shadow-none"
                />
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Powerful Learning Features
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Everything you need for effective, personalized learning in one platform
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* AI-Powered Content Generation */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <CardHeader className="text-center pb-4">
                <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
                  <Brain className="h-8 w-8 text-blue-600" />
                </div>
                <CardTitle className="text-xl">AI Content Generation</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-gray-600 mb-4">
                  Our AI creates personalized learning content tailored to your specific needs, 
                  learning style, and current knowledge level.
                </p>
                <ul className="text-sm text-gray-500 space-y-2">
                  <li className="flex items-center justify-center">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                    Custom lesson plans
                  </li>
                  <li className="flex items-center justify-center">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                    Interactive exercises
                  </li>
                  <li className="flex items-center justify-center">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                    Adaptive difficulty
                  </li>
                </ul>
              </CardContent>
            </Card>

            {/* Multi-Step Learning */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <CardHeader className="text-center pb-4">
                <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                  <BookOpen className="h-8 w-8 text-green-600" />
                </div>
                <CardTitle className="text-xl">Multi-Step Learning</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-gray-600 mb-4">
                  Break down complex topics into digestible steps with various content types 
                  including text, visuals, comparisons, and interactive elements.
                </p>
                <ul className="text-sm text-gray-500 space-y-2">
                  <li className="flex items-center justify-center">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                    Step-by-step progression
                  </li>
                  <li className="flex items-center justify-center">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                    Multiple content formats
                  </li>
                  <li className="flex items-center justify-center">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                    Visual learning aids
                  </li>
                </ul>
              </CardContent>
            </Card>

            {/* Progress Tracking */}
            <Card className="border-0 shadow-lg hover:shadow-xl transition-shadow duration-300">
              <CardHeader className="text-center pb-4">
                <div className="mx-auto w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mb-4">
                  <TrendingUp className="h-8 w-8 text-purple-600" />
                </div>
                <CardTitle className="text-xl">Progress Tracking</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <p className="text-gray-600 mb-4">
                  Monitor your learning journey with detailed analytics, completion tracking, 
                  and personalized insights to optimize your study sessions.
                </p>
                <ul className="text-sm text-gray-500 space-y-2">
                  <li className="flex items-center justify-center">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                    Learning analytics
                  </li>
                  <li className="flex items-center justify-center">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                    Progress milestones
                  </li>
                  <li className="flex items-center justify-center">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                    Performance insights
                  </li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Dashboard Preview */}
      {isAuthenticated && (
        <section className="py-20 bg-gray-50">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Your Learning Dashboard
              </h2>
              <p className="text-xl text-gray-600">
                Access all your learning tools and track your progress
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Link href="/dashboard/learn" className="group">
                <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                  <CardContent className="p-6">
                    <div className="flex items-center mb-4">
                      <Brain className="h-8 w-8 text-blue-600 mr-3" />
                      <h3 className="text-lg font-semibold">Start Learning</h3>
                    </div>
                    <p className="text-gray-600 mb-4">
                      Generate AI-powered learning content on any topic
                    </p>
                    <div className="flex items-center text-blue-600 font-medium">
                      Learn Now <ArrowRight className="ml-2 h-4 w-4" />
                    </div>
                  </CardContent>
                </Card>
              </Link>

              <Link href="/dashboard/my-learning" className="group">
                <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                  <CardContent className="p-6">
                    <div className="flex items-center mb-4">
                      <BookOpen className="h-8 w-8 text-green-600 mr-3" />
                      <h3 className="text-lg font-semibold">My Learning</h3>
                    </div>
                    <p className="text-gray-600 mb-4">
                      View and manage your learning content library
                    </p>
                    <div className="flex items-center text-green-600 font-medium">
                      View Library <ArrowRight className="ml-2 h-4 w-4" />
                    </div>
                  </CardContent>
                </Card>
              </Link>

              <Link href="/dashboard" className="group">
                <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                  <CardContent className="p-6">
                    <div className="flex items-center mb-4">
                      <TrendingUp className="h-8 w-8 text-purple-600 mr-3" />
                      <h3 className="text-lg font-semibold">Dashboard</h3>
                    </div>
                    <p className="text-gray-600 mb-4">
                      Access your complete learning dashboard
                    </p>
                    <div className="flex items-center text-purple-600 font-medium">
                      Open Dashboard <ArrowRight className="ml-2 h-4 w-4" />
                    </div>
                  </CardContent>
                </Card>
              </Link>
            </div>
          </div>
        </section>
      )}

      {/* CTA Section */}
      {!isAuthenticated && (
        <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600">
          <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Ready to Transform Your Learning?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Join thousands of learners who are already experiencing personalized, 
              AI-powered education. Start your journey today.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/register">
                <Button size="lg" variant="secondary" className="w-full sm:w-auto">
                  Create Free Account
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="/login">
                <Button size="lg" variant="outline" className="w-full sm:w-auto border-white hover:bg-white hover:text-blue-600">
                  Sign In
                </Button>
              </Link>
            </div>
          </div>
        </section>
      )}

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center space-x-2 mb-4">
                <Brain className="h-8 w-8 text-blue-400" />
                <span className="text-xl font-bold">Learning Platform</span>
              </div>
              <p className="text-gray-400 max-w-md">
                Empowering learners worldwide with AI-driven, personalized education 
                that adapts to individual learning styles and goals.
              </p>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold mb-4">Platform</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/dashboard" className="hover:text-white transition-colors">Dashboard</Link></li>
                <li><Link href="/dashboard/learn" className="hover:text-white transition-colors">Start Learning</Link></li>
                <li><Link href="/dashboard/my-learning" className="hover:text-white transition-colors">My Learning</Link></li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold mb-4">Account</h3>
              <ul className="space-y-2 text-gray-400">
                {isAuthenticated ? (
                  <>
                    <li><span>Welcome, {user?.name}</span></li>
                    <li><button onClick={signOut} className="hover:text-white transition-colors">Sign Out</button></li>
                  </>
                ) : (
                  <>
                    <li><Link href="/login" className="hover:text-white transition-colors">Sign In</Link></li>
                    <li><Link href="/register" className="hover:text-white transition-colors">Create Account</Link></li>
                  </>
                )}
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 Learning Platform. Powered by AI for personalized education.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
