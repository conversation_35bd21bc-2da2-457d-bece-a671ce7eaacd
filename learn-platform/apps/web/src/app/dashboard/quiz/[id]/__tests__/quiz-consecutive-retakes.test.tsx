/**
 * @jest-environment jsdom
 */

import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { jest } from '@jest/globals';
import React from 'react';

// Mock Next.js router with more sophisticated URL handling
const mockReplace = jest.fn();
const mockPush = jest.fn();
let currentUrl = '/dashboard/quiz/test-quiz';

const mockRouter = {
  push: mockPush,
  replace: mockReplace,
};

const mockParams = { id: 'test-quiz' };
let mockSearchParams = new URLSearchParams();

jest.mock('next/navigation', () => ({
  useRouter: () => mockRouter,
  useParams: () => mockParams,
  useSearchParams: () => mockSearchParams,
}));

// Create a component that simulates the full quiz retake flow
function QuizRetakeFlowSimulator() {
  const [currentAttempt, setCurrentAttempt] = React.useState<any>({
    id: 'completed-attempt',
    isCompleted: true,
    score: { percentage: 85 }
  });
  const [isQuizStarted, setIsQuizStarted] = React.useState(false);
  const [retakeCount, setRetakeCount] = React.useState(0);

  const isRetakeMode = mockSearchParams.get('mode') === 'retake';

  // Simulate the useEffect from the actual component
  React.useEffect(() => {
    if (isRetakeMode) {
      setCurrentAttempt(null);
      setIsQuizStarted(false);

      // Clear the retake parameter from URL after state reset
      const newUrl = `/dashboard/quiz/test-quiz`;
      mockRouter.replace(newUrl);

      // Simulate URL parameter being cleared
      mockSearchParams.delete('mode');
    }
  }, [isRetakeMode]);

  // Simulate the handleRetake function
  const handleRetake = () => {
    setRetakeCount(prev => prev + 1);

    if (isRetakeMode) {
      // If already in retake mode, directly reset state
      setCurrentAttempt(null);
      setIsQuizStarted(false);
    } else {
      // Navigate to retake mode
      mockSearchParams.set('mode', 'retake');
      mockRouter.push(`/dashboard/quiz/test-quiz?mode=retake`);
      // Force re-render by updating a state that triggers the effect
      setCurrentAttempt(null);
    }
  };

  // Simulate starting a quiz (user clicks "Start Quiz")
  const handleStartQuiz = () => {
    setCurrentAttempt({
      id: `attempt-${retakeCount}`,
      isCompleted: false,
      score: null
    });
    setIsQuizStarted(true);
  };

  // Simulate completing a quiz
  const handleCompleteQuiz = () => {
    setCurrentAttempt(prev => ({
      ...prev,
      isCompleted: true,
      score: { percentage: 90 }
    }));
    setIsQuizStarted(false);
  };

  return (
    <div data-testid="quiz-flow">
      <div data-testid="current-attempt-id">
        {currentAttempt?.id || 'no-attempt'}
      </div>
      <div data-testid="attempt-completed">
        {currentAttempt?.isCompleted ? 'completed' : 'in-progress'}
      </div>
      <div data-testid="quiz-started">
        {isQuizStarted ? 'started' : 'not-started'}
      </div>
      <div data-testid="retake-count">
        {retakeCount}
      </div>
      <div data-testid="retake-mode">
        {isRetakeMode ? 'retake' : 'normal'}
      </div>

      {!isQuizStarted && (
        <button data-testid="start-quiz" onClick={handleStartQuiz}>
          {isRetakeMode ? 'Retake Quiz' : 'Start Quiz'}
        </button>
      )}

      {isQuizStarted && !currentAttempt?.isCompleted && (
        <button data-testid="complete-quiz" onClick={handleCompleteQuiz}>
          Complete Quiz
        </button>
      )}

      {currentAttempt?.isCompleted && (
        <button data-testid="retake-button" onClick={handleRetake}>
          Retake Quiz
        </button>
      )}
    </div>
  );
}

describe('Quiz Consecutive Retakes Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockSearchParams = new URLSearchParams();
    currentUrl = '/dashboard/quiz/test-quiz';
  });

  it('should handle multiple consecutive retake attempts correctly', async () => {
    const { rerender } = render(<QuizRetakeFlowSimulator />);

    // Initial state: completed quiz
    expect(screen.getByTestId('current-attempt-id')).toHaveTextContent('completed-attempt');
    expect(screen.getByTestId('attempt-completed')).toHaveTextContent('completed');
    expect(screen.getByTestId('retake-count')).toHaveTextContent('0');

    // First retake attempt
    fireEvent.click(screen.getByTestId('retake-button'));

    await waitFor(() => {
      expect(screen.getByTestId('retake-count')).toHaveTextContent('1');
      expect(screen.getByTestId('current-attempt-id')).toHaveTextContent('no-attempt');
    });

    // Start the retake
    fireEvent.click(screen.getByTestId('start-quiz'));

    await waitFor(() => {
      expect(screen.getByTestId('current-attempt-id')).toHaveTextContent('attempt-1');
      expect(screen.getByTestId('quiz-started')).toHaveTextContent('started');
    });

    // Complete the retake
    fireEvent.click(screen.getByTestId('complete-quiz'));

    await waitFor(() => {
      expect(screen.getByTestId('attempt-completed')).toHaveTextContent('completed');
      expect(screen.getByTestId('quiz-started')).toHaveTextContent('not-started');
    });

    // Second consecutive retake attempt
    fireEvent.click(screen.getByTestId('retake-button'));

    await waitFor(() => {
      expect(screen.getByTestId('retake-count')).toHaveTextContent('2');
      expect(screen.getByTestId('current-attempt-id')).toHaveTextContent('no-attempt');
    });

    // Start the second retake
    fireEvent.click(screen.getByTestId('start-quiz'));

    await waitFor(() => {
      expect(screen.getByTestId('current-attempt-id')).toHaveTextContent('attempt-2');
      expect(screen.getByTestId('quiz-started')).toHaveTextContent('started');
    });

    // Verify that URL clearing was called
    expect(mockRouter.replace).toHaveBeenCalledWith('/dashboard/quiz/test-quiz');
  });

  it('should handle rapid consecutive retake clicks gracefully', async () => {
    render(<QuizRetakeFlowSimulator />);

    // Initial state: completed quiz
    expect(screen.getByTestId('retake-count')).toHaveTextContent('0');

    // First click should trigger retake
    const retakeButton = screen.getByTestId('retake-button');
    fireEvent.click(retakeButton);

    await waitFor(() => {
      expect(screen.getByTestId('retake-count')).toHaveTextContent('1');
      expect(screen.getByTestId('current-attempt-id')).toHaveTextContent('no-attempt');
    });

    // After first click, the button should no longer be visible (quiz is reset)
    // This is the correct behavior - prevents rapid clicking issues
    expect(screen.queryByTestId('retake-button')).not.toBeInTheDocument();
    expect(screen.getByTestId('start-quiz')).toBeInTheDocument();
  });
});
