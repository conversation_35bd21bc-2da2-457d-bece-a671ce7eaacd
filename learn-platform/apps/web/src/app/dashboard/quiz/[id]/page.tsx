'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter, useSearchParams } from 'next/navigation';
import { ProtectedRoute } from '../../../../components/auth/protected-route';
import { DashboardLayout } from '../../../../lib/components/layout';
import { QuizContainer } from '../../../../lib/components/quiz';
import { Button } from '@learn-platform/shared-ui';
import { api } from '../../../../lib/trpc';
import { ArrowLeft, Brain, AlertCircle } from 'lucide-react';
import Link from 'next/link';

export default function QuizTakingPage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const quizId = params.id as string;
  const isReviewMode = searchParams.get('mode') === 'review';
  const isRetakeMode = searchParams.get('retake') === 'true';

  const [currentAttempt, setCurrentAttempt] = useState<any>(null);
  const [isQuizStarted, setIsQuizStarted] = useState(false);
  const [isRetaking, setIsRetaking] = useState(isRetakeMode);

  // Get utils at component level to avoid hook call in callback
  const utils = api.useUtils();

  // Fetch quiz data
  const { data: quizData, isLoading: isLoadingQuiz, error: quizError } = api.quiz.getById.useQuery(
    { id: quizId },
    {
      enabled: !!quizId,
      retry: (failureCount, error) => {
        // Don't retry on 404 or 403 errors
        if (error?.data?.code === 'NOT_FOUND' || error?.data?.code === 'FORBIDDEN') {
          return false;
        }
        return failureCount < 3;
      },
    }
  );

  // Fetch quiz attempts for review mode
  const { data: attemptsData, isLoading: isLoadingAttempts } = api.quiz.getMyAttempts.useQuery(
    { quizId, limit: 1, offset: 0 },
    {
      enabled: !!quizId && isReviewMode,
    }
  );

  // Check for existing incomplete attempt
  const { data: currentAttemptData, isLoading: isLoadingCurrentAttempt } = api.quiz.getCurrentAttempt.useQuery(
    { quizId },
    {
      enabled: !!quizId && !isReviewMode,
    }
  );

  // Note: Removed completed attempts query since we no longer auto-load completed quizzes
  // Users should use ?mode=review to explicitly review completed quizzes

  // Start quiz attempt mutation
  const startAttemptMutation = api.quiz.startAttempt.useMutation({
    onSuccess: (data) => {
      // Clear retaking flag when new attempt is successfully started
      setIsRetaking(false);

      // Invalidate queries to refresh data
      utils.quiz.getCurrentAttempt.invalidate({ quizId });
      utils.quiz.getMyAttempts.invalidate({ quizId });

      // Set the new attempt and start the quiz
      // Handle both response formats: { attemptId } and { attempt }
      const attemptId = data.attemptId || data.attempt?.id;
      const attemptData = data.attempt || {
        id: attemptId,
        quizId: quiz.id,
        userId: '', // Will be filled by the query
        startedAt: new Date(),
        completedAt: null,
        isCompleted: false,
        score: null,
        totalTimeSpent: 0,
        answers: [],
        questionResults: []
      };

      setCurrentAttempt(attemptData);
      setIsQuizStarted(true);
    },
    onError: (error) => {
      alert(`Failed to start quiz: ${error.message}`);
      // Clear retaking flag on error too
      setIsRetaking(false);
    },
  });

  // Submit answer mutation
  const submitAnswerMutation = api.quiz.submitAnswer.useMutation({
    onSuccess: () => {
      // Invalidate getCurrentAttempt query to refresh the attempt data
      utils.quiz.getCurrentAttempt.invalidate({ quizId });
      // Invalidate getMyAttempts query to update progress indicators on dashboard
      utils.quiz.getMyAttempts.invalidate();
    },
    onError: (error) => {
      console.error('Failed to submit answer:', error);
      // Show a non-intrusive notification for answer submission errors
      // You could implement a toast notification system here
    },
  });

  // Complete quiz mutation
  const completeQuizMutation = api.quiz.complete.useMutation({
    onSuccess: (data) => {
      // Update attempt with completion data
      setCurrentAttempt(prev => ({
        ...prev,
        isCompleted: true,
        completedAt: new Date().toISOString(),
        score: data.score,
        questionResults: data.questionResults,
      }));
      // Invalidate getMyAttempts query to update progress indicators on dashboard
      utils.quiz.getMyAttempts.invalidate();
    },
    onError: (error) => {
      alert(`Failed to complete quiz: ${error.message}`);
    },
  });

  const quiz = quizData?.quiz;
  const attempts = attemptsData?.attempts || [];
  const reviewAttempt = attempts.find(attempt => attempt.isCompleted) || attempts[0];
  const existingAttempt = currentAttemptData?.attempt;
  const existingProgress = currentAttemptData?.progress;



  // Note: Removed completed attempts variables since we no longer auto-load

  // Auto-start review mode if we have a completed attempt
  useEffect(() => {
    if (isReviewMode && reviewAttempt && !isQuizStarted) {
      setCurrentAttempt(reviewAttempt);
      setIsQuizStarted(true);
    }
  }, [isReviewMode, reviewAttempt, isQuizStarted]);

  // Clear retake URL parameter after processing
  useEffect(() => {
    if (isRetakeMode) {
      // Clear the URL parameter to avoid confusion on subsequent navigations
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('retake');
      window.history.replaceState({}, '', newUrl.toString());
    }
  }, [isRetakeMode]);

  // Auto-resume existing incomplete attempt
  useEffect(() => {
    // Auto-resume incomplete attempts, but NOT during retakes
    if (!isReviewMode && !isRetaking && existingAttempt && !existingAttempt.isCompleted && !isQuizStarted && !currentAttempt) {
      setCurrentAttempt(existingAttempt);
      setIsQuizStarted(true);
    }
  }, [isReviewMode, isRetaking, isRetakeMode, existingAttempt, isQuizStarted, currentAttempt]);

  // Note: Removed auto-loading of completed attempts to always show preview screen
  // Users should explicitly use ?mode=review to review completed quizzes
  // This ensures retakes from quiz list show the preview screen as expected



  const handleStartQuiz = () => {
    if (!quiz) return;

    // If we're in retake mode, always create a new attempt
    if (isRetaking) {
      startAttemptMutation.mutate({ quizId: quiz.id });
      return;
    }

    // If there's an existing incomplete attempt, resume it instead of creating new one
    if (existingAttempt) {
      setCurrentAttempt(existingAttempt);
      setIsQuizStarted(true);
    } else {
      startAttemptMutation.mutate({ quizId: quiz.id });
    }
  };

  const handleAnswerSubmit = (questionId: string, answer: any, timeSpent: number) => {
    if (!currentAttempt || !currentAttempt.id) {
      return;
    }

    submitAnswerMutation.mutate({
      attemptId: currentAttempt.id,
      questionId,
      answer,
      timeSpent,
      isTemporary: false,
    });
  };

  const handleQuizComplete = () => {
    if (!currentAttempt) return;

    completeQuizMutation.mutate({
      attemptId: currentAttempt.id,
    });
  };

  const handleQuizExit = () => {
    router.push('/dashboard/quizzes');
  };

  const handleRetake = () => {
    if (!quiz) return;

    // Reset state to show the quiz preview screen
    // Don't create a new attempt immediately - let the user click "Start Quiz"
    setIsRetaking(true);
    setCurrentAttempt(null);
    setIsQuizStarted(false);

    // Note: Removed immediate startAttemptMutation.mutate() call
    // The new attempt will be created when user clicks "Start Quiz" via handleStartQuiz
  };

  // Loading state
  if (isLoadingQuiz || (isReviewMode && isLoadingAttempts) || (!isReviewMode && isLoadingCurrentAttempt)) {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-300">Loading quiz...</p>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    );
  }

  // Error state
  if (quizError) {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="text-center py-12">
            <AlertCircle className="h-12 w-12 text-red-600 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">Quiz Not Found</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              {quizError.data?.code === 'NOT_FOUND'
                ? 'The quiz you\'re looking for doesn\'t exist or has been removed.'
                : quizError.data?.code === 'FORBIDDEN'
                ? 'You don\'t have permission to access this quiz.'
                : `Error loading quiz: ${quizError.message}`
              }
            </p>
            <div className="space-x-4">
              <Link href="/dashboard/quizzes">
                <Button>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Quizzes
                </Button>
              </Link>
            </div>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    );
  }

  // Quiz not found
  if (!quiz) {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="text-center py-12">
            <Brain className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">Quiz Not Available</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">This quiz is not available at the moment.</p>
            <Link href="/dashboard/quizzes">
              <Button>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Quizzes
              </Button>
            </Link>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    );
  }

  // Review mode but no completed attempts
  if (isReviewMode && !isLoadingAttempts && !reviewAttempt) {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="text-center py-12">
            <AlertCircle className="h-12 w-12 text-yellow-600 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">No Quiz Attempts Found</h2>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              You haven't completed this quiz yet. Take the quiz first to review your answers.
            </p>
            <div className="space-x-4">
              <Link href={`/dashboard/quiz/${quizId}`}>
                <Button>
                  <Brain className="h-4 w-4 mr-2" />
                  Take Quiz
                </Button>
              </Link>
              <Link href="/dashboard/quizzes">
                <Button variant="outline">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Quizzes
                </Button>
              </Link>
            </div>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    );
  }

  // Quiz taking interface
  if (isQuizStarted && currentAttempt) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
          <QuizContainer
            key={currentAttempt?.id || 'no-attempt'}
            quiz={quiz}
            attempt={currentAttempt}
            progress={existingProgress}
            onAnswerSubmit={isReviewMode ? () => {} : handleAnswerSubmit}
            onQuizComplete={isReviewMode ? () => {} : handleQuizComplete}
            onQuizExit={handleQuizExit}
            onRetake={handleRetake}
            isSubmittingAnswer={submitAnswerMutation.isPending}
            isCompletingQuiz={completeQuizMutation.isPending}
            isRetaking={isRetaking}
          />
        </div>
      </ProtectedRoute>
    );
  }

  // Quiz preview/start screen (show if not in review mode)
  if (!isReviewMode) {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="max-w-4xl mx-auto space-y-6">
            {/* Navigation */}
            <div className="flex items-center justify-between">
              <Link href="/dashboard/quizzes">
                <Button variant="outline">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Quizzes
                </Button>
              </Link>
            </div>

          {/* Quiz Preview */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-8">
            <div className="text-center mb-8">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 dark:bg-blue-900 rounded-full mb-4">
                <Brain className="h-8 w-8 text-blue-600 dark:text-blue-400" />
              </div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">{quiz.title}</h1>
              {quiz.description && (
                <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">{quiz.description}</p>
              )}
            </div>

            {/* Quiz Details */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="text-2xl font-bold text-blue-600 dark:text-blue-400 mb-1">
                  {Array.isArray(quiz.questions) ? quiz.questions.length : 0}
                </div>
                <div className="text-sm text-gray-600 dark:text-gray-300">Questions</div>
              </div>
              <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="text-2xl font-bold text-green-600 dark:text-green-400 mb-1">{quiz.estimatedDuration}</div>
                <div className="text-sm text-gray-600 dark:text-gray-300">Minutes</div>
              </div>
              <div className="text-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="text-2xl font-bold text-purple-600 dark:text-purple-400 mb-1">{quiz.totalPoints}</div>
                <div className="text-sm text-gray-600 dark:text-gray-300">Points</div>
              </div>
            </div>

            {/* Quiz Info */}
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700 rounded-lg p-4 mb-8">
              <h3 className="font-semibold text-blue-900 dark:text-blue-200 mb-2">Before you start:</h3>
              <ul className="text-blue-800 dark:text-blue-300 text-sm space-y-1">
                <li>• Make sure you have a stable internet connection</li>
                <li>• You can navigate between questions freely</li>
                {quiz.timeLimit && <li>• Time limit: {quiz.timeLimit} minutes</li>}
                {quiz.allowRetakes && <li>• You can retake this quiz if needed</li>}
                <li>• Your progress will be saved automatically</li>
              </ul>
            </div>

            {/* Start Button */}
            <div className="text-center">
              <Button
                size="lg"
                onClick={handleStartQuiz}
                disabled={startAttemptMutation.isPending}
                className="px-8 py-3"
              >
                {startAttemptMutation.isPending ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Starting Quiz...
                  </>
                ) : (
                  <>
                    <Brain className="h-5 w-5 mr-2" />
                    Start Quiz
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
  }

  // Fallback for review mode (shouldn't reach here normally)
  return (
    <ProtectedRoute>
      <DashboardLayout>
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-300">Loading review...</p>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
}
