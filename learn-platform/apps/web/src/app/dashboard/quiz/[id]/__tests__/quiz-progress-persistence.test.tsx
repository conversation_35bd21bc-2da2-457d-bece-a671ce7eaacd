/**
 * @jest-environment jsdom
 */

import { render, screen, waitFor } from '@testing-library/react';
import { jest } from '@jest/globals';
import QuizPage from '../page';

// Mock the tRPC client
const mockGetCurrentAttempt = jest.fn();
const mockGetQuiz = jest.fn();
const mockGetMyAttempts = jest.fn();

jest.mock('../../../../lib/trpc', () => ({
  api: {
    quiz: {
      getCurrentAttempt: {
        useQuery: mockGetCurrentAttempt,
      },
      getById: {
        useQuery: mockGetQuiz,
      },
      getMyAttempts: {
        useQuery: mockGetMyAttempts,
      },
      startAttempt: {
        useMutation: () => ({
          mutate: jest.fn(),
          isPending: false,
        }),
      },
      submitAnswer: {
        useMutation: () => ({
          mutate: jest.fn(),
          isPending: false,
        }),
      },
      complete: {
        useMutation: () => ({
          mutate: jest.fn(),
          isPending: false,
        }),
      },
    },
  },
}));

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useParams: () => ({ id: 'test-quiz-id' }),
  useSearchParams: () => new URLSearchParams(),
  useRouter: () => ({
    push: jest.fn(),
    back: jest.fn(),
  }),
}));

// Mock ProtectedRoute
jest.mock('@/lib/components/auth/ProtectedRoute', () => {
  return function MockProtectedRoute({ children }: { children: React.ReactNode }) {
    return <div data-testid="protected-route">{children}</div>;
  };
});

// Mock DashboardLayout
jest.mock('@/lib/components/layout/DashboardLayout', () => {
  return function MockDashboardLayout({ children }: { children: React.ReactNode }) {
    return <div data-testid="dashboard-layout">{children}</div>;
  };
});

// Mock QuizContainer
jest.mock('@/lib/components/quiz/QuizContainer', () => {
  return function MockQuizContainer({
    quiz,
    attempt,
    progress
  }: {
    quiz: any;
    attempt: any;
    progress: any;
  }) {
    return (
      <div data-testid="quiz-container">
        <div data-testid="quiz-title">{quiz?.title}</div>
        <div data-testid="attempt-id">{attempt?.id}</div>
        <div data-testid="progress-questions-answered">{progress?.questionsAnswered}</div>
      </div>
    );
  };
});

const mockQuiz = {
  id: 'test-quiz-id',
  title: 'Test Quiz',
  description: 'A test quiz',
  questions: [
    { id: 'q1', type: 'multipleChoice', question: 'Question 1' },
    { id: 'q2', type: 'multipleChoice', question: 'Question 2' },
  ],
  difficulty: 'medium',
  totalPoints: 20,
};

const mockExistingAttempt = {
  id: 'existing-attempt-id',
  quizId: 'test-quiz-id',
  userId: 'test-user-id',
  startedAt: new Date(),
  isCompleted: false,
  answers: [
    {
      questionId: 'q1',
      answer: 'A',
      timeSpent: 30,
    },
  ],
};

const mockProgress = {
  currentQuestionIndex: 1,
  questionsAnswered: 1,
  totalQuestions: 2,
  timeSpentSoFar: 30,
};

describe('Quiz Progress Persistence', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Default mock implementations
    mockGetQuiz.mockReturnValue({
      data: { quiz: mockQuiz },
      isLoading: false,
    });

    mockGetMyAttempts.mockReturnValue({
      data: { attempts: [] },
      isLoading: false,
    });
  });

  it('should load existing incomplete attempt and progress on page load', async () => {
    // Mock getCurrentAttempt to return existing attempt
    mockGetCurrentAttempt.mockReturnValue({
      data: {
        success: true,
        attempt: mockExistingAttempt,
        progress: mockProgress,
      },
      isLoading: false,
    });

    render(<QuizPage />);

    // Should show loading initially, then quiz container
    await waitFor(() => {
      expect(screen.getByTestId('quiz-container')).toBeInTheDocument();
    });

    // Should display the existing attempt and progress
    expect(screen.getByTestId('attempt-id')).toHaveTextContent('existing-attempt-id');
    expect(screen.getByTestId('progress-questions-answered')).toHaveTextContent('1');
  });

  it('should show start screen when no existing attempt exists', async () => {
    // Mock getCurrentAttempt to return no attempt
    mockGetCurrentAttempt.mockReturnValue({
      data: {
        success: true,
        attempt: null,
        progress: null,
      },
      isLoading: false,
    });

    render(<QuizPage />);

    // Should show the quiz start screen (not the quiz container)
    await waitFor(() => {
      expect(screen.queryByTestId('quiz-container')).not.toBeInTheDocument();
    });

    // Should show quiz information for starting
    expect(screen.getByText('Test Quiz')).toBeInTheDocument();
  });

  it('should call getCurrentAttempt with correct quiz ID', () => {
    mockGetCurrentAttempt.mockReturnValue({
      data: { success: true, attempt: null, progress: null },
      isLoading: false,
    });

    render(<QuizPage />);

    // Should call getCurrentAttempt with the quiz ID
    expect(mockGetCurrentAttempt).toHaveBeenCalledWith(
      { quizId: 'test-quiz-id' },
      { enabled: true }
    );
  });

  it('should handle loading state correctly', () => {
    // Mock loading state
    mockGetCurrentAttempt.mockReturnValue({
      data: undefined,
      isLoading: true,
    });

    render(<QuizPage />);

    // Should show loading spinner
    expect(screen.getByText('Loading quiz...')).toBeInTheDocument();
  });

  it('should pass progress data to QuizContainer when resuming', async () => {
    mockGetCurrentAttempt.mockReturnValue({
      data: {
        success: true,
        attempt: mockExistingAttempt,
        progress: mockProgress,
      },
      isLoading: false,
    });

    render(<QuizPage />);

    await waitFor(() => {
      expect(screen.getByTestId('quiz-container')).toBeInTheDocument();
    });

    // Should pass the progress data to QuizContainer
    expect(screen.getByTestId('progress-questions-answered')).toHaveTextContent('1');
  });
});
