'use client';

import { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from 'next/navigation';
import { ProtectedRoute } from '../../../../../components/auth/protected-route';
import { DashboardLayout } from '../../../../../lib/components/layout';
import { Button } from '@learn-platform/shared-ui';
import { api } from '../../../../../lib/trpc';
import { 
  ArrowLeft, 
  Trophy, 
  Clock, 
  Target,
  CheckCircle,
  XCircle,
  AlertCircle,
  RotateCcw,
  Eye,
  Brain
} from 'lucide-react';
import Link from 'next/link';

export default function QuizResultsPage() {
  const params = useParams();
  const router = useRouter();
  const searchParams = useSearchParams();
  const quizId = params.id as string;
  const attemptId = searchParams.get('attemptId');

  // Fetch quiz data
  const { data: quizData, isLoading: isLoadingQuiz } = api.quiz.getById.useQuery(
    { id: quizId },
    { enabled: !!quizId }
  );

  // Fetch quiz attempts to find the specific attempt
  const { data: attemptsData, isLoading: isLoadingAttempts } = api.quiz.getMyAttempts.useQuery(
    { quizId, limit: 10, offset: 0 },
    { enabled: !!quizId }
  );

  const quiz = quizData?.quiz;
  const attempts = attemptsData?.attempts || [];
  
  // Find the specific attempt or use the latest completed one
  const attempt = attemptId 
    ? attempts.find(a => a.id === attemptId)
    : attempts.find(a => a.isCompleted) || attempts[0];

  const isLoading = isLoadingQuiz || isLoadingAttempts;

  const handleRetakeQuiz = () => {
    router.push(`/dashboard/quiz/${quizId}?retake=true`);
  };

  const handleReviewQuiz = () => {
    router.push(`/dashboard/quiz/${quizId}?mode=review`);
  };

  const formatDuration = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getScoreColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600';
    if (percentage >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBgColor = (percentage: number) => {
    if (percentage >= 80) return 'bg-green-100';
    if (percentage >= 60) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  // Loading state
  if (isLoading) {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading quiz results...</p>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    );
  }

  // Quiz not found
  if (!quiz) {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="text-center py-12">
            <AlertCircle className="h-12 w-12 text-red-600 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Quiz Not Found</h2>
            <p className="text-gray-600 mb-6">The quiz you're looking for doesn't exist or has been removed.</p>
            <Link href="/dashboard/quizzes">
              <Button>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Quizzes
              </Button>
            </Link>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    );
  }

  // No attempt found
  if (!attempt) {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="text-center py-12">
            <AlertCircle className="h-12 w-12 text-yellow-600 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">No Quiz Results Found</h2>
            <p className="text-gray-600 mb-6">
              {attemptId 
                ? 'The specific quiz attempt you\'re looking for was not found.'
                : 'You haven\'t completed this quiz yet.'
              }
            </p>
            <div className="space-x-4">
              <Link href={`/dashboard/quiz/${quizId}`}>
                <Button>
                  <Brain className="h-4 w-4 mr-2" />
                  Take Quiz
                </Button>
              </Link>
              <Link href="/dashboard/quizzes">
                <Button variant="outline">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Quizzes
                </Button>
              </Link>
            </div>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    );
  }

  // Attempt not completed
  if (!attempt.isCompleted) {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="text-center py-12">
            <Clock className="h-12 w-12 text-blue-600 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Quiz In Progress</h2>
            <p className="text-gray-600 mb-6">This quiz attempt is still in progress.</p>
            <div className="space-x-4">
              <Link href={`/dashboard/quiz/${quizId}`}>
                <Button>
                  <Brain className="h-4 w-4 mr-2" />
                  Continue Quiz
                </Button>
              </Link>
              <Link href="/dashboard/quizzes">
                <Button variant="outline">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Quizzes
                </Button>
              </Link>
            </div>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    );
  }

  const score = attempt.score;
  const scorePercentage = score?.percentage || 0;

  return (
    <ProtectedRoute>
      <DashboardLayout>
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Navigation */}
          <div className="flex items-center justify-between">
            <Link href="/dashboard/quizzes">
              <Button variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Quizzes
              </Button>
            </Link>
          </div>

          {/* Results Header */}
          <div className="bg-white shadow rounded-lg p-8">
            <div className="text-center mb-8">
              <div className={`inline-flex items-center justify-center w-20 h-20 rounded-full mb-4 ${getScoreBgColor(scorePercentage)}`}>
                <Trophy className={`h-10 w-10 ${getScoreColor(scorePercentage)}`} />
              </div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Quiz Results</h1>
              <h2 className="text-xl text-gray-600 mb-4">{quiz.title}</h2>
              <div className={`text-4xl font-bold mb-2 ${getScoreColor(scorePercentage)}`}>
                {Math.round(scorePercentage)}%
              </div>
              <p className="text-gray-600">
                {score?.correctAnswers || 0} out of {score?.totalQuestions || 0} questions correct
              </p>
            </div>

            {/* Score Breakdown */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600 mb-1">
                  {score?.earnedPoints || 0}
                </div>
                <div className="text-sm text-gray-600">Points Earned</div>
                <div className="text-xs text-gray-500">
                  out of {score?.totalPoints || 0}
                </div>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600 mb-1">
                  {score?.correctAnswers || 0}
                </div>
                <div className="text-sm text-gray-600">Correct Answers</div>
                <div className="text-xs text-gray-500">
                  out of {score?.totalQuestions || 0}
                </div>
              </div>
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-2xl font-bold text-purple-600 mb-1">
                  {formatDuration(attempt.totalTimeSpent || 0)}
                </div>
                <div className="text-sm text-gray-600">Time Spent</div>
              </div>
            </div>

            {/* Attempt Details */}
            <div className="border-t border-gray-200 pt-6 mb-8">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Attempt Details</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
                <div>
                  <span className="font-medium">Started:</span> {formatDate(attempt.startedAt)}
                </div>
                <div>
                  <span className="font-medium">Completed:</span> {attempt.completedAt ? formatDate(attempt.completedAt) : 'N/A'}
                </div>
                <div>
                  <span className="font-medium">Difficulty:</span> {quiz.difficulty}
                </div>
                <div>
                  <span className="font-medium">Attempt ID:</span> {attempt.id.slice(0, 8)}...
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-center space-x-4">
              <Button
                onClick={handleReviewQuiz}
                variant="outline"
                className="px-6 py-3"
              >
                <Eye className="h-5 w-5 mr-2" />
                Review Answers
              </Button>
              
              {quiz.allowRetakes && (
                <Button
                  onClick={handleRetakeQuiz}
                  className="px-6 py-3"
                >
                  <RotateCcw className="h-5 w-5 mr-2" />
                  Retake Quiz
                </Button>
              )}
            </div>
          </div>

          {/* Performance Feedback */}
          <div className="bg-white shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Performance Feedback</h3>
            <div className="space-y-4">
              {scorePercentage >= 80 ? (
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                  <div>
                    <p className="font-medium text-green-900">Excellent Performance!</p>
                    <p className="text-green-700 text-sm">
                      You demonstrated a strong understanding of the material. Keep up the great work!
                    </p>
                  </div>
                </div>
              ) : scorePercentage >= 60 ? (
                <div className="flex items-start space-x-3">
                  <Target className="h-5 w-5 text-yellow-600 mt-0.5" />
                  <div>
                    <p className="font-medium text-yellow-900">Good Progress!</p>
                    <p className="text-yellow-700 text-sm">
                      You're on the right track. Consider reviewing the material and trying again to improve your score.
                    </p>
                  </div>
                </div>
              ) : (
                <div className="flex items-start space-x-3">
                  <XCircle className="h-5 w-5 text-red-600 mt-0.5" />
                  <div>
                    <p className="font-medium text-red-900">Needs Improvement</p>
                    <p className="text-red-700 text-sm">
                      Consider reviewing the learning material more thoroughly before retaking the quiz.
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
}
