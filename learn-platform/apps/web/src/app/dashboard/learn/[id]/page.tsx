'use client';

import { useState, useCallback } from 'react';
import { ProtectedRoute } from '../../../../components/auth/protected-route';
import { DashboardLayout } from '../../../../lib/components/layout';
import { MultiStepExplain } from '../../../../lib/components/templates';
import { ShareContentModal, ProgressTracker, FeedbackModal } from '../../../../lib/components/learn';
import { QuizSelectionModal, QuizHistorySidebar } from '../../../../lib/components/quiz';
import { But<PERSON> } from '@learn-platform/shared-ui';
import { ArrowLeft, Share, RotateCcw, BookOpen, Brain, Lightbulb, Target, Zap, Users, TrendingUp, Clock, AlertCircle, MessageSquare, FileQuestion } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useParams } from 'next/navigation';
import { api } from '../../../../lib/trpc';
import type { StepConfig } from '../../../../lib/components/templates/types';

// Icon mapping for string icons to React components
const iconMap: Record<string, React.ComponentType<any>> = {
  Brain,
  Lightbulb,
  Target,
  Zap,
  Users,
  TrendingUp,
  Clock,
  BookOpen,
  AlertCircle,
};

// Transform database step format to MultiStepExplain format
function transformStepsForDisplay(dbSteps: any[]): StepConfig[] {
  return dbSteps.map((step) => {
    // Get the first block as the primary content
    const primaryBlock = step.blocks?.[0];

    if (!primaryBlock) {
      return {
        title: step.title,
        icon: getIconComponent(step.icon),
        type: 'paragraph',
        data: 'No content available for this step.',
      };
    }

    return {
      title: step.title,
      icon: getIconComponent(step.icon),
      type: primaryBlock.type,
      data: primaryBlock.data,
    };
  });
}

// Get icon component from string name
function getIconComponent(iconName: string) {
  const IconComponent = iconMap[iconName] || BookOpen;
  return <IconComponent className="w-8 h-8 text-blue-500" />;
}

export default function LearningDetailPage() {
  const params = useParams();
  const router = useRouter();
  const id = params.id as string;
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);
  const [isQuizModalOpen, setIsQuizModalOpen] = useState(false);

  const [currentStep, setCurrentStepState] = useState(0);

  // Memoize the step change callback to prevent unnecessary re-renders
  const setCurrentStep = useCallback((step: number) => {
    setCurrentStepState(step);
  }, []);

  // Fetch learning content
  const { data: contentData, isLoading, error, refetch } = api.learningContent.getById.useQuery(
    { id },
    {
      enabled: !!id,
      staleTime: 5 * 60 * 1000, // 5 minutes - reduce unnecessary refetches
      retry: (failureCount, error) => {
        // Don't retry on 404 or 403 errors
        if (error?.data?.code === 'NOT_FOUND' || error?.data?.code === 'FORBIDDEN') {
          return false;
        }
        return failureCount < 3;
      },
    }
  );

  // Toggle public status mutation
  const togglePublicMutation = api.learningContent.togglePublic.useMutation({
    onSuccess: () => {
      refetch(); // Refresh the content data
    },
    onError: (error) => {
      alert(`Failed to update visibility: ${error.message}`);
    },
  });

  // Quiz generation mutation
  const generateQuizMutation = api.quiz.generate.useMutation({
    onSuccess: (data) => {
      setIsQuizModalOpen(false);
      // Redirect directly to the quiz content page for immediate quiz taking
      router.push(`/dashboard/quiz/${data.quiz.id}`);
    },
    onError: (error) => {
      alert(`Failed to generate quiz: ${error.message}`);
    },
  });

  const handleTogglePublic = async (isPublic: boolean) => {
    await togglePublicMutation.mutateAsync({ id, isPublic });
  };

  const handleShare = () => {
    setIsShareModalOpen(true);
  };

  const handleRegenerate = () => {
    // TODO: Implement regeneration functionality
    alert('Content regeneration will be implemented in a future update');
  };

  const handleFeedback = () => {
    setIsFeedbackModalOpen(true);
  };

  const handleQuizGenerate = () => {
    setIsQuizModalOpen(true);
  };

  const handleQuizGenerateSubmit = (config: any) => {
    generateQuizMutation.mutate(config);
  };



  const handleQuizReview = (quizId: string) => {
    // Navigate to quiz taking page in review mode
    router.push(`/dashboard/quiz/${quizId}?mode=review`);
  };

  const handleQuizRetake = (quizId: string) => {
    // Navigate to quiz taking page with retake parameter to show the start screen
    router.push(`/dashboard/quiz/${quizId}?retake=true`);
  };

  // Loading state
  if (isLoading) {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="space-y-6">
            {/* Navigation Header */}
            <div className="flex items-center justify-between">
              <Link href="/dashboard/my-learning">
                <Button variant="outline">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to My Learning
                </Button>
              </Link>
            </div>

            {/* Loading State */}
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  Loading Learning Content
                </h3>
                <p className="text-gray-500 dark:text-gray-300">
                  Please wait while we fetch your learning content...
                </p>
              </div>
            </div>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    );
  }

  // Error state
  if (error) {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="space-y-6">
            {/* Navigation Header */}
            <div className="flex items-center justify-between">
              <Link href="/dashboard/my-learning">
                <Button variant="outline">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to My Learning
                </Button>
              </Link>
            </div>

            {/* Error State */}
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
              <div className="text-center py-12">
                <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  {error.data?.code === 'NOT_FOUND' ? 'Content Not Found' : 'Error Loading Content'}
                </h3>
                <p className="text-gray-500 dark:text-gray-300 mb-4">
                  {error.data?.code === 'NOT_FOUND'
                    ? 'The learning content you requested could not be found.'
                    : error.data?.code === 'FORBIDDEN'
                    ? 'You do not have permission to view this content.'
                    : 'An error occurred while loading the learning content.'}
                </p>
                <Link href="/dashboard/my-learning">
                  <Button>
                    Return to My Learning
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    );
  }

  const content = contentData?.content;
  if (!content) {
    return null;
  }

  // Transform steps for MultiStepExplain
  const transformedSteps = transformStepsForDisplay(content.steps);

  return (
    <ProtectedRoute>
      <DashboardLayout>
        <div className="space-y-6">
          {/* Navigation Header */}
          <div className="flex items-center justify-between">
            <Link href="/dashboard/my-learning">
              <Button variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to My Learning
              </Button>
            </Link>
            <div className="flex items-center space-x-2">
              <Button variant="outline" onClick={handleShare}>
                <Share className="h-4 w-4 mr-2" />
                Share
              </Button>
              <Button variant="outline" onClick={handleRegenerate}>
                <RotateCcw className="h-4 w-4 mr-2" />
                Regenerate
              </Button>
            </div>
          </div>

          {/* Content Header */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {content.title}
                </h1>
                {content.description && (
                  <p className="text-gray-600 dark:text-gray-300 mt-2">
                    {content.description}
                  </p>
                )}
                <div className="flex items-center space-x-4 mt-4 text-sm text-gray-500 dark:text-gray-400">
                  <div className="flex items-center space-x-1">
                    <Clock className="h-4 w-4" />
                    <span>{content.estimatedReadingTime} min read</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Target className="h-4 w-4" />
                    <span className="capitalize">{content.learningLevel}</span>
                  </div>
                  {content.tags && content.tags.length > 0 && (
                    <div className="flex items-center space-x-1">
                      <span>Tags:</span>
                      <div className="flex space-x-1">
                        {content.tags.map((tag, index) => (
                          <span key={index} className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded text-xs">
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                <BookOpen className="h-4 w-4" />
                <span>AI Generated</span>
              </div>
            </div>
          </div>

          {/* Learning Content Display */}
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Main Content */}
            <div className="lg:col-span-3">
              <MultiStepExplain
                steps={transformedSteps}
                initialStep={currentStep}
                onStepChange={setCurrentStep}
              />
            </div>

            {/* Sidebar */}
            <div className="lg:col-span-1 space-y-6">
              <ProgressTracker
                contentId={content.id}
                totalSteps={transformedSteps.length}
                currentStep={currentStep}
                onStepChange={setCurrentStep}
              />

              <QuizHistorySidebar
                learningContentId={content.id}
                onQuizSelect={handleQuizReview}
                onRetakeQuiz={handleQuizRetake}
                onGenerateNew={handleQuizGenerate}
              />
            </div>
          </div>

          {/* Additional Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Quick Actions</h3>
              <div className="space-y-2">
                <Button variant="outline" className="w-full justify-start" onClick={handleQuizGenerate}>
                  <FileQuestion className="h-4 w-4 mr-2" />
                  Generate Quiz
                </Button>
                <Button variant="outline" className="w-full justify-start" onClick={handleFeedback}>
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Rate & Provide Feedback
                </Button>
                <Button variant="outline" className="w-full justify-start" onClick={handleShare}>
                  <Share className="h-4 w-4 mr-2" />
                  Share This Content
                </Button>
                <Button variant="outline" className="w-full justify-start" onClick={handleRegenerate}>
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Generate Similar Content
                </Button>
                <Link href="/dashboard/learn">
                  <Button variant="outline" className="w-full justify-start">
                    <BookOpen className="h-4 w-4 mr-2" />
                    Create New Content
                  </Button>
                </Link>
              </div>
            </div>

            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Content Info</h3>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-500 dark:text-gray-400">Created:</span>
                  <span className="dark:text-gray-300">{new Date(content.createdAt).toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500 dark:text-gray-400">Steps:</span>
                  <span className="dark:text-gray-300">{transformedSteps.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500 dark:text-gray-400">Visibility:</span>
                  <span className={content.isPublic ? 'text-green-600 dark:text-green-400' : 'text-gray-600 dark:text-gray-400'}>
                    {content.isPublic ? 'Public' : 'Private'}
                  </span>
                </div>
                {content.aiMetadata?.aiModel && (
                  <div className="flex justify-between">
                    <span className="text-gray-500 dark:text-gray-400">AI Model:</span>
                    <span className="dark:text-gray-300">{content.aiMetadata.aiModel}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Share Modal */}
          {content && (
            <ShareContentModal
              isOpen={isShareModalOpen}
              onClose={() => setIsShareModalOpen(false)}
              content={{
                id: content.id,
                title: content.title,
                description: content.description,
                isPublic: content.isPublic,
              }}
              onTogglePublic={handleTogglePublic}
            />
          )}

          {/* Feedback Modal */}
          {content && (
            <FeedbackModal
              isOpen={isFeedbackModalOpen}
              onClose={() => setIsFeedbackModalOpen(false)}
              contentId={content.id}
              contentTitle={content.title}
            />
          )}

          {/* Quiz Selection Modal */}
          {content && (
            <QuizSelectionModal
              isOpen={isQuizModalOpen}
              onClose={() => setIsQuizModalOpen(false)}
              onGenerate={handleQuizGenerateSubmit}
              learningContentId={content.id}
              isGenerating={generateQuizMutation.isPending}
            />
          )}
        </div>


      </DashboardLayout>
    </ProtectedRoute>
  );
}
