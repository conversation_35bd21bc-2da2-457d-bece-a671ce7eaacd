'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { ProtectedRoute } from '../../../components/auth/protected-route';
import { DashboardLayout } from '../../../lib/components/layout';
import { LearningInputForm } from '../../../lib/components/learn';
import { Button } from '@learn-platform/shared-ui';
import { ArrowLeft, Lightbulb, Zap, Target, CheckCircle, AlertCircle, Clock, Brain } from 'lucide-react';
import Link from 'next/link';
import { api } from '../../../lib/trpc';

// Type for form data from LearningInputForm
interface FormData {
  topic: string;
  learningLevel: 'beginner' | 'intermediate' | 'advanced';
  preferredContentTypes: string[];
  focusAreas?: string;
}

export default function LearnPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [generationStage, setGenerationStage] = useState<'idle' | 'generating' | 'success' | 'error'>('idle');
  const [generationProgress, setGenerationProgress] = useState(0);
  const [generationStatus, setGenerationStatus] = useState('');
  const [generatedContentId, setGeneratedContentId] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [initialFormValues, setInitialFormValues] = useState<Partial<FormData> | null>(null);

  // Derived state for loading/generating status
  const isGenerating = generationStage === 'generating';

  // Store interval references to clean them up
  const [progressInterval, setProgressInterval] = useState<NodeJS.Timeout | null>(null);
  const [statusInterval, setStatusInterval] = useState<NodeJS.Timeout | null>(null);

  // Read URL parameters and set initial form values
  useEffect(() => {
    const topic = searchParams.get('topic');
    const learningLevel = searchParams.get('learningLevel');
    const preferredContentTypes = searchParams.get('preferredContentTypes');
    const focusAreas = searchParams.get('focusAreas');

    if (topic || learningLevel || preferredContentTypes || focusAreas) {
      const initialValues: Partial<FormData> = {};
      
      if (topic) initialValues.topic = topic;
      if (learningLevel && ['beginner', 'intermediate', 'advanced'].includes(learningLevel)) {
        initialValues.learningLevel = learningLevel as 'beginner' | 'intermediate' | 'advanced';
      }
      if (preferredContentTypes) {
        initialValues.preferredContentTypes = preferredContentTypes.split(',');
      }
      if (focusAreas) initialValues.focusAreas = focusAreas;
      
      setInitialFormValues(initialValues);
    }
  }, [searchParams]);

  // AI generation mutation
  const generateMutation = api.learningContent.generateWithAI.useMutation({
    onSuccess: (data) => {
      // Clean up intervals
      if (progressInterval) clearInterval(progressInterval);
      if (statusInterval) clearInterval(statusInterval);

      setGenerationStage('success');
      setGeneratedContentId(data.contentId);
      setGenerationStatus('Content generated successfully!');
      setGenerationProgress(100);

      // Redirect to the generated content after a short delay
      setTimeout(() => {
        router.push(`/dashboard/learn/${data.contentId}`);
      }, 2000);
    },
    onError: (error) => {
      // Clean up intervals
      if (progressInterval) clearInterval(progressInterval);
      if (statusInterval) clearInterval(statusInterval);

      setGenerationStage('error');
      setError(error.message || 'Failed to generate content. Please try again.');
      setGenerationProgress(0);
    },
  });

  const handleFormSubmit = async (data: FormData) => {
    setGenerationStage('generating');
    setGenerationProgress(0);
    setError(null);
    setGeneratedContentId(null);

    // Simulate progress updates
    const newProgressInterval = setInterval(() => {
      setGenerationProgress(prev => {
        if (prev >= 90) {
          return 90; // Stop at 90% until actual completion
        }
        return prev + Math.random() * 15;
      });
    }, 500);
    setProgressInterval(newProgressInterval);

    // Update status messages
    const statusMessages = [
      'Analyzing your topic...',
      'Selecting optimal AI model...',
      'Generating content structure...',
      'Creating learning steps...',
      'Finalizing content...',
    ];

    let statusIndex = 0;
    const newStatusInterval = setInterval(() => {
      if (statusIndex < statusMessages.length) {
        setGenerationStatus(statusMessages[statusIndex]);
        statusIndex++;
      }
    }, 1000);
    setStatusInterval(newStatusInterval);

    // Start the actual generation (single call only)
    generateMutation.mutate({
      topic: data.topic,
      learningLevel: data.learningLevel,
      preferredContentTypes: data.preferredContentTypes,
      focusAreas: data.focusAreas,
    });
  };

  // Show loading state during generation
  if (generationStage === 'generating') {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="space-y-6">
            {/* Navigation */}
            <div className="flex items-center justify-between">
              <Link href="/dashboard">
                <Button variant="outline" disabled>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Dashboard
                </Button>
              </Link>
            </div>

            {/* Generation Progress */}
            <div className="bg-white shadow rounded-lg p-8">
              <div className="text-center">
                <div className="mb-6">
                  <Brain className="h-16 w-16 text-blue-500 mx-auto animate-pulse" />
                </div>
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  Generating Your Learning Content
                </h2>
                <p className="text-gray-600 mb-6">
                  Our AI is creating personalized learning content just for you. This may take a few moments.
                </p>

                {/* Progress Bar */}
                <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
                  <div
                    className="bg-blue-600 h-3 rounded-full transition-all duration-500 ease-out"
                    style={{ width: `${generationProgress}%` }}
                  ></div>
                </div>

                {/* Status Message */}
                <div className="flex items-center justify-center space-x-2 text-sm text-gray-600">
                  <Clock className="h-4 w-4 animate-spin" />
                  <span>{generationStatus || 'Preparing to generate content...'}</span>
                </div>

                {/* Progress Percentage */}
                <p className="text-xs text-gray-500 mt-2">
                  {Math.round(generationProgress)}% complete
                </p>
              </div>
            </div>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    );
  }

  // Show success state
  if (generationStage === 'success') {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="space-y-6">
            {/* Navigation */}
            <div className="flex items-center justify-between">
              <Link href="/dashboard">
                <Button variant="outline">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Dashboard
                </Button>
              </Link>
            </div>

            {/* Success State */}
            <div className="bg-white shadow rounded-lg p-8">
              <div className="text-center">
                <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-6" />
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  Content Generated Successfully!
                </h2>
                <p className="text-gray-600 mb-6">
                  Your personalized learning content is ready. You&#39;ll be redirected to view it shortly.
                </p>

                {generatedContentId && (
                  <Link href={`/dashboard/learn/${generatedContentId}`}>
                    <Button className="bg-green-600 hover:bg-green-700">
                      View Your Learning Content
                    </Button>
                  </Link>
                )}
              </div>
            </div>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    );
  }

  // Show error state
  if (generationStage === 'error') {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="space-y-6">
            {/* Navigation */}
            <div className="flex items-center justify-between">
              <Link href="/dashboard">
                <Button variant="outline">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Dashboard
                </Button>
              </Link>
            </div>

            {/* Error State */}
            <div className="bg-white shadow rounded-lg p-8">
              <div className="text-center">
                <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-6" />
                <h2 className="text-2xl font-bold text-gray-900 mb-4">
                  Generation Failed
                </h2>
                <p className="text-gray-600 mb-6">
                  {error || 'An error occurred while generating your learning content.'}
                </p>

                <div className="space-x-4">
                  <Button
                    onClick={() => {
                      setGenerationStage('idle');
                      setError(null);
                      setGenerationProgress(0);
                    }}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    Try Again
                  </Button>
                  <Link href="/dashboard/my-learning">
                    <Button variant="outline">
                      View Existing Content
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    );
  }

  // Default form state
  return (
    <ProtectedRoute>
      <DashboardLayout>
        <div className="space-y-6">
          {/* Navigation */}
          <div className="flex items-center justify-between">
            <Link href="/dashboard">
              <Button variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
          </div>

          {/* Page Header */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Create Learning Content</h1>
            <p className="text-gray-600 dark:text-gray-300 mt-2">
              Generate personalized learning content powered by AI
            </p>
          </div>

          {/* Tips Section */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <Target className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2" />
                <h3 className="font-medium text-blue-900 dark:text-blue-200">Be Specific</h3>
              </div>
              <p className="text-sm text-blue-700 dark:text-blue-300">
                The more specific your topic, the better our AI can tailor the content to your needs.
              </p>
            </div>

            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <Lightbulb className="h-5 w-5 text-green-600 dark:text-green-400 mr-2" />
                <h3 className="font-medium text-green-900 dark:text-green-200">Choose Your Style</h3>
              </div>
              <p className="text-sm text-green-700 dark:text-green-300">
                Select content types that match your learning preferences for the best experience.
              </p>
            </div>

            <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
              <div className="flex items-center mb-2">
                <Zap className="h-5 w-5 text-purple-600 dark:text-purple-400 mr-2" />
                <h3 className="font-medium text-purple-900 dark:text-purple-200">AI-Powered</h3>
              </div>
              <p className="text-sm text-purple-700 dark:text-purple-300">
                Our AI creates comprehensive, step-by-step learning content tailored to your level.
              </p>
            </div>
          </div>

          {/* Learning Input Form */}
          <LearningInputForm
            onSubmit={handleFormSubmit}
            isLoading={isGenerating}
            initialValues={initialFormValues || undefined}
          />

          {/* Example Topics */}
          <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Need inspiration? Try these topics:</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {[
                "How does machine learning work?",
                "JavaScript async/await explained",
                "Photosynthesis process",
                "Blockchain technology basics",
                "React hooks tutorial",
                "Climate change causes",
                "Database normalization",
                "Investment strategies for beginners",
                "Human digestive system"
              ].map((topic, index) => (
                <button
                  key={index}
                  className="text-left p-3 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg hover:border-blue-300 dark:hover:border-blue-500 hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors text-sm text-gray-900 dark:text-white"
                  onClick={() => {
                    // TODO: Pre-fill the form with this topic
                    console.log('Selected topic:', topic);
                  }}
                  disabled={isGenerating}
                >
                  {topic}
                </button>
              ))}
            </div>
          </div>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
}
