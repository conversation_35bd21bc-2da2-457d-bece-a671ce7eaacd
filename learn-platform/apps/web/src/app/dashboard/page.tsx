'use client';

import { ProtectedRoute } from '../../components/auth/protected-route';
import { useAuth } from '../../components/auth/auth-provider';
import { DashboardLayout } from '../../lib/components/layout';
import { ProgressCard, QuizCard, LearningCard, UserActivityCard, RecommendedTopics } from '../../lib/components/learn';
import { Button } from '@learn-platform/shared-ui';
import { Brain, BookOpen, TrendingUp } from 'lucide-react';
import Link from 'next/link';

export default function DashboardPage() {
  const { user } = useAuth();

  return (
    <ProtectedRoute>
      <DashboardLayout>
        <div className="space-y-6">
          {/* Welcome Header */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  Welcome back, {user?.name}!
                </h1>
                <p className="text-gray-600 dark:text-gray-300 mt-1">
                  Ready to learn something new today?
                </p>
              </div>
              <div className="hidden sm:block">
                <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    user?.emailVerified
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                  }`}>
                    {user?.emailVerified ? '✓ Verified' : '⚠ Unverified'}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
            <div className="h-full">
              <UserActivityCard className="h-full" />
            </div>

            <div className="h-full">
              <LearningCard className="h-full" />
            </div>

            <div className="h-full">
              <ProgressCard className="h-full" />
            </div>

            <div className="h-full">
              <QuizCard className="h-full" />
            </div>
          </div>

          {/* Recommended Topics */}
          <RecommendedTopics />

          {/* Getting Started */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
            <h4 className="text-lg font-medium text-blue-800 dark:text-blue-200 mb-2">
              🚀 Get Started with AI Learning
            </h4>
            <p className="text-sm text-blue-700 dark:text-blue-300 mb-4">
              Create personalized learning content powered by AI. Simply tell us what you want to learn,
              and we&apos;ll generate comprehensive, step-by-step explanations tailored to your level.
            </p>
            <Link href="/dashboard/learn">
              <Button className="bg-blue-600 hover:bg-blue-700">
                Create Your First Learning Content
              </Button>
            </Link>
          </div>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
}
