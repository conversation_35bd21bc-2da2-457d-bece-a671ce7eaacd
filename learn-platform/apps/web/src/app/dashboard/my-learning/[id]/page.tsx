'use client';

import { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { ProtectedRoute } from '../../../../components/auth/protected-route';
import { DashboardLayout } from '../../../../lib/components/layout';
import { ProgressTracker } from '../../../../lib/components/learn';
import { Button } from '@learn-platform/shared-ui';
import { api } from '../../../../lib/trpc';
import { 
  ArrowLeft, 
  BookOpen, 
  Clock, 
  User, 
  Calendar,
  Tag,
  Share2,
  Edit,
  Trash2,
  AlertCircle,
  CheckCircle,
  Play
} from 'lucide-react';
import Link from 'next/link';

export default function LearningContentDetailPage() {
  const params = useParams();
  const router = useRouter();
  const contentId = params.id as string;

  const [isDeleting, setIsDeleting] = useState(false);

  // Fetch learning content details
  const { data: contentData, isLoading, error } = api.learningContent.getById.useQuery(
    { id: contentId },
    {
      enabled: !!contentId,
      retry: (failureCount, error) => {
        if (error?.data?.code === 'NOT_FOUND' || error?.data?.code === 'FORBIDDEN') {
          return false;
        }
        return failureCount < 3;
      },
    }
  );

  // Fetch learning progress
  const { data: progressData } = api.learningProgress.getProgress.useQuery(
    { contentId },
    { enabled: !!contentId }
  );

  // Delete mutation
  const deleteMutation = api.learningContent.delete.useMutation({
    onSuccess: () => {
      router.push('/dashboard/my-learning');
    },
    onError: (error) => {
      alert(`Failed to delete content: ${error.message}`);
      setIsDeleting(false);
    },
  });

  const content = contentData?.content;
  const progress = progressData?.progress;

  const handleDelete = () => {
    if (confirm('Are you sure you want to delete this learning content? This action cannot be undone.')) {
      setIsDeleting(true);
      deleteMutation.mutate({ id: contentId });
    }
  };

  const handleStartLearning = () => {
    router.push(`/dashboard/learn/${contentId}`);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatTimeSpent = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
  };

  // Loading state
  if (isLoading) {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading learning content...</p>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    );
  }

  // Error state
  if (error) {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="text-center py-12">
            <AlertCircle className="h-12 w-12 text-red-600 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Content Not Found</h2>
            <p className="text-gray-600 mb-6">
              {error.data?.code === 'NOT_FOUND'
                ? 'The learning content you\'re looking for doesn\'t exist or has been removed.'
                : error.data?.code === 'FORBIDDEN'
                ? 'You don\'t have permission to access this content.'
                : `Error loading content: ${error.message}`
              }
            </p>
            <div className="space-x-4">
              <Link href="/dashboard/my-learning">
                <Button>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to My Learning
                </Button>
              </Link>
            </div>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    );
  }

  // Content not found
  if (!content) {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="text-center py-12">
            <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Content Not Available</h2>
            <p className="text-gray-600 mb-6">This learning content is not available at the moment.</p>
            <Link href="/dashboard/my-learning">
              <Button>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to My Learning
              </Button>
            </Link>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <DashboardLayout>
        <div className="max-w-4xl mx-auto space-y-6">
          {/* Navigation */}
          <div className="flex items-center justify-between">
            <Link href="/dashboard/my-learning">
              <Button variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to My Learning
              </Button>
            </Link>
            
            <div className="flex items-center space-x-2">
              <Link href={`/dashboard/learn/${contentId}`}>
                <Button variant="outline">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </Button>
              </Link>
              <Button
                variant="outline"
                onClick={handleDelete}
                disabled={isDeleting}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                {isDeleting ? 'Deleting...' : 'Delete'}
              </Button>
            </div>
          </div>

          {/* Content Header */}
          <div className="bg-white shadow rounded-lg p-8">
            <div className="flex items-start justify-between mb-6">
              <div className="flex-1">
                <h1 className="text-3xl font-bold text-gray-900 mb-4">{content.title}</h1>
                {content.description && (
                  <p className="text-lg text-gray-600 mb-6">{content.description}</p>
                )}
              </div>
              <div className="ml-6">
                <div className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                  {content.learningLevel}
                </div>
              </div>
            </div>

            {/* Content Metadata */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <div className="flex items-center text-sm text-gray-600">
                <Clock className="h-4 w-4 mr-2" />
                {content.estimatedReadingTime} min read
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <Calendar className="h-4 w-4 mr-2" />
                {formatDate(content.createdAt)}
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <User className="h-4 w-4 mr-2" />
                {content.isPublic ? 'Public' : 'Private'}
              </div>
              <div className="flex items-center text-sm text-gray-600">
                <BookOpen className="h-4 w-4 mr-2" />
                {Array.isArray(content.steps) ? content.steps.length : 0} steps
              </div>
            </div>

            {/* Tags */}
            {content.tags && content.tags.length > 0 && (
              <div className="flex items-center flex-wrap gap-2 mb-6">
                <Tag className="h-4 w-4 text-gray-500" />
                {content.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            )}

            {/* Progress Section */}
            {progress && (
              <div className="border-t border-gray-200 pt-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">Your Progress</h3>
                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    {progress.isCompleted ? (
                      <div className="flex items-center text-green-600">
                        <CheckCircle className="h-4 w-4 mr-1" />
                        Completed
                      </div>
                    ) : (
                      <div className="flex items-center">
                        <Play className="h-4 w-4 mr-1" />
                        {progress.completionPercentage || 0}% complete
                      </div>
                    )}
                    {progress.totalTimeSpent > 0 && (
                      <div>Time spent: {formatTimeSpent(progress.totalTimeSpent)}</div>
                    )}
                  </div>
                </div>
                
                <ProgressTracker
                  contentId={contentId}
                  progress={progress}
                  totalSteps={Array.isArray(content.steps) ? content.steps.length : 0}
                />
              </div>
            )}

            {/* Action Buttons */}
            <div className="border-t border-gray-200 pt-6">
              <div className="flex items-center justify-center space-x-4">
                <Button
                  size="lg"
                  onClick={handleStartLearning}
                  className="px-8 py-3"
                >
                  <Play className="h-5 w-5 mr-2" />
                  {progress?.isCompleted ? 'Review Content' : progress?.currentStepIndex > 0 ? 'Continue Learning' : 'Start Learning'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
}
