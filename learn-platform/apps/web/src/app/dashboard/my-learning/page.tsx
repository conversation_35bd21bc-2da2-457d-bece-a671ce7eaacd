'use client';

import { useState } from 'react';
import { ProtectedRoute } from '../../../components/auth/protected-route';
import { DashboardLayout } from '../../../lib/components/layout';
import { LearningContentCard, ShareContentModal, AdvancedSearchFilters } from '../../../lib/components/learn';
import { Button } from '@learn-platform/shared-ui';
import { Plus, Grid, List, AlertCircle } from 'lucide-react';
import Link from 'next/link';
import { api } from '../../../lib/trpc';

export default function MyLearningPage() {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [shareModalContent, setShareModalContent] = useState<any>(null);
  const [filters, setFilters] = useState({
    search: '',
    learningLevel: 'all' as 'all' | 'beginner' | 'intermediate' | 'advanced',
    dateRange: { from: '', to: '' },
    tags: [] as string[],
    contentTypes: [] as string[],
    readingTimeRange: { min: 0, max: 300 },
    isPublic: undefined as boolean | undefined,
    completionStatus: 'in-progress' as 'all' | 'completed' | 'in-progress',
  });

  // Fetch user's learning content
  const { data: contentData, isLoading, error, refetch } = api.learningContent.getMy.useQuery({
    limit: 50,
    offset: 0,
    search: filters.search || undefined,
    learningLevel: filters.learningLevel !== 'all' ? filters.learningLevel : undefined,
    dateRange: filters.dateRange.from || filters.dateRange.to ? filters.dateRange : undefined,
    tags: filters.tags.length > 0 ? filters.tags : undefined,
    contentTypes: filters.contentTypes.length > 0 ? filters.contentTypes : undefined,
    readingTimeRange: filters.readingTimeRange.min > 0 || filters.readingTimeRange.max < 300 ? filters.readingTimeRange : undefined,
    isPublic: filters.isPublic,
    completionStatus: filters.completionStatus !== 'all' ? filters.completionStatus : undefined,
  });

  // Delete mutation
  const deleteMutation = api.learningContent.delete.useMutation({
    onSuccess: () => {
      refetch();
    },
    onError: (error) => {
      alert(`Failed to delete content: ${error.message}`);
    },
  });

  // Toggle public status mutation
  const togglePublicMutation = api.learningContent.togglePublic.useMutation({
    onSuccess: () => {
      refetch(); // Refresh the content list
    },
    onError: (error) => {
      alert(`Failed to update visibility: ${error.message}`);
    },
  });

  const handleDelete = (id: string) => {
    if (confirm('Are you sure you want to delete this learning content? This action cannot be undone.')) {
      deleteMutation.mutate({ id });
    }
  };

  const handleShare = (content: any) => {
    setShareModalContent(content);
  };

  const handleTogglePublic = async (isPublic: boolean) => {
    if (shareModalContent) {
      await togglePublicMutation.mutateAsync({
        id: shareModalContent.id,
        isPublic
      });
      // Update the modal content to reflect the change
      setShareModalContent({ ...shareModalContent, isPublic });
    }
  };

  const filteredAndSortedContent = contentData?.content || [];

  // Check if any filters are active
  const hasActiveFilters = () => {
    return (
      filters.search ||
      filters.learningLevel !== 'all' ||
      filters.dateRange.from ||
      filters.dateRange.to ||
      filters.tags.length > 0 ||
      filters.contentTypes.length > 0 ||
      filters.readingTimeRange.min > 0 ||
      filters.readingTimeRange.max < 300 ||
      filters.isPublic !== undefined ||
      filters.completionStatus !== 'all'
    );
  };

  return (
    <ProtectedRoute>
      <DashboardLayout>
        <div className="space-y-6">
          {/* Page Header */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">My Learning</h1>
                <p className="text-gray-600 dark:text-gray-300 mt-2">
                  View and manage your AI-generated learning content.
                </p>
              </div>
              <Link href="/dashboard/learn">
                <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                  <Plus className="h-4 w-4 mr-2" />
                  Create New Content
                </Button>
              </Link>
            </div>
          </div>

          {/* Advanced Search and Filters */}
          <AdvancedSearchFilters
            filters={filters}
            onFiltersChange={setFilters}
            availableTags={[]} // TODO: Extract available tags from content
            viewMode={viewMode}
            onViewModeChange={setViewMode}
          />

          {/* Content Display */}
          {isLoading ? (
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  Loading Your Learning Content
                </h3>
                <p className="text-gray-500 dark:text-gray-400">
                  Please wait while we fetch your content...
                </p>
              </div>
            </div>
          ) : error ? (
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
              <div className="text-center py-12">
                <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  Error Loading Content
                </h3>
                <p className="text-gray-500 dark:text-gray-400 mb-4">
                  {error.message || 'An error occurred while loading your learning content.'}
                </p>
                <Button onClick={() => refetch()}>
                  Try Again
                </Button>
              </div>
            </div>
          ) : filteredAndSortedContent.length === 0 ? (
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
              <div className="text-center py-12">
                <div className="text-6xl mb-4">📚</div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  {hasActiveFilters() ? 'No Matching Content' : 'No Learning Content Yet'}
                </h3>
                <p className="text-gray-500 dark:text-gray-400 mb-6">
                  {hasActiveFilters()
                    ? 'Try adjusting your search or filters to find content.'
                    : 'You haven\'t created any learning content yet. Start by creating your first AI-powered learning experience.'
                  }
                </p>
                {!hasActiveFilters() ? (
                  <Link href="/dashboard/learn">
                    <Button className="bg-blue-600 hover:bg-blue-700">
                      <Plus className="h-4 w-4 mr-2" />
                      Create Your First Content
                    </Button>
                  </Link>
                ) : (
                  <Button
                    variant="outline"
                    onClick={() => setFilters({
                      search: '',
                      learningLevel: 'all',
                      dateRange: { from: '', to: '' },
                      tags: [],
                      contentTypes: [],
                      readingTimeRange: { min: 0, max: 300 },
                      isPublic: undefined,
                      completionStatus: 'all',
                    })}
                  >
                    Clear Filters
                  </Button>
                )}
              </div>
            </div>
          ) : (
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
              {/* Results Header */}
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                    {filteredAndSortedContent.length} Learning Content{filteredAndSortedContent.length !== 1 ? 's' : ''}
                  </h3>
                  {hasActiveFilters() && (
                    <div className="text-sm text-gray-500 dark:text-gray-400 mt-1 space-y-1">
                      {filters.search && <div>Searching for &quot;{filters.search}&quot;</div>}
                      {filters.learningLevel !== 'all' && <div>Level: {filters.learningLevel}</div>}
                      {filters.tags.length > 0 && <div>Tags: {filters.tags.join(', ')}</div>}
                      {filters.contentTypes.length > 0 && <div>Content types: {filters.contentTypes.length} selected</div>}
                      {(filters.dateRange.from || filters.dateRange.to) && (
                        <div>
                          Date: {filters.dateRange.from || 'Any'} to {filters.dateRange.to || 'Any'}
                        </div>
                      )}
                      {filters.isPublic !== undefined && (
                        <div>
                          Visibility: {filters.isPublic ? 'Public Only' : 'Private Only'}
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {filteredAndSortedContent.length > 0 && (
                  <Link href="/dashboard/learn">
                    <Button variant="outline">
                      <Plus className="h-4 w-4 mr-2" />
                      Create New
                    </Button>
                  </Link>
                )}
              </div>

              {/* Content Grid/List */}
              <div className={
                viewMode === 'grid'
                  ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
                  : 'space-y-4'
              }>
                {filteredAndSortedContent.map((content) => (
                  <LearningContentCard
                    key={content.id}
                    content={content}
                    progress={content.progress}
                    onDelete={handleDelete}
                    onShare={handleShare}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Share Modal */}
          {shareModalContent && (
            <ShareContentModal
              isOpen={!!shareModalContent}
              onClose={() => setShareModalContent(null)}
              content={{
                id: shareModalContent.id,
                title: shareModalContent.title,
                description: shareModalContent.description,
                isPublic: shareModalContent.isPublic,
              }}
              onTogglePublic={handleTogglePublic}
            />
          )}
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
}
