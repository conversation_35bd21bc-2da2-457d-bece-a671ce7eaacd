'use client';

import React, { useState } from 'react';
import { ProtectedRoute } from '../../../components/auth/protected-route';
import { DashboardLayout } from '../../../lib/components/layout';
import { Button } from '@learn-platform/shared-ui';
import {
  ArrowLeft,
  TrendingUp,
  Clock,
  Target,
  Trophy,
  BookOpen,
  Calendar,
  BarChart3,
  PieChart,
  Activity
} from 'lucide-react';
import Link from 'next/link';
import { api } from '../../../lib/trpc';

export default function ProgressPage() {
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'all'>('all');

  // Fetch overall stats
  const { data: statsData, isLoading: statsLoading } = api.learningProgress.getStats.useQuery();

  // Fetch user's learning content with progress
  const { data: contentData, isLoading: contentLoading } = api.learningContent.getMy.useQuery({
    limit: 50,
    offset: 0
  });

  const stats = statsData?.stats;
  const content = contentData?.content || [];

  // Format time spent (convert seconds to hours/minutes)
  const formatTimeSpent = (seconds: number): string => {
    if (seconds < 60) return `${seconds}s`;
    if (seconds < 3600) return `${Math.round(seconds / 60)}m`;
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.round((seconds % 3600) / 60);
    return minutes > 0 ? `${hours}h ${minutes}m` : `${hours}h`;
  };

  // Format date
  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Loading state
  if (statsLoading || contentLoading) {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Link href="/dashboard">
                  <Button variant="outline" size="sm">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Dashboard
                  </Button>
                </Link>
                <div>
                  <h1 className="text-2xl font-bold text-foreground">Learning Progress</h1>
                  <p className="text-muted-foreground">Track your learning journey and achievements</p>
                </div>
              </div>
            </div>

            {/* Loading skeleton */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="bg-card shadow rounded-lg p-6">
                  <div className="animate-pulse">
                    <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                    <div className="h-8 bg-muted rounded w-1/2"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <DashboardLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/dashboard">
                <Button variant="outline" size="sm">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Dashboard
                </Button>
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-foreground">Learning Progress</h1>
                <p className="text-muted-foreground">Track your learning journey and achievements</p>
              </div>
            </div>

            {/* Time Range Filter */}
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">View:</span>
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value as 'week' | 'month' | 'all')}
                className="border border-input rounded-md px-3 py-1 text-sm bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-ring"
              >
                <option value="week">This Week</option>
                <option value="month">This Month</option>
                <option value="all">All Time</option>
              </select>
            </div>
          </div>

          {/* Stats Overview */}
          {stats && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="bg-card shadow rounded-lg p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <BookOpen className="h-8 w-8 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-muted-foreground">Total Content</p>
                    <p className="text-2xl font-bold text-card-foreground">{stats.totalContent}</p>
                  </div>
                </div>
              </div>

              <div className="bg-card shadow rounded-lg p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Trophy className="h-8 w-8 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-muted-foreground">Completed</p>
                    <p className="text-2xl font-bold text-card-foreground">{stats.completedContent}</p>
                  </div>
                </div>
              </div>

              <div className="bg-card shadow rounded-lg p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Target className="h-8 w-8 text-orange-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-muted-foreground">Average Progress</p>
                    <p className="text-2xl font-bold text-card-foreground">{stats.averageCompletion}%</p>
                  </div>
                </div>
              </div>

              <div className="bg-card shadow rounded-lg p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Clock className="h-8 w-8 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-muted-foreground">Time Spent</p>
                    <p className="text-2xl font-bold text-card-foreground">{formatTimeSpent(stats.totalTimeSpent)}</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Progress Overview */}
          {stats && (
            <div className="bg-card shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-card-foreground mb-4">Progress Overview</h3>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm text-muted-foreground mb-1">
                    <span>Overall Completion</span>
                    <span>{stats.averageCompletion}%</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-3">
                    <div
                      className="bg-gradient-to-r from-blue-500 to-purple-600 h-3 rounded-full transition-all duration-300"
                      style={{ width: `${stats.averageCompletion}%` }}
                    ></div>
                  </div>
                </div>

                {stats.inProgressContent > 0 && (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">In Progress</span>
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                      {stats.inProgressContent} content items
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Individual Content Progress */}
          <div className="bg-card shadow rounded-lg p-6">
            <h3 className="text-lg font-medium text-card-foreground mb-4">Individual Content Progress</h3>

            {content.length === 0 ? (
              <div className="text-center py-8">
                <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground mb-4">No learning content yet</p>
                <Link href="/dashboard/learn">
                  <Button className="bg-primary hover:bg-primary/90">
                    Create Your First Content
                  </Button>
                </Link>
              </div>
            ) : (
              <div className="space-y-4">
                {content.map((item: any) => (
                  <div key={item.id} className="border border-border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex-1">
                        <h4 className="font-medium text-foreground">{item.title}</h4>
                        <p className="text-sm text-muted-foreground">{item.description}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-foreground">
                          {item.progress?.completionPercentage || 0}%
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {formatDate(item.progress?.lastAccessedAt || item.createdAt)}
                        </p>
                      </div>
                    </div>

                    <div className="w-full bg-muted rounded-full h-2 mb-2">
                      <div
                        className="bg-primary h-2 rounded-full transition-all duration-300"
                        style={{ width: `${item.progress?.completionPercentage || 0}%` }}
                      ></div>
                    </div>

                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>
                        Step {item.progress?.currentStepIndex + 1 || 1} of {item.steps?.length || 0}
                      </span>
                      <span>
                        {item.progress?.totalTimeSpent ? formatTimeSpent(item.progress.totalTimeSpent) : '0m'} spent
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
}
