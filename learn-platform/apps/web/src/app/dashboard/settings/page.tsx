'use client';

import { ProtectedRoute } from '../../../components/auth/protected-route';
import { DashboardLayout } from '../../../lib/components/layout';
import { useAuth } from '../../../components/auth/auth-provider';
import { Button } from '@learn-platform/shared-ui';
import { ThemeSelector } from '@learn-platform/theme';
import { User, Bell, Shield, Palette } from 'lucide-react';

export default function SettingsPage() {
  const { user } = useAuth();

  return (
    <ProtectedRoute>
      <DashboardLayout>
        <div className="space-y-6">
          {/* Page Header */}
          <div className="bg-card shadow rounded-lg p-6 border border-border">
            <h1 className="text-2xl font-bold text-card-foreground">Settings</h1>
            <p className="text-muted-foreground mt-2">
              Manage your account settings and preferences.
            </p>
          </div>

          {/* Settings Sections */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Profile Settings */}
            <div className="bg-card shadow rounded-lg p-6 border border-border">
              <div className="flex items-center mb-4">
                <User className="h-5 w-5 text-muted-foreground mr-2" />
                <h3 className="text-lg font-medium text-card-foreground">Profile</h3>
              </div>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-foreground">Name</label>
                  <input
                    type="text"
                    value={user?.name || ''}
                    disabled
                    className="mt-1 block w-full px-3 py-2 border border-input rounded-md bg-muted text-muted-foreground"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-foreground">Email</label>
                  <input
                    type="email"
                    value={user?.email || ''}
                    disabled
                    className="mt-1 block w-full px-3 py-2 border border-input rounded-md bg-muted text-muted-foreground"
                  />
                </div>
                <Button variant="outline" disabled>
                  Update Profile (Coming Soon)
                </Button>
              </div>
            </div>

            {/* Notification Settings */}
            <div className="bg-card shadow rounded-lg p-6 border border-border">
              <div className="flex items-center mb-4">
                <Bell className="h-5 w-5 text-muted-foreground mr-2" />
                <h3 className="text-lg font-medium text-card-foreground">Notifications</h3>
              </div>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-foreground">Email Notifications</p>
                    <p className="text-xs text-muted-foreground">Receive updates about your learning progress</p>
                  </div>
                  <input type="checkbox" className="rounded" disabled />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-foreground">Learning Reminders</p>
                    <p className="text-xs text-muted-foreground">Get reminded to continue your learning</p>
                  </div>
                  <input type="checkbox" className="rounded" disabled />
                </div>
                <Button variant="outline" disabled>
                  Save Preferences (Coming Soon)
                </Button>
              </div>
            </div>

            {/* Privacy Settings */}
            <div className="bg-card shadow rounded-lg p-6 border border-border">
              <div className="flex items-center mb-4">
                <Shield className="h-5 w-5 text-muted-foreground mr-2" />
                <h3 className="text-lg font-medium text-card-foreground">Privacy</h3>
              </div>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-foreground">Public Profile</p>
                    <p className="text-xs text-muted-foreground">Allow others to see your learning progress</p>
                  </div>
                  <input type="checkbox" className="rounded" disabled />
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-foreground">Share Learning Content</p>
                    <p className="text-xs text-muted-foreground">Allow sharing of your generated content</p>
                  </div>
                  <input type="checkbox" className="rounded" disabled />
                </div>
                <Button variant="outline" disabled>
                  Update Privacy (Coming Soon)
                </Button>
              </div>
            </div>

            {/* Appearance Settings */}
            <div className="bg-card shadow rounded-lg p-6 border border-border">
              <div className="flex items-center mb-4">
                <Palette className="h-5 w-5 text-muted-foreground mr-2" />
                <h3 className="text-lg font-medium text-card-foreground">Appearance</h3>
              </div>
              <div className="space-y-6">
                <ThemeSelector />
                <div>
                  <label className="block text-sm font-medium text-foreground mb-2">Language</label>
                  <select disabled className="block w-full px-3 py-2 border border-input rounded-md bg-muted text-muted-foreground">
                    <option>English</option>
                    <option>Spanish</option>
                    <option>French</option>
                  </select>
                  <p className="text-xs text-muted-foreground mt-1">Language selection coming soon</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
}
