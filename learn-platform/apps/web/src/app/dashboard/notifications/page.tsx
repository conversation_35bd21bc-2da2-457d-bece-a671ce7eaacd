'use client';

import { useState } from 'react';
import { ProtectedRoute } from '../../../components/auth/protected-route';
import { DashboardLayout } from '../../../lib/components/layout';
import { <PERSON><PERSON>, Card, CardHeader, CardTitle, CardContent } from '@learn-platform/shared-ui';
import { api } from '../../../lib/trpc';
import { Bell, BellOff, Calendar, Clock, CheckCircle, Circle, Megaphone, Settings as SettingsIcon } from 'lucide-react';

export default function NotificationsPage() {
  const [activeTab, setActiveTab] = useState<'system' | 'releases'>('releases');

  // Fetch releases data
  const { data: releasesData, isLoading, refetch } = api.releases.getPublished.useQuery();
  const releases = releasesData?.releases || [];

  // Mark as read mutation
  const markAsReadMutation = api.releases.markAsRead.useMutation({
    onSuccess: () => {
      refetch();
    },
  });

  const handleMarkAsRead = async (releaseId: string) => {
    try {
      await markAsReadMutation.mutateAsync({ releaseId });
    } catch (error) {
      console.error('Error marking release as read:', error);
    }
  };

  const formatDate = (date: string | Date) => {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatRelativeTime = (date: string | Date) => {
    const releaseDate = new Date(date);
    const now = new Date();
    const diffInMs = now.getTime() - releaseDate.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) {
      return 'Today';
    } else if (diffInDays === 1) {
      return 'Yesterday';
    } else if (diffInDays < 7) {
      return `${diffInDays} days ago`;
    } else if (diffInDays < 30) {
      const weeks = Math.floor(diffInDays / 7);
      return weeks === 1 ? '1 week ago' : `${weeks} weeks ago`;
    } else {
      const months = Math.floor(diffInDays / 30);
      return months === 1 ? '1 month ago' : `${months} months ago`;
    }
  };

  const isNewRelease = (publishedAt: string | Date) => {
    const publishedDate = new Date(publishedAt);
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    return publishedDate > sevenDaysAgo;
  };

  const unreadCount = releases.filter((r: { isReadByUser: boolean }) => !r.isReadByUser).length;

  const tabs = [
    {
      id: 'system' as const,
      name: 'System Notifications',
      icon: SettingsIcon,
      count: 0,
    },
    {
      id: 'releases' as const,
      name: 'Release Updates',
      icon: Megaphone,
      count: unreadCount,
    },
  ];

  if (isLoading) {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="space-y-6">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
              <div className="h-12 bg-gray-200 dark:bg-gray-700 rounded mb-6"></div>
              <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="h-24 bg-gray-200 dark:bg-gray-700 rounded"></div>
                ))}
              </div>
            </div>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <DashboardLayout>
        <div className="space-y-6">
          {/* Page Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Notifications</h1>
              <p className="text-gray-600 dark:text-gray-300">Stay updated with system announcements and releases</p>
            </div>
            <div className="flex items-center space-x-2">
              <div className="flex items-center space-x-1 text-sm text-gray-500 dark:text-gray-400">
                <Bell className="w-4 h-4" />
                <span>{unreadCount} unread</span>
              </div>
            </div>
          </div>

          {/* Tabs */}
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                const isActive = activeTab === tab.id;
                
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`
                      flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors
                      ${isActive
                        ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                        : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
                      }
                    `}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{tab.name}</span>
                    {tab.count > 0 && (
                      <span className="bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300 text-xs font-medium px-2 py-0.5 rounded-full">
                        {tab.count}
                      </span>
                    )}
                  </button>
                );
              })}
            </nav>
          </div>

          {/* Tab Content */}
          {activeTab === 'system' && (
            <Card className="dark:bg-gray-800 dark:border-gray-700">
              <CardContent className="p-12">
                <div className="text-center">
                  <SettingsIcon className="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No system notifications</h3>
                  <p className="text-gray-500 dark:text-gray-400">
                    System notifications and announcements will appear here when available.
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {activeTab === 'releases' && (
            <div className="space-y-4">
              {releases.length === 0 ? (
                <Card className="dark:bg-gray-800 dark:border-gray-700">
                  <CardContent className="p-12">
                    <div className="text-center">
                      <Megaphone className="w-12 h-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">No releases yet</h3>
                      <p className="text-gray-500 dark:text-gray-400">
                        Release announcements and updates will appear here when available.
                      </p>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                releases.map((release: { 
                  id: string;
                  version: string;
                  description: string;
                  publishedAt: string | Date;
                  isReadByUser: boolean;
                }) => (
                  <Card 
                    key={release.id} 
                    className={`dark:bg-gray-800 dark:border-gray-700 ${
                      !release.isReadByUser 
                        ? 'ring-2 ring-blue-200 dark:ring-blue-800/50 bg-blue-50/30 dark:bg-blue-900/10' 
                        : ''
                    }`}
                  >
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                              {release.version}
                            </h3>
                            {isNewRelease(release.publishedAt!) && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300">
                                New
                              </span>
                            )}
                            {!release.isReadByUser && (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300">
                                Unread
                              </span>
                            )}
                          </div>
                          <div className="prose prose-sm max-w-none mb-4">
                            <p className="text-gray-600 dark:text-gray-300 whitespace-pre-wrap">
                              {release.description}
                            </p>
                          </div>
                          <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                            <div className="flex items-center">
                              <Calendar className="w-4 h-4 mr-1" />
                              Released {formatDate(release.publishedAt!)}
                            </div>
                            <div className="flex items-center">
                              <Clock className="w-4 h-4 mr-1" />
                              {formatRelativeTime(release.publishedAt!)}
                            </div>
                          </div>
                        </div>
                        <div className="ml-4 flex-shrink-0">
                          {release.isReadByUser ? (
                            <div className="flex items-center text-green-600 dark:text-green-400">
                              <CheckCircle className="w-5 h-5 mr-1" />
                              <span className="text-sm">Read</span>
                            </div>
                          ) : (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleMarkAsRead(release.id)}
                              disabled={markAsReadMutation.isPending}
                              className="flex items-center space-x-1 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
                            >
                              <Circle className="w-4 h-4" />
                              <span>Mark as read</span>
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          )}
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
}
