'use client';

import { useState, useEffect, useRef } from 'react';
import { ProtectedRoute } from '../../../components/auth/protected-route';
import { DashboardLayout } from '../../../lib/components/layout';
import { Button } from '@learn-platform/shared-ui';
import { api } from '../../../lib/trpc';
import {
  Brain,
  Clock,
  Target,
  Filter,
  Search,
  Play,
  BookOpen,
  TrendingUp,
  Calendar,
  User,
  CheckCircle,
  RotateCcw,
  AlertCircle,
  X,
  Grid,
  List,
} from 'lucide-react';
import Link from 'next/link';
import { DifficultyIndicator } from '../../../lib/components/learn/DifficultyIndicator';

interface QuizFilters {
  search: string;
  difficulty: 'all' | 'easy' | 'medium' | 'hard';
  completionStatus: 'all' | 'completed' | 'in-progress' | 'not-started';
  learningContentId?: string;
}

export default function QuizzesPage() {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filters, setFilters] = useState<QuizFilters>({
    search: '',
    difficulty: 'all',
    completionStatus: 'all',
  });

  // Debounced search state for API calls
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Debounce search input
  useEffect(() => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    debounceTimeoutRef.current = setTimeout(() => {
      setDebouncedSearch(filters.search);
    }, 300); // 300ms debounce delay

    return () => {
      if (debounceTimeoutRef.current) {
        clearTimeout(debounceTimeoutRef.current);
      }
    };
  }, [filters.search]);

  // Fetch quizzes using tRPC
  const {
    data: quizzesData,
    isLoading,
    error,
    refetch,
  } = api.quiz.getAll.useQuery({
    difficulty: filters.difficulty !== 'all' ? filters.difficulty : undefined,
    search: debouncedSearch || undefined,
    limit: 20,
    offset: 0,
    includePublic: true,
  });

  const quizzes = quizzesData?.quizzes || [];

  // Fetch user's quiz attempts to show progress
  const { data: attemptsData } = api.quiz.getMyAttempts.useQuery({
    limit: 50, // Get attempts to match with quizzes (max allowed is 50)
    offset: 0,
  });

  const attempts = attemptsData?.attempts || [];

  // Create a map of current attempts for quick lookup
  const currentAttemptsByQuizId = attempts.reduce<Record<string, any>>(
    (
      acc: Record<string, any>,
      attempt: { isCompleted: boolean; quizId: string }
    ) => {
      if (!attempt.isCompleted) {
        acc[attempt.quizId] = attempt;
      }
      return acc;
    },
    {} as Record<string, any>
  );

  // Create a map of completed attempts for quick lookup
  const completedAttemptsByQuizId = attempts.reduce<Record<string, any>>(
    (
      acc: Record<string, any>,
      attempt: {
        isCompleted: boolean;
        quizId: string;
        score?: { percentage: number };
      }
    ) => {
      if (attempt.isCompleted) {
        if (
          !acc[attempt.quizId] ||
          (attempt.score?.percentage || 0) >
            (acc[attempt.quizId].score?.percentage || 0)
        ) {
          acc[attempt.quizId] = attempt;
        }
      }
      return acc;
    },
    {} as Record<string, any>
  );

  const handleFilterChange = (key: keyof QuizFilters, value: string) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleClearSearch = () => {
    setFilters((prev) => ({
      ...prev,
      search: '',
    }));
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900/30';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900/30';
      case 'hard':
        return 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900/30';
      default:
        return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-700';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  // Get quiz progress information
  const getQuizProgress = (quizId: string) => {
    const currentAttempt = currentAttemptsByQuizId[quizId];
    const completedAttempt = completedAttemptsByQuizId[quizId];
    const allQuizAttempts = attempts.filter(
      (attempt: { quizId: string }) => attempt.quizId === quizId
    );

    if (currentAttempt) {
      // There's an in-progress attempt
      const answersCount = currentAttempt.answers
        ? currentAttempt.answers.length
        : 0;
      const totalQuestions = currentAttempt.quizQuestions
        ? currentAttempt.quizQuestions.length
        : 0;
      return {
        status: 'in-progress',
        attempts: allQuizAttempts.length,
        questionsAnswered: answersCount,
        totalQuestions,
        timeSpent: currentAttempt.totalTimeSpent || 0,
        lastActiveAt: currentAttempt.startedAt,
      };
    }

    if (completedAttempt) {
      // There's a completed attempt but no current one
      return {
        status: 'completed',
        attempts: allQuizAttempts.length,
        score: completedAttempt.score?.percentage || 0,
        completedAt: completedAttempt.completedAt,
      };
    }

    // No attempts found
    return { status: 'not-started', attempts: 0 };
  };

  // Filter quizzes based on completion status
  const filteredQuizzes = quizzes.filter((quiz: { id: string }) => {
    if (filters.completionStatus === 'all') return true;

    const progress = getQuizProgress(quiz.id);
    return progress.status === filters.completionStatus;
  });

  // Get progress indicator component
  const getProgressIndicator = (
    progress: ReturnType<typeof getQuizProgress>
  ) => {
    switch (progress.status) {
      case 'completed':
        return (
          <div className="flex items-center space-x-2 text-green-600 dark:text-green-400">
            <CheckCircle className="h-4 w-4" />
            <span className="text-sm font-medium">
              Score: {progress.score}%
            </span>
          </div>
        );
      case 'in-progress':
        return (
          <div className="flex items-center space-x-2 text-blue-600 dark:text-blue-400">
            <RotateCcw className="h-4 w-4" />
            <span className="text-sm font-medium">
              In Progress
              {progress.questionsAnswered > 0 &&
                progress.totalQuestions > 0 && (
                  <span className="text-xs text-blue-500 dark:text-blue-400 ml-2">
                    ({progress.questionsAnswered}/{progress.totalQuestions})
                  </span>
                )}
            </span>
          </div>
        );
      default:
        return (
          <div className="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
            <AlertCircle className="h-4 w-4" />
            <span className="text-sm font-medium">Not Started</span>
          </div>
        );
    }
  };

  // Get button properties based on progress
  const getButtonProps = (progress: ReturnType<typeof getQuizProgress>) => {
    switch (progress.status) {
      case 'completed':
        return {
          text: 'Retake Quiz',
          variant: 'outline' as const,
        };
      case 'in-progress':
        return {
          text: 'Continue Quiz',
          variant: 'default' as const,
        };
      default:
        return {
          text: 'Take Quiz',
          variant: 'default' as const,
        };
    }
  };

  if (error) {
    return (
      <ProtectedRoute>
        <DashboardLayout>
          <div className="text-center py-12">
            <div className="text-red-600 dark:text-red-400 mb-4">
              <Brain className="h-12 w-12 mx-auto mb-2" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                Failed to Load Quizzes
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                {error.message}
              </p>
            </div>
            <div className="space-x-4">
              <Button onClick={() => refetch()} disabled={isLoading}>
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Retrying...
                  </>
                ) : (
                  'Try Again'
                )}
              </Button>
              <Link href="/dashboard/my-learning">
                <Button variant="outline">
                  <BookOpen className="h-4 w-4 mr-2" />
                  Browse Learning Content
                </Button>
              </Link>
            </div>
          </div>
        </DashboardLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <DashboardLayout>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Quizzes
              </h1>
              <p className="text-gray-600 dark:text-gray-300">
                Test your knowledge with interactive quizzes
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <Link href="/dashboard/my-learning">
                <Button variant="outline">
                  <BookOpen className="h-4 w-4 mr-2" />
                  Browse Learning Content
                </Button>
              </Link>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <div className="flex items-center space-x-4 mb-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500" />
                  <input
                    type="text"
                    placeholder="Search quizzes by title or description..."
                    value={filters.search}
                    onChange={(e) =>
                      handleFilterChange('search', e.target.value)
                    }
                    className="w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                  />
                  {filters.search && (
                    <button
                      onClick={handleClearSearch}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 hover:text-gray-600 dark:hover:text-gray-300"
                      title="Clear search"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  )}
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Filter className="h-5 w-5 text-gray-400 dark:text-gray-500" />
                <select
                  value={filters.difficulty}
                  onChange={(e) =>
                    handleFilterChange('difficulty', e.target.value)
                  }
                  className="border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                >
                  <option value="all">All Difficulties</option>
                  <option value="easy">Easy</option>
                  <option value="medium">Medium</option>
                  <option value="hard">Hard</option>
                </select>
              </div>
            </div>

            {/* Completion Status Filter and View Mode Toggle */}
            <div className="flex items-center justify-between flex-wrap gap-4">
              <div className="flex items-center gap-4">
                {/* Completion Status Filter */}
                <div className="flex gap-2">
                  <button
                    onClick={() =>
                      setFilters((prev) => ({
                        ...prev,
                        completionStatus: 'all',
                      }))
                    }
                    className={`px-3 py-1.5 text-sm font-medium rounded-md transition-colors ${
                      filters.completionStatus === 'all'
                        ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300'
                        : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200'
                    }`}
                  >
                    All
                  </button>
                  <button
                    onClick={() =>
                      setFilters((prev) => ({
                        ...prev,
                        completionStatus: 'completed',
                      }))
                    }
                    className={`px-3 py-1.5 text-sm font-medium rounded-md transition-colors ${
                      filters.completionStatus === 'completed'
                        ? 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-300'
                        : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200'
                    }`}
                  >
                    Completed
                  </button>
                  <button
                    onClick={() =>
                      setFilters((prev) => ({
                        ...prev,
                        completionStatus: 'in-progress',
                      }))
                    }
                    className={`px-3 py-1.5 text-sm font-medium rounded-md transition-colors ${
                      filters.completionStatus === 'in-progress'
                        ? 'bg-orange-100 text-orange-700 dark:bg-orange-900/20 dark:text-orange-300'
                        : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200'
                    }`}
                  >
                    In Progress
                  </button>
                  <button
                    onClick={() =>
                      setFilters((prev) => ({
                        ...prev,
                        completionStatus: 'not-started',
                      }))
                    }
                    className={`px-3 py-1.5 text-sm font-medium rounded-md transition-colors ${
                      filters.completionStatus === 'not-started'
                        ? 'bg-gray-100 text-gray-700 dark:bg-gray-900/20 dark:text-gray-300'
                        : 'text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200'
                    }`}
                  >
                    Not Started
                  </button>
                </div>

                {/* View Mode Toggle */}
                <div className="flex border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`px-3 py-2 ${
                      viewMode === 'grid'
                        ? 'bg-blue-500 text-white'
                        : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
                    }`}
                  >
                    <Grid className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`px-3 py-2 ${
                      viewMode === 'list'
                        ? 'bg-blue-500 text-white'
                        : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600'
                    }`}
                  >
                    <List className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Loading State */}
          {isLoading && (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 dark:border-blue-400 mx-auto mb-4"></div>
              <p className="text-gray-600 dark:text-gray-300">
                Loading quizzes...
              </p>
            </div>
          )}

          {/* Quiz Grid/List */}
          {!isLoading && (
            <div
              className={`${
                viewMode === 'grid'
                  ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
                  : 'space-y-4'
              }`}
            >
              {filteredQuizzes.length === 0 ? (
                <div className="col-span-full text-center py-12">
                  <Brain className="h-12 w-12 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    No quizzes found
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    {filters.search ||
                    filters.difficulty !== 'all' ||
                    filters.completionStatus !== 'all'
                      ? 'Try adjusting your filters or search terms.'
                      : 'Create some learning content first, then generate quizzes from it.'}
                  </p>
                  <Link href="/dashboard/my-learning">
                    <Button>
                      <BookOpen className="h-4 w-4 mr-2" />
                      Browse Learning Content
                    </Button>
                  </Link>
                </div>
              ) : (
                filteredQuizzes.map(
                  (quiz: {
                    id: string;
                    title: string;
                    description?: string;
                    difficulty: string;
                    questionCount: number;
                    estimatedDuration: number;
                    totalPoints: number;
                    createdAt: string;
                  }) => {
                    const progress = getQuizProgress(quiz.id);
                    const buttonProps = getButtonProps(progress);

                    return (
                      <div
                        key={quiz.id}
                        className={`bg-white dark:bg-gray-800 shadow rounded-lg hover:shadow-md transition-all duration-200 border border-gray-200 dark:border-gray-700 ${
                          viewMode === 'list'
                            ? 'flex items-center p-4'
                            : 'p-6 flex flex-col h-full'
                        }`}
                      >
                        {/* Quiz Content */}
                        <div className={viewMode === 'list' ? 'flex-1' : 'flex-1'}>
                          {/* Quiz Header */}
                          <div className={viewMode === 'list' ? 'mb-0' : 'mb-4'}>
                            <div className={`flex items-start justify-between mb-2 ${
                              viewMode === 'list' ? 'mb-1' : ''
                            }`}>
                              <h3 className={`font-semibold text-gray-900 dark:text-white ${
                                viewMode === 'list'
                                  ? 'text-base line-clamp-1'
                                  : 'text-lg line-clamp-2 min-h-[3.5rem] flex items-start'
                              }`}>
                                {quiz.title}
                              </h3>
                              {viewMode === 'grid' && (
                                <DifficultyIndicator difficulty={quiz.difficulty as "easy" | "medium" | "hard"} size="sm" />
                              )}
                            </div>
                            {quiz.description && (
                              <p className={`text-gray-600 dark:text-gray-300 text-sm ${
                                viewMode === 'list'
                                  ? 'line-clamp-1 mb-3'
                                  : 'line-clamp-2 min-h-[2.5rem] flex items-start'
                              }`}>
                                {quiz.description}
                              </p>
                            )}

                            {/* List View: Quiz Information Below Description */}
                            {viewMode === 'list' && (
                              <div className="space-y-2">
                                {/* Quiz Stats Row */}
                                <div className="flex items-center space-x-6 text-sm text-gray-600 dark:text-gray-300">
                                  <div className="flex items-center space-x-1">
                                    <Target className="h-4 w-4" />
                                    <span>{quiz.questionCount} questions</span>
                                  </div>
                                  <div className="flex items-center space-x-1">
                                    <Clock className="h-4 w-4" />
                                    <span>{quiz.estimatedDuration} min</span>
                                  </div>
                                  <div className="flex items-center space-x-1">
                                    <Calendar className="h-4 w-4" />
                                    <span>{formatDate(quiz.createdAt)}</span>
                                  </div>
                                  <div className="flex items-center space-x-2">
                                    <DifficultyIndicator difficulty={quiz.difficulty as "easy" | "medium" | "hard"} size="sm" />
                                    <span className="text-xs font-medium capitalize">{quiz.difficulty}</span>
                                  </div>
                                </div>

                                {/* Progress and Attempts Row */}
                                <div className="flex items-center space-x-4">
                                  {getProgressIndicator(progress)}
                                  {progress.attempts > 0 && (
                                    <div className="text-xs text-gray-500 dark:text-gray-400">
                                      {progress.attempts} attempt{progress.attempts !== 1 ? 's' : ''}
                                      {progress.status === 'completed' && progress.score !== undefined && (
                                        <span className="ml-2 font-medium">
                                          Best: {progress.score}%
                                        </span>
                                      )}
                                    </div>
                                  )}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Grid View: Full Layout (unchanged) */}
                        {viewMode === 'grid' && (
                          <>
                            {/* Progress Indicator */}
                            <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                              <div className="flex items-center justify-between">
                                {getProgressIndicator(progress)}
                                {progress.attempts > 0 && (
                                  <div className="text-xs text-gray-500 dark:text-gray-400">
                                    {progress.attempts} attempt
                                    {progress.attempts !== 1 ? 's' : ''}
                                    {progress.status === 'completed' &&
                                      progress.score !== undefined && (
                                        <span className="ml-2 font-medium">
                                          Best: {progress.score}%
                                        </span>
                                      )}
                                  </div>
                                )}
                              </div>
                            </div>

                            {/* Quiz Stats */}
                            <div className="grid grid-cols-2 gap-4 mb-4 text-sm text-gray-600 dark:text-gray-300">
                              <div className="flex items-center">
                                <Target className="h-4 w-4 mr-1" />
                                <span>{quiz.questionCount} questions</span>
                              </div>
                              <div className="flex items-center">
                                <Clock className="h-4 w-4 mr-1" />
                                <span>{quiz.estimatedDuration} min</span>
                              </div>
                              <div className="flex items-center">
                                <TrendingUp className="h-4 w-4 mr-1" />
                                <span>{quiz.totalPoints} points</span>
                              </div>
                              <div className="flex items-center">
                                <Calendar className="h-4 w-4 mr-1" />
                                <span>{formatDate(quiz.createdAt)}</span>
                              </div>
                            </div>
                          </>
                        )}

                        {/* Action Button */}
                        <div className={viewMode === 'list' ? 'flex-shrink-0 ml-4' : ''}>
                          <Link
                            href={`/dashboard/quiz/${quiz.id}${
                              buttonProps.text === 'Retake Quiz'
                                ? '?retake=true'
                                : ''
                            }`}
                          >
                            <Button
                              className={viewMode === 'list' ? 'w-32' : 'w-full'}
                              variant={buttonProps.variant}
                              disabled={isLoading}
                              size={viewMode === 'list' ? 'sm' : 'default'}
                            >
                              <Play className="h-4 w-4 mr-2" />
                              {buttonProps.text}
                            </Button>
                          </Link>
                        </div>
                      </div>
                    );
                  }
                )
              )}
            </div>
          )}

          {/* Stats Summary */}
          {!isLoading && filteredQuizzes.length > 0 && (
            <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
              <div className="flex items-center justify-between text-sm">
                <span className="text-blue-700 dark:text-blue-300">
                  Showing {filteredQuizzes.length} quiz
                  {filteredQuizzes.length !== 1 ? 'es' : ''}
                </span>
                <span className="text-blue-600 dark:text-blue-400">
                  Total estimated time:{' '}
                  {filteredQuizzes.reduce(
                    (acc: number, quiz: { estimatedDuration: number }) =>
                      acc + quiz.estimatedDuration,
                    0
                  )}{' '}
                  minutes
                </span>
              </div>
            </div>
          )}
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
}
