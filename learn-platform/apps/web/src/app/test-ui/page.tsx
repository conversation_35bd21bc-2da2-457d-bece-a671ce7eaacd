'use client';

import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardT<PERSON>le,
  CardDescription,
  CardContent,
  Input
} from '@learn-platform/shared-ui';
import { useState } from 'react';
import Link from 'next/link';

export default function TestUIPage() {
  const [inputValue, setInputValue] = useState('');

  return (
    <div className="min-h-screen bg-background p-8">
      <div className="container-narrow">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-foreground mb-4">
            Web App - Shared UI Components Test
          </h1>
          <p className="text-muted-foreground text-lg">
            Testing TailwindCSS integration with shared UI components from @learn-platform/shared-ui
          </p>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {/* Button Variants Test */}
          <Card className="p-6">
            <CardHeader>
              <CardTitle>Button Components</CardTitle>
              <CardDescription>
                Testing different button variants and sizes
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Button variant="default" size="sm">
                  Default Small
                </Button>
                <Button variant="secondary" size="default">
                  Secondary Default
                </Button>
                <Button variant="destructive" size="lg">
                  Destructive Large
                </Button>
                <Button variant="outline" size="default">
                  Outline
                </Button>
                <Button variant="ghost" size="default">
                  Ghost
                </Button>
                <Button variant="link" size="default">
                  Link
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Input Components Test */}
          <Card className="p-6">
            <CardHeader>
              <CardTitle>Input Components</CardTitle>
              <CardDescription>
                Testing input components with Tailwind styling
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label htmlFor="test-input" className="text-sm font-medium">
                  Test Input
                </label>
                <Input
                  id="test-input"
                  type="text"
                  placeholder="Enter some text..."
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="email-input" className="text-sm font-medium">
                  Email Input
                </label>
                <Input
                  id="email-input"
                  type="email"
                  placeholder="<EMAIL>"
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="password-input" className="text-sm font-medium">
                  Password Input
                </label>
                <Input
                  id="password-input"
                  type="password"
                  placeholder="••••••••"
                />
              </div>
            </CardContent>
          </Card>

          {/* Tailwind Utilities Test */}
          <Card className="p-6">
            <CardHeader>
              <CardTitle>Tailwind Utilities</CardTitle>
              <CardDescription>
                Testing various Tailwind CSS utilities
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="p-4 bg-primary text-primary-foreground rounded-lg">
                Primary Background
              </div>
              <div className="p-4 bg-secondary text-secondary-foreground rounded-lg">
                Secondary Background
              </div>
              <div className="p-4 bg-muted text-muted-foreground rounded-lg">
                Muted Background
              </div>
              <div className="p-4 bg-accent text-accent-foreground rounded-lg">
                Accent Background
              </div>
              <div className="flex space-x-2">
                <div className="w-4 h-4 bg-brand-500 rounded"></div>
                <div className="w-4 h-4 bg-success-500 rounded"></div>
                <div className="w-4 h-4 bg-warning-500 rounded"></div>
                <div className="w-4 h-4 bg-error-500 rounded"></div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Interactive Test */}
        <Card className="mt-8 p-6">
          <CardHeader>
            <CardTitle>Interactive Test</CardTitle>
            <CardDescription>
              Testing component interactions and state management
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
              <Input
                placeholder="Type something..."
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                className="flex-1"
              />
              <Button
                onClick={() => setInputValue('')}
                variant="outline"
              >
                Clear
              </Button>
            </div>
            {inputValue && (
              <div className="p-4 bg-muted rounded-lg">
                <p className="text-sm text-muted-foreground">You typed:</p>
                <p className="font-medium">{inputValue}</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className="mt-8 flex justify-center">
          <Button asChild variant="outline">
            <Link href="/">← Back to Home</Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
