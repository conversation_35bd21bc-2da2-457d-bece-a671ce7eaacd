/**
 * Tests for the authentication API route handler
 */

import { GET, POST } from './route';

// Enhanced Request and Response mocks for tests
const originalRequest = global.Request;
const originalResponse = global.Response;

if (originalRequest) {
  global.Request = class extends originalRequest {
    clone() {
      return new originalRequest(this.url, {
        method: this.method,
        headers: this.headers,
        body: this.body,
      });
    }

    async text() {
      if (this.body && typeof this.body === 'string') {
        return this.body;
      }
      return '';
    }
  } as any;
}

if (originalResponse) {
  global.Response = class extends originalResponse {
    clone() {
      return new originalResponse(this.body, {
        status: this.status,
        statusText: this.statusText,
        headers: this.headers,
      });
    }

    async text() {
      if (this.body && typeof this.body === 'string') {
        return this.body;
      }
      return '';
    }
  } as any;
}

/**
 * Get environment variable with fallback for tests
 */
function getTestEnvVar(key: string, fallback: string): string {
  return process.env[key] || fallback;
}

// Test credentials using environment variables for consistency
const TEST_CREDENTIALS = {
  email: getTestEnvVar('TEST_USER_EMAIL', '<EMAIL>'),
  password: getTestEnvVar('TEST_USER_PASSWORD', 'TestPassword123!'),
};

// Mock the auth module
jest.mock('@learn-platform/auth', () => ({
  auth: {
    handler: jest.fn(),
  },
  traceBetterAuthFlow: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    timing: jest.fn(),
  })),
}));

describe('Auth API Route', () => {
  let mockHandler: jest.MockedFunction<(request: Request) => Promise<Response>>;
  let consoleSpy: jest.SpyInstance;

  beforeEach(() => {
    jest.clearAllMocks();

    // Suppress console logs during tests for cleaner output
    consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {
      // Intentionally suppress console.log during tests
    });

    // Get the mocked handler
    const authModule = jest.requireMock('@learn-platform/auth');
    mockHandler = authModule.auth.handler;
  });

  afterEach(() => {
    consoleSpy.mockRestore();
  });

  describe('GET handler', () => {
    it('should call auth.handler with the request', async () => {
      const mockRequest = new Request('http://localhost/api/auth/session');
      const mockResponse = new Response('{"session": null}');

      mockHandler.mockResolvedValue(mockResponse);

      const result = await GET(mockRequest);

      expect(mockHandler).toHaveBeenCalledWith(mockRequest);
      expect(result).toBe(mockResponse);
    });

    it('should handle auth handler errors', async () => {
      const mockRequest = new Request('http://localhost/api/auth/session');
      const error = new Error('Auth handler failed');

      mockHandler.mockRejectedValue(error);

      await expect(GET(mockRequest)).rejects.toThrow('Auth handler failed');
    });
  });

  describe('POST handler', () => {
    it('should call auth.handler with the request', async () => {
      const mockRequest = new Request('http://localhost/api/auth/sign-in', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: TEST_CREDENTIALS.email,
          password: TEST_CREDENTIALS.password,
        }),
      });
      const mockResponse = new Response('{"success": true}');

      mockHandler.mockResolvedValue(mockResponse);

      const result = await POST(mockRequest);

      expect(mockHandler).toHaveBeenCalledWith(mockRequest);
      expect(result).toBe(mockResponse);
    });

    it('should handle POST requests with different content types', async () => {
      const mockRequest = new Request('http://localhost/api/auth/sign-up', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `email=${TEST_CREDENTIALS.email}&password=${TEST_CREDENTIALS.password}`,
      });
      const mockResponse = new Response('{"success": true}');

      mockHandler.mockResolvedValue(mockResponse);

      const result = await POST(mockRequest);

      expect(mockHandler).toHaveBeenCalledWith(mockRequest);
      expect(result).toBe(mockResponse);
    });

    it('should handle auth handler errors in POST', async () => {
      const mockRequest = new Request('http://localhost/api/auth/sign-in', {
        method: 'POST',
        body: JSON.stringify({ email: TEST_CREDENTIALS.email }),
      });
      const error = new Error('Invalid credentials');

      mockHandler.mockRejectedValue(error);

      await expect(POST(mockRequest)).rejects.toThrow('Invalid credentials');
    });
  });

  describe('Request handling', () => {
    it('should preserve request headers', async () => {
      const mockRequest = new Request('http://localhost/api/auth/session', {
        headers: {
          'Authorization': 'Bearer token123',
          'User-Agent': 'Test Agent',
        },
      });
      const mockResponse = new Response('{"session": null}');

      mockHandler.mockResolvedValue(mockResponse);

      await GET(mockRequest);

      expect(mockHandler).toHaveBeenCalledWith(mockRequest);

      // Verify the request object passed to handler has the correct headers
      const passedRequest = mockHandler.mock.calls[0][0];
      expect(passedRequest.headers.get('authorization')).toBe('Bearer token123');
      expect(passedRequest.headers.get('user-agent')).toBe('Test Agent');
    });

    it('should preserve request URL and method', async () => {
      const testUrl = 'http://localhost/api/auth/callback/google';
      const mockRequest = new Request(testUrl, { method: 'GET' });
      const mockResponse = new Response('{"success": true}');

      mockHandler.mockResolvedValue(mockResponse);

      await GET(mockRequest);

      const passedRequest = mockHandler.mock.calls[0][0];
      expect(passedRequest.url).toBe(testUrl);
      expect(passedRequest.method).toBe('GET');
    });
  });
});
