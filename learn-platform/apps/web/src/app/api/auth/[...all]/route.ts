/**
 * Better-auth API route handler for Next.js
 * This handles all authentication endpoints with comprehensive debug logging
 */

// eslint-disable-next-line @nx/enforce-module-boundaries
import { auth, traceBetterAuthFlow } from '@learn-platform/auth';

/**
 * Sanitize request body for logging (remove sensitive data)
 */
function sanitizeRequestBody(body: any): any {
  if (!body || typeof body !== 'object') return body;

  const sanitized = { ...body };
  const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth', 'credential'];

  for (const field of sensitiveFields) {
    if (sanitized[field]) {
      sanitized[field] = '[REDACTED]';
    }
  }

  return sanitized;
}

/**
 * Sanitize headers for logging (remove sensitive data)
 */
function sanitizeHeaders(headers: Headers): Record<string, string> {
  const sanitized: Record<string, string> = {};
  const sensitiveHeaders = ['authorization', 'cookie', 'x-api-key', 'x-auth-token'];

  headers.forEach((value, key) => {
    const lowerKey = key.toLowerCase();
    if (sensitiveHeaders.includes(lowerKey)) {
      sanitized[key] = '[REDACTED]';
    } else {
      sanitized[key] = value;
    }
  });

  return sanitized;
}

/**
 * Extract endpoint path from request URL
 */
function extractEndpoint(request: Request): string {
  const url = new URL(request.url);
  const pathParts = url.pathname.split('/');
  const authIndex = pathParts.indexOf('auth');

  if (authIndex !== -1 && authIndex < pathParts.length - 1) {
    return '/' + pathParts.slice(authIndex + 1).join('/');
  }

  return url.pathname;
}

/**
 * Enhanced auth handler with comprehensive logging
 */
async function handleAuthRequest(request: Request, method: string) {
  const startTime = Date.now();
  const requestId = Math.random().toString(36).substring(2, 15);
  const url = new URL(request.url);
  const endpoint = extractEndpoint(request);

  console.log(`\n🔐 [AUTH-${requestId}] ===== ${method} Request Started =====`);
  console.log(`🔐 [AUTH-${requestId}] Timestamp: ${new Date().toISOString()}`);
  console.log(`🔐 [AUTH-${requestId}] Method: ${method}`);
  console.log(`🔐 [AUTH-${requestId}] Full URL: ${request.url}`);
  console.log(`🔐 [AUTH-${requestId}] Endpoint: ${endpoint}`);
  console.log(`🔐 [AUTH-${requestId}] Origin: ${request.headers.get('origin') || 'none'}`);
  console.log(`🔐 [AUTH-${requestId}] User-Agent: ${request.headers.get('user-agent') || 'none'}`);
  console.log(`🔐 [AUTH-${requestId}] Referer: ${request.headers.get('referer') || 'none'}`);

  // Log sanitized headers
  const sanitizedHeaders = sanitizeHeaders(request.headers);
  console.log(`🔐 [AUTH-${requestId}] Headers:`, JSON.stringify(sanitizedHeaders, null, 2));

  // Log request body for POST requests
  let requestBody = null;
  if (method === 'POST') {
    try {
      const clonedRequest = request.clone();
      const bodyText = await clonedRequest.text();

      if (bodyText) {
        try {
          requestBody = JSON.parse(bodyText);
          const sanitizedBody = sanitizeRequestBody(requestBody);
          console.log(`🔐 [AUTH-${requestId}] Request Body:`, JSON.stringify(sanitizedBody, null, 2));
        } catch {
          console.log(`🔐 [AUTH-${requestId}] Request Body (raw):`, bodyText.substring(0, 200) + (bodyText.length > 200 ? '...' : ''));
        }
      } else {
        console.log(`🔐 [AUTH-${requestId}] Request Body: empty`);
      }
    } catch (error) {
      console.log(`🔐 [AUTH-${requestId}] Failed to read request body:`, error);
    }
  }

  // Log route resolution
  console.log(`🔐 [AUTH-${requestId}] Next.js Route: /api/auth/[...all]`);
  console.log(`🔐 [AUTH-${requestId}] Captured segments: ${url.pathname.split('/').slice(3).join('/')}`);
  console.log(`🔐 [AUTH-${requestId}] Better-auth endpoint: ${endpoint}`);

  // Trace better-auth flow
  const flowLogger = traceBetterAuthFlow(endpoint, method, requestId);

  // Call better-auth handler with timing
  console.log(`🔐 [AUTH-${requestId}] 🚀 Calling better-auth handler...`);
  const handlerStartTime = Date.now();

  let response: Response;
  try {
    response = await auth.handler(request);
    const handlerDuration = Date.now() - handlerStartTime;

    console.log(`🔐 [AUTH-${requestId}] ✅ Better-auth handler completed in ${handlerDuration}ms`);
    console.log(`🔐 [AUTH-${requestId}] Response Status: ${response.status} ${response.statusText}`);

    // Log success with flow logger
    flowLogger.info(`✅ Better-auth processing completed successfully`);
    flowLogger.timing('Better-auth handler execution', handlerStartTime);

    // Log response headers
    const responseHeaders: Record<string, string> = {};
    response.headers.forEach((value, key) => {
      responseHeaders[key] = key.toLowerCase().includes('cookie') ? '[REDACTED]' : value;
    });
    console.log(`🔐 [AUTH-${requestId}] Response Headers:`, JSON.stringify(responseHeaders, null, 2));

    // Check for redirects
    if (response.status >= 300 && response.status < 400) {
      const location = response.headers.get('location');
      console.log(`🔐 [AUTH-${requestId}] 🔄 Redirect to: ${location}`);
    }

    // Log response body for non-binary responses (clone to avoid consuming the stream)
    try {
      const clonedResponse = response.clone();
      const contentType = response.headers.get('content-type') || '';

      if (contentType.includes('application/json') || contentType.includes('text/')) {
        const responseText = await clonedResponse.text();
        if (responseText && responseText.length < 1000) {
          console.log(`🔐 [AUTH-${requestId}] Response Body:`, responseText);
        } else if (responseText) {
          console.log(`🔐 [AUTH-${requestId}] Response Body (truncated):`, responseText.substring(0, 200) + '...');
        }
      } else {
        console.log(`🔐 [AUTH-${requestId}] Response Body: binary/non-text content (${contentType})`);
      }
    } catch (error) {
      console.log(`🔐 [AUTH-${requestId}] Could not read response body:`, error);
    }

  } catch (error) {
    const handlerDuration = Date.now() - handlerStartTime;
    console.log(`🔐 [AUTH-${requestId}] ❌ Better-auth handler failed after ${handlerDuration}ms`);
    console.log(`🔐 [AUTH-${requestId}] Error:`, error);

    // Log error with flow logger
    flowLogger.error(`❌ Better-auth processing failed`, error);
    flowLogger.timing('Better-auth handler execution (failed)', handlerStartTime);

    throw error;
  }

  const totalDuration = Date.now() - startTime;
  console.log(`🔐 [AUTH-${requestId}] ⏱️  Total request duration: ${totalDuration}ms`);
  console.log(`🔐 [AUTH-${requestId}] ===== ${method} Request Completed =====\n`);

  return response;
}

export async function GET(request: Request) {
  return handleAuthRequest(request, 'GET');
}

export async function POST(request: Request) {
  return handleAuthRequest(request, 'POST');
}
