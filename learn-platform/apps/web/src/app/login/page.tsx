'use client';

import Link from 'next/link';
import { LoginForm } from '../../components/auth/login-form';
import { PublicOnlyRoute } from '../../components/auth/protected-route';
import { AuthDebug } from '../../components/debug/auth-debug';

export default function LoginPage() {
  const handleLoginSuccess = () => {
    // No manual redirect needed - PublicOnlyRoute will handle it automatically
    // when the authentication state is updated
    console.log('Login successful - waiting for automatic redirect...');
  };

  return (
    <PublicOnlyRoute>
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          {/* Debug component - remove this after fixing the issue */}
          <AuthDebug />

          <LoginForm
            onSuccess={handleLoginSuccess}
            onError={(error) => console.error('Login error:', error)}
          />

          <div className="text-center">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Don&apos;t have an account?{' '}
              <Link
                href="/register"
                className="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
              >
                Sign up
              </Link>
            </p>
          </div>
        </div>
      </div>
    </PublicOnlyRoute>
  );
}
