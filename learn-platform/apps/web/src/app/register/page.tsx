'use client';

import Link from 'next/link';
import { RegisterForm } from '../../components/auth/register-form';
import { PublicOnlyRoute } from '../../components/auth/protected-route';

export default function RegisterPage() {
  const handleRegisterSuccess = () => {
    // No manual redirect needed - PublicOnlyRoute will handle it automatically
    // when the authentication state is updated
    console.log('Registration successful - waiting for automatic redirect...');
  };

  return (
    <PublicOnlyRoute>
      <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full space-y-8">
          <RegisterForm
            onSuccess={handleRegisterSuccess}
            onError={(error) => console.error('Register error:', error)}
          />

          <div className="text-center">
            <p className="text-sm text-gray-600">
              Already have an account?{' '}
              <Link
                href="/login"
                className="font-medium text-blue-600 hover:text-blue-500"
              >
                Sign in
              </Link>
            </p>
          </div>
        </div>
      </div>
    </PublicOnlyRoute>
  );
}
