/**
 * Tests for the web application root layout
 */

import { render, screen } from '@testing-library/react';
import { metadata } from './layout';

// Mock the tRPC client configuration
jest.mock('../lib/trpc', () => ({
  api: {
    Provider: ({ children }: { children: React.ReactNode }) => (
      <div data-testid="trpc-api-provider">{children}</div>
    ),
    createClient: jest.fn(() => ({})),
  },
  trpcClientConfig: {
    links: [],
  },
}));

// Mock React Query
jest.mock('@tanstack/react-query', () => ({
  QueryClient: jest.fn().mockImplementation(() => ({
    mount: jest.fn(),
    unmount: jest.fn(),
    clear: jest.fn(),
  })),
  QueryClientProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="query-client-provider">{children}</div>
  ),
}));

// Mock AuthProvider
jest.mock('../components/auth/auth-provider', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="auth-provider">{children}</div>
  ),
}));

// Mock the TRPCProvider with proper structure
jest.mock('../lib/providers', () => ({
  TRPCProvider: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="query-client-provider">
      <div data-testid="trpc-api-provider">
        <div data-testid="auth-provider">
          <div data-testid="trpc-provider">{children}</div>
        </div>
      </div>
    </div>
  ),
}));

// Custom render function to test only the body content
function renderLayoutBody(children: React.ReactNode) {
  const LayoutBody = () => {
    // Extract just the body content from RootLayout
    return (
      <div data-testid="layout-body">
        <div data-testid="query-client-provider">
          <div data-testid="trpc-api-provider">
            <div data-testid="auth-provider">
              <div data-testid="trpc-provider">{children}</div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return render(<LayoutBody />);
}

describe('Web RootLayout', () => {
  it('should render children within TRPCProvider', () => {
    const TestChild = () => <div data-testid="test-child">Test Content</div>;

    renderLayoutBody(<TestChild />);

    expect(screen.getByTestId('trpc-provider')).toBeInTheDocument();
    expect(screen.getByTestId('test-child')).toBeInTheDocument();
    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  it('should have proper provider nesting structure', () => {
    const TestChild = () => <div data-testid="test-child">Test Content</div>;

    renderLayoutBody(<TestChild />);

    const queryProvider = screen.getByTestId('query-client-provider');
    const trpcProvider = screen.getByTestId('trpc-api-provider');
    const authProvider = screen.getByTestId('auth-provider');
    const trpcWrapper = screen.getByTestId('trpc-provider');
    const testChild = screen.getByTestId('test-child');

    // Check nesting: QueryClient > tRPC API > Auth > tRPC Provider > Children
    expect(queryProvider).toContainElement(trpcProvider);
    expect(trpcProvider).toContainElement(authProvider);
    expect(authProvider).toContainElement(trpcWrapper);
    expect(trpcWrapper).toContainElement(testChild);
  });

  it('should wrap children in TRPCProvider', () => {
    const TestChild = () => <div data-testid="child">Child Component</div>;

    renderLayoutBody(<TestChild />);

    const trpcProvider = screen.getByTestId('trpc-provider');
    const child = screen.getByTestId('child');

    expect(trpcProvider).toContainElement(child);
  });

  describe('Metadata', () => {
    it('should have correct metadata', () => {
      expect(metadata).toEqual({
        title: 'Learning Platform - Web',
        description: 'Public-facing learning platform application',
      });
    });

    it('should have web-specific title', () => {
      expect(metadata.title).toContain('Web');
    });

    it('should have descriptive description', () => {
      expect(metadata.description).toContain('Public-facing');
      expect(metadata.description).toContain('learning platform');
    });
  });

  it('should handle multiple children', () => {
    renderLayoutBody(
      <>
        <div data-testid="child-1">Child 1</div>
        <div data-testid="child-2">Child 2</div>
        <span data-testid="child-3">Child 3</span>
      </>
    );

    expect(screen.getByTestId('child-1')).toBeInTheDocument();
    expect(screen.getByTestId('child-2')).toBeInTheDocument();
    expect(screen.getByTestId('child-3')).toBeInTheDocument();
  });

  it('should maintain provider state across re-renders', () => {
    const TestChild = ({ count }: { count: number }) => (
      <div data-testid="test-child">Count: {count}</div>
    );

    const { rerender } = render(
      <div data-testid="layout-body">
        <div data-testid="query-client-provider">
          <div data-testid="trpc-api-provider">
            <div data-testid="auth-provider">
              <div data-testid="trpc-provider">
                <TestChild count={1} />
              </div>
            </div>
          </div>
        </div>
      </div>
    );

    expect(screen.getByText('Count: 1')).toBeInTheDocument();

    rerender(
      <div data-testid="layout-body">
        <div data-testid="query-client-provider">
          <div data-testid="trpc-api-provider">
            <div data-testid="auth-provider">
              <div data-testid="trpc-provider">
                <TestChild count={2} />
              </div>
            </div>
          </div>
        </div>
      </div>
    );

    expect(screen.getByText('Count: 2')).toBeInTheDocument();
    expect(screen.getByTestId('query-client-provider')).toBeInTheDocument();
  });
});
