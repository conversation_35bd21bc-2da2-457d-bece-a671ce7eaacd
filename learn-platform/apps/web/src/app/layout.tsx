import './global.css';
import { TRPCProvider } from '../lib/providers';
import { ThemeProvider } from '@learn-platform/theme';

export const metadata = {
  title: 'Learning Platform - Web',
  description: 'Public-facing learning platform application',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body>
        <ThemeProvider>
          <TRPCProvider>
            {children}
          </TRPCProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
