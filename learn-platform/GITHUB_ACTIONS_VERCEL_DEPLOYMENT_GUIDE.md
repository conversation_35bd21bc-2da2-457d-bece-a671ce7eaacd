# GitHub Actions + Vercel CLI Deployment Guide

## 🏗️ **Your Deployment Architecture**

Your setup uses **GitHub Actions with Vercel CLI**, which is different from Vercel's automatic Git integration:

```
GitHub Actions → Vercel CLI → Vercel Platform
     ↓              ↓            ↓
   Build         Pull Env     Deploy
   Process       Variables    Application
```

## 🔑 **How Environment Variables Work**

### **The `vercel pull` Command**
```bash
vercel pull --yes --environment=production
```

This command:
1. ✅ Downloads project configuration from Vercel
2. ✅ Downloads ALL environment variables from Vercel dashboard
3. ✅ Creates `.vercel/.env.production.local` file
4. ✅ Makes variables available to `vercel build`

### **Why This is Better Than GitHub Secrets**
- 🔐 **More Secure**: Sensitive variables stay in Vercel's secure environment
- 🎯 **Environment-Specific**: Different variables for production/preview
- 🔄 **Automatic Sync**: Always uses latest values from Vercel dashboard
- 🛠️ **Easier Management**: Single source of truth in Vercel dashboard

## ✅ **Correct Setup Process**

### **Step 1: Set Environment Variables in Vercel Dashboard**

Go to your Vercel project → Settings → Environment Variables:

#### **Production Environment Variables**
```bash
BETTER_AUTH_SECRET=your-production-secret-32-chars-minimum
BETTER_AUTH_URL=https://kwaci-learning.bmbn.dev
BETTER_AUTH_TRUSTED_ORIGINS=https://kwaci-learning.bmbn.dev
DATABASE_URL=postgresql://user:pass@host:port/production_db
NEXT_PUBLIC_API_URL=https://learn-platform-api-prod.your-account.workers.dev
```

#### **Preview Environment Variables**
```bash
BETTER_AUTH_SECRET=your-preview-secret-32-chars-minimum
BETTER_AUTH_URL=https://your-preview-domain.vercel.app
BETTER_AUTH_TRUSTED_ORIGINS=https://your-preview-domain.vercel.app
DATABASE_URL=postgresql://user:pass@host:port/preview_db
NEXT_PUBLIC_API_URL=https://learn-platform-api-dev.your-account.workers.dev
```

### **Step 2: Environment Variable Settings**

For each variable, configure:
- **Environment**: 
  - ✅ Production (for main branch)
  - ✅ Preview (for dev branch and PRs)
- **Git Branch**: Leave empty (applies to all branches in that environment)

### **Step 3: Verify GitHub Actions Workflow**

Your workflow is already correctly configured! The key steps:

```yaml
# 1. Pull environment variables from Vercel
- name: Pull Vercel Environment Information (Production)
  run: vercel pull --yes --environment=production

# 2. Build with those variables
- name: Build Project Artifacts (Production)  
  run: vercel build --prod

# 3. Deploy the built application
- name: Deploy Project Artifacts to Vercel (Production)
  run: vercel deploy --prebuilt --prod
```

## 🔍 **Verification Steps**

### **Step 1: Check Workflow Logs**

After setting environment variables, trigger a deployment and check the logs for:

```
Checking if environment variables were pulled correctly...
BETTER_AUTH_URL is set: ✅ Yes
BETTER_AUTH_SECRET is set: ✅ Yes
DATABASE_URL is set: ✅ Yes
NEXT_PUBLIC_API_URL is set: ✅ Yes
```

### **Step 2: Check Application Logs**

Look for the debug messages we added to the auth configuration:

```
[Better Auth Config] Environment: Node.js, NODE_ENV: production
[Better Auth Config] Base URL: https://kwaci-learning.bmbn.dev
[Better Auth Config] Secret set: true
[Better Auth] Configured trusted origins: ..., https://kwaci-learning.bmbn.dev
```

### **Step 3: Test Authentication**

1. Visit `https://kwaci-learning.bmbn.dev/login`
2. No INVALID_ORIGIN errors should appear
3. Authentication should work properly

## 🚨 **Common Issues & Solutions**

### **Issue: Environment variables not found**
**Cause**: Variables not set in Vercel dashboard
**Solution**: 
- Add variables in Vercel dashboard
- Ensure they're enabled for correct environment (Production/Preview)
- Retrigger deployment

### **Issue: Wrong environment variables loaded**
**Cause**: Environment mismatch in workflow
**Solution**:
- Production deployments use `--environment=production`
- Preview deployments use `--environment=preview`
- Check branch triggers in workflow

### **Issue: BETTER_AUTH_SECRET still not set**
**Cause**: Variable name mismatch or not saved properly
**Solution**:
- Verify exact variable name: `BETTER_AUTH_SECRET`
- Check it's saved in Vercel dashboard
- Ensure it's enabled for the correct environment

## 🎯 **Why NOT to Use GitHub Secrets**

❌ **Don't do this**:
```yaml
env:
  BETTER_AUTH_SECRET: ${{ secrets.BETTER_AUTH_SECRET }}
```

✅ **Do this instead**:
```yaml
run: vercel pull --yes --environment=production
```

**Reasons**:
1. **Security**: Vercel's environment is more secure for runtime secrets
2. **Environment Management**: Easier to manage different environments
3. **Vercel Integration**: Better integration with Vercel's platform features
4. **Consistency**: Same approach for all Vercel deployments

## 📋 **Deployment Checklist**

- [ ] Environment variables set in Vercel dashboard
- [ ] Variables enabled for Production environment
- [ ] Variables enabled for Preview environment
- [ ] GitHub Actions workflow unchanged (it's already correct)
- [ ] Deployment triggered after setting variables
- [ ] Verification logs show variables are loaded
- [ ] Auth debug logs show correct configuration
- [ ] INVALID_ORIGIN error resolved
- [ ] Authentication flow working

## 🎉 **Expected Result**

After following this guide:

1. ✅ **Environment variables automatically pulled** by `vercel pull`
2. ✅ **Build process has access** to all required variables
3. ✅ **No BETTER_AUTH_SECRET warnings**
4. ✅ **Trusted origins include production domain**
5. ✅ **INVALID_ORIGIN error resolved**
6. ✅ **Authentication working properly**

Your GitHub Actions + Vercel CLI setup is actually excellent for this use case!
