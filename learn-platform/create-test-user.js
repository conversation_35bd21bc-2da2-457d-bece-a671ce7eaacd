/**
 * <PERSON><PERSON><PERSON> to create a test user for authentication testing
 */

const API_BASE = 'http://localhost:8787';

async function createTestUser() {
  console.log('🔍 Creating test user...\n');

  const testUser = {
    email: '<EMAIL>',
    password: 'testpassword123',
    name: 'Test Admin'
  };

  try {
    const response = await fetch(`${API_BASE}/api/auth/sign-up/email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify(testUser)
    });

    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Test user created successfully!');
      console.log('   Email:', testUser.email);
      console.log('   Password:', testUser.password);
      console.log('   User data:', data);
    } else {
      console.log('❌ Failed to create test user:', data);
      
      // If user already exists, that's fine
      if (data.code === 'USER_ALREADY_EXISTS' || data.message?.includes('already exists')) {
        console.log('✅ Test user already exists, you can use:');
        console.log('   Email:', testUser.email);
        console.log('   Password:', testUser.password);
      }
    }
  } catch (error) {
    console.log('❌ Error creating test user:', error.message);
  }

  console.log('\n🔍 Test user setup complete!');
}

createTestUser().catch(console.error);
