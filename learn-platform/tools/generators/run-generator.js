#!/usr/bin/env node

/**
 * Simple CLI to run the tRPC procedure generator
 * Usage: node tools/generators/run-generator.js <procedureName> <router> [options]
 */

const { Tree, formatFiles } = require('@nx/devkit');
const generator = require('./trpc-procedure/index.js');
const fs = require('fs');
const path = require('path');

function parseArgs() {
  const args = process.argv.slice(2);
  
  if (args.length < 2) {
    console.log(`
Usage: node tools/generators/run-generator.js <procedureName> <router> [options]

Arguments:
  procedureName    Name of the procedure (camelCase)
  router          Target router name (e.g., 'user', 'auth', 'admin')

Options:
  --type=<type>       Type of procedure: 'query' or 'mutation' (default: 'query')
  --protected         Require authentication
  --admin             Require admin privileges
  --dry-run           Show what would be generated without writing files
  --help              Show this help message

Examples:
  node tools/generators/run-generator.js getUserProfile user
  node tools/generators/run-generator.js createUser user --type=mutation --protected
  node tools/generators/run-generator.js deleteUser admin --type=mutation --admin
  node tools/generators/run-generator.js testProcedure test --dry-run
`);
    process.exit(1);
  }

  const options = {
    procedureName: args[0],
    router: args[1],
    type: 'query',
    protected: false,
    admin: false,
    skipFormat: false,
    dryRun: false
  };

  // Parse options
  for (let i = 2; i < args.length; i++) {
    const arg = args[i];
    if (arg.startsWith('--type=')) {
      options.type = arg.split('=')[1];
    } else if (arg === '--protected') {
      options.protected = true;
    } else if (arg === '--admin') {
      options.admin = true;
    } else if (arg === '--dry-run') {
      options.dryRun = true;
      options.skipFormat = true;
    } else if (arg === '--help') {
      console.log('Help message already shown above');
      process.exit(0);
    }
  }

  return options;
}

async function runGenerator() {
  const options = parseArgs();
  
  console.log('🚀 Running tRPC Procedure Generator...');
  console.log(`📝 Procedure: ${options.procedureName}`);
  console.log(`🔗 Router: ${options.router}`);
  console.log(`📋 Type: ${options.type}`);
  console.log(`🔒 Protected: ${options.protected}`);
  console.log(`👑 Admin: ${options.admin}`);
  console.log(`🧪 Dry Run: ${options.dryRun}`);
  console.log('');

  try {
    const tree = new Tree();
    
    // Read the current workspace files
    const workspaceRoot = process.cwd();
    
    // Read router file if it exists
    const routerPath = path.join(workspaceRoot, 'libs/trpc/src/router.ts');
    if (fs.existsSync(routerPath)) {
      const routerContent = fs.readFileSync(routerPath, 'utf-8');
      tree.write('libs/trpc/src/router.ts', routerContent);
    }
    
    // Run the generator
    await generator(tree, options);
    
    if (!options.dryRun) {
      // Write the changes back to the filesystem
      const changes = tree.listChanges();
      
      for (const change of changes) {
        const fullPath = path.join(workspaceRoot, change.path);
        
        if (change.type === 'CREATE' || change.type === 'UPDATE') {
          // Ensure directory exists
          const dir = path.dirname(fullPath);
          if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
          }
          
          // Write file
          fs.writeFileSync(fullPath, change.content);
          console.log(`✅ ${change.type === 'CREATE' ? 'Created' : 'Updated'}: ${change.path}`);
        }
      }
      
      console.log('');
      console.log('🎉 Generator completed successfully!');
      console.log('');
      console.log('Next steps:');
      console.log('1. Implement the procedure logic in the generated file');
      console.log('2. Define proper input/output schemas using Zod');
      console.log('3. Write tests for the new procedure');
      console.log('4. Update API documentation if needed');
      
    } else {
      console.log('');
      console.log('🧪 Dry run completed. Files that would be created/updated:');
      const changes = tree.listChanges();
      for (const change of changes) {
        console.log(`  ${change.type}: ${change.path}`);
      }
    }
    
  } catch (error) {
    console.error('❌ Generator failed:', error.message);
    process.exit(1);
  }
}

runGenerator();
