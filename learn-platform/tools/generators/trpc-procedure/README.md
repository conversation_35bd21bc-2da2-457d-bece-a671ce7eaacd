# tRPC Procedure Generator

A custom Nx generator that automates the creation of tRPC procedures with automatic router integration.

## Usage

### Recommended Usage (CLI Runner)

```bash
# Generate a simple query procedure
node tools/generators/run-generator.js getUserProfile user

# Generate a mutation procedure
node tools/generators/run-generator.js createUser user --type=mutation

# Generate a protected procedure (requires authentication)
node tools/generators/run-generator.js getProfile user --protected

# Generate an admin procedure (requires admin privileges)
node tools/generators/run-generator.js deleteUser admin --type=mutation --admin

# Dry run to preview changes
node tools/generators/run-generator.js testProcedure test --dry-run
```

### Alternative Usage (if Nx workspace generators are configured)

```bash
# Short form using Nx (requires proper workspace generator setup)
nx g trpc-procedure getUserProfile --router=user
nx g procedure createUser --router=user --type=mutation
```

## Options

| Option | Type | Default | Description |
|--------|------|---------|-------------|
| `procedureName` | `string` | - | **Required.** Name of the procedure (camelCase) |
| `router` | `string` | - | **Required.** Target router name (e.g., 'user', 'auth', 'admin') |
| `type` | `'query' \| 'mutation'` | `'query'` | Type of procedure |
| `protected` | `boolean` | `false` | Whether this procedure requires authentication |
| `admin` | `boolean` | `false` | Whether this procedure requires admin privileges |
| `skipFormat` | `boolean` | `false` | Skip formatting files |

## What It Does

1. **Creates a procedure file** at `libs/trpc/src/procedures/<procedureName>.ts` with:
   - Proper TypeScript imports for tRPC and Zod validation
   - Input/output schema definitions using Zod
   - Procedure implementation with proper typing
   - Export statement for the procedure

2. **Updates the router file** at `libs/trpc/src/router.ts` to:
   - Import the new procedure
   - Add it to the specified router definition
   - Create a new router if it doesn't exist
   - Maintain proper TypeScript typing

## Generated File Structure

### Procedure File (`libs/trpc/src/procedures/<procedureName>.ts`)

```typescript
import { z } from 'zod';
import { publicProcedure } from '../router';

export const getUserProfileInput = z.object({
  // TODO: Define your input schema
});

export const getUserProfileOutput = z.object({
  // TODO: Define your output schema
});

export const getUserProfile = publicProcedure
  .input(getUserProfileInput)
  .output(getUserProfileOutput)
  .query(async ({ input, ctx }) => {
    // TODO: Implement your query logic here
    return {
      success: true,
      message: 'GetUserProfile query executed successfully',
    };
  });
```

### Router Integration

The generator automatically updates `libs/trpc/src/router.ts`:

```typescript
// Adds import
import { getUserProfile } from './procedures/getUserProfile';

// Adds to router
export const appRouter = router({
  // ... existing routes
  user: router({
    getUserProfile,
    // ... other user procedures
  }),
});
```

## Examples

### Creating a User Management API

```bash
# Create user procedures
nx g trpc-procedure getUser --router=user --type=query
nx g trpc-procedure createUser --router=user --type=mutation --protected
nx g trpc-procedure updateUser --router=user --type=mutation --protected
nx g trpc-procedure deleteUser --router=user --type=mutation --admin

# Create auth procedures
nx g trpc-procedure signUp --router=auth --type=mutation
nx g trpc-procedure signIn --router=auth --type=mutation
nx g trpc-procedure signOut --router=auth --type=mutation --protected
```

### Creating Admin Procedures

```bash
# Admin-only procedures
nx g trpc-procedure getSystemStats --router=admin --admin
nx g trpc-procedure manageUsers --router=admin --admin --type=mutation
```

## Validation

The generator includes validation for:

- **Procedure name format**: Must be camelCase starting with lowercase letter
- **Router name format**: Must be camelCase starting with lowercase letter
- **File conflicts**: Prevents overwriting existing procedure files
- **tRPC setup**: Ensures the tRPC library is properly configured

## Error Handling

Common errors and solutions:

- **"tRPC router file not found"**: Ensure `@learn-platform/trpc` library is set up
- **"Procedure file already exists"**: Choose a different procedure name
- **"Invalid procedure name"**: Use camelCase format (e.g., `getUserProfile`)
- **"Invalid router name"**: Use camelCase format (e.g., `user`, `auth`)

## Integration with Existing Code

The generator is designed to work with the existing tRPC setup:

- Compatible with Cloudflare Workers and Hono framework
- Supports existing authentication middleware
- Maintains type safety with TypeScript
- Follows established code patterns and conventions

## Next Steps

After generating a procedure:

1. **Implement the logic** in the generated procedure file
2. **Define input/output schemas** using Zod validation
3. **Write tests** for the new procedure
4. **Update API documentation** if needed

## Tips

- Use descriptive procedure names that clearly indicate their purpose
- Group related procedures in the same router (e.g., all user operations in `user` router)
- Always define input schemas for better validation and type safety
- Consider output schemas for consistent API responses
- Use protected procedures for operations requiring authentication
- Use admin procedures for sensitive operations
