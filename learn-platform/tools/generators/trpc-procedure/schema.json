{"$schema": "http://json-schema.org/schema", "$id": "TrpcProcedureGenerator", "title": "tRPC Procedure Generator", "description": "Generate a new tRPC procedure with automatic router integration", "type": "object", "properties": {"procedureName": {"type": "string", "description": "Name of the procedure (camelCase)", "pattern": "^[a-z][a-zA-Z0-9]*$", "$default": {"$source": "argv", "index": 0}, "x-prompt": "What is the name of the procedure?"}, "router": {"type": "string", "description": "Target router name (e.g., 'user', 'auth', 'admin')", "pattern": "^[a-z][a-zA-Z0-9]*$", "$default": {"$source": "argv", "index": 1}, "x-prompt": "Which router should this procedure be added to?"}, "type": {"type": "string", "description": "Type of procedure", "enum": ["query", "mutation"], "default": "query", "x-prompt": {"message": "What type of procedure is this?", "type": "list", "items": [{"value": "query", "label": "Query (for reading data)"}, {"value": "mutation", "label": "Mutation (for modifying data)"}]}}, "protected": {"type": "boolean", "description": "Whether this procedure requires authentication", "default": false, "x-prompt": "Should this procedure require authentication?"}, "admin": {"type": "boolean", "description": "Whether this procedure requires admin privileges", "default": false, "x-prompt": "Should this procedure require admin privileges?"}, "skipFormat": {"type": "boolean", "description": "Skip formatting files", "default": false}}, "required": ["procedureName", "router"], "additionalProperties": false}