import { z } from 'zod';
import { <%= procedureType %> } from '../router';

/**
 * Input schema for <%= procedureName %> procedure
 * Add your input validation rules here
 */
export const <%= procedureName %>Input = z.object({
  // TODO: Define your input schema
  // Example:
  // id: z.string().uuid('Invalid ID format'),
  // name: z.string().min(1, 'Name is required'),
});

/**
 * Output schema for <%= procedureName %> procedure
 * This is optional but recommended for better type safety
 */
export const <%= procedureName %>Output = z.object({
  // TODO: Define your output schema
  // Example:
  // success: z.boolean(),
  // data: z.object({
  //   id: z.string(),
  //   name: z.string(),
  // }),
});

/**
 * <%= pascalCaseName %> <%= type %> procedure<% if (protected) { %> (requires authentication)<% } %><% if (admin) { %> (requires admin privileges)<% } %>
 * 
 * @description TODO: Add description of what this procedure does
 */
export const <%= procedureName %> = <%= procedureType %>
  .input(<%= procedureName %>Input)
  .output(<%= procedureName %>Output) // Remove this line if you don't want output validation
  .<%= type %>(async ({ input, ctx }) => {
    // TODO: Implement your <%= type %> logic here
    <% if (protected || admin) { %>
    // User is authenticated and available as ctx.user
    // User ID is available as ctx.userId
    <% } %>
    <% if (admin) { %>
    // User has admin privileges
    <% } %>
    
    // Example implementation:
    // const result = await someService.process(input);
    
    return {
      // TODO: Return your response here
      success: true,
      message: '<%= pascalCaseName %> <%= type %> executed successfully',
      // data: result,
    };
  });
