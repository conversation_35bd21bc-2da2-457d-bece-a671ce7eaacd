const {
  formatFiles,
  generateFiles,
  names,
  logger,
} = require('@nx/devkit');
const { join } = require('path');

async function trpcProcedureGenerator(tree, options) {
  const normalizedOptions = normalizeOptions(tree, options);
  
  // Validate inputs
  validateOptions(tree, normalizedOptions);
  
  // Create procedures directory if it doesn't exist
  ensureProceduresDirectory(tree, normalizedOptions);
  
  // Generate the procedure file
  generateProcedureFile(tree, normalizedOptions);
  
  // Update the router file
  updateRouterFile(tree, normalizedOptions);
  
  if (!options.skipFormat) {
    await formatFiles(tree);
  }
  
  logger.info(`✅ Generated tRPC procedure: ${normalizedOptions.procedureName}`);
  logger.info(`📁 File: ${normalizedOptions.proceduresDir}/${normalizedOptions.procedureFileName}`);
  logger.info(`🔗 Added to router: ${normalizedOptions.router}`);
}

function normalizeOptions(tree, options) {
  const procedureNames = names(options.procedureName);
  
  // Determine procedure type based on protection level
  let procedureType = 'publicProcedure';
  if (options.admin) {
    procedureType = 'adminProcedure';
  } else if (options.protected) {
    procedureType = 'protectedProcedure';
  }
  
  return {
    ...options,
    type: options.type || 'query',
    procedureFileName: `${procedureNames.fileName}.ts`,
    pascalCaseName: procedureNames.className,
    procedureType,
    routerPath: 'libs/trpc/src/router.ts',
    proceduresDir: 'libs/trpc/src/procedures',
  };
}

function validateOptions(tree, options) {
  // Check if tRPC library exists
  if (!tree.exists(options.routerPath)) {
    throw new Error(
      `tRPC router file not found at ${options.routerPath}. Make sure the @learn-platform/trpc library is set up.`
    );
  }
  
  // Check if procedure already exists
  const procedureFilePath = join(options.proceduresDir, options.procedureFileName);
  if (tree.exists(procedureFilePath)) {
    throw new Error(
      `Procedure file already exists: ${procedureFilePath}. Choose a different name.`
    );
  }
  
  // Validate procedure name format
  if (!/^[a-z][a-zA-Z0-9]*$/.test(options.procedureName)) {
    throw new Error(
      `Invalid procedure name: ${options.procedureName}. Must be camelCase and start with a lowercase letter.`
    );
  }
  
  // Validate router name format
  if (!/^[a-z][a-zA-Z0-9]*$/.test(options.router)) {
    throw new Error(
      `Invalid router name: ${options.router}. Must be camelCase and start with a lowercase letter.`
    );
  }
}

function ensureProceduresDirectory(tree, options) {
  if (!tree.exists(options.proceduresDir)) {
    tree.write(join(options.proceduresDir, '.gitkeep'), '');
    logger.info(`📁 Created procedures directory: ${options.proceduresDir}`);
  }
}

function generateProcedureFile(tree, options) {
  generateFiles(
    tree,
    join(__dirname, 'files'),
    options.proceduresDir,
    {
      ...options,
      tmpl: '',
    }
  );
}

function updateRouterFile(tree, options) {
  const routerContent = tree.read(options.routerPath, 'utf-8');
  if (!routerContent) {
    throw new Error(`Could not read router file: ${options.routerPath}`);
  }
  
  let updatedContent = routerContent;
  
  // Add import statement
  updatedContent = addImportStatement(updatedContent, options);
  
  // Add procedure to router
  updatedContent = addProcedureToRouter(updatedContent, options);
  
  tree.write(options.routerPath, updatedContent);
}

function addImportStatement(content, options) {
  const importStatement = `import { ${options.procedureName} } from './procedures/${options.procedureName}';`;
  
  // Find the last import statement
  const importRegex = /^import\s+.*?;$/gm;
  const imports = content.match(importRegex) || [];
  
  if (imports.length === 0) {
    // No imports found, add at the beginning
    return `${importStatement}\n${content}`;
  }
  
  // Check if import already exists
  if (content.includes(importStatement)) {
    return content;
  }
  
  // Find the position after the last import
  let lastImportIndex = -1;
  let match;
  while ((match = importRegex.exec(content)) !== null) {
    lastImportIndex = match.index + match[0].length;
  }
  
  // Insert the new import after the last import
  return content.slice(0, lastImportIndex) + '\n' + importStatement + content.slice(lastImportIndex);
}

function addProcedureToRouter(content, options) {
  // Look for the target router
  const routerPattern = new RegExp(
    `(${options.router}:\\s*router\\s*\\(\\s*\\{)([\\s\\S]*?)(\\}\\s*\\))`
  );
  
  const match = content.match(routerPattern);
  
  if (match) {
    // Router exists, add procedure to it
    const [fullMatch, opening, existingProcedures, closing] = match;
    const procedureEntry = `\n    ${options.procedureName},`;
    
    // Check if procedure already exists in router
    if (existingProcedures.includes(options.procedureName)) {
      logger.warn(`Procedure ${options.procedureName} already exists in ${options.router} router`);
      return content;
    }
    
    const updatedRouter = opening + existingProcedures + procedureEntry + '\n  ' + closing;
    return content.replace(fullMatch, updatedRouter);
  } else {
    // Router doesn't exist, create it
    return addNewRouter(content, options);
  }
}

function addNewRouter(content, options) {
  // Find the appRouter definition
  const appRouterPattern = /(export\s+const\s+appRouter\s*=\s*router\s*\(\s*\{)([\s\S]*?)(\}\s*\);)/;
  const match = content.match(appRouterPattern);
  
  if (!match) {
    throw new Error('Could not find appRouter definition in router file');
  }
  
  const [fullMatch, opening, existingRouters, closing] = match;
  const newRouter = `\n  // ${options.router.charAt(0).toUpperCase() + options.router.slice(1)} procedures\n  ${options.router}: router({\n    ${options.procedureName},\n  }),`;
  
  const updatedAppRouter = opening + existingRouters + newRouter + '\n' + closing;
  return content.replace(fullMatch, updatedAppRouter);
}

module.exports = trpcProcedureGenerator;
module.exports.default = trpcProcedureGenerator;
