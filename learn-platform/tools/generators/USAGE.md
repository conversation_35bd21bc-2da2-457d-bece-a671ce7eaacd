# tRPC Procedure Generator Usage Guide

This guide shows you how to use the custom Nx generator for creating tRPC procedures with automatic router integration.

## Quick Start

The generator is located at `tools/generators/trpc-procedure/` and can be used to quickly scaffold new tRPC procedures.

### Basic Usage

```bash
# Using the CLI runner (recommended)
node tools/generators/run-generator.js <procedureName> <router> [options]

# Examples:
node tools/generators/run-generator.js getUserProfile user
node tools/generators/run-generator.js createUser user --type=mutation --protected
node tools/generators/run-generator.js deleteUser admin --type=mutation --admin
```

### Available Options

- `--type=<type>` - Procedure type: 'query' or 'mutation' (default: 'query')
- `--protected` - Require authentication
- `--admin` - Require admin privileges  
- `--dry-run` - Preview changes without writing files

## What Gets Generated

### 1. Procedure File

Creates `libs/trpc/src/procedures/<procedureName>.ts`:

```typescript
import { z } from 'zod';
import { publicProcedure } from '../router';

export const getUserProfileInput = z.object({
  // TODO: Define your input schema
});

export const getUserProfileOutput = z.object({
  // TODO: Define your output schema
});

export const getUserProfile = publicProcedure
  .input(getUserProfileInput)
  .output(getUserProfileOutput)
  .query(async ({ input, ctx }) => {
    // TODO: Implement your query logic here
    return {
      success: true,
      message: 'GetUserProfile query executed successfully',
    };
  });
```

### 2. Router Integration

Automatically updates `libs/trpc/src/router.ts`:

```typescript
// Adds import
import { getUserProfile } from './procedures/getUserProfile';

// Adds to router
export const appRouter = router({
  // ... existing routes
  user: router({
    getUserProfile,
    // ... other user procedures
  }),
});
```

## Examples

### User Management Procedures

```bash
# Basic user queries
node tools/generators/run-generator.js getUser user --type=query
node tools/generators/run-generator.js getUserProfile user --type=query --protected

# User mutations
node tools/generators/run-generator.js createUser user --type=mutation
node tools/generators/run-generator.js updateUser user --type=mutation --protected
node tools/generators/run-generator.js deleteUser user --type=mutation --admin
```

### Authentication Procedures

```bash
# Auth procedures
node tools/generators/run-generator.js signUp auth --type=mutation
node tools/generators/run-generator.js signIn auth --type=mutation
node tools/generators/run-generator.js signOut auth --type=mutation --protected
node tools/generators/run-generator.js refreshToken auth --type=mutation --protected
```

### Admin Procedures

```bash
# Admin-only procedures
node tools/generators/run-generator.js getSystemStats admin --admin
node tools/generators/run-generator.js manageUsers admin --type=mutation --admin
node tools/generators/run-generator.js viewAuditLogs admin --admin
```

## Generated File Structure

After running the generator, your file structure will look like:

```
libs/trpc/src/
├── procedures/
│   ├── getUserProfile.ts
│   ├── createUser.ts
│   ├── updateUser.ts
│   └── deleteUser.ts
├── router.ts (updated)
├── context.ts
└── index.ts
```

## Procedure Types

### Public Procedures
- No authentication required
- Use `publicProcedure`
- Generated by default

### Protected Procedures  
- Require user authentication
- Use `protectedProcedure`
- Generated with `--protected` flag
- Access to `ctx.user` and `ctx.userId`

### Admin Procedures
- Require admin privileges
- Use `adminProcedure` 
- Generated with `--admin` flag
- Access to admin-specific context

## Next Steps After Generation

1. **Implement Logic**: Add your business logic to the generated procedure
2. **Define Schemas**: Update input/output Zod schemas for validation
3. **Add Tests**: Write unit tests for the new procedure
4. **Update Docs**: Document the new API endpoint

## Example Implementation

After generating a procedure, implement it like this:

```typescript
// libs/trpc/src/procedures/getUserProfile.ts
import { z } from 'zod';
import { protectedProcedure } from '../router';

export const getUserProfileInput = z.object({
  userId: z.string().uuid('Invalid user ID format'),
});

export const getUserProfileOutput = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string().email(),
  createdAt: z.string(),
});

export const getUserProfile = protectedProcedure
  .input(getUserProfileInput)
  .output(getUserProfileOutput)
  .query(async ({ input, ctx }) => {
    // Ensure user can only access their own profile or is admin
    if (input.userId !== ctx.userId && !ctx.hasRole('admin')) {
      throw new TRPCError({
        code: 'FORBIDDEN',
        message: 'You can only access your own profile',
      });
    }

    // Fetch user from database
    const foundUser = await ctx.db
      .select()
      .from(user)
      .where(eq(user.id, input.userId))
      .get();

    if (!foundUser) {
      throw new TRPCError({
        code: 'NOT_FOUND',
        message: 'User not found',
      });
    }

    return {
      id: foundUser.id,
      name: foundUser.name,
      email: foundUser.email,
      createdAt: foundUser.createdAt.toISOString(),
    };
  });
```

## Troubleshooting

### Common Issues

1. **"tRPC router file not found"**
   - Ensure `libs/trpc/src/router.ts` exists
   - Make sure you're running from the workspace root

2. **"Procedure file already exists"**
   - Choose a different procedure name
   - Or remove the existing file if you want to regenerate

3. **"Invalid procedure name"**
   - Use camelCase format (e.g., `getUserProfile`)
   - Start with lowercase letter
   - Only use letters and numbers

4. **"Invalid router name"**
   - Use camelCase format (e.g., `user`, `auth`)
   - Start with lowercase letter

### Validation Rules

- Procedure names must be camelCase
- Router names must be camelCase  
- No special characters except letters and numbers
- Names must start with lowercase letter

## Integration with Existing Code

The generator is designed to work seamlessly with:

- ✅ Existing tRPC setup with Cloudflare Workers
- ✅ Hono framework integration
- ✅ Better-auth authentication system
- ✅ Drizzle ORM database integration
- ✅ TypeScript type safety
- ✅ Zod validation schemas

## Tips for Best Practices

1. **Naming Conventions**
   - Use descriptive procedure names (`getUserProfile` not `getUser`)
   - Group related procedures in the same router
   - Use consistent naming patterns

2. **Schema Design**
   - Always define input schemas for validation
   - Consider output schemas for consistent responses
   - Use descriptive error messages in Zod schemas

3. **Security**
   - Use protected procedures for authenticated operations
   - Use admin procedures for sensitive operations
   - Always validate user permissions in procedure logic

4. **Organization**
   - Keep procedures focused and single-purpose
   - Group related procedures in the same router
   - Consider creating sub-routers for complex domains
