# Vercel Environment Variables Debug Guide

## 🚨 **Current Issue**

Your GitHub Actions workflow shows:
```
BETTER_AUTH_URL is set: ❌ No
BETTER_AUTH_SECRET is set: ❌ No
DATABASE_URL is set: ❌ No
NEXT_PUBLIC_API_URL is set: ❌ No
```

This means `vercel pull` is not downloading the environment variables properly.

## 🔍 **Root Cause Analysis**

The issue is likely one of these:

### **1. Environment Variables Not Set in Vercel Dashboard**
- Variables might not be configured in your Vercel project
- Variables might not be enabled for the correct environment (Preview/Production)

### **2. Environment Mismatch**
- Your workflow uses `--environment=preview` for dev branch
- Your workflow uses `--environment=production` for main branch
- Variables must be set for the corresponding environment in Vercel

### **3. Project Configuration Issue**
- Wrong Vercel project ID
- Authentication issues with Vercel CLI

## ✅ **Step-by-Step Fix**

### **Step 1: Verify Vercel Project Configuration**

1. **Check your Vercel project ID**:
   - Go to Vercel Dashboard → Your Project → Settings → General
   - Copy the Project ID
   - Verify it matches `VERCEL_PROJECT_ID_WEB` in your GitHub Secrets

2. **Check your Vercel organization ID**:
   - Go to Vercel Dashboard → Settings → General
   - Copy the Team ID (Organization ID)
   - Verify it matches `VERCEL_ORG_ID` in your GitHub Secrets

### **Step 2: Set Environment Variables in Vercel Dashboard**

Go to Vercel Dashboard → Your Project → Settings → Environment Variables

#### **For Preview Environment** (dev branch deployments):
```bash
BETTER_AUTH_SECRET=your-preview-secret-32-chars-minimum
BETTER_AUTH_URL=https://your-preview-domain.vercel.app
BETTER_AUTH_TRUSTED_ORIGINS=https://your-preview-domain.vercel.app
DATABASE_URL=postgresql://user:pass@host:port/preview_db
NEXT_PUBLIC_API_URL=https://learn-platform-api-dev.your-account.workers.dev
```

**Environment Settings**:
- ✅ **Preview** (checked)
- ❌ **Production** (unchecked)
- ❌ **Development** (unchecked)

#### **For Production Environment** (main branch deployments):
```bash
BETTER_AUTH_SECRET=your-production-secret-32-chars-minimum
BETTER_AUTH_URL=https://kwaci-learning.bmbn.dev
BETTER_AUTH_TRUSTED_ORIGINS=https://kwaci-learning.bmbn.dev
DATABASE_URL=postgresql://user:pass@host:port/production_db
NEXT_PUBLIC_API_URL=https://learn-platform-api-prod.your-account.workers.dev
```

**Environment Settings**:
- ❌ **Preview** (unchecked)
- ✅ **Production** (checked)
- ❌ **Development** (unchecked)

### **Step 3: Verify GitHub Secrets**

Ensure these secrets are set in your GitHub repository:
- `VERCEL_TOKEN` - Your Vercel API token
- `VERCEL_ORG_ID` - Your Vercel organization/team ID
- `VERCEL_PROJECT_ID_WEB` - Your web project ID

### **Step 4: Test the Configuration**

1. **Trigger a deployment** by pushing to dev or main branch
2. **Check the workflow logs** for the verification step
3. **Look for these indicators**:
   ```
   Contents of .vercel directory:
   .env.preview.local (or .env.production.local)
   
   BETTER_AUTH_URL is set: ✅ Yes
   BETTER_AUTH_SECRET is set: ✅ Yes
   DATABASE_URL is set: ✅ Yes
   NEXT_PUBLIC_API_URL is set: ✅ Yes
   ```

## 🛠️ **Alternative Debugging Steps**

### **Option 1: Manual Verification**

Run these commands locally to test:
```bash
cd learn-platform/apps/web
vercel pull --yes --environment=preview --token=YOUR_TOKEN
cat .vercel/.env.preview.local
```

### **Option 2: Check Vercel CLI Version**

Ensure you're using the latest Vercel CLI:
```bash
npm install --global vercel@latest
```

### **Option 3: Verify Project Linking**

Make sure your project is properly linked:
```bash
cd learn-platform/apps/web
vercel link
```

## 🚨 **Common Mistakes**

### **❌ Wrong Environment Configuration**
```bash
# DON'T set variables for all environments
Environment: Production ✅ Preview ✅ Development ✅
```

```bash
# DO set variables for specific environments
For Preview: Preview ✅ only
For Production: Production ✅ only
```

### **❌ Missing Required Variables**
Make sure ALL these variables are set:
- `BETTER_AUTH_SECRET`
- `BETTER_AUTH_URL`
- `BETTER_AUTH_TRUSTED_ORIGINS`
- `DATABASE_URL`
- `NEXT_PUBLIC_API_URL`

### **❌ Wrong Variable Names**
Variable names are case-sensitive and must match exactly.

## 🎯 **Expected Result**

After fixing the configuration, your workflow should show:
```
✅ Environment variables pulled successfully
✅ BETTER_AUTH_URL is set: ✅ Yes
✅ BETTER_AUTH_SECRET is set: ✅ Yes
✅ DATABASE_URL is set: ✅ Yes
✅ NEXT_PUBLIC_API_URL is set: ✅ Yes
```

And your application should:
- ✅ Build successfully
- ✅ Deploy without INVALID_ORIGIN errors
- ✅ Authenticate users properly

## 📞 **Next Steps**

1. **Set environment variables** in Vercel Dashboard for both Preview and Production
2. **Verify GitHub Secrets** are correct
3. **Trigger a new deployment**
4. **Check the workflow logs** for successful variable loading
5. **Test authentication** on the deployed application

The key is ensuring the environment variables are properly configured in the Vercel Dashboard for the correct environments (Preview vs Production).
