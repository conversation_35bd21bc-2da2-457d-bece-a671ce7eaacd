#!/usr/bin/env node

/**
 * Verification script for TailwindCSS and Shared UI Component setup
 * This script verifies that our configuration is working correctly
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying TailwindCSS and Shared UI Component Setup...\n');

// Check 1: Verify Tailwind configs include shared UI library
function checkTailwindConfigs() {
  console.log('1. Checking Tailwind configurations...');
  
  const webConfig = path.join(__dirname, 'apps/web/tailwind.config.js');
  const adminConfig = path.join(__dirname, 'apps/admin/tailwind.config.js');
  
  [webConfig, adminConfig].forEach(configPath => {
    const appName = configPath.includes('web') ? 'web' : 'admin';
    
    if (!fs.existsSync(configPath)) {
      console.log(`   ❌ ${appName}: Config file not found`);
      return;
    }
    
    const content = fs.readFileSync(configPath, 'utf8');
    
    // Check for required imports and patterns
    const hasCreateGlobPatterns = content.includes('createGlobPatternsForDependencies');
    const hasSharedUIPath = content.includes('libs/shared/ui/src');
    const hasJoinImport = content.includes("require('path')");
    
    if (hasCreateGlobPatterns && hasSharedUIPath && hasJoinImport) {
      console.log(`   ✅ ${appName}: Tailwind config properly configured`);
    } else {
      console.log(`   ⚠️  ${appName}: Missing some configurations:`);
      if (!hasCreateGlobPatterns) console.log(`      - Missing createGlobPatternsForDependencies`);
      if (!hasSharedUIPath) console.log(`      - Missing shared UI library path`);
      if (!hasJoinImport) console.log(`      - Missing path join import`);
    }
  });
}

// Check 2: Verify TypeScript path mappings
function checkTypeScriptPaths() {
  console.log('\n2. Checking TypeScript path mappings...');
  
  const tsconfigBase = path.join(__dirname, 'tsconfig.base.json');
  
  if (!fs.existsSync(tsconfigBase)) {
    console.log('   ❌ tsconfig.base.json not found');
    return;
  }
  
  const content = fs.readFileSync(tsconfigBase, 'utf8');
  const config = JSON.parse(content);
  
  const requiredPaths = [
    '@learn-platform/shared-ui',
    '@learn-platform/shared-styles'
  ];
  
  const paths = config.compilerOptions?.paths || {};
  
  requiredPaths.forEach(pathKey => {
    if (paths[pathKey]) {
      console.log(`   ✅ ${pathKey}: Mapped correctly`);
    } else {
      console.log(`   ❌ ${pathKey}: Missing path mapping`);
    }
  });
}

// Check 3: Verify shared UI library structure
function checkSharedUILibrary() {
  console.log('\n3. Checking shared UI library structure...');
  
  const uiLibPath = path.join(__dirname, 'libs/shared/ui/src');
  const indexFile = path.join(uiLibPath, 'index.ts');
  const componentsDir = path.join(uiLibPath, 'components/ui');
  
  if (!fs.existsSync(uiLibPath)) {
    console.log('   ❌ Shared UI library directory not found');
    return;
  }
  
  if (!fs.existsSync(indexFile)) {
    console.log('   ❌ index.ts export file not found');
    return;
  }
  
  if (!fs.existsSync(componentsDir)) {
    console.log('   ❌ UI components directory not found');
    return;
  }
  
  // Check for key components
  const expectedComponents = ['button.tsx', 'card.tsx', 'input.tsx'];
  const missingComponents = expectedComponents.filter(comp => 
    !fs.existsSync(path.join(componentsDir, comp))
  );
  
  if (missingComponents.length === 0) {
    console.log('   ✅ All core components found (Button, Card, Input)');
  } else {
    console.log(`   ⚠️  Missing components: ${missingComponents.join(', ')}`);
  }
  
  // Check index.ts exports
  const indexContent = fs.readFileSync(indexFile, 'utf8');
  const hasButtonExport = indexContent.includes('Button');
  const hasCardExport = indexContent.includes('Card');
  const hasInputExport = indexContent.includes('Input');
  
  if (hasButtonExport && hasCardExport && hasInputExport) {
    console.log('   ✅ Core components properly exported');
  } else {
    console.log('   ⚠️  Some components not exported in index.ts');
  }
}

// Check 4: Verify test pages exist
function checkTestPages() {
  console.log('\n4. Checking test pages...');
  
  const webTestPage = path.join(__dirname, 'apps/web/src/app/test-ui/page.tsx');
  const adminTestPage = path.join(__dirname, 'apps/admin/src/app/test-ui/page.tsx');
  
  [
    { path: webTestPage, name: 'Web app test page' },
    { path: adminTestPage, name: 'Admin app test page' }
  ].forEach(({ path: pagePath, name }) => {
    if (fs.existsSync(pagePath)) {
      const content = fs.readFileSync(pagePath, 'utf8');
      const hasSharedUIImport = content.includes('@learn-platform/shared-ui');
      const hasButtonUsage = content.includes('<Button');
      const hasCardUsage = content.includes('<Card');
      
      if (hasSharedUIImport && hasButtonUsage && hasCardUsage) {
        console.log(`   ✅ ${name}: Created and uses shared components`);
      } else {
        console.log(`   ⚠️  ${name}: Exists but may not use shared components properly`);
      }
    } else {
      console.log(`   ❌ ${name}: Not found`);
    }
  });
}

// Check 5: Verify global CSS imports
function checkGlobalCSS() {
  console.log('\n5. Checking global CSS imports...');
  
  const webGlobalCSS = path.join(__dirname, 'apps/web/src/app/global.css');
  const adminGlobalCSS = path.join(__dirname, 'apps/admin/src/app/global.css');
  
  [
    { path: webGlobalCSS, name: 'Web app' },
    { path: adminGlobalCSS, name: 'Admin app' }
  ].forEach(({ path: cssPath, name }) => {
    if (fs.existsSync(cssPath)) {
      const content = fs.readFileSync(cssPath, 'utf8');
      const hasSharedStylesImport = content.includes('@learn-platform/shared-styles');
      
      if (hasSharedStylesImport) {
        console.log(`   ✅ ${name}: Imports shared styles correctly`);
      } else {
        console.log(`   ⚠️  ${name}: Missing shared styles import`);
      }
    } else {
      console.log(`   ❌ ${name}: global.css not found`);
    }
  });
}

// Run all checks
function runAllChecks() {
  checkTailwindConfigs();
  checkTypeScriptPaths();
  checkSharedUILibrary();
  checkTestPages();
  checkGlobalCSS();
  
  console.log('\n🎉 Verification complete!');
  console.log('\n📝 Next steps:');
  console.log('   1. Run development servers to test in browser');
  console.log('   2. Visit /test-ui pages in both apps');
  console.log('   3. Verify Tailwind styles are applied correctly');
  console.log('   4. Test hot reload with shared component changes');
}

runAllChecks();
