# INVALID_ORIGIN Error Fix Guide

## 🎯 Problem Summary

The INVALID_ORIGIN error occurs when the authentication system receives requests from an origin that's not in the trusted origins list. This typically happens in cross-origin authentication setups between frontend (Vercel) and backend (Cloudflare Workers).

### **Root Cause**
The issue was in the `generateTrustedOrigins()` function in `libs/auth/src/auth.ts`. Despite having logic to include production domains, the function was only generating localhost URLs. According to the official better-auth documentation, the correct approach is to explicitly configure `trustedOrigins` directly in the `betterAuth()` configuration.

## 🔧 Fixes Applied

### 1. **CORS Configuration Updated**
- **File**: `learn-platform/apps/api/src/index.ts`
- **Change**: Added production domain to CORS origins
- **Added**: `https://kwaci-learning.bmbn.dev` to allowed origins
- **Added**: `Cookie` and `Set-Cookie` headers for authentication

### 2. **Auth Client Configuration Fixed**
- **File**: `learn-platform/apps/web/src/lib/auth-client.ts`
- **Change**: Modified to always use API URL instead of relative URLs
- **Result**: Auth requests now go to Cloudflare Workers instead of Vercel

### 3. **Better-Auth Trusted Origins Fixed** ⭐ **MAIN FIX**
- **File**: `learn-platform/libs/auth/src/auth.ts`
- **Change**: Replaced complex origin generation with explicit trusted origins array
- **Added**: Direct configuration of `trustedOrigins` in `betterAuth()` call
- **Includes**: Development and production domains explicitly

### 4. **Production Environment Variables**
- **File**: `learn-platform/apps/api/wrangler.jsonc`
- **Change**: Added `BETTER_AUTH_URL` and `BETTER_AUTH_TRUSTED_ORIGINS` for production
- **Values**: Both set to `https://kwaci-learning.bmbn.dev`

## 🚀 Required Environment Variable Setup

### **⚠️ CRITICAL: Vercel Dashboard Environment Variables**
The error is coming from your Vercel deployment! Set these in your Vercel project dashboard:

```bash
# REQUIRED - Authentication Configuration
BETTER_AUTH_SECRET=your-production-secret-min-32-chars
BETTER_AUTH_URL=https://kwaci-learning.bmbn.dev
BETTER_AUTH_TRUSTED_ORIGINS=https://kwaci-learning.bmbn.dev

# REQUIRED - Database and API
DATABASE_URL=your-production-database-url
NEXT_PUBLIC_API_URL=https://your-cloudflare-workers-url.workers.dev
```

**📋 See `VERCEL_ENVIRONMENT_SETUP.md` for detailed setup instructions.**

### **Cloudflare Workers Dashboard Secrets**
Set these as secrets in your Cloudflare Workers dashboard (NOT in wrangler.jsonc):

```bash
# Authentication secret (sensitive)
BETTER_AUTH_SECRET=your-production-secret-min-32-chars

# Database connection (sensitive)
DATABASE_URL=your-production-database-url
```

## 📋 Deployment Checklist

### **Step 1: Get Your Cloudflare Workers URL**
1. Deploy your API to Cloudflare Workers
2. Note the deployed URL (e.g., `https://learn-platform-api-prod.your-account.workers.dev`)

### **Step 2: Update Vercel Environment Variables**
1. Go to Vercel Dashboard → Your Project → Settings → Environment Variables
2. Set `NEXT_PUBLIC_API_URL` to your Cloudflare Workers URL
3. Set other required environment variables

### **Step 3: Update Cloudflare Workers Secrets**
1. Go to Cloudflare Dashboard → Workers → Your Worker → Settings → Variables
2. Add `BETTER_AUTH_SECRET` as an encrypted variable
3. Add `DATABASE_URL` as an encrypted variable

### **Step 4: Redeploy Both Applications**
1. Redeploy your Cloudflare Workers API
2. Redeploy your Vercel web application

## 🔍 Verification Steps

### **1. Check CORS Headers**
```bash
curl -H "Origin: https://kwaci-learning.bmbn.dev" \
     -H "Access-Control-Request-Method: POST" \
     -H "Access-Control-Request-Headers: Content-Type" \
     -X OPTIONS \
     https://your-api-url.workers.dev/api/auth/sign-in
```

Expected response should include:
```
Access-Control-Allow-Origin: https://kwaci-learning.bmbn.dev
Access-Control-Allow-Credentials: true
```

### **2. Test Authentication Flow**
1. Visit `https://kwaci-learning.bmbn.dev/login`
2. Open browser dev tools → Network tab
3. Attempt to sign in
4. Verify auth requests go to your Cloudflare Workers URL
5. Check for successful authentication without INVALID_ORIGIN errors

## 🚨 Common Issues & Solutions

### **Issue**: Still getting INVALID_ORIGIN error
**Solution**: 
- Verify `NEXT_PUBLIC_API_URL` is set correctly in Vercel
- Check that the Cloudflare Workers URL is accessible
- Ensure both applications are redeployed after changes

### **Issue**: Auth requests going to wrong URL
**Solution**:
- Clear browser cache and cookies
- Verify `NEXT_PUBLIC_API_URL` environment variable
- Check auth client configuration

### **Issue**: CORS errors in browser console
**Solution**:
- Verify production domain is in CORS origins list
- Check that credentials are enabled in CORS config
- Ensure proper headers are allowed

## 📞 Next Steps

After implementing these fixes:

1. **Deploy the API changes** to Cloudflare Workers
2. **Set environment variables** in both Vercel and Cloudflare dashboards
3. **Redeploy the web application** to Vercel
4. **Test the authentication flow** thoroughly

The INVALID_ORIGIN error should be resolved, and authentication should work properly between your Vercel frontend and Cloudflare Workers backend.
