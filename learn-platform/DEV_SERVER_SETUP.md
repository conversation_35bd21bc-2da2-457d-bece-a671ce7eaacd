# Development Server Management Documentation

## 📖 Overview

This document describes the enhanced development server management system implemented for the Learning Platform Nx monorepo. The system provides a unified interface for starting and managing development servers across all applications with proper process management, environment variable integration, and developer-friendly features.

## 🔄 Before vs After

### Previous Implementation (Old Script)
- ❌ Only supported Next.js apps (web/admin)
- ❌ No API server integration
- ❌ Manual Next.js startup methods
- ❌ Limited argument support
- ❌ No graceful shutdown handling
- ❌ Interactive menu-based selection only
- ❌ Basic error handling

### Enhanced Implementation (New Script)
- ✅ Supports all three applications (web, admin, API)
- ✅ Uses Nx commands for consistent execution
- ✅ Command-line argument support
- ✅ Proper process management with PID tracking
- ✅ Graceful shutdown with signal handling
- ✅ Colored output and clear status messages
- ✅ Environment variable verification
- ✅ Port conflict detection
- ✅ Comprehensive error handling and recovery suggestions

## 🚀 Available Commands

### Development Server Script Commands

#### Start All Applications (Default)
```bash
./start-dev-servers.sh
./start-dev-servers.sh all
```

#### Start Individual Applications
```bash
./start-dev-servers.sh web     # Web frontend only
./start-dev-servers.sh admin   # Admin dashboard only
./start-dev-servers.sh api     # API server only
```

#### Help and Information
```bash
./start-dev-servers.sh help
./start-dev-servers.sh -h
./start-dev-servers.sh --help
```

### Direct Nx Commands

For advanced users or CI/CD integration, you can run applications directly using Nx:

```bash
# Web Frontend (Next.js)
npx nx run web:dev

# Admin Dashboard (Next.js)
npx nx run admin:dev

# API Server (Cloudflare Workers)
npx nx run api:dev
```

## ⚙️ Configuration Changes

### API Project Configuration Updates

The `apps/api/project.json` file was updated to use the new JSONC configuration format:

```json
{
  "dev": {
    "executor": "nx:run-commands",
    "options": {
      "command": "wrangler dev --config wrangler.jsonc",
      "cwd": "apps/api"
    }
  },
  "build": {
    "assets": ["apps/api/wrangler.jsonc"]
  }
}
```

### Port Assignments

| Application | Port | URL | Description |
|-------------|------|-----|-------------|
| Web Frontend | 3000 | http://localhost:3000 | Next.js web application |
| Admin Dashboard | 4000 | http://localhost:4000 | Next.js admin interface |
| API Server | 8787 | http://localhost:8787 | Cloudflare Workers API |
| tRPC Endpoint | 8787 | http://localhost:8787/trpc | tRPC API endpoint |

### Environment Variable Integration

The script integrates with the hybrid environment variable setup:

- **Root `.env.local`**: Shared variables (database, auth)
- **App-specific `.env.local`**: Application-specific variables
- **`wrangler.jsonc`**: Cloudflare Workers configuration

## 💡 Usage Examples

### Full-Stack Development
Start all applications for complete development environment:
```bash
./start-dev-servers.sh
```
Output:
```
🚀 Starting development servers...
📱 Starting API Server (Cloudflare Workers)...
   ✅ Started API Server (PID: 12345)
   🌐 Available at: http://localhost:8787

📱 Starting Web Frontend (Next.js)...
   ✅ Started Web Frontend (PID: 12346)
   🌐 Available at: http://localhost:3000

📱 Starting Admin Dashboard (Next.js)...
   ✅ Started Admin Dashboard (PID: 12347)
   🌐 Available at: http://localhost:4000
```

### Frontend-Only Development
When working only on frontend features:
```bash
./start-dev-servers.sh web
```

### API-Only Development
When working on backend/API features:
```bash
./start-dev-servers.sh api
```

### Admin-Only Development
When working on admin interface:
```bash
./start-dev-servers.sh admin
```

## 🔧 Troubleshooting

### Common Issues and Solutions

#### Port Already in Use
**Problem**: `⚠️ Port 3000 is already in use`

**Solutions**:
```bash
# Find and kill the process using the port
lsof -ti:3000 | xargs kill -9

# Or use a different port (for direct Nx commands)
npx nx run web:dev -- --port 3001
```

#### Missing Dependencies
**Problem**: `❌ npx not found`

**Solution**:
```bash
# Install Node.js and npm
# macOS with Homebrew
brew install node

# Or download from nodejs.org
```

#### Environment Variables Not Loaded
**Problem**: Applications can't connect to database or API

**Solutions**:
1. Verify `.env.local` files exist:
   ```bash
   ls -la .env.local apps/web/.env.local apps/admin/.env.local
   ```

2. Check environment variable values:
   ```bash
   cat .env.local
   ```

3. Restart the development servers after changing environment variables

#### Wrangler Configuration Issues
**Problem**: `❌ Failed to start api`

**Solutions**:
1. Verify `wrangler.jsonc` exists:
   ```bash
   ls -la apps/api/wrangler.jsonc
   ```

2. Test wrangler directly:
   ```bash
   cd apps/api && npx wrangler dev --config wrangler.jsonc
   ```

3. Check Cloudflare Workers compatibility:
   ```bash
   npx wrangler --version
   ```

#### Script Permission Issues
**Problem**: `Permission denied: ./start-dev-servers.sh`

**Solution**:
```bash
chmod +x start-dev-servers.sh
```

### Debug Mode

For detailed debugging, you can run individual Nx commands with verbose output:
```bash
npx nx run web:dev --verbose
npx nx run admin:dev --verbose
npx nx run api:dev --verbose
```

## 🔬 Technical Details

### Process Management Features

#### PID Tracking
The script maintains arrays of process IDs and application names:
```bash
declare -a PIDS=()
declare -a APP_NAMES=()
```

Each started application's PID is stored for proper cleanup.

#### Graceful Shutdown
Signal handlers ensure clean shutdown:
```bash
trap cleanup SIGINT SIGTERM EXIT
```

The cleanup process:
1. Sends `SIGTERM` to all tracked processes
2. Waits up to 10 seconds for graceful shutdown
3. Force kills (`SIGKILL`) any remaining processes
4. Displays shutdown status for each application

#### Port Conflict Detection
Before starting any application, the script checks if the required port is available:
```bash
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
        print_status $YELLOW "⚠️  Port $port is already in use"
        return 1
    fi
}
```

### Environment Verification

The script performs comprehensive environment checks:

1. **Directory Structure**: Verifies `nx.json` and `package.json` exist
2. **Application Directories**: Confirms all app directories are present
3. **Dependencies**: Checks for `npx` availability
4. **Environment Files**: Warns if `.env.local` files are missing

### Startup Sequence

When starting all applications (`./start-dev-servers.sh all`):

1. **API Server First**: Started first as frontend apps depend on it
2. **3-second delay**: Allows API server to fully initialize
3. **Web Frontend**: Started second
4. **2-second delay**: Prevents resource conflicts
5. **Admin Dashboard**: Started last

This sequence ensures proper dependency resolution and reduces startup conflicts.

## 🔗 Integration Notes

### Nx Monorepo Integration

The development server management system is fully integrated with the Nx monorepo structure:

#### Project Configuration
Each application uses Nx's `project.json` for configuration:
- **Executors**: Uses `nx:run-commands` for flexible command execution
- **Dependencies**: Implicit dependencies ensure proper build order
- **Assets**: Configuration files are included in build outputs

#### Nx Commands
The script leverages Nx's built-in capabilities:
```bash
npx nx run <app>:dev    # Development server
npx nx run <app>:build  # Production build
npx nx run <app>:lint   # Code linting
npx nx run <app>:test   # Unit tests
```

#### Workspace Benefits
- **Shared Dependencies**: Common packages managed at workspace level
- **Code Generation**: Nx generators for consistent project structure
- **Caching**: Nx caching improves build and test performance
- **Dependency Graph**: Visual representation of project relationships

### Environment Variable Integration

The system works seamlessly with the hybrid environment variable setup:

#### Loading Order (Next.js Apps)
1. **Root `.env.local`**: Shared variables loaded first
2. **App `.env.local`**: App-specific variables override shared ones
3. **Runtime**: Variables available in both server and client contexts

#### Cloudflare Workers (API)
- Uses `wrangler.jsonc` configuration
- Variables defined in `[vars]` section for local development
- Environment-specific sections for production deployment

#### Verification
The script verifies environment setup:
```bash
# Checks performed
- Root .env.local exists
- App-specific .env.local files exist
- wrangler.jsonc configuration is valid
```

### Development Workflow Integration

#### Hot Reload Support
All applications support hot reload:
- **Next.js Apps**: Automatic page refresh on file changes
- **Cloudflare Workers**: Manual restart required for changes
- **Shared Libraries**: Changes trigger rebuilds in dependent apps

#### Database Integration
- **Shared Connection**: All apps use the same `DATABASE_URL`
- **Migrations**: Run `bun run db:generate` and `bun run db:migrate` after schema changes
- **Development Data**: Consistent data across all applications

#### API Development
- **tRPC Integration**: Type-safe API calls between frontend and backend
- **CORS Configuration**: Properly configured for local development
- **Authentication**: Shared better-auth configuration across apps

## 📚 Quick Reference

### Essential Commands
```bash
# Start all development servers
./start-dev-servers.sh

# Start individual servers
./start-dev-servers.sh web
./start-dev-servers.sh admin
./start-dev-servers.sh api

# Get help
./start-dev-servers.sh help

# Stop servers
# Press Ctrl+C in the terminal running the script
```

### Port Reference
| Service | Port | URL |
|---------|------|-----|
| Web App | 3000 | http://localhost:3000 |
| Admin App | 4000 | http://localhost:4000 |
| API Server | 8787 | http://localhost:8787 |
| tRPC Endpoint | 8787 | http://localhost:8787/trpc |

### File Locations
```
learn-platform/
├── start-dev-servers.sh          # Main development server script
├── .env.local                    # Shared environment variables
├── apps/web/.env.local           # Web app variables
├── apps/admin/.env.local         # Admin app variables
├── apps/api/wrangler.jsonc       # API configuration
└── DEV_SERVER_SETUP.md          # This documentation
```

### Environment Files
- **Root**: Database URL, auth secrets
- **Web/Admin**: API endpoint URLs
- **API**: All variables (duplicated from root for Cloudflare Workers)

---

## 🎯 Next Steps

After setting up the development servers:

1. **Verify Setup**: All applications should start without errors
2. **Test Connectivity**: Frontend apps should connect to API successfully
3. **Check Hot Reload**: Make changes to verify automatic reloading works
4. **Database Setup**: Ensure database migrations are up to date
5. **Environment Customization**: Update `.env.local` files for your specific setup

For additional help, refer to the individual application documentation or the main project README.
