# Environment Variables Setup Guide

## 🚀 Quick Setup

To set up your local development environment, copy the example files and customize them:

```bash
# Copy root environment variables (shared: database, auth)
cp .env.example .env.local

# Copy frontend environment variables (shared by web & admin apps)
cp .env.frontend.example .env.frontend.local
```

## 📁 Environment File Structure

```
learn-platform/
├── .env.local                    # Shared variables (database, auth)
├── .env.example                  # Template for shared variables
├── .env.frontend.local           # Frontend apps variables (web & admin)
├── .env.frontend.example         # Template for frontend apps
└── apps/api/wrangler.jsonc       # API configuration (includes env vars)
```

## 🔧 Required Variables

### Root `.env.local` (Shared)
```bash
# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/database_name

# Authentication Configuration
BETTER_AUTH_SECRET=your-super-secret-key-min-32-chars
BETTER_AUTH_URL=http://localhost:3000
```

### Frontend Apps `.env.frontend.local`
```bash
# API Configuration (shared by web and admin apps)
NEXT_PUBLIC_API_URL=http://localhost:8787
```

## 🔒 Security Notes

- **Never commit `.env.local` files** - They contain sensitive information
- **Always use `.env.example` files** - These are safe templates for other developers
- **Generate strong secrets** - Use at least 32 characters for auth secrets
- **Use different values for production** - Never use development values in production

## 🛠️ Development vs Production

### Development
- Use `http://localhost` URLs
- Use local database connections
- Use development-friendly secrets

### Production
- Use HTTPS URLs for all services
- Use secure database connections
- Use strong, unique secrets
- Set environment variables in deployment platforms (Vercel, Cloudflare)

## 🔍 Verification

After setting up your environment variables, verify they're loaded correctly:

```bash
# Start development servers
./start-dev-servers.sh

# Check that all services start without errors
# Web: http://localhost:3000
# Admin: http://localhost:4000  
# API: http://localhost:8787
```

## 🆘 Troubleshooting

### Missing Environment Variables
If you see errors about missing environment variables:

1. Check that all `.env.local` files exist
2. Verify the variable names match the examples
3. Restart development servers after changes

### Database Connection Issues
If database connections fail:

1. Verify `DATABASE_URL` is correct
2. Ensure your database server is running
3. Check database credentials and permissions

### API Connection Issues
If frontend apps can't connect to the API:

1. Verify `NEXT_PUBLIC_API_URL` is set correctly
2. Ensure the API server is running on port 8787
3. Check for CORS configuration issues

For more detailed information, see `ENVIRONMENT_VARIABLES.md` and `DEV_SERVER_SETUP.md`.
