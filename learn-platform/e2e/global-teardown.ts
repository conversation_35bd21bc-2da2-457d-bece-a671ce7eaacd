/**
 * Global teardown for Playwright tests
 * Cleans up test data and performs post-test cleanup
 */

import { chromium, FullConfig } from '@playwright/test';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting global test teardown...');

  const { baseURL } = config.projects[0].use;
  const browser = await chromium.launch();
  const page = await browser.newPage();

  try {
    // Clean up test data
    await cleanupTestData(page, baseURL);

    // Generate test report summary
    await generateTestSummary();

    console.log('✅ Global teardown completed successfully');
  } catch (error) {
    console.error('❌ Global teardown failed:', error);
    // Don't throw error to avoid failing the test run
  } finally {
    await browser.close();
  }
}

async function cleanupTestData(page: any, baseURL: string | undefined) {
  console.log('🗑️ Cleaning up test data...');

  const testUser = {
    email: process.env.TEST_USER_EMAIL || '<EMAIL>',
    password: process.env.TEST_USER_PASSWORD || 'TestPassword123!',
  };

  try {
    // Sign in as test user
    await page.goto(`${baseURL}/auth/signin`);
    await page.fill('[data-testid="email-input"]', testUser.email);
    await page.fill('[data-testid="password-input"]', testUser.password);
    await page.click('[data-testid="signin-button"]');
    await page.waitForURL('**/dashboard', { timeout: 5000 });

    // Clean up test content
    await cleanupTestContent(page, baseURL);

    console.log('✅ Test data cleanup completed');
  } catch (error) {
    console.log('⚠️ Could not clean up test data:', error);
    // This is not critical - test data can accumulate
  }
}

async function cleanupTestContent(page: any, baseURL: string | undefined) {
  console.log('📄 Cleaning up test learning content...');

  try {
    // Navigate to content list
    await page.goto(`${baseURL}/dashboard/content`);
    await page.waitForLoadState('networkidle');

    // Find test content (content with test-related titles)
    const testContentSelectors = [
      '[data-testid="content-card"]:has-text("test")',
      '[data-testid="content-card"]:has-text("Test")',
      '[data-testid="content-card"]:has-text("machine learning")',
      '[data-testid="content-card"]:has-text("Mobile test")',
      '[data-testid="content-card"]:has-text("Accessibility test")',
      '[data-testid="content-card"]:has-text("Network error test")',
    ];

    for (const selector of testContentSelectors) {
      try {
        const testCards = page.locator(selector);
        const count = await testCards.count();

        for (let i = 0; i < count; i++) {
          const card = testCards.nth(i);
          
          // Click delete button
          await card.locator('[data-testid="delete-button"]').click();
          
          // Confirm deletion
          await page.click('[data-testid="confirm-delete-button"]');
          
          // Wait for deletion to complete
          await page.waitForTimeout(1000);
        }

        if (count > 0) {
          console.log(`🗑️ Deleted ${count} test content items matching: ${selector}`);
        }
      } catch (error) {
        console.log(`⚠️ Could not delete content for selector ${selector}:`, error);
      }
    }
  } catch (error) {
    console.log('⚠️ Content cleanup had issues:', error);
  }
}

async function generateTestSummary() {
  console.log('📊 Generating test summary...');

  try {
    const fs = require('fs');
    const path = require('path');

    // Read test results if available
    const resultsPath = path.join(process.cwd(), 'test-results', 'results.json');
    
    if (fs.existsSync(resultsPath)) {
      const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
      
      const summary = {
        timestamp: new Date().toISOString(),
        totalTests: results.stats?.total || 0,
        passed: results.stats?.passed || 0,
        failed: results.stats?.failed || 0,
        skipped: results.stats?.skipped || 0,
        duration: results.stats?.duration || 0,
        environment: {
          baseURL: process.env.BASE_URL || 'http://localhost:3000',
          nodeVersion: process.version,
          platform: process.platform,
        },
      };

      // Write summary
      const summaryPath = path.join(process.cwd(), 'test-results', 'summary.json');
      fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));

      console.log('📈 Test Summary:');
      console.log(`   Total Tests: ${summary.totalTests}`);
      console.log(`   Passed: ${summary.passed}`);
      console.log(`   Failed: ${summary.failed}`);
      console.log(`   Skipped: ${summary.skipped}`);
      console.log(`   Duration: ${(summary.duration / 1000).toFixed(2)}s`);
    }
  } catch (error) {
    console.log('⚠️ Could not generate test summary:', error);
  }
}

export default globalTeardown;
