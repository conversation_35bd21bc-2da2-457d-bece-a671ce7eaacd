/**
 * Global setup for Playwright tests
 * Prepares test environment, creates test users, and sets up test data
 */

import { chromium, FullConfig } from '@playwright/test';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting global test setup...');

  const { baseURL } = config.projects[0].use;
  const browser = await chromium.launch();
  const page = await browser.newPage();

  try {
    // Wait for the application to be ready
    console.log('⏳ Waiting for application to be ready...');
    await page.goto(baseURL || 'http://localhost:3000');
    await page.waitForLoadState('networkidle');

    // Check if test user exists, create if not
    await setupTestUser(page, baseURL);

    // Warm up the application
    await warmupApplication(page, baseURL);

    console.log('✅ Global setup completed successfully');
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

async function setupTestUser(page: any, baseURL: string | undefined) {
  console.log('👤 Setting up test user...');

  const testUser = {
    email: process.env.TEST_USER_EMAIL || '<EMAIL>',
    password: process.env.TEST_USER_PASSWORD || 'TestPassword123!',
    name: 'Test User',
  };

  try {
    // Try to sign in first to check if user exists
    await page.goto(`${baseURL}/auth/signin`);
    await page.fill('[data-testid="email-input"]', testUser.email);
    await page.fill('[data-testid="password-input"]', testUser.password);
    await page.click('[data-testid="signin-button"]');

    // If sign in successful, user exists
    await page.waitForURL('**/dashboard', { timeout: 5000 });
    console.log('✅ Test user already exists and can sign in');
    
    // Sign out for clean state
    await page.click('[data-testid="user-menu"]');
    await page.click('[data-testid="signout-button"]');
    
  } catch (error) {
    // User doesn't exist or sign in failed, try to create account
    console.log('📝 Test user not found, creating new account...');
    
    try {
      await page.goto(`${baseURL}/auth/signup`);
      await page.fill('[data-testid="name-input"]', testUser.name);
      await page.fill('[data-testid="email-input"]', testUser.email);
      await page.fill('[data-testid="password-input"]', testUser.password);
      await page.fill('[data-testid="confirm-password-input"]', testUser.password);
      await page.click('[data-testid="signup-button"]');

      // Wait for successful signup
      await page.waitForURL('**/dashboard', { timeout: 10000 });
      console.log('✅ Test user created successfully');
      
      // Sign out for clean state
      await page.click('[data-testid="user-menu"]');
      await page.click('[data-testid="signout-button"]');
      
    } catch (signupError) {
      console.log('ℹ️ User creation failed (may already exist):', signupError);
      // This is okay - user might already exist from previous runs
    }
  }
}

async function warmupApplication(page: any, baseURL: string | undefined) {
  console.log('🔥 Warming up application...');

  try {
    // Visit key pages to warm up caches
    const pagesToWarmup = [
      '/',
      '/dashboard',
      '/learn/create',
      '/dashboard/content',
    ];

    for (const path of pagesToWarmup) {
      try {
        await page.goto(`${baseURL}${path}`);
        await page.waitForLoadState('networkidle', { timeout: 5000 });
        console.log(`✅ Warmed up: ${path}`);
      } catch (error) {
        console.log(`⚠️ Could not warm up ${path}:`, error);
      }
    }

    // Warm up API endpoints
    await warmupAPI(page, baseURL);

  } catch (error) {
    console.log('⚠️ Application warmup had issues:', error);
    // Don't fail the setup for warmup issues
  }
}

async function warmupAPI(page: any, baseURL: string | undefined) {
  console.log('🌐 Warming up API endpoints...');

  const apiEndpoints = [
    '/api/trpc/auth.getSession',
    '/api/trpc/learningContent.getAll',
  ];

  for (const endpoint of apiEndpoints) {
    try {
      const response = await page.request.get(`${baseURL}${endpoint}`);
      console.log(`✅ API warmed up: ${endpoint} (${response.status()})`);
    } catch (error) {
      console.log(`⚠️ Could not warm up API ${endpoint}:`, error);
    }
  }
}

export default globalSetup;
