/**
 * End-to-End Tests for Learning Content Flow
 * Tests the complete user journey from authentication to content generation and management
 */

import { test, expect, Page } from '@playwright/test';

// Test configuration
const TEST_USER = {
  email: process.env.TEST_USER_EMAIL || '<EMAIL>',
  password: process.env.TEST_USER_PASSWORD || 'TestPassword123!',
};

const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';

// Helper functions
async function signIn(page: Page) {
  await page.goto(`${BASE_URL}/auth/signin`);

  await page.fill('[data-testid="email-input"]', TEST_USER.email);
  await page.fill('[data-testid="password-input"]', TEST_USER.password);
  await page.click('[data-testid="signin-button"]');

  // Wait for redirect to dashboard
  await page.waitForURL('**/dashboard');
  await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
}

async function navigateToLearningForm(page: Page) {
  await page.click('[data-testid="create-content-button"]');
  await page.waitForURL('**/learn/create');
  await expect(page.locator('[data-testid="learning-input-form"]')).toBeVisible();
}

async function fillLearningForm(page: Page, topic: string) {
  // Fill topic
  await page.fill('[data-testid="topic-input"]', topic);

  // Select learning level
  await page.selectOption('[data-testid="learning-level-select"]', 'beginner');

  // Select content types
  await page.check('[data-testid="content-type-paragraph"]');
  await page.check('[data-testid="content-type-bulletList"]');

  // Add focus areas
  await page.fill('[data-testid="focus-areas-input"]', 'practical examples and real-world applications');
}

test.describe('Learning Content Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Set up test environment
    await page.goto(BASE_URL);
  });

  test('complete learning content creation flow', async ({ page }) => {
    // Step 1: Sign in
    await signIn(page);

    // Step 2: Navigate to learning form
    await navigateToLearningForm(page);

    // Step 3: Fill and submit form
    const testTopic = 'How does machine learning work?';
    await fillLearningForm(page, testTopic);

    // Submit form
    await page.click('[data-testid="generate-content-button"]');

    // Step 4: Wait for AI generation
    await expect(page.locator('[data-testid="generation-progress"]')).toBeVisible();
    await expect(page.locator('[data-testid="generation-progress"]')).toBeHidden({ timeout: 60000 });

    // Step 5: Verify content was generated
    await expect(page.locator('[data-testid="generated-content"]')).toBeVisible();
    await expect(page.locator('[data-testid="content-title"]')).toContainText(testTopic);

    // Step 6: Verify content structure
    await expect(page.locator('[data-testid="content-steps"]')).toBeVisible();
    await expect(page.locator('[data-testid="step-item"]')).toHaveCount({ min: 1 });

    // Step 7: Test content interaction
    const firstStep = page.locator('[data-testid="step-item"]').first();
    await expect(firstStep).toBeVisible();

    // Click on step to expand/collapse
    await firstStep.click();
    await expect(firstStep.locator('[data-testid="step-content"]')).toBeVisible();

    // Step 8: Save content
    await page.click('[data-testid="save-content-button"]');
    await expect(page.locator('[data-testid="save-success-message"]')).toBeVisible();

    // Step 9: Navigate to content list
    await page.click('[data-testid="view-my-content-button"]');
    await page.waitForURL('**/dashboard/content');

    // Step 10: Verify content appears in list
    await expect(page.locator('[data-testid="content-card"]')).toHaveCount({ min: 1 });
    await expect(page.locator('[data-testid="content-card"]').first()).toContainText(testTopic);
  });

  test('form validation and error handling', async ({ page }) => {
    await signIn(page);
    await navigateToLearningForm(page);

    // Test empty form submission
    await page.click('[data-testid="generate-content-button"]');
    await expect(page.locator('[data-testid="topic-error"]')).toContainText('Topic is required');

    // Test topic too short
    await page.fill('[data-testid="topic-input"]', 'AI');
    await page.click('[data-testid="generate-content-button"]');
    await expect(page.locator('[data-testid="topic-error"]')).toContainText('at least 3 characters');

    // Test no content types selected
    await page.fill('[data-testid="topic-input"]', 'How does AI work?');
    await page.click('[data-testid="generate-content-button"]');
    await expect(page.locator('[data-testid="content-types-error"]')).toContainText('at least one content type');

    // Test valid form
    await page.check('[data-testid="content-type-paragraph"]');
    await page.click('[data-testid="generate-content-button"]');
    await expect(page.locator('[data-testid="generation-progress"]')).toBeVisible();
  });

  test('content management operations', async ({ page }) => {
    await signIn(page);

    // Navigate to content list
    await page.goto(`${BASE_URL}/dashboard/content`);

    // Assume there's at least one content item
    const contentCard = page.locator('[data-testid="content-card"]').first();
    await expect(contentCard).toBeVisible();

    // Test view content
    await contentCard.locator('[data-testid="view-button"]').click();
    await expect(page.locator('[data-testid="content-viewer"]')).toBeVisible();
    await page.goBack();

    // Test edit content
    await contentCard.locator('[data-testid="edit-button"]').click();
    await expect(page.locator('[data-testid="content-editor"]')).toBeVisible();
    await page.goBack();

    // Test duplicate content
    await contentCard.locator('[data-testid="duplicate-button"]').click();
    await expect(page.locator('[data-testid="duplicate-success-message"]')).toBeVisible();

    // Test toggle public/private
    await contentCard.locator('[data-testid="toggle-public-button"]').click();
    await expect(page.locator('[data-testid="visibility-updated-message"]')).toBeVisible();

    // Test delete content (with confirmation)
    await contentCard.locator('[data-testid="delete-button"]').click();
    await expect(page.locator('[data-testid="delete-confirmation-dialog"]')).toBeVisible();
    await page.click('[data-testid="cancel-delete-button"]');
    await expect(page.locator('[data-testid="delete-confirmation-dialog"]')).toBeHidden();
  });

  test('search and filtering functionality', async ({ page }) => {
    await signIn(page);
    await page.goto(`${BASE_URL}/dashboard/content`);

    // Test search
    await page.fill('[data-testid="search-input"]', 'machine learning');
    await page.press('[data-testid="search-input"]', 'Enter');

    // Wait for search results
    await page.waitForTimeout(1000);

    // Verify search results
    const searchResults = page.locator('[data-testid="content-card"]');
    await expect(searchResults).toHaveCount({ min: 0 });

    // Test learning level filter
    await page.selectOption('[data-testid="level-filter"]', 'beginner');
    await page.waitForTimeout(1000);

    // Test clear filters
    await page.click('[data-testid="clear-filters-button"]');
    await expect(page.locator('[data-testid="search-input"]')).toHaveValue('');
  });

  test('responsive design and mobile compatibility', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });

    await signIn(page);

    // Test mobile navigation
    await page.click('[data-testid="mobile-menu-button"]');
    await expect(page.locator('[data-testid="mobile-menu"]')).toBeVisible();

    // Test mobile form
    await navigateToLearningForm(page);
    await expect(page.locator('[data-testid="learning-input-form"]')).toBeVisible();

    // Verify form is usable on mobile
    await fillLearningForm(page, 'Mobile test topic');
    await page.click('[data-testid="generate-content-button"]');
    await expect(page.locator('[data-testid="generation-progress"]')).toBeVisible();

    // Test tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.reload();
    await expect(page.locator('[data-testid="learning-input-form"]')).toBeVisible();
  });

  test('accessibility compliance', async ({ page }) => {
    await signIn(page);
    await navigateToLearningForm(page);

    // Test keyboard navigation
    await page.keyboard.press('Tab');
    await expect(page.locator('[data-testid="topic-input"]')).toBeFocused();

    await page.keyboard.press('Tab');
    await expect(page.locator('[data-testid="learning-level-select"]')).toBeFocused();

    // Test ARIA labels and roles
    await expect(page.locator('[data-testid="topic-input"]')).toHaveAttribute('aria-label');
    await expect(page.locator('[data-testid="learning-level-select"]')).toHaveAttribute('aria-label');

    // Test form submission with keyboard
    await page.fill('[data-testid="topic-input"]', 'Accessibility test topic');
    await page.check('[data-testid="content-type-paragraph"]');
    await page.keyboard.press('Tab');
    await page.keyboard.press('Enter');

    await expect(page.locator('[data-testid="generation-progress"]')).toBeVisible();
  });

  test('error scenarios and recovery', async ({ page }) => {
    await signIn(page);
    await navigateToLearningForm(page);

    // Test network error simulation
    await page.route('**/api/trpc/learningContent.generateWithAI', route => {
      route.abort('failed');
    });

    await fillLearningForm(page, 'Network error test');
    await page.click('[data-testid="generate-content-button"]');

    // Verify error handling
    await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="retry-button"]')).toBeVisible();

    // Test retry functionality
    await page.unroute('**/api/trpc/learningContent.generateWithAI');
    await page.click('[data-testid="retry-button"]');

    await expect(page.locator('[data-testid="generation-progress"]')).toBeVisible();
  });

  test('performance and loading states', async ({ page }) => {
    await signIn(page);

    // Test loading states
    await page.goto(`${BASE_URL}/dashboard/content`);

    // Verify loading indicators
    await expect(page.locator('[data-testid="content-loading"]')).toBeVisible();
    await expect(page.locator('[data-testid="content-loading"]')).toBeHidden({ timeout: 10000 });

    // Test lazy loading
    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight);
    });

    // Verify additional content loads
    await page.waitForTimeout(2000);
    const contentCards = page.locator('[data-testid="content-card"]');
    await expect(contentCards).toHaveCount({ min: 1 });
  });
});
