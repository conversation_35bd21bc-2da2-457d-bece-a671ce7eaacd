/**
 * Simple test script to verify the new quiz history endpoint
 */

const testQuizHistory = async () => {
  try {
    console.log('Testing quiz history endpoint...');
    
    // Test the new endpoint
    const response = await fetch('http://localhost:8787/trpc/quiz.getHistoryByLearningContent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        learningContentId: '930bc9fa-5cc8-4a10-9ec1-46b29cae0ba5',
        limit: 20,
        offset: 0
      })
    });

    if (!response.ok) {
      console.error('HTTP Error:', response.status, response.statusText);
      const text = await response.text();
      console.error('Response body:', text);
      return;
    }

    const data = await response.json();
    console.log('Quiz history response:', JSON.stringify(data, null, 2));
    
  } catch (error) {
    console.error('Test failed:', error);
  }
};

// Run the test
testQuizHistory();
