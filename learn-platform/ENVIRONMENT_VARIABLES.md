# Environment Variables Configuration

This document explains the environment variable setup for the learning platform monorepo.

## 📁 File Structure

```
learn-platform/
├── .env.local                    # Shared variables (database, auth)
├── .env.example                  # Template for shared variables
├── .env.frontend.local           # Frontend apps variables (web & admin)
├── .env.frontend.example         # Template for frontend apps
└── apps/
    └── api/
        └── wrangler.jsonc        # Cloudflare Worker configuration
```

## 🔧 Variable Categories

### Shared Variables (Root `.env.local`)
These variables are used by multiple applications:

- `DATABASE_URL`: PostgreSQL connection string for Drizzle ORM
- `BETTER_AUTH_SECRET`: Secret key for better-auth (min 32 characters)
- `BETTER_AUTH_URL`: Base URL for authentication endpoints

### Frontend Apps Variables

#### Frontend Apps (`.env.frontend.local`)
- `NEXT_PUBLIC_API_URL`: API endpoint URL for tRPC client (shared by web & admin)

#### API App (`apps/api/wrangler.jsonc`)
- All shared variables are duplicated in `[vars]` section
- Environment-specific variables in `[env.production.vars]`

## 🚀 Setup Instructions

### Development Setup

1. Copy the example files:
```bash
cp .env.example .env.local
cp .env.frontend.example .env.frontend.local
```

2. Update the values in each file according to your local setup.

**Note**: The `.env.local` and `.env.frontend.local` files are ignored by Git to prevent committing sensitive information. Always use the `.env.example` files as templates.

### Production Setup

#### Vercel (Web & Admin Apps)
Set these environment variables in your Vercel dashboard:
- `DATABASE_URL`
- `BETTER_AUTH_SECRET`
- `BETTER_AUTH_URL`
- `NEXT_PUBLIC_API_URL`

#### Cloudflare Workers (API App)
Update `wrangler.toml` with production values:
```toml
[env.production.vars]
DATABASE_URL = "your-production-database-url"
BETTER_AUTH_SECRET = "your-production-secret"
BETTER_AUTH_URL = "https://yourdomain.com"
```

## 🔍 How It Works

### Next.js Apps (Web & Admin)
- Automatically load `.env.local` files from both root and app directories
- Root variables are available to all Next.js apps
- App-specific variables override root variables if same name

### Cloudflare Worker (API)
- Uses `wrangler.toml` configuration
- Cannot directly use `.env` files
- Variables defined in `[vars]` section for local development
- Environment-specific sections for production

## 🛡️ Security Notes

- Never commit actual `.env.local` files to version control
- Use strong, unique secrets for production
- Rotate secrets regularly
- Use environment-specific values for production vs development
