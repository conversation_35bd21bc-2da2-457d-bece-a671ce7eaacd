# Database Setup Guide

This guide will help you set up the database for the Learn Platform monorepo with better-auth integration.

## Prerequisites

- PostgreSQL database (local or cloud-hosted like Supabase)
- Bun package manager
- Environment variables configured

## Quick Setup

### 1. Create Environment File

Copy the example environment file and update it with your database credentials:

```bash
cp .env.example .env.local
```

**Note**: The configuration prioritizes `.env.local` over `.env` for local development.

### 2. Update Database URL

Edit the `.env` file and replace the DATABASE_URL with your actual PostgreSQL connection string:

```env
# For local PostgreSQL
DATABASE_URL=postgresql://username:password@localhost:5432/database_name

# For Supabase (recommended)
DATABASE_URL=postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-PROJECT-REF].supabase.co:5432/postgres

# For other cloud providers
DATABASE_URL=postgresql://username:password@host:port/database
```

### 3. Update Authentication Secret

Generate a secure secret key (minimum 32 characters) for better-auth:

```env
BETTER_AUTH_SECRET=your-super-secret-key-change-this-in-production-min-32-chars
```

### 4. Run Database Migrations

Generate and apply the database schema:

```bash
# Generate migration files from schema
bun run db:generate

# Apply migrations to database
bun run db:migrate
```

## Database Schema

The database includes the following tables for better-auth:

- **`user`** - User accounts with email/password authentication
- **`session`** - User sessions for cross-app authentication
- **`account`** - OAuth providers and credential storage
- **`verification`** - Email verification tokens

## Troubleshooting

### Error: "Environment variable DATABASE_URL is not set"

1. Ensure you have created a `.env.local` file in the workspace root (preferred) or `.env` file
2. Verify the DATABASE_URL is properly set in the environment file
3. Restart your terminal/IDE to reload environment variables

### Error: "Cannot connect to database"

1. Verify your database is running and accessible
2. Check the connection string format
3. Ensure firewall/network settings allow connections
4. For Supabase: verify the project is active and password is correct

### Error: "TypeScript warnings in drizzle.config.ts"

The configuration has been updated to handle TypeScript properly. If you still see warnings:

1. Ensure `@types/node` is in devDependencies (already included)
2. The drizzle config includes proper type declarations

## Using with Supabase (Recommended)

1. Create a new Supabase project at https://supabase.com
2. Go to Settings → Database
3. Copy the connection string from "Connection parameters"
4. Use the "URI" format in your `.env` file

## Verification

After setup, verify everything works:

```bash
# Test schema generation
bun run db:generate

# Test migration (requires DATABASE_URL)
bun run db:migrate
```

## Next Steps

Once the database is set up:

1. Test user registration in your applications
2. Verify session sharing between web app, admin app, and API
3. Test authentication flows across all applications

The database schema is now fully compatible with better-auth and ready for production use!
