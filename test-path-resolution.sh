#!/bin/bash

# Test script to verify path resolution for Vercel CLI
# This simulates the GitHub Actions environment

set -e

echo "🧪 Testing Vercel CLI Path Resolution"
echo "====================================="

# Simulate GitHub Actions environment
export GITHUB_WORKSPACE="/tmp/test-workspace"
mkdir -p "$GITHUB_WORKSPACE"
cd "$GITHUB_WORKSPACE"

echo "✅ Current working directory: $(pwd)"

# Create mock directory structure
mkdir -p learn-platform/apps/web
cd learn-platform/apps/web

# Create mock package.json
cat > package.json << 'EOF'
{
  "name": "@learn-platform/web",
  "version": "0.0.1",
  "private": true,
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "vercel-build": "next build"
  },
  "dependencies": {
    "next": "~15.2.4",
    "react": "19.0.0",
    "react-dom": "19.0.0"
  }
}
EOF

# Create mock vercel.json
cat > vercel.json << 'EOF'
{
  "version": 2,
  "public": false,
  "github": {
    "enabled": false
  },
  "buildCommand": "npm run vercel-build",
  "installCommand": "cd ../../ && bun install --frozen-lockfile && cd apps/web",
  "framework": "nextjs",
  "outputDirectory": ".next"
}
EOF

echo "✅ Created mock project structure"

# Go back to workspace root
cd "$GITHUB_WORKSPACE"

echo "📁 Directory structure:"
find . -name "package.json" -o -name "vercel.json" | head -10

echo ""
echo "🔍 Testing path resolution..."

# Test 1: Check if package.json exists at expected path
EXPECTED_PATH="./learn-platform/apps/web/package.json"
if [ -f "$EXPECTED_PATH" ]; then
    echo "✅ package.json found at: $(realpath $EXPECTED_PATH)"
else
    echo "❌ package.json NOT found at: $EXPECTED_PATH"
    exit 1
fi

# Test 2: Simulate working-directory behavior
echo ""
echo "🧪 Testing working-directory simulation..."
cd learn-platform/apps/web
if [ -f "package.json" ]; then
    echo "✅ package.json accessible from working-directory: $(pwd)"
    echo "   Full path: $(realpath package.json)"
else
    echo "❌ package.json NOT accessible from working-directory: $(pwd)"
    exit 1
fi

# Test 3: Check vercel.json
if [ -f "vercel.json" ]; then
    echo "✅ vercel.json found at: $(realpath vercel.json)"
else
    echo "❌ vercel.json NOT found"
    exit 1
fi

echo ""
echo "🎉 All path resolution tests passed!"
echo ""
echo "📋 Summary:"
echo "   - package.json: $(realpath package.json)"
echo "   - vercel.json: $(realpath vercel.json)"
echo "   - Working directory: $(pwd)"
echo ""
echo "✅ The GitHub Actions workflow should now work correctly!"

# Cleanup
cd /
rm -rf "$GITHUB_WORKSPACE"
